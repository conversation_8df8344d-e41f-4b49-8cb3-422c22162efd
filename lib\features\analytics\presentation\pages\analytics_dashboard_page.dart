import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../widgets/simple_analytics_widgets.dart';
import '../../domain/models/analytics_models.dart';
import '../../data/services/demo_analytics_data.dart';

class AnalyticsDashboardPage extends ConsumerStatefulWidget {
  final String sellerId;

  const AnalyticsDashboardPage({super.key, required this.sellerId});

  @override
  ConsumerState<AnalyticsDashboardPage> createState() =>
      _AnalyticsDashboardPageState();
}

class _AnalyticsDashboardPageState extends ConsumerState<AnalyticsDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  AnalyticsFilter _currentFilter = const AnalyticsFilter(
    timeRange: AnalyticsTimeRange.last30Days,
  );

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Dashboard'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Sales'),
            Tab(text: 'Performance'),
            Tab(text: 'Customers'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportData,
            tooltip: 'Export Data',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Section
          AnalyticsFilterWidget(
            currentFilter: _currentFilter,
            onFilterChanged: (filter) {
              setState(() {
                _currentFilter = filter;
              });
            },
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildSalesTab(),
                _buildPerformanceTab(),
                _buildCustomersTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    final demoData = DemoAnalyticsData.getDemoOverviewData(widget.sellerId);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Key Metrics Grid
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.5,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              MetricCard(
                title: 'Total Revenue',
                value: '₹${demoData['totalRevenue'].toStringAsFixed(0)}',
                subtitle: 'Last 30 days',
                icon: Icons.currency_rupee,
                color: AppColors.success,
                trendPercentage: 12.5,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Total Orders',
                value: demoData['totalOrders'].toString(),
                subtitle: 'Last 30 days',
                icon: Icons.shopping_bag,
                color: AppColors.primaryBlue,
                trendPercentage: 8.3,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Average Rating',
                value: demoData['averageRating'].toStringAsFixed(1),
                subtitle: 'Out of 5.0',
                icon: Icons.star,
                color: AppColors.warning,
                trendPercentage: 2.1,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Fulfillment Rate',
                value: '${demoData['fulfillmentRate'].toStringAsFixed(1)}%',
                subtitle: 'Orders completed',
                icon: Icons.check_circle,
                color: AppColors.info,
                trendPercentage: 1.2,
                isPositiveTrend: false,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Charts Section
          Text('Performance Trends', style: AppTextStyles.headlineMedium),
          const SizedBox(height: 16),

          SimpleChartWidget(
            title: 'Revenue Trend',
            subtitle: 'Daily revenue over time',
            dataPoints: DemoAnalyticsData.getDemoRevenueData(),
            chartType: 'line',
            primaryColor: AppColors.success,
          ),

          const SizedBox(height: 16),

          SimpleChartWidget(
            title: 'Order Distribution',
            subtitle: 'Orders by category',
            dataPoints: DemoAnalyticsData.getDemoCategoryData(),
            chartType: 'pie',
            primaryColor: AppColors.primaryBlue,
          ),
        ],
      ),
    );
  }

  Widget _buildSalesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Sales Analytics', style: AppTextStyles.headlineMedium),
          const SizedBox(height: 16),

          // Sales Metrics
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.5,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              MetricCard(
                title: 'Gross Revenue',
                value: '₹45,230',
                subtitle: 'Before deductions',
                icon: Icons.trending_up,
                color: AppColors.success,
                trendPercentage: 15.2,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Net Revenue',
                value: '₹38,450',
                subtitle: 'After deductions',
                icon: Icons.account_balance_wallet,
                color: AppColors.primaryBlue,
                trendPercentage: 12.8,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Average Order Value',
                value: '₹485',
                subtitle: 'Per order',
                icon: Icons.shopping_cart,
                color: AppColors.info,
                trendPercentage: 3.5,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Conversion Rate',
                value: '3.2%',
                subtitle: 'Views to orders',
                icon: Icons.trending_up,
                color: AppColors.warning,
                trendPercentage: 0.8,
                isPositiveTrend: false,
              ),
            ],
          ),

          const SizedBox(height: 24),

          SimpleChartWidget(
            title: 'Sales by Category',
            subtitle: 'Revenue breakdown',
            dataPoints: DemoAnalyticsData.getDemoSalesData(),
            chartType: 'bar',
            primaryColor: AppColors.success,
          ),

          const SizedBox(height: 16),

          SimpleChartWidget(
            title: 'Hourly Sales Pattern',
            subtitle: 'Peak hours analysis',
            dataPoints: DemoAnalyticsData.getDemoHourlyData(),
            chartType: 'line',
            primaryColor: AppColors.primaryBlue,
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Performance Metrics', style: AppTextStyles.headlineMedium),
          const SizedBox(height: 16),

          // Performance Metrics
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.5,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              MetricCard(
                title: 'Response Time',
                value: '12 min',
                subtitle: 'Average response',
                icon: Icons.timer,
                color: AppColors.success,
                trendPercentage: 5.2,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'On-Time Delivery',
                value: '94.5%',
                subtitle: 'Delivered on time',
                icon: Icons.delivery_dining,
                color: AppColors.primaryBlue,
                trendPercentage: 2.1,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Customer Satisfaction',
                value: '4.6/5.0',
                subtitle: 'Average rating',
                icon: Icons.sentiment_satisfied,
                color: AppColors.warning,
                trendPercentage: 1.8,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Return Rate',
                value: '2.1%',
                subtitle: 'Orders returned',
                icon: Icons.keyboard_return,
                color: AppColors.error,
                trendPercentage: 0.5,
                isPositiveTrend: false,
              ),
            ],
          ),

          const SizedBox(height: 24),

          SimpleChartWidget(
            title: 'Rating Distribution',
            subtitle: 'Customer ratings breakdown',
            dataPoints: DemoAnalyticsData.getDemoRatingData(),
            chartType: 'bar',
            primaryColor: AppColors.warning,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Customer Insights', style: AppTextStyles.headlineMedium),
          const SizedBox(height: 16),

          // Customer Metrics
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.5,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              MetricCard(
                title: 'Total Customers',
                value: '1,245',
                subtitle: 'Unique customers',
                icon: Icons.people,
                color: AppColors.primaryBlue,
                trendPercentage: 18.5,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'New Customers',
                value: '156',
                subtitle: 'This month',
                icon: Icons.person_add,
                color: AppColors.success,
                trendPercentage: 22.3,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Retention Rate',
                value: '68.5%',
                subtitle: 'Returning customers',
                icon: Icons.repeat,
                color: AppColors.info,
                trendPercentage: 4.2,
                isPositiveTrend: true,
              ),
              MetricCard(
                title: 'Lifetime Value',
                value: '₹2,450',
                subtitle: 'Average CLV',
                icon: Icons.account_balance,
                color: AppColors.warning,
                trendPercentage: 8.7,
                isPositiveTrend: true,
              ),
            ],
          ),

          const SizedBox(height: 24),

          SimpleChartWidget(
            title: 'Customer Demographics',
            subtitle: 'Age group distribution',
            dataPoints: DemoAnalyticsData.getDemoDemographicsData(),
            chartType: 'pie',
            primaryColor: AppColors.info,
          ),
        ],
      ),
    );
  }

  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Analytics Data'),
        content: const Text('Choose export format:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showExportSuccess('CSV');
            },
            child: const Text('CSV'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showExportSuccess('PDF');
            },
            child: const Text('PDF'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showExportSuccess(String format) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Analytics data exported as $format successfully!'),
        backgroundColor: AppColors.success,
      ),
    );
  }
}
