import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../../core/config/app_config.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../orders/orders_page.dart';
import '../tracking/rider_tracking_page.dart';
import '../profile/profile_page.dart';
import '../../../../../features/user/presentation/pages/chat/chat_list_page.dart';

class RiderHomePage extends ConsumerStatefulWidget {
  const RiderHomePage({super.key});

  @override
  ConsumerState<RiderHomePage> createState() => _RiderHomePageState();
}

class _RiderHomePageState extends ConsumerState<RiderHomePage> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const RiderDashboardTab(),
    const RiderOrdersPage(),
    const RiderTrackingTab(),
    const ChatListPage(),
    const RiderProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        selectedItemColor: AppConfig.primaryColor,
        unselectedItemColor: AppColors.grey500,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.local_shipping),
            label: 'Orders',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.navigation),
            label: 'Navigate',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.chat), label: 'Messages'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}

class RiderDashboardTab extends ConsumerStatefulWidget {
  const RiderDashboardTab({super.key});

  @override
  ConsumerState<RiderDashboardTab> createState() => _RiderDashboardTabState();
}

class _RiderDashboardTabState extends ConsumerState<RiderDashboardTab> {
  bool _isOnline = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rider Dashboard'),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.security),
            onPressed: () {
              Navigator.pushNamed(context, '/safety');
            },
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Navigate to notifications
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Online Status Card
            _buildStatusCard(),

            const SizedBox(height: 16),

            // Today's Stats
            Text('Today\'s Performance', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 12),
            _buildStatsGrid(),

            const SizedBox(height: 24),

            // Quick Actions
            Text('Quick Actions', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 12),
            _buildQuickActions(),

            const SizedBox(height: 24),

            // Recent Deliveries
            Text('Recent Deliveries', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 12),
            _buildRecentDeliveries(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _isOnline ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              _isOnline ? 'Online - Ready for deliveries' : 'Offline',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Switch(
              value: _isOnline,
              onChanged: (value) {
                setState(() {
                  _isOnline = value;
                });
                // TODO: Update online status in backend
              },
              activeColor: AppConfig.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid() {
    final stats = [
      {
        'title': 'Deliveries',
        'value': '12',
        'icon': Icons.local_shipping,
        'color': Colors.blue,
      },
      {
        'title': 'Earnings',
        'value': '₹850',
        'icon': Icons.currency_rupee,
        'color': Colors.green,
      },
      {
        'title': 'Distance',
        'value': '45 km',
        'icon': Icons.route,
        'color': Colors.orange,
      },
      {
        'title': 'Rating',
        'value': '4.8',
        'icon': Icons.star,
        'color': Colors.amber,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.5,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return Card(
          elevation: 1,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  stat['icon'] as IconData,
                  size: 32,
                  color: stat['color'] as Color,
                ),
                const SizedBox(height: 8),
                Text(
                  stat['value'] as String,
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: stat['color'] as Color,
                  ),
                ),
                Text(
                  stat['title'] as String,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      {'title': 'View Orders', 'icon': Icons.list_alt, 'route': '/orders'},
      {
        'title': 'Start Navigation',
        'icon': Icons.navigation,
        'route': '/navigation',
      },
      {'title': 'Emergency', 'icon': Icons.emergency, 'route': '/emergency'},
      {'title': 'Support', 'icon': Icons.support_agent, 'route': '/support'},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return Card(
          elevation: 1,
          child: InkWell(
            onTap: () {
              // Navigate to respective page
              if (action['route'] == '/orders') {
                // Switch to orders tab
                setState(() {
                  // This would need to be handled by parent widget
                });
              }
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Icon(
                    action['icon'] as IconData,
                    size: 24,
                    color: AppConfig.primaryColor,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    action['title'] as String,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentDeliveries() {
    // Mock recent deliveries data
    final deliveries = [
      {
        'id': 'ORD001',
        'customer': 'John Doe',
        'address': '123 Main St, Delhi',
        'amount': 299.0,
        'status': 'Delivered',
        'time': '2 hours ago',
      },
      {
        'id': 'ORD002',
        'customer': 'Jane Smith',
        'address': '456 Park Ave, Mumbai',
        'amount': 450.0,
        'status': 'Delivered',
        'time': '4 hours ago',
      },
    ];

    if (deliveries.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(
              Icons.local_shipping_outlined,
              size: 64,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              'No recent deliveries',
              style: AppTextStyles.bodyLarge.copyWith(color: AppColors.grey600),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: deliveries.length,
      itemBuilder: (context, index) {
        final delivery = deliveries[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.check_circle, color: Colors.green),
            ),
            title: Text(
              delivery['customer'] as String,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(delivery['address'] as String),
                Text(
                  delivery['time'] as String,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
            trailing: Text(
              '₹${(delivery['amount'] as double).toStringAsFixed(0)}',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ),
        );
      },
    );
  }
}

class RiderTrackingTab extends ConsumerWidget {
  const RiderTrackingTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Navigation'),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.navigation, size: 64, color: AppColors.grey400),
            SizedBox(height: 16),
            Text(
              'No active delivery',
              style: TextStyle(fontSize: 18, color: AppColors.grey600),
            ),
            SizedBox(height: 8),
            Text(
              'Accept an order to start navigation',
              style: TextStyle(color: AppColors.grey500),
            ),
          ],
        ),
      ),
    );
  }
}
