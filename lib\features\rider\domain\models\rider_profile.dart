import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'rider_kyc.dart';

part 'rider_profile.g.dart';

@HiveType(typeId: 20)
@JsonSerializable()
class RiderProfile {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String firstName;

  @HiveField(2)
  final String lastName;

  @HiveField(3)
  final String phoneNumber;

  @HiveField(4)
  final String email;

  @HiveField(5)
  final String? profileImageUrl;

  @HiveField(6)
  final DateTime dateOfBirth;

  @HiveField(7)
  final String address;

  @HiveField(8)
  final String city;

  @HiveField(9)
  final String state;

  @HiveField(10)
  final String pincode;

  @HiveField(11)
  final RiderStatus status;

  @HiveField(12)
  final KYCStatus kycStatus;

  @HiveField(13)
  final VehicleInfo? vehicleInfo;

  @HiveField(14)
  final BankDetails? bankDetails;

  @HiveField(15)
  final double rating;

  @HiveField(16)
  final int totalRides;

  @HiveField(17)
  final DateTime joinedDate;

  @HiveField(18)
  final bool isOnline;

  @HiveField(19)
  final String? currentLocation;

  @HiveField(20)
  final List<String> languages;

  @HiveField(21)
  final RiderPreferences preferences;

  const RiderProfile({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.phoneNumber,
    required this.email,
    this.profileImageUrl,
    required this.dateOfBirth,
    required this.address,
    required this.city,
    required this.state,
    required this.pincode,
    required this.status,
    required this.kycStatus,
    this.vehicleInfo,
    this.bankDetails,
    required this.rating,
    required this.totalRides,
    required this.joinedDate,
    required this.isOnline,
    this.currentLocation,
    required this.languages,
    required this.preferences,
  });

  factory RiderProfile.fromJson(Map<String, dynamic> json) =>
      _$RiderProfileFromJson(json);

  Map<String, dynamic> toJson() => _$RiderProfileToJson(this);

  RiderProfile copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? email,
    String? profileImageUrl,
    DateTime? dateOfBirth,
    String? address,
    String? city,
    String? state,
    String? pincode,
    RiderStatus? status,
    KYCStatus? kycStatus,
    VehicleInfo? vehicleInfo,
    BankDetails? bankDetails,
    double? rating,
    int? totalRides,
    DateTime? joinedDate,
    bool? isOnline,
    String? currentLocation,
    List<String>? languages,
    RiderPreferences? preferences,
  }) {
    return RiderProfile(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      status: status ?? this.status,
      kycStatus: kycStatus ?? this.kycStatus,
      vehicleInfo: vehicleInfo ?? this.vehicleInfo,
      bankDetails: bankDetails ?? this.bankDetails,
      rating: rating ?? this.rating,
      totalRides: totalRides ?? this.totalRides,
      joinedDate: joinedDate ?? this.joinedDate,
      isOnline: isOnline ?? this.isOnline,
      currentLocation: currentLocation ?? this.currentLocation,
      languages: languages ?? this.languages,
      preferences: preferences ?? this.preferences,
    );
  }

  String get fullName => '$firstName $lastName';
}

@HiveType(typeId: 21)
enum RiderStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  active,
  @HiveField(2)
  suspended,
  @HiveField(3)
  blocked,
  @HiveField(4)
  inactive,
}

@HiveType(typeId: 23)
@JsonSerializable()
class VehicleInfo {
  @HiveField(0)
  final String vehicleType;

  @HiveField(1)
  final String make;

  @HiveField(2)
  final String model;

  @HiveField(3)
  final String year;

  @HiveField(4)
  final String registrationNumber;

  @HiveField(5)
  final String color;

  @HiveField(6)
  final List<String> vehicleImages;

  @HiveField(7)
  final String? insuranceNumber;

  @HiveField(8)
  final DateTime? insuranceExpiry;

  @HiveField(9)
  final String? rcNumber;

  @HiveField(10)
  final DateTime? rcExpiry;

  @HiveField(11)
  final String? pollutionCertNumber;

  @HiveField(12)
  final DateTime? pollutionExpiry;

  const VehicleInfo({
    required this.vehicleType,
    required this.make,
    required this.model,
    required this.year,
    required this.registrationNumber,
    required this.color,
    required this.vehicleImages,
    this.insuranceNumber,
    this.insuranceExpiry,
    this.rcNumber,
    this.rcExpiry,
    this.pollutionCertNumber,
    this.pollutionExpiry,
  });

  factory VehicleInfo.fromJson(Map<String, dynamic> json) =>
      _$VehicleInfoFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleInfoToJson(this);
}

@HiveType(typeId: 24)
@JsonSerializable()
class BankDetails {
  @HiveField(0)
  final String accountHolderName;

  @HiveField(1)
  final String accountNumber;

  @HiveField(2)
  final String ifscCode;

  @HiveField(3)
  final String bankName;

  @HiveField(4)
  final String branchName;

  @HiveField(5)
  final bool isVerified;

  @HiveField(6)
  final String? upiId;

  const BankDetails({
    required this.accountHolderName,
    required this.accountNumber,
    required this.ifscCode,
    required this.bankName,
    required this.branchName,
    required this.isVerified,
    this.upiId,
  });

  factory BankDetails.fromJson(Map<String, dynamic> json) =>
      _$BankDetailsFromJson(json);

  Map<String, dynamic> toJson() => _$BankDetailsToJson(this);
}

@HiveType(typeId: 25)
@JsonSerializable()
class RiderPreferences {
  @HiveField(0)
  final bool acceptSharedRides;

  @HiveField(1)
  final bool acceptLongDistanceRides;

  @HiveField(2)
  final List<String> preferredAreas;

  @HiveField(3)
  final String workingHoursStart;

  @HiveField(4)
  final String workingHoursEnd;

  @HiveField(5)
  final List<String> workingDays;

  @HiveField(6)
  final bool enableNotifications;

  @HiveField(7)
  final bool enableLocationTracking;

  @HiveField(8)
  final String preferredLanguage;

  const RiderPreferences({
    required this.acceptSharedRides,
    required this.acceptLongDistanceRides,
    required this.preferredAreas,
    required this.workingHoursStart,
    required this.workingHoursEnd,
    required this.workingDays,
    required this.enableNotifications,
    required this.enableLocationTracking,
    required this.preferredLanguage,
  });

  factory RiderPreferences.fromJson(Map<String, dynamic> json) =>
      _$RiderPreferencesFromJson(json);

  Map<String, dynamic> toJson() => _$RiderPreferencesToJson(this);
}
