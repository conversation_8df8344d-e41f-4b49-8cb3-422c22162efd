import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'enhanced_payment_models.g.dart';

@HiveType(typeId: 40)
enum PaymentMethod {
  @HiveField(0)
  creditCard,
  @HiveField(1)
  debitCard,
  @HiveField(2)
  upi,
  @HiveField(3)
  netBanking,
  @HiveField(4)
  wallet,
  @HiveField(5)
  cashOnDelivery,
  @HiveField(6)
  emi,
  @HiveField(7)
  buyNowPayLater,
}

@HiveType(typeId: 41)
enum PaymentStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  processing,
  @HiveField(2)
  completed,
  @HiveField(3)
  failed,
  @HiveField(4)
  cancelled,
  @HiveField(5)
  refunded,
  @HiveField(6)
  partiallyRefunded,
}

@HiveType(typeId: 42)
@JsonSerializable()
class PaymentCard extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String cardNumber; // Masked: **** **** **** 1234
  
  @HiveField(3)
  final String cardHolderName;
  
  @HiveField(4)
  final String expiryMonth;
  
  @HiveField(5)
  final String expiryYear;
  
  @HiveField(6)
  final String cardType; // Visa, MasterCard, RuPay
  
  @HiveField(7)
  final String bankName;
  
  @HiveField(8)
  final bool isDefault;
  
  @HiveField(9)
  final DateTime createdAt;
  
  @HiveField(10)
  final DateTime updatedAt;

  const PaymentCard({
    required this.id,
    required this.userId,
    required this.cardNumber,
    required this.cardHolderName,
    required this.expiryMonth,
    required this.expiryYear,
    required this.cardType,
    required this.bankName,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PaymentCard.fromJson(Map<String, dynamic> json) => _$PaymentCardFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentCardToJson(this);

  String get maskedCardNumber => '**** **** **** ${cardNumber.substring(cardNumber.length - 4)}';
  String get expiryDate => '$expiryMonth/$expiryYear';

  @override
  List<Object?> get props => [
    id, userId, cardNumber, cardHolderName, expiryMonth, expiryYear,
    cardType, bankName, isDefault, createdAt, updatedAt,
  ];
}

@HiveType(typeId: 43)
@JsonSerializable()
class UPIAccount extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String upiId;
  
  @HiveField(3)
  final String bankName;
  
  @HiveField(4)
  final String accountHolderName;
  
  @HiveField(5)
  final bool isVerified;
  
  @HiveField(6)
  final bool isDefault;
  
  @HiveField(7)
  final DateTime createdAt;

  const UPIAccount({
    required this.id,
    required this.userId,
    required this.upiId,
    required this.bankName,
    required this.accountHolderName,
    this.isVerified = false,
    this.isDefault = false,
    required this.createdAt,
  });

  factory UPIAccount.fromJson(Map<String, dynamic> json) => _$UPIAccountFromJson(json);
  Map<String, dynamic> toJson() => _$UPIAccountToJson(this);

  @override
  List<Object?> get props => [
    id, userId, upiId, bankName, accountHolderName, isVerified, isDefault, createdAt,
  ];
}

@HiveType(typeId: 44)
@JsonSerializable()
class PaymentSplit extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String paymentId;
  
  @HiveField(2)
  final PaymentMethod method;
  
  @HiveField(3)
  final double amount;
  
  @HiveField(4)
  final double percentage;
  
  @HiveField(5)
  final String? cardId;
  
  @HiveField(6)
  final String? upiId;
  
  @HiveField(7)
  final PaymentStatus status;
  
  @HiveField(8)
  final String? transactionId;
  
  @HiveField(9)
  final DateTime createdAt;

  const PaymentSplit({
    required this.id,
    required this.paymentId,
    required this.method,
    required this.amount,
    required this.percentage,
    this.cardId,
    this.upiId,
    required this.status,
    this.transactionId,
    required this.createdAt,
  });

  factory PaymentSplit.fromJson(Map<String, dynamic> json) => _$PaymentSplitFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentSplitToJson(this);

  @override
  List<Object?> get props => [
    id, paymentId, method, amount, percentage, cardId, upiId, status, transactionId, createdAt,
  ];
}

@HiveType(typeId: 45)
@JsonSerializable()
class EnhancedPayment extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String orderId;
  
  @HiveField(3)
  final double totalAmount;
  
  @HiveField(4)
  final double paidAmount;
  
  @HiveField(5)
  final double refundedAmount;
  
  @HiveField(6)
  final String currency;
  
  @HiveField(7)
  final PaymentStatus status;
  
  @HiveField(8)
  final List<PaymentSplit> splits;
  
  @HiveField(9)
  final Map<String, dynamic> metadata;
  
  @HiveField(10)
  final DateTime createdAt;
  
  @HiveField(11)
  final DateTime updatedAt;
  
  @HiveField(12)
  final String? failureReason;
  
  @HiveField(13)
  final int retryCount;

  const EnhancedPayment({
    required this.id,
    required this.userId,
    required this.orderId,
    required this.totalAmount,
    this.paidAmount = 0.0,
    this.refundedAmount = 0.0,
    this.currency = 'INR',
    required this.status,
    this.splits = const [],
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    this.failureReason,
    this.retryCount = 0,
  });

  factory EnhancedPayment.fromJson(Map<String, dynamic> json) => _$EnhancedPaymentFromJson(json);
  Map<String, dynamic> toJson() => _$EnhancedPaymentToJson(this);

  double get remainingAmount => totalAmount - paidAmount;
  double get refundableAmount => paidAmount - refundedAmount;
  bool get isFullyPaid => paidAmount >= totalAmount;
  bool get isPartiallyPaid => paidAmount > 0 && paidAmount < totalAmount;
  bool get canRefund => refundableAmount > 0;

  @override
  List<Object?> get props => [
    id, userId, orderId, totalAmount, paidAmount, refundedAmount, currency,
    status, splits, metadata, createdAt, updatedAt, failureReason, retryCount,
  ];
}

@HiveType(typeId: 46)
@JsonSerializable()
class PaymentRefund extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String paymentId;
  
  @HiveField(2)
  final String userId;
  
  @HiveField(3)
  final double amount;
  
  @HiveField(4)
  final String reason;
  
  @HiveField(5)
  final PaymentStatus status;
  
  @HiveField(6)
  final String? transactionId;
  
  @HiveField(7)
  final DateTime createdAt;
  
  @HiveField(8)
  final DateTime? processedAt;
  
  @HiveField(9)
  final String? processingNote;

  const PaymentRefund({
    required this.id,
    required this.paymentId,
    required this.userId,
    required this.amount,
    required this.reason,
    required this.status,
    this.transactionId,
    required this.createdAt,
    this.processedAt,
    this.processingNote,
  });

  factory PaymentRefund.fromJson(Map<String, dynamic> json) => _$PaymentRefundFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentRefundToJson(this);

  @override
  List<Object?> get props => [
    id, paymentId, userId, amount, reason, status, transactionId,
    createdAt, processedAt, processingNote,
  ];
}

@HiveType(typeId: 47)
@JsonSerializable()
class PaymentHistory extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String paymentId;
  
  @HiveField(2)
  final PaymentStatus fromStatus;
  
  @HiveField(3)
  final PaymentStatus toStatus;
  
  @HiveField(4)
  final String? note;
  
  @HiveField(5)
  final DateTime timestamp;
  
  @HiveField(6)
  final Map<String, dynamic> metadata;

  const PaymentHistory({
    required this.id,
    required this.paymentId,
    required this.fromStatus,
    required this.toStatus,
    this.note,
    required this.timestamp,
    this.metadata = const {},
  });

  factory PaymentHistory.fromJson(Map<String, dynamic> json) => _$PaymentHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentHistoryToJson(this);

  @override
  List<Object?> get props => [id, paymentId, fromStatus, toStatus, note, timestamp, metadata];
}

// Payment method configurations
class PaymentMethodConfig {
  static const Map<PaymentMethod, Map<String, dynamic>> configs = {
    PaymentMethod.creditCard: {
      'name': 'Credit Card',
      'icon': 'credit_card',
      'minAmount': 1.0,
      'maxAmount': 200000.0,
      'processingFee': 2.0, // percentage
      'isInstantaneous': false,
    },
    PaymentMethod.debitCard: {
      'name': 'Debit Card',
      'icon': 'payment',
      'minAmount': 1.0,
      'maxAmount': 100000.0,
      'processingFee': 1.5,
      'isInstantaneous': false,
    },
    PaymentMethod.upi: {
      'name': 'UPI',
      'icon': 'account_balance',
      'minAmount': 1.0,
      'maxAmount': 100000.0,
      'processingFee': 0.0,
      'isInstantaneous': true,
    },
    PaymentMethod.netBanking: {
      'name': 'Net Banking',
      'icon': 'account_balance',
      'minAmount': 1.0,
      'maxAmount': 500000.0,
      'processingFee': 1.0,
      'isInstantaneous': false,
    },
    PaymentMethod.wallet: {
      'name': 'Wallet',
      'icon': 'account_balance_wallet',
      'minAmount': 1.0,
      'maxAmount': 50000.0,
      'processingFee': 0.0,
      'isInstantaneous': true,
    },
    PaymentMethod.cashOnDelivery: {
      'name': 'Cash on Delivery',
      'icon': 'local_shipping',
      'minAmount': 1.0,
      'maxAmount': 10000.0,
      'processingFee': 0.0,
      'isInstantaneous': false,
    },
  };

  static Map<String, dynamic>? getConfig(PaymentMethod method) {
    return configs[method];
  }

  static String getMethodName(PaymentMethod method) {
    return configs[method]?['name'] ?? method.toString();
  }

  static String getMethodIcon(PaymentMethod method) {
    return configs[method]?['icon'] ?? 'payment';
  }

  static double getProcessingFee(PaymentMethod method, double amount) {
    final feePercentage = configs[method]?['processingFee'] ?? 0.0;
    return (amount * feePercentage) / 100;
  }
}
