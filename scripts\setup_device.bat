@echo off
REM Script to help setup Android device for development
echo Flutter Android Device Setup for Hot Reload
echo ==========================================
echo.

echo Checking Flutter installation...
flutter doctor
echo.

echo Checking connected devices...
flutter devices
echo.

echo Checking ADB devices...
adb devices
echo.

echo If no devices are shown above, please:
echo 1. Enable Developer Options on your Android device
echo 2. Enable USB Debugging in Developer Options
echo 3. Connect your device via USB
echo 4. Accept the USB debugging prompt on your device
echo 5. Run this script again
echo.

echo If devices are shown, you're ready for development!
echo.

echo Getting your development machine's IP address...
echo Your IP addresses:
ipconfig | findstr /i "IPv4"
echo.

echo IMPORTANT: Add your development machine's IP address to:
echo android\app\src\main\res\xml\network_security_config.xml
echo.
echo Example: ^<domain includeSubdomains="true"^>*************^</domain^>
echo Replace ************* with your actual IP address shown above.
echo.

pause
