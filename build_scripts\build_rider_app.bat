@echo off
echo Building Rider App...
echo.

REM Clean previous builds
echo Cleaning previous builds...
flutter clean
flutter pub get

REM Build Rider App Debug APK
echo Building Rider App Debug APK...
flutter build apk --debug --target=lib/main_rider.dart --flavor=riderDev --no-tree-shake-icons

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Rider App Debug APK built successfully!
    echo Location: build\app\outputs\flutter-apk\app-rider-dev-debug.apk
) else (
    echo.
    echo ❌ Rider App Debug build failed!
    exit /b 1
)

echo.
echo Building Rider App Release APK...
flutter build apk --release --target=lib/main_rider.dart --flavor=riderProd --no-tree-shake-icons

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Rider App Release APK built successfully!
    echo Location: build\app\outputs\flutter-apk\app-rider-prod-release.apk
) else (
    echo.
    echo ❌ Rider App Release build failed!
    exit /b 1
)

echo.
echo 🎉 Rider App build completed successfully!
echo.
echo Debug APK: build\app\outputs\flutter-apk\app-rider-dev-debug.apk
echo Release APK: build\app\outputs\flutter-apk\app-rider-prod-release.apk
pause
