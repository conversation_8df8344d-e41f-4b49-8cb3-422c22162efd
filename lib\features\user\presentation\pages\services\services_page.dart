import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';

class UserServicesPage extends ConsumerWidget {
  const UserServicesPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Services'),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Available Services',
              style: AppTextStyles.headlineMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Book professional services at your convenience',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),

            // Services Grid
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
              ),
              itemCount: _services.length,
              itemBuilder: (context, index) {
                final service = _services[index];
                return _ServiceCard(service: service);
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _ServiceCard extends StatelessWidget {
  final ServiceItem service;

  const _ServiceCard({required this.service});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // Navigate to service booking page
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${service.name} service coming soon!'),
              backgroundColor: AppColors.userPrimary,
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                service.icon,
                size: 48,
                color: AppColors.userPrimary,
              ),
              const SizedBox(height: 12),
              Text(
                service.name,
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                service.description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ServiceItem {
  final String name;
  final String description;
  final IconData icon;

  const ServiceItem({
    required this.name,
    required this.description,
    required this.icon,
  });
}

final List<ServiceItem> _services = [
  ServiceItem(
    name: 'Home Cleaning',
    description: 'Professional home cleaning services',
    icon: Icons.cleaning_services,
  ),
  ServiceItem(
    name: 'Plumbing',
    description: 'Expert plumbing repairs and installation',
    icon: Icons.plumbing,
  ),
  ServiceItem(
    name: 'Electrical',
    description: 'Licensed electrician services',
    icon: Icons.electrical_services,
  ),
  ServiceItem(
    name: 'Beauty Services',
    description: 'At-home beauty and wellness',
    icon: Icons.face,
  ),
  ServiceItem(
    name: 'Appliance Repair',
    description: 'Fix your home appliances',
    icon: Icons.build,
  ),
  ServiceItem(
    name: 'Tutoring',
    description: 'Professional tutoring services',
    icon: Icons.school,
  ),
  ServiceItem(
    name: 'Pest Control',
    description: 'Safe and effective pest control',
    icon: Icons.bug_report,
  ),
  ServiceItem(
    name: 'Gardening',
    description: 'Garden maintenance and landscaping',
    icon: Icons.grass,
  ),
];
