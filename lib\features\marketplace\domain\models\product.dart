import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'product.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class Product extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final double price;

  @HiveField(4)
  final double originalPrice;

  @HiveField(5)
  final String currency;

  @HiveField(6)
  final List<String> images;

  @HiveField(7)
  final String category;

  @HiveField(8)
  final String subcategory;

  @HiveField(9)
  final String brand;

  @HiveField(10)
  final double rating;

  @HiveField(11)
  final int reviewCount;

  @HiveField(12)
  final bool inStock;

  @HiveField(13)
  final int stockQuantity;

  @HiveField(14)
  final String vendorId;

  @HiveField(15)
  final String vendorName;

  @HiveField(16)
  final Map<String, dynamic> specifications;

  @HiveField(17)
  final List<String> tags;

  @HiveField(18)
  final DateTime createdAt;

  @HiveField(19)
  final DateTime updatedAt;

  @HiveField(20)
  final bool isFeatured;

  @HiveField(21)
  final double? discountPercentage;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.originalPrice,
    this.currency = 'INR',
    required this.images,
    required this.category,
    required this.subcategory,
    required this.brand,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.inStock = true,
    this.stockQuantity = 0,
    required this.vendorId,
    required this.vendorName,
    this.specifications = const {},
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isFeatured = false,
    this.discountPercentage,
  });

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
  Map<String, dynamic> toJson() => _$ProductToJson(this);

  // Helper getters
  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
  String get formattedOriginalPrice => '₹${originalPrice.toStringAsFixed(2)}';

  bool get hasDiscount => originalPrice > price;

  double get calculatedDiscountPercentage {
    if (!hasDiscount) return 0.0;
    return ((originalPrice - price) / originalPrice) * 100;
  }

  String get primaryImage => images.isNotEmpty ? images.first : '';

  bool get isAvailable => inStock && stockQuantity > 0;

  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    double? originalPrice,
    String? currency,
    List<String>? images,
    String? category,
    String? subcategory,
    String? brand,
    double? rating,
    int? reviewCount,
    bool? inStock,
    int? stockQuantity,
    String? vendorId,
    String? vendorName,
    Map<String, dynamic>? specifications,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isFeatured,
    double? discountPercentage,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      currency: currency ?? this.currency,
      images: images ?? this.images,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      brand: brand ?? this.brand,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      inStock: inStock ?? this.inStock,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      vendorId: vendorId ?? this.vendorId,
      vendorName: vendorName ?? this.vendorName,
      specifications: specifications ?? this.specifications,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isFeatured: isFeatured ?? this.isFeatured,
      discountPercentage: discountPercentage ?? this.discountPercentage,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    price,
    originalPrice,
    currency,
    images,
    category,
    subcategory,
    brand,
    rating,
    reviewCount,
    inStock,
    stockQuantity,
    vendorId,
    vendorName,
    specifications,
    tags,
    createdAt,
    updatedAt,
    isFeatured,
    discountPercentage,
  ];
}

// Category model
@HiveType(typeId: 3)
@JsonSerializable()
class Category extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String imageUrl;

  @HiveField(4)
  final String iconName;

  @HiveField(5)
  final String colorHex;

  @HiveField(6)
  final int itemCount;

  @HiveField(7)
  final bool isActive;

  @HiveField(8)
  final int sortOrder;

  @HiveField(9)
  final List<String> subcategories;

  const Category({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.iconName,
    required this.colorHex,
    this.itemCount = 0,
    this.isActive = true,
    this.sortOrder = 0,
    this.subcategories = const [],
  });

  factory Category.fromJson(Map<String, dynamic> json) =>
      _$CategoryFromJson(json);
  Map<String, dynamic> toJson() => _$CategoryToJson(this);

  Category copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? iconName,
    String? colorHex,
    int? itemCount,
    bool? isActive,
    int? sortOrder,
    List<String>? subcategories,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      iconName: iconName ?? this.iconName,
      colorHex: colorHex ?? this.colorHex,
      itemCount: itemCount ?? this.itemCount,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      subcategories: subcategories ?? this.subcategories,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    imageUrl,
    iconName,
    colorHex,
    itemCount,
    isActive,
    sortOrder,
    subcategories,
  ];
}

// Sample categories for testing
class SampleCategories {
  static List<Category> get foodCategories => [
    Category(
      id: 'food_1',
      name: 'Fast Food',
      description: 'Quick and delicious fast food options',
      imageUrl: 'assets/images/food/category_177.png',
      iconName: 'restaurant',
      colorHex: '#FF6B6B',
      itemCount: 45,
      subcategories: ['Burgers', 'Pizza', 'Sandwiches', 'Wraps'],
    ),
    Category(
      id: 'food_2',
      name: 'Beverages',
      description: 'Refreshing drinks and beverages',
      imageUrl: 'assets/images/food/category_181.png',
      iconName: 'local_drink',
      colorHex: '#4ECDC4',
      itemCount: 32,
      subcategories: ['Soft Drinks', 'Juices', 'Coffee', 'Tea'],
    ),
    Category(
      id: 'food_3',
      name: 'Desserts',
      description: 'Sweet treats and desserts',
      imageUrl: 'assets/images/food/category_187.png',
      iconName: 'cake',
      colorHex: '#FFE66D',
      itemCount: 28,
      subcategories: ['Cakes', 'Ice Cream', 'Pastries', 'Cookies'],
    ),
    Category(
      id: 'food_4',
      name: 'Indian Cuisine',
      description: 'Traditional Indian dishes',
      imageUrl: 'assets/images/food/category_203.png',
      iconName: 'restaurant_menu',
      colorHex: '#FF8E53',
      itemCount: 67,
      subcategories: ['North Indian', 'South Indian', 'Street Food', 'Biryani'],
    ),
    Category(
      id: 'food_5',
      name: 'Chinese',
      description: 'Authentic Chinese cuisine',
      imageUrl: 'assets/images/food/category_204.png',
      iconName: 'ramen_dining',
      colorHex: '#A8E6CF',
      itemCount: 38,
      subcategories: ['Noodles', 'Rice', 'Dumplings', 'Soups'],
    ),
    Category(
      id: 'food_6',
      name: 'Healthy Food',
      description: 'Nutritious and healthy meal options',
      imageUrl: 'assets/images/food/category_243.png',
      iconName: 'eco',
      colorHex: '#88D8B0',
      itemCount: 42,
      subcategories: ['Salads', 'Smoothies', 'Organic', 'Low Calorie'],
    ),
  ];

  static List<Category> get productCategories => [
    Category(
      id: 'electronics',
      name: 'Electronics',
      description: 'Latest gadgets and electronic devices',
      imageUrl: 'assets/images/products/preview_14.png',
      iconName: 'devices',
      colorHex: '#667EEA',
      itemCount: 156,
      subcategories: ['Smartphones', 'Laptops', 'Audio', 'Accessories'],
    ),
    Category(
      id: 'fashion',
      name: 'Fashion',
      description: 'Trendy clothing and accessories',
      imageUrl: 'assets/images/products/preview_15.png',
      iconName: 'checkroom',
      colorHex: '#F093FB',
      itemCount: 234,
      subcategories: ['Men\'s Wear', 'Women\'s Wear', 'Shoes', 'Accessories'],
    ),
    Category(
      id: 'home_garden',
      name: 'Home & Garden',
      description: 'Everything for your home and garden',
      imageUrl: 'assets/images/products/preview_17.png',
      iconName: 'home',
      colorHex: '#4FACFE',
      itemCount: 189,
      subcategories: ['Furniture', 'Decor', 'Kitchen', 'Garden'],
    ),
    Category(
      id: 'sports',
      name: 'Sports & Fitness',
      description: 'Sports equipment and fitness gear',
      imageUrl: 'assets/images/products/preview_18.png',
      iconName: 'sports_soccer',
      colorHex: '#43E97B',
      itemCount: 98,
      subcategories: [
        'Fitness',
        'Outdoor Sports',
        'Team Sports',
        'Accessories',
      ],
    ),
  ];
}

// Sample products for testing
class SampleProducts {
  static List<Product> get products => [
    // Electronics
    Product(
      id: '1',
      name: 'Wireless Bluetooth Headphones',
      description: 'High-quality wireless headphones with noise cancellation',
      price: 2999.0,
      originalPrice: 3999.0,
      images: ['assets/images/products/preview_14.png'],
      category: 'Electronics',
      subcategory: 'Audio',
      brand: 'TechBrand',
      rating: 4.5,
      reviewCount: 128,
      stockQuantity: 50,
      vendorId: 'vendor1',
      vendorName: 'Tech Store',
      specifications: {
        'Battery Life': '30 hours',
        'Connectivity': 'Bluetooth 5.0',
        'Weight': '250g',
      },
      tags: ['wireless', 'bluetooth', 'headphones'],
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
      isFeatured: true,
    ),
    Product(
      id: '2',
      name: 'Smartphone Pro Max',
      description: 'Latest flagship smartphone with advanced camera system',
      price: 79999.0,
      originalPrice: 89999.0,
      images: ['assets/images/products/preview_15.png'],
      category: 'Electronics',
      subcategory: 'Smartphones',
      brand: 'TechBrand',
      rating: 4.8,
      reviewCount: 245,
      stockQuantity: 25,
      vendorId: 'vendor1',
      vendorName: 'Tech Store',
      specifications: {
        'Display': '6.7" OLED',
        'Storage': '256GB',
        'RAM': '12GB',
        'Camera': '108MP Triple',
      },
      tags: ['smartphone', 'camera', 'flagship'],
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now(),
      isFeatured: true,
    ),

    // Fashion
    Product(
      id: '3',
      name: 'Cotton T-Shirt',
      description: 'Comfortable 100% cotton t-shirt in various colors',
      price: 599.0,
      originalPrice: 799.0,
      images: ['assets/images/products/preview_17.png'],
      category: 'Fashion',
      subcategory: 'Men\'s Clothing',
      brand: 'FashionBrand',
      rating: 4.2,
      reviewCount: 89,
      stockQuantity: 100,
      vendorId: 'vendor2',
      vendorName: 'Fashion Store',
      specifications: {
        'Material': '100% Cotton',
        'Fit': 'Regular',
        'Care': 'Machine Wash',
      },
      tags: ['cotton', 'tshirt', 'casual'],
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
      isFeatured: true,
    ),
    Product(
      id: '4',
      name: 'Designer Jeans',
      description: 'Premium denim jeans with perfect fit and comfort',
      price: 2499.0,
      originalPrice: 3499.0,
      images: ['assets/images/products/preview_18.png'],
      category: 'Fashion',
      subcategory: 'Men\'s Clothing',
      brand: 'DenimCo',
      rating: 4.6,
      reviewCount: 167,
      stockQuantity: 75,
      vendorId: 'vendor2',
      vendorName: 'Fashion Store',
      specifications: {
        'Material': 'Premium Denim',
        'Fit': 'Slim Fit',
        'Wash': 'Dark Blue',
      },
      tags: ['jeans', 'denim', 'premium'],
      createdAt: DateTime.now().subtract(const Duration(days: 12)),
      updatedAt: DateTime.now(),
      isFeatured: false,
    ),
  ];

  // Food items by category
  static List<Product> getFoodItemsByCategory(String categoryId) {
    switch (categoryId) {
      case 'food_1': // Fast Food
        return [
          Product(
            id: 'food_1_1',
            name: 'Classic Burger',
            description:
                'Juicy beef patty with fresh vegetables and special sauce',
            price: 299.0,
            originalPrice: 349.0,
            images: ['assets/images/food/preview_42.png'],
            category: 'Fast Food',
            subcategory: 'Burgers',
            brand: 'Burger House',
            rating: 4.3,
            reviewCount: 89,
            stockQuantity: 50,
            vendorId: 'food_vendor_1',
            vendorName: 'Burger House',
            specifications: {
              'Prep Time': '15 minutes',
              'Serves': '1 person',
              'Spice Level': 'Mild',
            },
            tags: ['burger', 'beef', 'fast food'],
            createdAt: DateTime.now().subtract(const Duration(days: 5)),
            updatedAt: DateTime.now(),
            isFeatured: true,
          ),
          Product(
            id: 'food_1_2',
            name: 'Margherita Pizza',
            description:
                'Classic pizza with fresh mozzarella, tomatoes, and basil',
            price: 449.0,
            originalPrice: 549.0,
            images: ['assets/images/food/preview_44.png'],
            category: 'Fast Food',
            subcategory: 'Pizza',
            brand: 'Pizza Corner',
            rating: 4.5,
            reviewCount: 156,
            stockQuantity: 30,
            vendorId: 'food_vendor_2',
            vendorName: 'Pizza Corner',
            specifications: {
              'Size': '12 inch',
              'Prep Time': '20 minutes',
              'Serves': '2-3 people',
            },
            tags: ['pizza', 'margherita', 'italian'],
            createdAt: DateTime.now().subtract(const Duration(days: 3)),
            updatedAt: DateTime.now(),
            isFeatured: true,
          ),
        ];
      case 'food_2': // Beverages
        return [
          Product(
            id: 'food_2_1',
            name: 'Fresh Orange Juice',
            description: 'Freshly squeezed orange juice with no added sugar',
            price: 89.0,
            originalPrice: 119.0,
            images: ['assets/images/food/packageitem_image_2.png'],
            category: 'Beverages',
            subcategory: 'Juices',
            brand: 'Fresh Juice Co',
            rating: 4.7,
            reviewCount: 234,
            stockQuantity: 100,
            vendorId: 'food_vendor_3',
            vendorName: 'Fresh Juice Co',
            specifications: {
              'Volume': '300ml',
              'Sugar': 'No added sugar',
              'Preservatives': 'None',
            },
            tags: ['juice', 'orange', 'fresh', 'healthy'],
            createdAt: DateTime.now().subtract(const Duration(days: 2)),
            updatedAt: DateTime.now(),
            isFeatured: true,
          ),
        ];
      default:
        return [];
    }
  }

  static List<Product> getProductsByCategory(String categoryId) {
    switch (categoryId) {
      case 'electronics':
        return products.where((p) => p.category == 'Electronics').toList();
      case 'fashion':
        return products.where((p) => p.category == 'Fashion').toList();
      default:
        return [];
    }
  }
}
