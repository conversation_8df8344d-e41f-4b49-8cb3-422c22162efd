@echo off
REM Script to validate the hot reload setup
echo Flutter Hot Reload Setup Validation
echo ===================================
echo.

echo 1. Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ERROR: Flutter not found in PATH
    goto :error
)
echo ✓ Flutter is installed
echo.

echo 2. Checking Flutter doctor...
flutter doctor --android-licenses > nul 2>&1
flutter doctor -v
echo.

echo 3. Checking connected devices...
flutter devices > devices_temp.txt
type devices_temp.txt
findstr /C:"android" devices_temp.txt > nul
if %errorlevel% neq 0 (
    echo WARNING: No Android devices detected
    echo Please connect your Android device and enable USB debugging
) else (
    echo ✓ Android device detected
)
del devices_temp.txt
echo.

echo 4. Checking ADB connection...
adb devices
echo.

echo 5. Validating Android build configuration...
if exist "android\app\build.gradle.kts" (
    echo ✓ Android build configuration found
) else (
    echo ERROR: Android build configuration missing
    goto :error
)

if exist "android\app\src\debug\AndroidManifest.xml" (
    echo ✓ Debug manifest found
) else (
    echo ERROR: Debug manifest missing
    goto :error
)

if exist "android\app\src\main\res\xml\network_security_config.xml" (
    echo ✓ Network security config found
) else (
    echo ERROR: Network security config missing
    goto :error
)
echo.

echo 6. Testing basic Flutter commands...
echo Testing flutter clean...
flutter clean > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed
    goto :error
)

echo Testing flutter pub get...
flutter pub get > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed
    goto :error
)
echo ✓ Basic Flutter commands working
echo.

echo ===================================
echo ✓ Setup validation completed successfully!
echo.
echo Your Flutter project is ready for hot reload development.
echo.
echo Next steps:
echo 1. Connect your Android device via USB
echo 2. Enable USB debugging on your device
echo 3. Run: scripts\dev_run.bat
echo 4. Start coding with hot reload!
echo.
goto :end

:error
echo.
echo ===================================
echo ✗ Setup validation failed!
echo.
echo Please check the errors above and fix them before proceeding.
echo Refer to DEVELOPMENT_SETUP.md for detailed instructions.
echo.

:end
pause
