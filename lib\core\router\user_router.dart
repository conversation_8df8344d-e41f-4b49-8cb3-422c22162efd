import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/user/presentation/pages/splash_page.dart';
import '../../features/user/presentation/pages/onboarding_page.dart';
import '../../features/user/presentation/pages/auth/login_page.dart';
import '../../features/user/presentation/pages/auth/register_page.dart';
import '../../features/user/presentation/pages/auth/otp_verification_page.dart';
import '../../features/user/presentation/pages/home/<USER>';
import '../../features/user/presentation/pages/home/<USER>';

import '../../features/user/presentation/pages/marketplace/marketplace_page.dart';
import '../../features/user/presentation/pages/services/services_page.dart';
import '../../features/user/presentation/pages/profile/profile_page.dart';
import '../../features/user/presentation/pages/help/help_page.dart';
import '../../shared/widgets/main_wrapper.dart';

final userRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    routes: [
      // Splash Screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const UserSplashPage(),
      ),

      // Onboarding
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const UserOnboardingPage(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const UserLoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const UserRegisterPage(),
      ),
      GoRoute(
        path: '/otp-verification',
        name: 'otp-verification',
        builder: (context, state) {
          final phoneNumber = state.extra as String? ?? '';
          return UserOTPVerificationPage(phoneNumber: phoneNumber);
        },
      ),

      // Main App Shell with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainWrapper(child: child),
        routes: [
          // Home/Dashboard
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const UserHomePage(),
            routes: [
              GoRoute(
                path: '/dashboard',
                name: 'dashboard',
                builder: (context, state) => const UserDashboardPage(),
              ),
            ],
          ),

          // Wallet redirect to Services (User app focuses on booking)
          GoRoute(
            path: '/wallet',
            name: 'wallet',
            redirect: (context, state) => '/services',
          ),

          // Marketplace
          GoRoute(
            path: '/marketplace',
            name: 'marketplace',
            builder: (context, state) => const UserMarketplacePage(),
          ),

          // Services
          GoRoute(
            path: '/services',
            name: 'services',
            builder: (context, state) => const UserServicesPage(),
          ),

          // Profile
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const UserProfilePage(),
          ),
        ],
      ),

      // Help & Support
      GoRoute(
        path: '/help',
        name: 'help',
        builder: (context, state) => const UserHelpPage(),
      ),

      // Error/Fallback Route
      GoRoute(
        path: '/error',
        name: 'error',
        builder: (context, state) => Scaffold(
          appBar: AppBar(title: const Text('Error')),
          body: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text(
                  'Something went wrong!',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('Please try again later.'),
              ],
            ),
          ),
        ),
      ),
    ],

    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('The page you are looking for does not exist.'),
          ],
        ),
      ),
    ),

    // Redirect logic
    redirect: (context, state) {
      // Add authentication logic here if needed
      // For now, allow all routes
      return null;
    },
  );
});
