import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/common_widgets.dart';
import '../../../../shared/models/user_role.dart';

class RiderSplashPage extends StatelessWidget {
  const RiderSplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AuthSplashWidget(
      expectedRole: UserRole.rider,
      dashboardRoute: '/dashboard',
      loginRoute: '/login',
      animatedLogo: _buildAnimatedLogo(),
      tagline: 'Deliver with Pride',
      branding: _buildBottomBranding(),
      delay: const Duration(seconds: 2),
    );
  }

  Widget _buildAnimatedLogo() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
      ),
      child: const Icon(
        Icons.delivery_dining,
        size: 60,
        color: Colors.deepPurple,
      ),
    );
  }

  Widget _buildBottomBranding() {
    return Column(
      children: [
        Text(
          'Powered by Projek',
          style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14),
        ),
        const SizedBox(height: 8),
        Text(
          'v1.0.0',
          style: TextStyle(color: Colors.white.withOpacity(0.5), fontSize: 12),
        ),
      ],
    );
  }
}
