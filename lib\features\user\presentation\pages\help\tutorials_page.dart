import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/analytics_service.dart';

class TutorialsPage extends ConsumerWidget {
  final String category;
  
  const TutorialsPage({super.key, required this.category});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tutorials = _getTutorialsByCategory(category);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('📚 ${_getCategoryTitle(category)} Help'),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: tutorials.length,
        itemBuilder: (context, index) {
          final tutorial = tutorials[index];
          return _buildTutorialCard(context, tutorial);
        },
      ),
    );
  }

  Widget _buildTutorialCard(BuildContext context, Tutorial tutorial) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _openTutorial(context, tutorial),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: tutorial.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      tutorial.icon,
                      color: tutorial.color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tutorial.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          tutorial.description,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(Icons.arrow_forward_ios, size: 16),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Chip(
                    label: Text(
                      '${tutorial.duration} min',
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Colors.grey[200],
                  ),
                  const SizedBox(width: 8),
                  Chip(
                    label: Text(
                      tutorial.difficulty,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: _getDifficultyColor(tutorial.difficulty).withOpacity(0.2),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _openTutorial(BuildContext context, Tutorial tutorial) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TutorialDetailPage(tutorial: tutorial),
      ),
    );
    AnalyticsService.logEvent('tutorial_opened', {
      'category': category,
      'tutorial': tutorial.title,
    });
  }

  String _getCategoryTitle(String category) {
    switch (category) {
      case 'games':
        return 'Games & Rewards';
      case 'shopping':
        return 'Shopping';
      case 'delivery':
        return 'Delivery';
      case 'wallet':
        return 'Wallet';
      case 'account':
        return 'Account';
      default:
        return 'Help';
    }
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'Easy':
        return AppColors.success;
      case 'Medium':
        return AppColors.warning;
      case 'Hard':
        return AppColors.error;
      default:
        return Colors.grey;
    }
  }

  List<Tutorial> _getTutorialsByCategory(String category) {
    switch (category) {
      case 'games':
        return [
          Tutorial(
            title: 'How to Play Spin & Earn',
            description: 'Learn how to spin the wheel and earn ProjekCoins daily',
            icon: Icons.casino,
            color: AppColors.userPrimary,
            duration: 3,
            difficulty: 'Easy',
            steps: [
              'Open the Projek User app',
              'Tap on "🎰 Spin & Earn" from the dashboard',
              'Tap the "SPIN NOW!" button to spin the wheel',
              'Watch the wheel spin and see your reward',
              'Your ProjekCoins will be added automatically',
              'You get 5 free spins every day',
              'Spins reset at midnight every day',
            ],
          ),
          Tutorial(
            title: 'Daily Login Rewards',
            description: 'Maximize your earnings with daily login streaks',
            icon: Icons.calendar_today,
            color: AppColors.success,
            duration: 2,
            difficulty: 'Easy',
            steps: [
              'Open the app every day to maintain your streak',
              'Go to "Daily Rewards" section',
              'Claim your daily reward',
              'Complete 7 days for maximum rewards',
              'If you miss a day, streak resets to Day 1',
              'Rewards increase each day (10-100 coins)',
            ],
          ),
        ];
      case 'shopping':
        return [
          Tutorial(
            title: 'How to Place an Order',
            description: 'Step-by-step guide to ordering from marketplace',
            icon: Icons.shopping_cart,
            color: AppColors.accentGreen,
            duration: 5,
            difficulty: 'Easy',
            steps: [
              'Go to Marketplace tab',
              'Browse categories or search for items',
              'Tap on a product to view details',
              'Select quantity and options',
              'Add to cart',
              'Review your cart',
              'Choose delivery address',
              'Select payment method',
              'Place order and track delivery',
            ],
          ),
          Tutorial(
            title: 'Payment Methods',
            description: 'Learn about different ways to pay',
            icon: Icons.payment,
            color: AppColors.secondaryOrange,
            duration: 3,
            difficulty: 'Easy',
            steps: [
              'ProjekCoins - Use earned coins',
              'UPI - Google Pay, PhonePe, Paytm',
              'Credit/Debit Cards - Visa, Mastercard',
              'Net Banking - All major banks',
              'Digital Wallets - Paytm, Amazon Pay',
              'Cash on Delivery - Available in select areas',
            ],
          ),
        ];
      case 'delivery':
        return [
          Tutorial(
            title: 'Track Your Order',
            description: 'Monitor your delivery in real-time',
            icon: Icons.local_shipping,
            color: AppColors.info,
            duration: 2,
            difficulty: 'Easy',
            steps: [
              'Go to "My Orders" section',
              'Select your active order',
              'View order status and timeline',
              'See rider location on map',
              'Get real-time notifications',
              'Contact rider if needed',
              'Rate your experience after delivery',
            ],
          ),
        ];
      case 'wallet':
        return [
          Tutorial(
            title: 'Managing ProjekCoins',
            description: 'Learn how to use your digital wallet',
            icon: Icons.account_balance_wallet,
            color: AppColors.ratingGold,
            duration: 4,
            difficulty: 'Easy',
            steps: [
              'Go to Wallet section',
              'View your ProjekCoin balance',
              'Add money to convert to ProjekCoins',
              'Use coins for purchases and payments',
              'Check transaction history',
              'Set up auto-reload (optional)',
              'Gift coins to friends',
            ],
          ),
        ];
      case 'account':
        return [
          Tutorial(
            title: 'Profile Setup',
            description: 'Complete your profile for better experience',
            icon: Icons.person,
            color: AppColors.userPrimary,
            duration: 3,
            difficulty: 'Easy',
            steps: [
              'Go to Profile section',
              'Tap "Edit Profile"',
              'Add your name and photo',
              'Verify your phone number',
              'Add email address',
              'Set delivery addresses',
              'Configure notification preferences',
            ],
          ),
        ];
      default:
        return [];
    }
  }
}

class Tutorial {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int duration;
  final String difficulty;
  final List<String> steps;

  Tutorial({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.duration,
    required this.difficulty,
    required this.steps,
  });
}

class TutorialDetailPage extends StatelessWidget {
  final Tutorial tutorial;

  const TutorialDetailPage({super.key, required this.tutorial});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tutorial.title),
        backgroundColor: tutorial.color,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: tutorial.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    tutorial.icon,
                    size: 48,
                    color: tutorial.color,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    tutorial.title,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    tutorial.description,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Chip(
                        label: Text('${tutorial.duration} min'),
                        backgroundColor: Colors.white,
                      ),
                      const SizedBox(width: 8),
                      Chip(
                        label: Text(tutorial.difficulty),
                        backgroundColor: Colors.white,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Steps
            const Text(
              'Step-by-Step Guide:',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ...tutorial.steps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: tutorial.color,
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        step,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),

            const SizedBox(height: 24),

            // Action Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.popUntil(context, (route) => route.isFirst);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: tutorial.color,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'Got it! Take me back',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
