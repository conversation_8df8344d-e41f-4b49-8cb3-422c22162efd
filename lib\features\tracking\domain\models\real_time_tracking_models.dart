import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'real_time_tracking_models.g.dart';

@HiveType(typeId: 120)
enum TrackingStatus {
  @HiveField(0)
  inactive,
  @HiveField(1)
  active,
  @HiveField(2)
  paused,
  @HiveField(3)
  completed,
  @HiveField(4)
  failed,
}

@HiveType(typeId: 121)
enum RouteOptimizationType {
  @HiveField(0)
  fastest,
  @HiveField(1)
  shortest,
  @HiveField(2)
  balanced,
  @HiveField(3)
  fuelEfficient,
}

@HiveType(typeId: 122)
@JsonSerializable()
class RealTimeLocation extends Equatable {
  @HiveField(0)
  final double latitude;
  
  @HiveField(1)
  final double longitude;
  
  @HiveField(2)
  final double accuracy;
  
  @HiveField(3)
  final double? altitude;
  
  @HiveField(4)
  final double? speed; // m/s
  
  @HiveField(5)
  final double? heading; // degrees
  
  @HiveField(6)
  final DateTime timestamp;
  
  @HiveField(7)
  final String? address;
  
  @HiveField(8)
  final Map<String, dynamic> metadata;

  const RealTimeLocation({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    this.altitude,
    this.speed,
    this.heading,
    required this.timestamp,
    this.address,
    this.metadata = const {},
  });

  factory RealTimeLocation.fromJson(Map<String, dynamic> json) =>
      _$RealTimeLocationFromJson(json);

  Map<String, dynamic> toJson() => _$RealTimeLocationToJson(this);

  double get speedKmh => (speed ?? 0) * 3.6;
  String get formattedSpeed => '${speedKmh.toStringAsFixed(1)} km/h';
  String get formattedAccuracy => '±${accuracy.toStringAsFixed(0)}m';

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        accuracy,
        altitude,
        speed,
        heading,
        timestamp,
        address,
        metadata,
      ];
}

@HiveType(typeId: 123)
@JsonSerializable()
class RoutePoint extends Equatable {
  @HiveField(0)
  final double latitude;
  
  @HiveField(1)
  final double longitude;
  
  @HiveField(2)
  final String? instruction;
  
  @HiveField(3)
  final double? distance; // meters to next point
  
  @HiveField(4)
  final int? duration; // seconds to next point
  
  @HiveField(5)
  final String? streetName;
  
  @HiveField(6)
  final String? maneuver;

  const RoutePoint({
    required this.latitude,
    required this.longitude,
    this.instruction,
    this.distance,
    this.duration,
    this.streetName,
    this.maneuver,
  });

  factory RoutePoint.fromJson(Map<String, dynamic> json) =>
      _$RoutePointFromJson(json);

  Map<String, dynamic> toJson() => _$RoutePointToJson(this);

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        instruction,
        distance,
        duration,
        streetName,
        maneuver,
      ];
}

@HiveType(typeId: 124)
@JsonSerializable()
class OptimizedRoute extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final List<RoutePoint> points;
  
  @HiveField(2)
  final double totalDistance; // meters
  
  @HiveField(3)
  final int totalDuration; // seconds
  
  @HiveField(4)
  final RouteOptimizationType optimizationType;
  
  @HiveField(5)
  final DateTime calculatedAt;
  
  @HiveField(6)
  final DateTime? expiresAt;
  
  @HiveField(7)
  final Map<String, dynamic> trafficData;
  
  @HiveField(8)
  final List<String> warnings;
  
  @HiveField(9)
  final Map<String, dynamic> metadata;

  const OptimizedRoute({
    required this.id,
    required this.points,
    required this.totalDistance,
    required this.totalDuration,
    this.optimizationType = RouteOptimizationType.balanced,
    required this.calculatedAt,
    this.expiresAt,
    this.trafficData = const {},
    this.warnings = const [],
    this.metadata = const {},
  });

  factory OptimizedRoute.fromJson(Map<String, dynamic> json) =>
      _$OptimizedRouteFromJson(json);

  Map<String, dynamic> toJson() => _$OptimizedRouteToJson(this);

  String get formattedDistance {
    if (totalDistance < 1000) {
      return '${totalDistance.toStringAsFixed(0)}m';
    } else {
      return '${(totalDistance / 1000).toStringAsFixed(1)}km';
    }
  }

  String get formattedDuration {
    final minutes = (totalDuration / 60).round();
    if (minutes < 60) {
      return '${minutes}min';
    } else {
      final hours = (minutes / 60).floor();
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}min';
    }
  }

  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  @override
  List<Object?> get props => [
        id,
        points,
        totalDistance,
        totalDuration,
        optimizationType,
        calculatedAt,
        expiresAt,
        trafficData,
        warnings,
        metadata,
      ];
}

@HiveType(typeId: 125)
@JsonSerializable()
class ETACalculation extends Equatable {
  @HiveField(0)
  final DateTime estimatedArrival;
  
  @HiveField(1)
  final int confidenceLevel; // 0-100
  
  @HiveField(2)
  final double remainingDistance; // meters
  
  @HiveField(3)
  final int remainingTime; // seconds
  
  @HiveField(4)
  final double averageSpeed; // m/s
  
  @HiveField(5)
  final DateTime calculatedAt;
  
  @HiveField(6)
  final List<String> factors; // traffic, weather, etc.
  
  @HiveField(7)
  final Map<String, dynamic> trafficImpact;

  const ETACalculation({
    required this.estimatedArrival,
    required this.confidenceLevel,
    required this.remainingDistance,
    required this.remainingTime,
    required this.averageSpeed,
    required this.calculatedAt,
    this.factors = const [],
    this.trafficImpact = const {},
  });

  factory ETACalculation.fromJson(Map<String, dynamic> json) =>
      _$ETACalculationFromJson(json);

  Map<String, dynamic> toJson() => _$ETACalculationToJson(this);

  String get formattedETA {
    final now = DateTime.now();
    final difference = estimatedArrival.difference(now);
    
    if (difference.inMinutes < 1) {
      return 'Arriving now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min';
    } else {
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;
      return '${hours}h ${minutes}min';
    }
  }

  String get formattedRemainingDistance {
    if (remainingDistance < 1000) {
      return '${remainingDistance.toStringAsFixed(0)}m away';
    } else {
      return '${(remainingDistance / 1000).toStringAsFixed(1)}km away';
    }
  }

  String get confidenceText {
    if (confidenceLevel >= 90) return 'Very High';
    if (confidenceLevel >= 75) return 'High';
    if (confidenceLevel >= 60) return 'Medium';
    if (confidenceLevel >= 40) return 'Low';
    return 'Very Low';
  }

  @override
  List<Object?> get props => [
        estimatedArrival,
        confidenceLevel,
        remainingDistance,
        remainingTime,
        averageSpeed,
        calculatedAt,
        factors,
        trafficImpact,
      ];
}

@HiveType(typeId: 126)
@JsonSerializable()
class RealTimeTracking extends Equatable {
  @HiveField(0)
  final String orderId;
  
  @HiveField(1)
  final String riderId;
  
  @HiveField(2)
  final TrackingStatus status;
  
  @HiveField(3)
  final RealTimeLocation? currentLocation;
  
  @HiveField(4)
  final OptimizedRoute? activeRoute;
  
  @HiveField(5)
  final ETACalculation? currentETA;
  
  @HiveField(6)
  final List<RealTimeLocation> locationHistory;
  
  @HiveField(7)
  final DateTime startedAt;
  
  @HiveField(8)
  final DateTime? completedAt;
  
  @HiveField(9)
  final double totalDistanceTraveled;
  
  @HiveField(10)
  final int totalTimeElapsed; // seconds
  
  @HiveField(11)
  final Map<String, dynamic> performanceMetrics;
  
  @HiveField(12)
  final List<String> alerts;
  
  @HiveField(13)
  final Map<String, dynamic> metadata;

  const RealTimeTracking({
    required this.orderId,
    required this.riderId,
    this.status = TrackingStatus.inactive,
    this.currentLocation,
    this.activeRoute,
    this.currentETA,
    this.locationHistory = const [],
    required this.startedAt,
    this.completedAt,
    this.totalDistanceTraveled = 0.0,
    this.totalTimeElapsed = 0,
    this.performanceMetrics = const {},
    this.alerts = const [],
    this.metadata = const {},
  });

  factory RealTimeTracking.fromJson(Map<String, dynamic> json) =>
      _$RealTimeTrackingFromJson(json);

  Map<String, dynamic> toJson() => _$RealTimeTrackingToJson(this);

  factory RealTimeTracking.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return RealTimeTracking.fromJson({
      ...data,
      'startedAt': (data['startedAt'] as Timestamp?)?.toDate().toIso8601String(),
      'completedAt': (data['completedAt'] as Timestamp?)?.toDate().toIso8601String(),
    });
  }

  double get averageSpeed {
    if (totalTimeElapsed == 0) return 0.0;
    return totalDistanceTraveled / totalTimeElapsed; // m/s
  }

  String get formattedAverageSpeed => '${(averageSpeed * 3.6).toStringAsFixed(1)} km/h';

  String get formattedTotalDistance {
    if (totalDistanceTraveled < 1000) {
      return '${totalDistanceTraveled.toStringAsFixed(0)}m';
    } else {
      return '${(totalDistanceTraveled / 1000).toStringAsFixed(1)}km';
    }
  }

  String get formattedTotalTime {
    final minutes = (totalTimeElapsed / 60).round();
    if (minutes < 60) {
      return '${minutes}min';
    } else {
      final hours = (minutes / 60).floor();
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}min';
    }
  }

  bool get isActive => status == TrackingStatus.active;
  bool get isCompleted => status == TrackingStatus.completed;

  @override
  List<Object?> get props => [
        orderId,
        riderId,
        status,
        currentLocation,
        activeRoute,
        currentETA,
        locationHistory,
        startedAt,
        completedAt,
        totalDistanceTraveled,
        totalTimeElapsed,
        performanceMetrics,
        alerts,
        metadata,
      ];
}
