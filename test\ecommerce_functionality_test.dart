import 'package:flutter_test/flutter_test.dart';

import 'package:projek/features/marketplace/domain/models/product.dart';
import 'package:projek/features/services/domain/models/service.dart';
import 'package:projek/features/services/domain/models/booking.dart';

void main() {
  group('Multi-Role App Model Tests', () {
    late Product testProduct;

    setUp(() {
      // Create test product
      testProduct = Product(
        id: 'test_product_1',
        name: 'Test Product',
        description: 'A test product for unit testing',
        price: 299.0,
        originalPrice: 399.0,
        images: ['test_image.jpg'],
        category: 'Test Category',
        subcategory: 'Test Subcategory',
        brand: 'Test Brand',
        rating: 4.5,
        reviewCount: 100,
        stockQuantity: 50,
        vendorId: 'test_vendor',
        vendorName: 'Test Vendor',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isFeatured: true,
      );
    });

    group('Product Model Tests', () {
      test('Product should have correct formatted price', () {
        expect(testProduct.formattedPrice, '₹299.00');
        expect(testProduct.formattedOriginalPrice, '₹399.00');
      });

      test('Product should calculate discount correctly', () {
        expect(testProduct.hasDiscount, true);
        expect(testProduct.calculatedDiscountPercentage, closeTo(25.06, 0.1));
      });

      test('Product should have primary image', () {
        expect(testProduct.primaryImage, 'test_image.jpg');
      });

      test('Product should be available', () {
        expect(testProduct.isAvailable, true);
      });
    });

    group('Service Model Tests', () {
      late Service testService;

      setUp(() {
        testService = Service(
          id: 'service_1',
          title: 'House Cleaning Service',
          description: 'Professional house cleaning service',
          type: ServiceType.cleaning,
          category: ServiceCategory.homeServices,
          providerId: 'provider_1',
          providerName: 'CleanPro Services',
          providerPhone: '+91-**********',
          basePrice: 299.0,
          pricingType: PricingType.hourly,
          rating: 4.8,
          reviewCount: 150,
          images: ['cleaning_service.jpg'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      });

      test('Service should have correct base price', () {
        expect(testService.basePrice, 299.0);
      });

      test('Service should be active status', () {
        expect(testService.status, ServiceStatus.active);
      });

      test('Service should have images', () {
        expect(testService.images.isNotEmpty, true);
        expect(testService.images.first, 'cleaning_service.jpg');
      });

      test('Service should have correct pricing type', () {
        expect(testService.pricingType, PricingType.hourly);
      });
    });

    group('Booking Model Tests', () {
      late Service testService;
      late Booking testBooking;

      setUp(() {
        testService = Service(
          id: 'service_1',
          title: 'House Cleaning Service',
          description: 'Professional house cleaning service',
          type: ServiceType.cleaning,
          category: ServiceCategory.homeServices,
          providerId: 'provider_1',
          providerName: 'CleanPro Services',
          providerPhone: '+91-**********',
          basePrice: 299.0,
          pricingType: PricingType.hourly,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        testBooking = Booking(
          id: 'booking_1',
          bookingNumber: 'BK001',
          serviceId: testService.id,
          userId: 'user_1',
          providerId: testService.providerId,
          serviceName: testService.title,
          providerName: testService.providerName,
          providerPhone: testService.providerPhone,
          schedule: BookingSchedule(
            scheduledDate: DateTime.now().add(const Duration(days: 1)),
            startTime: '10:00',
            endTime: '12:00',
            durationHours: 2,
          ),
          address: BookingAddress(
            street: '123 Main St',
            city: 'Delhi',
            state: 'Delhi',
            pincode: '110001',
          ),
          status: BookingStatus.pending,
          paymentStatus: PaymentStatus.pending,
          totalAmount: 723.58,
          serviceAmount: 598.0,
          platformFee: 59.8,
          taxes: 65.78,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      });

      test('Should create booking with correct details', () {
        expect(testBooking.serviceId, testService.id);
        expect(testBooking.serviceName, testService.title);
        expect(testBooking.status, BookingStatus.pending);
      });

      test('Should calculate total amount correctly', () {
        expect(testBooking.totalAmount, 723.58);
      });

      test('Should have correct booking schedule', () {
        expect(testBooking.schedule.startTime, '10:00');
        expect(testBooking.schedule.endTime, '12:00');
        expect(testBooking.schedule.durationHours, 2);
      });
    });
  });
}
