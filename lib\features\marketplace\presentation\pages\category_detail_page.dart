import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class CategoryDetailPage extends StatefulWidget {
  final String categoryName;
  final String? categoryId;

  const CategoryDetailPage({
    super.key,
    required this.categoryName,
    this.categoryId,
  });

  @override
  State<CategoryDetailPage> createState() => _CategoryDetailPageState();
}

class _CategoryDetailPageState extends State<CategoryDetailPage> {
  String _selectedSortBy = 'Popular';
  String _selectedViewType = 'grid';

  final List<String> _sortOptions = [
    'Popular',
    'Price: Low to High',
    'Price: High to Low',
    'Newest',
    'Rating',
    'Distance',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => context.pop(),
        ),
        title: Text(
          widget.categoryName,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _selectedViewType == 'grid' ? Icons.view_list : Icons.grid_view,
              color: Colors.black87,
            ),
            onPressed: () {
              setState(() {
                _selectedViewType = _selectedViewType == 'grid' ? 'list' : 'grid';
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.search, color: Colors.black87),
            onPressed: () {
              context.push('/search?category=${Uri.encodeComponent(widget.categoryName)}');
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: _selectedViewType == 'grid' 
                ? _buildGridView() 
                : _buildListView(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('All', true),
                  const SizedBox(width: 8),
                  _buildFilterChip('Products', false),
                  const SizedBox(width: 8),
                  _buildFilterChip('Services', false),
                  const SizedBox(width: 8),
                  _buildFilterChip('Nearby', false),
                ],
              ),
            ),
          ),
          const SizedBox(width: 12),
          PopupMenuButton<String>(
            initialValue: _selectedSortBy,
            onSelected: (value) {
              setState(() {
                _selectedSortBy = value;
              });
            },
            itemBuilder: (context) => _sortOptions.map((option) {
              return PopupMenuItem(
                value: option,
                child: Text(
                  option,
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
              );
            }).toList(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.sort, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Sort',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.poppins(
          fontSize: 12,
          color: isSelected ? Colors.white : Colors.grey[700],
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        // Handle filter selection
      },
      backgroundColor: Colors.grey[200],
      selectedColor: Colors.blue,
      checkmarkColor: Colors.white,
    );
  }

  Widget _buildGridView() {
    final items = _getCategoryItems();
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.75,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildGridItem(item);
      },
    );
  }

  Widget _buildListView() {
    final items = _getCategoryItems();
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildListItem(item);
      },
    );
  }

  Widget _buildGridItem(Map<String, dynamic> item) {
    return GestureDetector(
      onTap: () => _navigateToItemDetail(item),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: item['color'] ?? Colors.grey[300],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Icon(
                        item['icon'] ?? Icons.image,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    if (item['discount'] != null)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            item['discount'],
                            style: GoogleFonts.poppins(
                              fontSize: 10,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.favorite_border,
                          size: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Content
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['name'] ?? '',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    if (item['rating'] != null)
                      Row(
                        children: [
                          const Icon(Icons.star, size: 12, color: Colors.orange),
                          const SizedBox(width: 2),
                          Text(
                            item['rating'].toString(),
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    const Spacer(),
                    if (item['price'] != null)
                      Row(
                        children: [
                          Text(
                            item['price'],
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          if (item['originalPrice'] != null) ...[
                            const SizedBox(width: 4),
                            Text(
                              item['originalPrice'],
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.grey[600],
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          ],
                        ],
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListItem(Map<String, dynamic> item) {
    return GestureDetector(
      onTap: () => _navigateToItemDetail(item),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ListTile(
          contentPadding: const EdgeInsets.all(16),
          leading: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: item['color'] ?? Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              item['icon'] ?? Icons.image,
              color: Colors.white,
              size: 24,
            ),
          ),
          title: Text(
            item['name'] ?? '',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (item['description'] != null) ...[
                const SizedBox(height: 4),
                Text(
                  item['description'],
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              if (item['rating'] != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.star, size: 12, color: Colors.orange),
                    const SizedBox(width: 2),
                    Text(
                      item['rating'].toString(),
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    if (item['price'] != null) ...[
                      const Spacer(),
                      Text(
                        item['price'],
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getCategoryItems() {
    // Mock data based on category - in real app, this would come from API
    return _getMockItemsForCategory(widget.categoryName);
  }

  List<Map<String, dynamic>> _getMockItemsForCategory(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return [
          {
            'id': '1',
            'name': 'Pizza Margherita',
            'description': 'Classic Italian pizza with fresh basil',
            'price': '\$12.99',
            'originalPrice': '\$15.99',
            'discount': '20% OFF',
            'rating': 4.5,
            'icon': Icons.local_pizza,
            'color': Colors.red,
            'type': 'food',
          },
          {
            'id': '2',
            'name': 'Burger Deluxe',
            'description': 'Juicy beef burger with premium toppings',
            'price': '\$8.99',
            'rating': 4.3,
            'icon': Icons.lunch_dining,
            'color': Colors.orange,
            'type': 'food',
          },
        ];
      case 'electronics':
        return [
          {
            'id': '3',
            'name': 'Wireless Headphones',
            'description': 'Premium noise-cancelling headphones',
            'price': '\$99.99',
            'originalPrice': '\$129.99',
            'discount': '25% OFF',
            'rating': 4.7,
            'icon': Icons.headphones,
            'color': Colors.blue,
            'type': 'product',
          },
          {
            'id': '4',
            'name': 'Smartphone',
            'description': 'Latest model with advanced features',
            'price': '\$699.99',
            'rating': 4.6,
            'icon': Icons.phone_android,
            'color': Colors.purple,
            'type': 'product',
          },
        ];
      default:
        return [
          {
            'id': '5',
            'name': 'Sample Item',
            'description': 'Sample description for ${category.toLowerCase()}',
            'price': '\$29.99',
            'rating': 4.0,
            'icon': Icons.shopping_bag,
            'color': Colors.green,
            'type': 'product',
          },
        ];
    }
  }

  void _navigateToItemDetail(Map<String, dynamic> item) {
    final type = item['type'] ?? 'product';
    final id = item['id'];
    
    switch (type) {
      case 'food':
        context.push('/restaurant/$id');
        break;
      case 'product':
        context.push('/product/$id');
        break;
      case 'service':
        context.push('/service/$id');
        break;
      default:
        context.push('/product/$id');
    }
  }
}
