import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:firebase_core/firebase_core.dart';
import 'dart:async';
import 'dart:io';

import 'firebase_options_rider.dart';
import 'features/rider/presentation/pages/rider_home_page.dart';
import 'features/rider/presentation/pages/rider_orders_page.dart';
import 'features/rider/presentation/pages/rider_profile_page.dart';
import 'features/rider/presentation/pages/rider_earnings_page.dart';
import 'features/auth/presentation/pages/onboarding_page.dart';
import 'features/auth/presentation/pages/modern_login_page.dart';
import 'features/auth/presentation/pages/registration_page.dart';
import 'features/auth/presentation/pages/forgot_password_page.dart';
import 'features/auth/presentation/pages/phone_auth_page.dart';
import 'services/auth_service.dart';
import 'core/constants/app_colors.dart';
import 'core/utils/responsive_layout.dart';
import 'demo/rider_demo_data.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase with proper error handling
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptionsRider.currentPlatform,
    );
    print('✅ Firebase initialized successfully for Rider app');
  } catch (e) {
    if (e.toString().contains('duplicate-app')) {
      print('ℹ️ Firebase already initialized: $e');
    } else {
      print('❌ Firebase initialization failed: $e');
      // Continue without Firebase for demo purposes
    }
  }

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
    ),
  );

  runApp(const RiderApp());
}

class RiderApp extends StatelessWidget {
  const RiderApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Projek Rider',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.green,
        primaryColor: AppColors.primary,
        scaffoldBackgroundColor: Colors.white,
        textTheme: GoogleFonts.poppinsTextTheme(),
        appBarTheme: AppBarTheme(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          titleTextStyle: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
      routerConfig: _router,
    );
  }
}

// Router configuration
final GoRouter _router = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const AuthenticatedRiderApp(),
    ),
    GoRoute(
      path: '/home',
      builder: (context, state) => const AuthenticatedRiderApp(),
    ),
    GoRoute(
      path: '/dashboard',
      builder: (context, state) => const AuthenticatedRiderApp(),
    ),
    GoRoute(
      path: '/onboarding',
      builder: (context, state) => const OnboardingPage(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => const ModernLoginPage(),
    ),
    GoRoute(
      path: '/auth/login',
      builder: (context, state) => const ModernLoginPage(),
    ),
    GoRoute(
      path: '/register',
      builder: (context, state) => const RegistrationPage(),
    ),
    GoRoute(
      path: '/auth/register',
      builder: (context, state) => const RegistrationPage(),
    ),
    GoRoute(
      path: '/forgot-password',
      builder: (context, state) => const ForgotPasswordPage(),
    ),
    GoRoute(
      path: '/auth/forgot-password',
      builder: (context, state) => const ForgotPasswordPage(),
    ),
    GoRoute(
      path: '/rider/home',
      builder: (context, state) => const RiderMainPage(),
    ),
    GoRoute(
      path: '/rider/dashboard',
      builder: (context, state) => const RiderMainPage(),
    ),
    GoRoute(
      path: '/dashboard',
      builder: (context, state) => const RiderMainPage(),
    ),
    GoRoute(
      path: '/kyc',
      builder: (context, state) => const Placeholder(), // KYC page placeholder
    ),
    GoRoute(
      path: '/profile',
      builder: (context, state) => const RiderProfilePage(),
    ),
    GoRoute(
      path: '/orders',
      builder: (context, state) => const EnhancedRiderOrdersPage(),
    ),
    GoRoute(
      path: '/earnings',
      builder: (context, state) => const RiderEarningsPage(),
    ),
    GoRoute(
      path: '/auth/phone',
      builder: (context, state) => const PhoneAuthPage(),
    ),
    GoRoute(
      path: '/phone-auth',
      builder: (context, state) => const PhoneAuthPage(),
    ),
  ],
  errorBuilder: (context, state) => Scaffold(
    appBar: AppBar(
      title: const Text('Page Not Found'),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
    ),
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Page Not Found',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Route: ${state.uri}',
            style: GoogleFonts.poppins(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.go('/'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('Go to Home'),
          ),
        ],
      ),
    ),
  ),
);

// Authenticated wrapper for the rider app
class AuthenticatedRiderApp extends StatefulWidget {
  const AuthenticatedRiderApp({super.key});

  @override
  State<AuthenticatedRiderApp> createState() => _AuthenticatedRiderAppState();
}

class _AuthenticatedRiderAppState extends State<AuthenticatedRiderApp> {
  final AuthService _authService = AuthService();
  bool _hasInitializationError = false;

  @override
  void initState() {
    super.initState();
    _initializeAuthService();
  }

  Future<void> _initializeAuthService() async {
    try {
      await _authService.initialize();
    } catch (e) {
      print('⚠️ AuthService initialization failed: $e');
      if (mounted) {
        setState(() {
          _hasInitializationError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // If there's an initialization error, show demo mode
    if (_hasInitializationError) {
      return const _DemoModeScreen();
    }

    return StreamBuilder<AuthState>(
      stream: _authService.authStateStream,
      initialData: _authService.currentState,
      builder: (context, snapshot) {
        final authState = snapshot.data ?? AuthState.initial;

        switch (authState) {
          case AuthState.authenticated:
            return const RiderMainPage();
          case AuthState.unauthenticated:
            return const _AuthenticationFlow();
          case AuthState.loading:
          case AuthState.initial:
            return const _LoadingScreen();
          case AuthState.error:
            return const _DemoModeScreen();
        }
      },
    );
  }
}

// Authentication flow widget
class _AuthenticationFlow extends StatefulWidget {
  const _AuthenticationFlow();

  @override
  State<_AuthenticationFlow> createState() => _AuthenticationFlowState();
}

class _AuthenticationFlowState extends State<_AuthenticationFlow> {
  bool _onboardingCompleted = false;

  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }

  Future<void> _checkOnboardingStatus() async {
    final completed = await AuthService().isOnboardingCompleted();
    setState(() {
      _onboardingCompleted = completed;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_onboardingCompleted) {
      return const OnboardingPage();
    }
    return const ModernLoginPage();
  }
}

// Loading screen
class _LoadingScreen extends StatelessWidget {
  const _LoadingScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.delivery_dining,
                  size: 60,
                  color: Color(0xFF4CAF50),
                ),
              ),
              const SizedBox(height: 32),
              Text(
                'Projek',
                style: GoogleFonts.poppins(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'Proudly Made in India 🇮🇳',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(height: 48),
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              const SizedBox(height: 16),
              Text(
                'Loading...',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Demo mode screen - shows login directly when Firebase fails
class _DemoModeScreen extends StatelessWidget {
  const _DemoModeScreen();

  @override
  Widget build(BuildContext context) {
    return const ModernLoginPage();
  }
}

// Error screen
class _ErrorScreen extends StatelessWidget {
  const _ErrorScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 80, color: Colors.white),
                const SizedBox(height: 24),
                Text(
                  'Something went wrong',
                  style: GoogleFonts.poppins(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'We encountered an error while loading the app. Please try again.',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () {
                    AuthService().initialize();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF4CAF50),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                  ),
                  child: Text(
                    'Retry',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Main page with bottom navigation
class RiderMainPage extends StatefulWidget {
  const RiderMainPage({super.key});

  @override
  State<RiderMainPage> createState() => _RiderMainPageState();
}

class _RiderMainPageState extends State<RiderMainPage>
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  bool _isOnline = true;
  late TabController _ordersTabController;

  final List<Widget> _pages = [
    const EnhancedRiderHomePage(),
    const EnhancedRiderOrdersPage(),
    const RiderEarningsPage(),
    const RiderProfilePage(),
  ];

  @override
  void initState() {
    super.initState();
    _ordersTabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _ordersTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Check if we're in split screen or tablet mode
    final isTabletOrSplitScreen =
        ResponsiveLayout.isTablet(context) ||
        ResponsiveLayout.isDesktop(context) ||
        MediaQuery.of(context).size.width > 600;

    if (isTabletOrSplitScreen) {
      // Split screen / tablet layout with side navigation
      return _buildSplitScreenLayout(context);
    } else {
      // Mobile layout with bottom navigation
      return _buildMobileLayout(context);
    }
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        selectedItemColor: const Color(0xFF4CAF50),
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.assignment),
            label: 'Orders',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'Earnings',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildSplitScreenLayout(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Side navigation panel
          Container(
            width: 280,
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(2, 0),
                ),
              ],
            ),
            child: _buildSideNavigation(),
          ),
          // Main content area
          Expanded(child: _pages[_currentIndex]),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildSideNavigation() {
    return Column(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const SizedBox(height: 40),
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.delivery_dining,
                  size: 40,
                  color: Color(0xFF4CAF50),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Projek Rider',
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              Text(
                'Split Screen Mode',
                style: GoogleFonts.poppins(fontSize: 12, color: Colors.white70),
              ),
            ],
          ),
        ),
        // Navigation items
        Expanded(
          child: ListView(
            padding: const EdgeInsets.symmetric(vertical: 8),
            children: [
              _buildSideNavItem(0, Icons.home, 'Home'),
              _buildSideNavItem(1, Icons.assignment, 'Orders'),
              _buildSideNavItem(2, Icons.account_balance_wallet, 'Earnings'),
              _buildSideNavItem(3, Icons.person, 'Profile'),
            ],
          ),
        ),
        // Online status toggle
        Container(
          padding: const EdgeInsets.all(24),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  _isOnline
                      ? Icons.radio_button_checked
                      : Icons.radio_button_off,
                  color: Colors.white,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _isOnline ? 'Online' : 'Offline',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Switch(
                  value: _isOnline,
                  onChanged: (value) => setState(() => _isOnline = value),
                  activeColor: Colors.white,
                  activeTrackColor: Colors.white.withValues(alpha: 0.3),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSideNavItem(int index, IconData icon, String label) {
    final isSelected = _currentIndex == index;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: isSelected ? Colors.white : Colors.white70),
        title: Text(
          label,
          style: GoogleFonts.poppins(
            color: isSelected ? Colors.white : Colors.white70,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
        selected: isSelected,
        selectedTileColor: Colors.white.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        onTap: () => setState(() => _currentIndex = index),
      ),
    );
  }

  Widget? _buildFloatingActionButtons() {
    if (_currentIndex == 1) {
      // Orders page
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: "emergency",
            mini: true,
            backgroundColor: Colors.red,
            onPressed: () => _showEmergencyContacts(context),
            child: const Icon(Icons.emergency, color: Colors.white),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: "online_toggle",
            backgroundColor: _isOnline ? Colors.green : Colors.grey,
            onPressed: () => setState(() => _isOnline = !_isOnline),
            child: Icon(
              _isOnline ? Icons.location_on : Icons.location_off,
              color: Colors.white,
            ),
          ),
        ],
      );
    }
    return null;
  }

  void _showEmergencyContacts(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Emergency Contacts',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ...RiderDemoData.emergencyContacts
                .map(
                  (contact) => ListTile(
                    leading: CircleAvatar(
                      backgroundColor: contact['type'] == 'emergency'
                          ? Colors.red
                          : AppColors.primary,
                      child: Icon(
                        contact['type'] == 'emergency'
                            ? Icons.emergency
                            : Icons.support_agent,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(contact['name']!),
                    subtitle: Text(contact['number']!),
                    trailing: IconButton(
                      icon: const Icon(Icons.call, color: Colors.green),
                      onPressed: () => _makeCall(contact['number']!),
                    ),
                  ),
                )
                .toList(),
          ],
        ),
      ),
    );
  }

  Future<void> _makeCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Could not make call. Please check your device settings.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error making call: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// Enhanced Orders Page with Available Orders and Active Deliveries
class EnhancedRiderOrdersPage extends StatefulWidget {
  const EnhancedRiderOrdersPage({super.key});

  @override
  State<EnhancedRiderOrdersPage> createState() =>
      _EnhancedRiderOrdersPageState();
}

class _EnhancedRiderOrdersPageState extends State<EnhancedRiderOrdersPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Timer? _refreshTimer;
  RiderMode _currentMode = RiderMode.foodDelivery; // Track current mode
  final AlarmService _alarmService = AlarmService(); // Alarm service instance

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Auto-refresh available orders every 5 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _currentMode == RiderMode.foodDelivery
              ? 'Food Orders'
              : 'Ride Requests',
        ),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        actions: [
          // Mode switcher in app bar
          PopupMenuButton<RiderMode>(
            icon: Icon(
              _currentMode == RiderMode.foodDelivery
                  ? Icons.restaurant
                  : Icons.directions_car,
            ),
            onSelected: (RiderMode mode) {
              setState(() {
                _currentMode = mode;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    mode == RiderMode.foodDelivery
                        ? 'Switched to Food Delivery mode'
                        : 'Switched to Ride Sharing mode',
                  ),
                  backgroundColor: Colors.blue,
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: RiderMode.foodDelivery,
                child: Row(
                  children: [
                    Icon(
                      Icons.restaurant,
                      color: _currentMode == RiderMode.foodDelivery
                          ? Colors.green
                          : Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    const Text('Food Delivery'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: RiderMode.rideSharing,
                child: Row(
                  children: [
                    Icon(
                      Icons.directions_car,
                      color: _currentMode == RiderMode.rideSharing
                          ? Colors.green
                          : Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    const Text('Ride Sharing'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              text: _currentMode == RiderMode.foodDelivery
                  ? 'Available Orders'
                  : 'Available Rides',
            ),
            Tab(
              text: _currentMode == RiderMode.foodDelivery
                  ? 'Active Deliveries'
                  : 'Active Rides',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _currentMode == RiderMode.foodDelivery
              ? _buildAvailableOrdersTab()
              : _buildAvailableRidesTab(),
          _currentMode == RiderMode.foodDelivery
              ? _buildActiveDeliveriesTab()
              : _buildActiveRidesTab(),
        ],
      ),
    );
  }

  Widget _buildAvailableOrdersTab() {
    final availableOrders = RiderDemoData.availableOrders;

    if (availableOrders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.delivery_dining, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No orders available',
              style: GoogleFonts.poppins(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'New orders will appear here automatically',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        setState(() {});
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: availableOrders.length,
        itemBuilder: (context, index) {
          final order = availableOrders[index];
          return _buildAvailableOrderCard(order);
        },
      ),
    );
  }

  Widget _buildAvailableOrderCard(AvailableOrder order) {
    final timeElapsed = DateTime.now()
        .difference(order.broadcastTime)
        .inSeconds;
    final timeRemaining = order.acceptanceTimeoutSeconds - timeElapsed;

    if (timeRemaining <= 0) {
      return const SizedBox.shrink(); // Hide expired orders
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with countdown timer
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  order.restaurantName,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                _buildCountdownTimer(timeRemaining),
              ],
            ),
            const SizedBox(height: 8),

            // Order details
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailRow(
                        Icons.location_on,
                        'Distance',
                        '${order.distanceKm} km',
                      ),
                      _buildDetailRow(
                        Icons.currency_rupee,
                        'Delivery Fee',
                        '₹${order.deliveryFee.toStringAsFixed(0)}',
                      ),
                      _buildDetailRow(
                        Icons.shopping_bag,
                        'Order Value',
                        '₹${order.orderValue.toStringAsFixed(0)}',
                      ),
                      _buildDetailRow(
                        Icons.access_time,
                        'Est. Time',
                        '${order.estimatedCompletionMinutes} min',
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Customer and restaurant info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.store, size: 16, color: Colors.orange),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          order.restaurantAddress,
                          style: GoogleFonts.poppins(fontSize: 12),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.call,
                          color: Colors.green,
                          size: 20,
                        ),
                        onPressed: () => _makeCall(order.restaurantPhone),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.person, size: 16, color: Colors.blue),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          '${order.customerName} - ${order.customerAddress}',
                          style: GoogleFonts.poppins(fontSize: 12),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.call,
                          color: Colors.green,
                          size: 20,
                        ),
                        onPressed: () => _makeCall(order.customerPhone),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _acceptOrder(order),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      'Accept Order',
                      style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _declineOrder(order),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      'Decline',
                      style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCountdownTimer(int timeRemaining) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: timeRemaining <= 10 ? Colors.red : Colors.orange,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.timer, size: 16, color: Colors.white),
          const SizedBox(width: 4),
          Text(
            '${timeRemaining}s',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveDeliveriesTab() {
    final activeDeliveries = RiderDemoData.activeDeliveries;

    if (activeDeliveries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.local_shipping, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No active deliveries',
              style: GoogleFonts.poppins(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Accepted orders will appear here',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: activeDeliveries.length,
      itemBuilder: (context, index) {
        final delivery = activeDeliveries[index];
        return _buildActiveDeliveryCard(delivery);
      },
    );
  }

  // ========== RIDE-SHARING TAB METHODS ==========

  Widget _buildAvailableRidesTab() {
    final availableRides = RiderDemoData.availableRideRequests;

    if (availableRides.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.directions_car_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No ride requests available',
              style: GoogleFonts.poppins(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'New ride requests will appear here',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: availableRides.length,
      itemBuilder: (context, index) {
        final ride = availableRides[index];
        return _buildAvailableRideCard(ride);
      },
    );
  }

  Widget _buildAvailableRideCard(RideRequest ride) {
    final timeRemaining =
        ride.acceptanceTimeoutSeconds -
        DateTime.now().difference(ride.requestTime).inSeconds;
    final isExpired = timeRemaining <= 0;

    if (isExpired) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with route name and countdown
            Row(
              children: [
                Expanded(
                  child: Text(
                    ride.routeName,
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: timeRemaining <= 10 ? Colors.red : Colors.orange,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${timeRemaining}s',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Ride details
            Row(
              children: [
                Icon(Icons.location_on, color: Colors.green, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Distance: ${ride.distance} km',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
                const SizedBox(width: 16),
                Icon(Icons.currency_rupee, color: Colors.green, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Fare: ₹${ride.fare}',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, color: Colors.blue, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Est. Time: ${(ride.distance * 3.5).round()} min',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
                if (ride.isScheduled) ...[
                  const SizedBox(width: 16),
                  Icon(Icons.schedule, color: Colors.purple, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    'Scheduled',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.purple,
                    ),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 12),

            // Pickup and dropoff locations
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          ride.pickupLocation,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.call,
                          color: Colors.green,
                          size: 20,
                        ),
                        onPressed: () => _makeCall(ride.passengerPhone),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          ride.dropoffLocation,
                          style: GoogleFonts.poppins(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Passenger info
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue, size: 16),
                const SizedBox(width: 4),
                Text(
                  '${ride.passengerName} - ${ride.passengerPhone}',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
              ],
            ),

            if (ride.specialInstructions.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.info_outline, color: Colors.orange, size: 16),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      ride.specialInstructions,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.orange[700],
                      ),
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _acceptRide(ride),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Accept Ride'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _declineRide(ride),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Decline'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveRidesTab() {
    final activeRides = RiderDemoData.activeRides;

    if (activeRides.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.directions_car_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No active rides',
              style: GoogleFonts.poppins(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Accepted rides will appear here',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: activeRides.length,
      itemBuilder: (context, index) {
        final ride = activeRides[index];
        return _buildActiveRideCard(ride);
      },
    );
  }

  Widget _buildActiveRideCard(ActiveRide ride) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with route and status
            Row(
              children: [
                Expanded(
                  child: Text(
                    ride.request.routeName,
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getRideStatusColor(ride.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getRideStatusText(ride.status),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Ride details
            Row(
              children: [
                Icon(Icons.currency_rupee, color: Colors.green, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Fare: ₹${ride.request.fare}',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
                const SizedBox(width: 16),
                Icon(Icons.location_on, color: Colors.blue, size: 16),
                const SizedBox(width: 4),
                Text(
                  '${ride.request.distance} km',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Pickup and dropoff with passenger info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          ride.request.pickupLocation,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.call,
                          color: Colors.green,
                          size: 20,
                        ),
                        onPressed: () => _makeCall(ride.request.passengerPhone),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          ride.request.dropoffLocation,
                          style: GoogleFonts.poppins(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.person, color: Colors.blue, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        '${ride.request.passengerName} - ${ride.request.passengerPhone}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Action buttons based on ride status
            _buildRideActionButtons(ride),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveDeliveryCard(ActiveDelivery delivery) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  delivery.order.restaurantName,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(delivery.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(delivery.status),
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Order details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.store, size: 16, color: Colors.orange),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Pickup: ${delivery.order.restaurantAddress}',
                          style: GoogleFonts.poppins(fontSize: 12),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.call,
                          color: Colors.green,
                          size: 20,
                        ),
                        onPressed: () =>
                            _makeCall(delivery.order.restaurantPhone),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Delivery: ${delivery.order.customerAddress}',
                          style: GoogleFonts.poppins(fontSize: 12),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.call,
                          color: Colors.green,
                          size: 20,
                        ),
                        onPressed: () =>
                            _makeCall(delivery.order.customerPhone),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Action buttons based on status
            _buildDeliveryActionButtons(delivery),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(DeliveryStatus status) {
    switch (status) {
      case DeliveryStatus.accepted:
        return Colors.blue;
      case DeliveryStatus.enRoutePickup:
        return Colors.orange;
      case DeliveryStatus.arrivedPickup:
        return Colors.amber;
      case DeliveryStatus.pickedUp:
        return Colors.green;
      case DeliveryStatus.enRouteDelivery:
        return Colors.teal;
      case DeliveryStatus.arrivedDelivery:
        return Colors.purple;
      case DeliveryStatus.deliveredConfirmed:
        return Colors.green[700]!;
      case DeliveryStatus.completed:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(DeliveryStatus status) {
    switch (status) {
      case DeliveryStatus.accepted:
        return 'ACCEPTED';
      case DeliveryStatus.enRoutePickup:
        return 'EN ROUTE TO PICKUP';
      case DeliveryStatus.arrivedPickup:
        return 'ARRIVED AT PICKUP';
      case DeliveryStatus.pickedUp:
        return 'PICKED UP';
      case DeliveryStatus.enRouteDelivery:
        return 'EN ROUTE TO DELIVERY';
      case DeliveryStatus.arrivedDelivery:
        return 'ARRIVED AT DELIVERY';
      case DeliveryStatus.deliveredConfirmed:
        return 'DELIVERED';
      case DeliveryStatus.completed:
        return 'COMPLETED';
      default:
        return 'UNKNOWN';
    }
  }

  Widget _buildDeliveryActionButtons(ActiveDelivery delivery) {
    switch (delivery.status) {
      case DeliveryStatus.accepted:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _startNavigation(delivery, isPickup: true),
                icon: const Icon(Icons.navigation),
                label: const Text('Navigate to Restaurant'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      case DeliveryStatus.enRoutePickup:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _markArrived(delivery, isPickup: true),
                icon: const Icon(Icons.location_on),
                label: const Text('Mark Arrived at Restaurant'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      case DeliveryStatus.arrivedPickup:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _confirmPickup(delivery),
                icon: const Icon(Icons.check_circle),
                label: const Text('Confirm Pickup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      case DeliveryStatus.pickedUp:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _startNavigation(delivery, isPickup: false),
                icon: const Icon(Icons.navigation),
                label: const Text('Navigate to Customer'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      case DeliveryStatus.enRouteDelivery:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _markArrived(delivery, isPickup: false),
                icon: const Icon(Icons.location_on),
                label: const Text('Mark Arrived at Customer'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      case DeliveryStatus.arrivedDelivery:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showOTPDialog(delivery),
                icon: const Icon(Icons.verified_user),
                label: const Text('Complete Delivery'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[700],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Future<void> _makeCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
        // Log call in history
        _logCall(phoneNumber);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Could not make call. Please check your device settings.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error making call: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _logCall(String phoneNumber) {
    // In a real app, this would log the call to a database
    print('Call logged: $phoneNumber at ${DateTime.now()}');
  }

  void _acceptOrder(AvailableOrder order) {
    // Use alarm service for urgent order notification
    _alarmService.startAlarm(
      title: 'New Food Order',
      message:
          'Order from ${order.restaurantName}\nDelivery Fee: ₹${order.deliveryFee}\nDistance: ${order.distanceKm} km',
      onAccept: () => _processOrderAcceptance(order),
      onDecline: () => _declineOrder(order),
      context: context,
    );
  }

  void _declineOrder(AvailableOrder order) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Declined order from ${order.restaurantName}'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _processOrderAcceptance(AvailableOrder order) {
    // In a real app, this would send the acceptance to the backend
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Order accepted! Navigate to ${order.restaurantName}'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'Navigate',
          onPressed: () => _startNavigation(
            ActiveDelivery(
              id: 'DEL_${order.id}',
              order: order,
              status: DeliveryStatus.accepted,
              acceptedTime: DateTime.now(),
              photoProofs: [],
              routeHistory: [],
              otpVerified: false,
            ),
            isPickup: true,
          ),
        ),
      ),
    );
  }

  void _startNavigation(ActiveDelivery delivery, {required bool isPickup}) {
    final destination = isPickup
        ? delivery.order.restaurantAddress
        : delivery.order.customerAddress;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting navigation to $destination'),
        backgroundColor: Colors.blue,
      ),
    );

    // In a real app, this would integrate with Google Maps/Apple Maps
    // For now, just update the status
    setState(() {
      // Update delivery status in demo data
    });
  }

  void _markArrived(ActiveDelivery delivery, {required bool isPickup}) {
    final location = isPickup ? 'restaurant' : 'customer location';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Marked as arrived at $location'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _confirmPickup(ActiveDelivery delivery) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Confirm Pickup',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Order ID: ${delivery.order.id}',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Restaurant: ${delivery.order.restaurantName}',
              style: GoogleFonts.poppins(fontSize: 14),
            ),
            const SizedBox(height: 16),

            // Verification options
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _scanOrderQR(delivery);
                    },
                    icon: const Icon(Icons.qr_code_scanner),
                    label: const Text('Scan QR Code'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _manualOrderEntry(delivery);
                    },
                    icon: const Icon(Icons.keyboard),
                    label: const Text('Manual Entry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                  _takePickupPhoto(delivery);
                },
                icon: const Icon(Icons.camera_alt),
                label: const Text('Take Photo & Complete Pickup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  void _scanOrderQR(ActiveDelivery delivery) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QRScannerPage(
          delivery: delivery,
          onQRScanned: (scannedData) {
            Navigator.pop(context);
            if (scannedData == delivery.order.id) {
              _takePickupPhoto(delivery);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Invalid QR code. Please scan the correct order QR.',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
      ),
    );
  }

  void _manualOrderEntry(ActiveDelivery delivery) {
    final TextEditingController orderIdController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Manual Order Verification'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Expected Order ID: ${delivery.order.id}'),
            const SizedBox(height: 16),
            TextField(
              controller: orderIdController,
              decoration: const InputDecoration(
                labelText: 'Enter Order ID',
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.characters,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (orderIdController.text.trim().toUpperCase() ==
                  delivery.order.id.toUpperCase()) {
                Navigator.pop(context);
                _takePickupPhoto(delivery);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Invalid Order ID. Please check and try again.',
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Verify'),
          ),
        ],
      ),
    );
  }

  void _takePickupPhoto(ActiveDelivery delivery) async {
    try {
      final ImagePicker picker = ImagePicker();

      // Show photo options
      final ImageSource? source = await showDialog<ImageSource>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Take Pickup Photo'),
          content: const Text('Choose how to capture the order photo:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, ImageSource.camera),
              child: const Text('Camera'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, ImageSource.gallery),
              child: const Text('Gallery'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );

      if (source == null) return;

      final XFile? image = await picker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        // Show photo preview and confirmation
        if (mounted) {
          final bool? confirmed = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Confirm Pickup Photo'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 200,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.file(File(image.path), fit: BoxFit.cover),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Is this photo clear and shows the complete order?',
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Retake'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('Confirm Pickup'),
                ),
              ],
            ),
          );

          if (confirmed == true) {
            _completePickup(delivery, image.path);
          } else {
            // Retake photo
            _takePickupPhoto(delivery);
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error taking photo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _completePickup(ActiveDelivery delivery, String photoPath) {
    // In a real app, this would upload the photo and update the delivery status
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Order picked up successfully! Photo saved: ${photoPath.split('/').last}',
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );

    // Update delivery status to picked up
    setState(() {
      // In a real app, this would update the backend
    });
  }

  void _showOTPDialog(ActiveDelivery delivery) {
    final TextEditingController otpController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Enter Delivery OTP'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Customer OTP: ${delivery.order.otp}'),
            const SizedBox(height: 16),
            TextField(
              controller: otpController,
              decoration: const InputDecoration(
                labelText: 'Enter OTP',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              maxLength: 4,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (otpController.text == delivery.order.otp) {
                Navigator.pop(context);
                _completeDelivery(delivery);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Invalid OTP. Please try again.'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Verify'),
          ),
        ],
      ),
    );
  }

  void _completeDelivery(ActiveDelivery delivery) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Delivery completed! Earned ₹${delivery.order.deliveryFee}',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  // ========== RIDE-SHARING METHODS ==========

  void _acceptRide(RideRequest ride) {
    // Use alarm service for urgent ride notification
    _alarmService.startAlarm(
      title: 'New Ride Request',
      message:
          'Passenger: ${ride.passengerName}\nRoute: ${ride.routeName}\nFare: ₹${ride.fare}\nDistance: ${ride.distance} km',
      onAccept: () => _processRideAcceptance(ride),
      onDecline: () => _declineRide(ride),
      context: context,
    );
  }

  void _declineRide(RideRequest ride) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Declined ride request from ${ride.passengerName}'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _processRideAcceptance(RideRequest ride) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ride accepted! Navigate to ${ride.pickupLocation}'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'Navigate',
          onPressed: () => _startRideNavigation(ride, isPickup: true),
        ),
      ),
    );
  }

  void _startRideNavigation(RideRequest ride, {required bool isPickup}) {
    final destination = isPickup ? ride.pickupLocation : ride.dropoffLocation;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting navigation to $destination'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Color _getRideStatusColor(RideStatus status) {
    switch (status) {
      case RideStatus.requested:
        return Colors.blue;
      case RideStatus.accepted:
        return Colors.green;
      case RideStatus.enRoutePickup:
        return Colors.orange;
      case RideStatus.arrivedPickup:
        return Colors.amber;
      case RideStatus.passengerPickedUp:
        return Colors.teal;
      case RideStatus.enRouteDropoff:
        return Colors.purple;
      case RideStatus.arrivedDropoff:
        return Colors.indigo;
      case RideStatus.completed:
        return Colors.green[700]!;
      case RideStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getRideStatusText(RideStatus status) {
    switch (status) {
      case RideStatus.requested:
        return 'REQUESTED';
      case RideStatus.accepted:
        return 'ACCEPTED';
      case RideStatus.enRoutePickup:
        return 'EN ROUTE TO PICKUP';
      case RideStatus.arrivedPickup:
        return 'ARRIVED AT PICKUP';
      case RideStatus.passengerPickedUp:
        return 'PASSENGER PICKED UP';
      case RideStatus.enRouteDropoff:
        return 'EN ROUTE TO DROPOFF';
      case RideStatus.arrivedDropoff:
        return 'ARRIVED AT DROPOFF';
      case RideStatus.completed:
        return 'COMPLETED';
      case RideStatus.cancelled:
        return 'CANCELLED';
      default:
        return 'UNKNOWN';
    }
  }

  Widget _buildRideActionButtons(ActiveRide ride) {
    switch (ride.status) {
      case RideStatus.accepted:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () =>
                    _startRideNavigation(ride.request, isPickup: true),
                icon: const Icon(Icons.navigation),
                label: const Text('Navigate to Pickup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      case RideStatus.enRoutePickup:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _markRideArrived(ride, isPickup: true),
                icon: const Icon(Icons.location_on),
                label: const Text('Mark Arrived at Pickup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      case RideStatus.arrivedPickup:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _confirmPassengerPickup(ride),
                icon: const Icon(Icons.person_add),
                label: const Text('Confirm Passenger Pickup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      case RideStatus.passengerPickedUp:
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () =>
                    _startRideNavigation(ride.request, isPickup: false),
                icon: const Icon(Icons.navigation),
                label: const Text('Navigate to Dropoff'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  void _markRideArrived(ActiveRide ride, {required bool isPickup}) {
    final location = isPickup ? 'pickup location' : 'dropoff location';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Marked as arrived at $location'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _confirmPassengerPickup(ActiveRide ride) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Passenger Pickup'),
        content: Text(
          'Confirm that ${ride.request.passengerName} has been picked up?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Passenger picked up! Navigate to dropoff location.',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }
}

// GPS Tracking Service for real-time location updates
class GPSTrackingService {
  static final GPSTrackingService _instance = GPSTrackingService._internal();
  factory GPSTrackingService() => _instance;
  GPSTrackingService._internal();

  StreamSubscription<Position>? _positionStream;
  Position? _currentPosition;
  bool _isTracking = false;
  final List<GPSPoint> _routeHistory = [];

  // Callbacks for location updates
  Function(Position)? onLocationUpdate;
  Function(String)? onError;

  bool get isTracking => _isTracking;
  Position? get currentPosition => _currentPosition;
  List<GPSPoint> get routeHistory => List.unmodifiable(_routeHistory);

  Future<bool> requestLocationPermissions() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        onError?.call(
          'Location services are disabled. Please enable them in settings.',
        );
        return false;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          onError?.call(
            'Location permissions are denied. Please grant location access.',
          );
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        onError?.call(
          'Location permissions are permanently denied. Please enable them in app settings.',
        );
        return false;
      }

      return true;
    } catch (e) {
      onError?.call('Error requesting location permissions: $e');
      return false;
    }
  }

  Future<void> startTracking() async {
    if (_isTracking) return;

    bool hasPermission = await requestLocationPermissions();
    if (!hasPermission) return;

    try {
      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      );

      _positionStream =
          Geolocator.getPositionStream(
            locationSettings: locationSettings,
          ).listen(
            (Position position) {
              _currentPosition = position;
              _addToRouteHistory(position);
              onLocationUpdate?.call(position);
            },
            onError: (error) {
              onError?.call('Location tracking error: $error');
            },
          );

      _isTracking = true;
    } catch (e) {
      onError?.call('Failed to start location tracking: $e');
    }
  }

  void stopTracking() {
    _positionStream?.cancel();
    _positionStream = null;
    _isTracking = false;
  }

  void _addToRouteHistory(Position position) {
    _routeHistory.add(
      GPSPoint(
        latitude: position.latitude,
        longitude: position.longitude,
        timestamp: DateTime.now(),
        accuracy: position.accuracy,
        speed: position.speed,
      ),
    );

    // Keep only last 1000 points to prevent memory issues
    if (_routeHistory.length > 1000) {
      _routeHistory.removeAt(0);
    }
  }

  Future<Position?> getCurrentLocation() async {
    try {
      bool hasPermission = await requestLocationPermissions();
      if (!hasPermission) return null;

      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      _currentPosition = position;
      return position;
    } catch (e) {
      onError?.call('Failed to get current location: $e');
      return null;
    }
  }

  double calculateDistance(
    double startLat,
    double startLng,
    double endLat,
    double endLng,
  ) {
    return Geolocator.distanceBetween(startLat, startLng, endLat, endLng);
  }

  void clearRouteHistory() {
    _routeHistory.clear();
  }

  void dispose() {
    stopTracking();
    _routeHistory.clear();
  }
}

// Alarm Service for order and ride notifications
class AlarmService {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  Timer? _alarmTimer;
  bool _isAlarmActive = false;
  int _alarmCount = 0;
  static const int maxAlarmCount = 10; // Maximum alarm repetitions
  static const Duration alarmInterval = Duration(seconds: 2);

  // Audio player for alarm sounds
  late AudioPlayer _audioPlayer;

  // Alarm settings
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _visualAlertEnabled = true;
  double _alarmVolume = 0.8;
  String _selectedRingtone = 'default_alarm.mp3';

  // Available ringtones
  final List<String> _availableRingtones = [
    'default_alarm.mp3',
    'urgent_beep.mp3',
    'notification_bell.mp3',
    'classic_ring.mp3',
    'modern_chime.mp3',
  ];

  // Initialize audio player
  void _initializeAudioPlayer() {
    _audioPlayer = AudioPlayer();
    _audioPlayer.setVolume(_alarmVolume);
  }

  // Getters
  bool get isAlarmActive => _isAlarmActive;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  bool get visualAlertEnabled => _visualAlertEnabled;
  double get alarmVolume => _alarmVolume;
  String get selectedRingtone => _selectedRingtone;
  List<String> get availableRingtones => List.unmodifiable(_availableRingtones);

  // Settings setters
  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
    if (!enabled) {
      _stopAlarmSound();
    }
  }

  void setVibrationEnabled(bool enabled) => _vibrationEnabled = enabled;
  void setVisualAlertEnabled(bool enabled) => _visualAlertEnabled = enabled;

  void setAlarmVolume(double volume) {
    _alarmVolume = volume.clamp(0.0, 1.0);
    _audioPlayer.setVolume(_alarmVolume);
  }

  void setSelectedRingtone(String ringtone) {
    if (_availableRingtones.contains(ringtone)) {
      _selectedRingtone = ringtone;
    }
  }

  // Start alarm for new order/ride
  void startAlarm({
    required String title,
    required String message,
    required VoidCallback onAccept,
    required VoidCallback onDecline,
    required BuildContext context,
  }) {
    if (_isAlarmActive) {
      stopAlarm(); // Stop any existing alarm
    }

    // Initialize audio player if not already done
    _initializeAudioPlayer();

    _isAlarmActive = true;
    _alarmCount = 0;

    // Show persistent notification overlay
    if (_visualAlertEnabled) {
      _showAlarmOverlay(context, title, message, onAccept, onDecline);
    }

    // Start repeating alarm
    _alarmTimer = Timer.periodic(alarmInterval, (timer) {
      _alarmCount++;

      // Play sound
      if (_soundEnabled) {
        _playAlarmSound();
      }

      // Trigger vibration
      if (_vibrationEnabled) {
        _triggerVibration();
      }

      // Stop after max count
      if (_alarmCount >= maxAlarmCount) {
        stopAlarm();
        _handleAlarmTimeout(context, title);
      }
    });
  }

  // Stop alarm
  void stopAlarm() {
    _alarmTimer?.cancel();
    _alarmTimer = null;
    _isAlarmActive = false;
    _alarmCount = 0;
    _stopAlarmSound();
  }

  // Play alarm sound
  void _playAlarmSound() {
    try {
      // Play the selected ringtone
      _audioPlayer.play(AssetSource('sounds/$_selectedRingtone'));
    } catch (e) {
      // Fallback to haptic feedback if audio fails
      HapticFeedback.mediumImpact();
    }
  }

  // Stop alarm sound
  void _stopAlarmSound() {
    try {
      _audioPlayer.stop();
    } catch (e) {
      // Handle error silently
    }
  }

  // Test specific ringtone
  Future<void> testRingtone(String ringtone) async {
    try {
      await _audioPlayer.stop(); // Stop any current sound
      await _audioPlayer.play(AssetSource('sounds/$ringtone'));
    } catch (e) {
      // Fallback to haptic feedback
      HapticFeedback.lightImpact();
    }
  }

  // Dispose resources
  void dispose() {
    stopAlarm();
    _audioPlayer.dispose();
  }

  // Trigger vibration
  void _triggerVibration() {
    try {
      HapticFeedback.heavyImpact();
    } catch (e) {
      // Handle error silently in production
    }
  }

  // Show alarm overlay
  void _showAlarmOverlay(
    BuildContext context,
    String title,
    String message,
    VoidCallback onAccept,
    VoidCallback onDecline,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlarmDialog(
        title: title,
        message: message,
        onAccept: () {
          stopAlarm();
          Navigator.of(context).pop();
          onAccept();
        },
        onDecline: () {
          stopAlarm();
          Navigator.of(context).pop();
          onDecline();
        },
        alarmService: this,
      ),
    );
  }

  // Handle alarm timeout
  void _handleAlarmTimeout(BuildContext context, String title) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$title - Request timed out'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Test alarm functionality
  void testAlarm(BuildContext context) {
    startAlarm(
      title: 'Test Alarm',
      message: 'This is a test of the alarm system',
      onAccept: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test alarm accepted'),
            backgroundColor: Colors.green,
          ),
        );
      },
      onDecline: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test alarm declined'),
            backgroundColor: Colors.orange,
          ),
        );
      },
      context: context,
    );
  }
}

// Custom alarm dialog widget
class AlarmDialog extends StatefulWidget {
  final String title;
  final String message;
  final VoidCallback onAccept;
  final VoidCallback onDecline;
  final AlarmService alarmService;

  const AlarmDialog({
    super.key,
    required this.title,
    required this.message,
    required this.onAccept,
    required this.onDecline,
    required this.alarmService,
  });

  @override
  State<AlarmDialog> createState() => _AlarmDialogState();
}

class _AlarmDialogState extends State<AlarmDialog>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _shakeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();

    // Pulse animation for visual alert
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Shake animation for urgency
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _shakeAnimation = Tween<double>(begin: -5, end: 5).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticIn),
    );

    // Start animations
    _pulseController.repeat(reverse: true);
    _shakeController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _shakeAnimation]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: Transform.scale(
            scale: _pulseAnimation.value,
            child: AlertDialog(
              backgroundColor: Colors.red[50],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: BorderSide(color: Colors.red, width: 2),
              ),
              title: Row(
                children: [
                  Icon(Icons.alarm, color: Colors.red, size: 28),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.red[800],
                      ),
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.message,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Countdown indicator
                  LinearProgressIndicator(
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Auto-decline in ${AlarmService.maxAlarmCount * 2} seconds',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              actions: [
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: widget.onDecline,
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('Decline'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: widget.onAccept,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('Accept'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Enhanced Rider Home Page with GPS integration
class EnhancedRiderHomePage extends StatefulWidget {
  const EnhancedRiderHomePage({super.key});

  @override
  State<EnhancedRiderHomePage> createState() => _EnhancedRiderHomePageState();
}

class _EnhancedRiderHomePageState extends State<EnhancedRiderHomePage> {
  final GPSTrackingService _gpsService = GPSTrackingService();
  final AlarmService _alarmService = AlarmService(); // Alarm service instance
  bool _isOnline = false;
  Position? _currentLocation;
  String _locationStatus = 'Location not available';
  bool _isTestingGPS = false;
  int _gpsSignalStrength = 0; // 0-4 bars
  String _permissionStatus = 'Unknown';
  bool _troubleshootingVisible = false;
  RiderMode _currentMode = RiderMode.foodDelivery; // Default mode

  @override
  void initState() {
    super.initState();
    _initializeGPS();
  }

  void _initializeGPS() {
    _checkPermissionStatus();

    _gpsService.onLocationUpdate = (position) {
      if (mounted) {
        setState(() {
          _currentLocation = position;
          _locationStatus =
              'Lat: ${position.latitude.toStringAsFixed(6)}, '
              'Lng: ${position.longitude.toStringAsFixed(6)}';
          _gpsSignalStrength = _calculateSignalStrength(position.accuracy);
        });
      }
    };

    _gpsService.onError = (error) {
      if (mounted) {
        setState(() {
          _locationStatus = 'Error: $error';
          _gpsSignalStrength = 0;
        });

        // Show enhanced error handling with troubleshooting
        _showLocationErrorDialog(error);
      }
    };
  }

  Future<void> _checkPermissionStatus() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();

      if (mounted) {
        setState(() {
          if (!serviceEnabled) {
            _permissionStatus = 'Location services disabled';
          } else if (permission == LocationPermission.denied) {
            _permissionStatus = 'Permission denied';
          } else if (permission == LocationPermission.deniedForever) {
            _permissionStatus = 'Permission permanently denied';
          } else if (permission == LocationPermission.whileInUse ||
              permission == LocationPermission.always) {
            _permissionStatus = 'Permission granted';
          } else {
            _permissionStatus = 'Permission unknown';
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _permissionStatus = 'Error checking permissions';
        });
      }
    }
  }

  int _calculateSignalStrength(double accuracy) {
    // Convert GPS accuracy to signal strength bars (0-4)
    if (accuracy <= 5) return 4; // Excellent (≤5m)
    if (accuracy <= 10) return 3; // Good (≤10m)
    if (accuracy <= 20) return 2; // Fair (≤20m)
    if (accuracy <= 50) return 1; // Poor (≤50m)
    return 0; // Very poor (>50m)
  }

  void _showLocationErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.location_off, color: Colors.red),
            const SizedBox(width: 8),
            const Text('Location Issue'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Error: $error'),
            const SizedBox(height: 16),
            const Text(
              'Quick fixes:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('• Enable Location Services in device settings'),
            const Text('• Grant location permission to this app'),
            const Text('• Restart the app after enabling permissions'),
            const Text('• Check if GPS is working in other apps'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showLocationTroubleshooting();
            },
            child: const Text('Troubleshoot'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _gpsService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rider Dashboard'),
        actions: [
          // Alarm settings button
          IconButton(
            icon: Icon(
              _alarmService.isAlarmActive ? Icons.alarm : Icons.alarm_off,
              color: _alarmService.isAlarmActive ? Colors.red : null,
            ),
            onPressed: _showAlarmSettings,
            tooltip: 'Alarm Settings',
          ),
          // GPS Signal Strength Indicator
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildSignalStrengthBars(),
                const SizedBox(width: 4),
                IconButton(
                  icon: Icon(
                    _gpsService.isTracking ? Icons.gps_fixed : Icons.gps_off,
                  ),
                  onPressed: _toggleGPSTracking,
                  tooltip: 'Toggle GPS Tracking',
                ),
              ],
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Online/Offline Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: _isOnline ? Colors.green : Colors.grey,
                      child: Icon(
                        _isOnline ? Icons.motorcycle : Icons.pause,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _isOnline ? 'You are ONLINE' : 'You are OFFLINE',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: _isOnline ? Colors.green : Colors.grey,
                            ),
                          ),
                          Text(
                            _isOnline
                                ? 'Ready to receive orders'
                                : 'Tap to go online',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: _isOnline,
                      onChanged: _toggleOnlineStatus,
                      activeColor: Colors.green,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Mode Selection Card
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Service Mode',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildModeButton(
                            RiderMode.foodDelivery,
                            'Food Delivery',
                            Icons.restaurant,
                            'Deliver food orders',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildModeButton(
                            RiderMode.rideSharing,
                            'Ride Sharing',
                            Icons.directions_car,
                            'Transport passengers',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _currentMode == RiderMode.foodDelivery
                          ? 'Currently accepting food delivery orders'
                          : 'Currently accepting ride requests',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Enhanced GPS Status Card
            Card(
              color: _gpsService.isTracking ? Colors.green[50] : Colors.red[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _gpsService.isTracking
                              ? Icons.gps_fixed
                              : Icons.gps_off,
                          color: _gpsService.isTracking
                              ? Colors.green
                              : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'GPS Status',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        // Signal strength bars
                        _buildSignalStrengthBars(),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _locationStatus,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Permission: $_permissionStatus',
                      style: GoogleFonts.poppins(
                        fontSize: 11,
                        color: _permissionStatus == 'Permission granted'
                            ? Colors.green[700]
                            : Colors.red[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (_currentLocation != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'Accuracy: ${_currentLocation!.accuracy.toStringAsFixed(1)}m',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            'Speed: ${(_currentLocation!.speed * 3.6).toStringAsFixed(1)} km/h',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                    const SizedBox(height: 12),
                    // Action buttons
                    Row(
                      children: [
                        if (!_gpsService.isTracking) ...[
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _requestLocationPermissions,
                              icon: const Icon(Icons.location_on, size: 18),
                              label: const Text('Enable Location'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _testGPS,
                            icon: Icon(
                              _isTestingGPS ? Icons.stop : Icons.speed,
                              size: 18,
                            ),
                            label: Text(
                              _isTestingGPS ? 'Stop Test' : 'Test GPS',
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: _showLocationTroubleshooting,
                          icon: const Icon(Icons.help_outline),
                          tooltip: 'GPS Troubleshooting',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Today's Performance
            Text(
              'Today\'s Performance',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildPerformanceCard(
                    _currentMode == RiderMode.foodDelivery
                        ? 'Deliveries'
                        : 'Rides',
                    _currentMode == RiderMode.foodDelivery
                        ? '${RiderDemoData.performanceAnalytics['today']['tripsCompleted']}'
                        : '${RiderDemoData.rideAnalytics['today']['ridesCompleted']}',
                    _currentMode == RiderMode.foodDelivery
                        ? Icons.delivery_dining
                        : Icons.directions_car,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceCard(
                    'Earnings',
                    _currentMode == RiderMode.foodDelivery
                        ? '₹${RiderDemoData.performanceAnalytics['today']['totalEarnings']}'
                        : '₹${RiderDemoData.rideAnalytics['today']['totalEarnings']}',
                    Icons.currency_rupee,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildPerformanceCard(
                    'Distance',
                    _currentMode == RiderMode.foodDelivery
                        ? '${RiderDemoData.performanceAnalytics['today']['totalDistance']} km'
                        : '${RiderDemoData.rideAnalytics['today']['totalDistance']} km',
                    Icons.route,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceCard(
                    'Rating',
                    _currentMode == RiderMode.foodDelivery
                        ? '${RiderDemoData.performanceAnalytics['today']['averageRating']}⭐'
                        : '${RiderDemoData.rideAnalytics['today']['averageRating']}⭐',
                    Icons.star,
                    Colors.amber,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Quick Actions
            Text(
              'Quick Actions',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    'View Orders',
                    Icons.list_alt,
                    Colors.blue,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EnhancedRiderOrdersPage(),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionCard(
                    'Analytics',
                    Icons.analytics,
                    Colors.purple,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            PerformanceAnalyticsPage(currentMode: _currentMode),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    'Earnings',
                    Icons.currency_rupee,
                    Colors.green,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            EarningsPage(currentMode: _currentMode),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionCard(
                    'History',
                    Icons.history,
                    Colors.orange,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            TripHistoryPage(currentMode: _currentMode),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleOnlineStatus(bool value) async {
    if (value && !_gpsService.isTracking) {
      // Need GPS to go online
      bool started = await _startGPSTracking();
      if (!started && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'GPS is required to go online. Please enable location services.',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    if (mounted) {
      setState(() {
        _isOnline = value;
      });

      if (!value) {
        _gpsService.stopTracking();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isOnline
                ? 'You are now ONLINE and ready for orders!'
                : 'You are now OFFLINE',
          ),
          backgroundColor: _isOnline ? Colors.green : Colors.orange,
        ),
      );
    }
  }

  void _toggleGPSTracking() async {
    if (_gpsService.isTracking) {
      _gpsService.stopTracking();
      setState(() {
        _isOnline = false;
        _locationStatus = 'GPS tracking stopped';
      });
    } else {
      await _startGPSTracking();
    }
  }

  Future<bool> _startGPSTracking() async {
    await _gpsService.startTracking();
    return _gpsService.isTracking;
  }

  // Build mode selection button
  Widget _buildModeButton(
    RiderMode mode,
    String title,
    IconData icon,
    String description,
  ) {
    final bool isSelected = _currentMode == mode;
    return GestureDetector(
      onTap: () => _switchMode(mode),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[100] : Colors.grey[100],
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.blue : Colors.grey[600],
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? Colors.blue : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 2),
            Text(
              description,
              style: GoogleFonts.poppins(fontSize: 10, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Switch between food delivery and ride-sharing modes
  void _switchMode(RiderMode newMode) {
    if (_currentMode == newMode) return;

    setState(() {
      _currentMode = newMode;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          newMode == RiderMode.foodDelivery
              ? 'Switched to Food Delivery mode'
              : 'Switched to Ride Sharing mode',
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build GPS signal strength bars (0-4 bars)
  Widget _buildSignalStrengthBars() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(4, (index) {
        return Container(
          width: 3,
          height: 8 + (index * 2),
          margin: const EdgeInsets.only(right: 1),
          decoration: BoxDecoration(
            color: index < _gpsSignalStrength
                ? (_gpsSignalStrength >= 3
                      ? Colors.green
                      : _gpsSignalStrength >= 2
                      ? Colors.orange
                      : Colors.red)
                : Colors.grey[300],
            borderRadius: BorderRadius.circular(1),
          ),
        );
      }),
    );
  }

  // Request location permissions with enhanced flow
  Future<void> _requestLocationPermissions() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showLocationServiceDialog();
        return;
      }

      // Request permissions
      LocationPermission permission = await Geolocator.requestPermission();

      if (mounted) {
        await _checkPermissionStatus();

        if (permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always) {
          // Permission granted, start GPS tracking
          await _startGPSTracking();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Location permissions granted! GPS tracking started.',
                ),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else if (permission == LocationPermission.deniedForever) {
          _showPermanentDenialDialog();
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Location permission denied. Please enable it to go online.',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show location service disabled dialog
  void _showLocationServiceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Services Disabled'),
        content: const Text(
          'Location services are disabled on your device. Please enable them in your device settings to use GPS tracking.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Geolocator.openLocationSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  // Show permanent denial dialog
  void _showPermanentDenialDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Permission Required'),
        content: const Text(
          'Location permission has been permanently denied. Please enable it manually in app settings to use GPS tracking.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('App Settings'),
          ),
        ],
      ),
    );
  }

  // Test GPS functionality
  Future<void> _testGPS() async {
    if (_isTestingGPS) {
      // Stop GPS test
      setState(() {
        _isTestingGPS = false;
      });
      _gpsService.stopTracking();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('GPS test stopped.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Start GPS test
    setState(() {
      _isTestingGPS = true;
    });

    try {
      // Get current location for immediate feedback
      Position? position = await _gpsService.getCurrentLocation();

      if (position != null && mounted) {
        // Start continuous tracking for testing
        await _gpsService.startTracking();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'GPS Test Started!\nLat: ${position.latitude.toStringAsFixed(6)}\n'
                'Lng: ${position.longitude.toStringAsFixed(6)}\n'
                'Accuracy: ${position.accuracy.toStringAsFixed(1)}m',
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } else {
        setState(() {
          _isTestingGPS = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'GPS test failed. Please check location permissions.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isTestingGPS = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('GPS test error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show location troubleshooting guide
  void _showLocationTroubleshooting() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'GPS Troubleshooting Guide',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            _buildTroubleshootingItem(
              Icons.location_on,
              'Enable Location Services',
              'Go to Settings > Location and turn on location services.',
            ),

            _buildTroubleshootingItem(
              Icons.apps,
              'Grant App Permissions',
              'Go to Settings > Apps > Projek Rider > Permissions > Location and select "Allow all the time" or "Allow only while using the app".',
            ),

            _buildTroubleshootingItem(
              Icons.gps_fixed,
              'Improve GPS Accuracy',
              'Go to Settings > Location > Location Services > Google Location Accuracy and turn on "Improve Location Accuracy".',
            ),

            _buildTroubleshootingItem(
              Icons.refresh,
              'Restart the App',
              'Close and reopen the app after changing location settings.',
            ),

            _buildTroubleshootingItem(
              Icons.wifi,
              'Check Network Connection',
              'Ensure you have a stable internet connection for assisted GPS.',
            ),

            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      openAppSettings();
                    },
                    child: const Text('Open Settings'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTroubleshootingItem(
    IconData icon,
    String title,
    String description,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: Colors.green, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Show alarm settings dialog
  void _showAlarmSettings() {
    showDialog(
      context: context,
      builder: (context) => AlarmSettingsDialog(alarmService: _alarmService),
    );
  }
}

// QR Scanner Page for order verification
class QRScannerPage extends StatefulWidget {
  final ActiveDelivery delivery;
  final Function(String) onQRScanned;

  const QRScannerPage({
    super.key,
    required this.delivery,
    required this.onQRScanned,
  });

  @override
  State<QRScannerPage> createState() => _QRScannerPageState();
}

class _QRScannerPageState extends State<QRScannerPage> {
  MobileScannerController cameraController = MobileScannerController();
  bool _isScanning = true;

  @override
  void dispose() {
    cameraController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Order QR Code'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(
              cameraController.torchEnabled ? Icons.flash_on : Icons.flash_off,
            ),
            onPressed: () => cameraController.toggleTorch(),
          ),
        ],
      ),
      body: Stack(
        children: [
          // QR Scanner
          MobileScanner(
            controller: cameraController,
            onDetect: (capture) {
              if (_isScanning) {
                final List<Barcode> barcodes = capture.barcodes;
                for (final barcode in barcodes) {
                  if (barcode.rawValue != null) {
                    _isScanning = false;
                    widget.onQRScanned(barcode.rawValue!);
                    break;
                  }
                }
              }
            },
          ),

          // Scanning area overlay
          Center(
            child: Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green, width: 3),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),

          // Instructions
          Positioned(
            bottom: 100,
            left: 0,
            right: 0,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Scan Order QR Code',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Expected Order: ${widget.delivery.order.id}',
                    style: GoogleFonts.poppins(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Position the QR code within the frame',
                    style: GoogleFonts.poppins(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Alarm Settings Dialog
class AlarmSettingsDialog extends StatefulWidget {
  final AlarmService alarmService;

  const AlarmSettingsDialog({super.key, required this.alarmService});

  @override
  State<AlarmSettingsDialog> createState() => _AlarmSettingsDialogState();
}

class _AlarmSettingsDialogState extends State<AlarmSettingsDialog> {
  late bool _soundEnabled;
  late bool _vibrationEnabled;
  late bool _visualAlertEnabled;
  late double _alarmVolume;

  @override
  void initState() {
    super.initState();
    _soundEnabled = widget.alarmService.soundEnabled;
    _vibrationEnabled = widget.alarmService.vibrationEnabled;
    _visualAlertEnabled = widget.alarmService.visualAlertEnabled;
    _alarmVolume = widget.alarmService.alarmVolume;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.alarm, color: Colors.blue),
          const SizedBox(width: 8),
          const Text('Alarm Settings'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sound toggle
          SwitchListTile(
            title: const Text('Sound Alerts'),
            subtitle: const Text('Play sound for new orders/rides'),
            value: _soundEnabled,
            onChanged: (value) {
              setState(() {
                _soundEnabled = value;
              });
              widget.alarmService.setSoundEnabled(value);
            },
          ),

          // Vibration toggle
          SwitchListTile(
            title: const Text('Vibration'),
            subtitle: const Text('Vibrate for new orders/rides'),
            value: _vibrationEnabled,
            onChanged: (value) {
              setState(() {
                _vibrationEnabled = value;
              });
              widget.alarmService.setVibrationEnabled(value);
            },
          ),

          // Visual alert toggle
          SwitchListTile(
            title: const Text('Visual Alerts'),
            subtitle: const Text('Show popup dialogs'),
            value: _visualAlertEnabled,
            onChanged: (value) {
              setState(() {
                _visualAlertEnabled = value;
              });
              widget.alarmService.setVisualAlertEnabled(value);
            },
          ),

          const SizedBox(height: 16),

          // Volume slider
          Text(
            'Alarm Volume: ${(_alarmVolume * 100).round()}%',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Slider(
            value: _alarmVolume,
            onChanged: (value) {
              setState(() {
                _alarmVolume = value;
              });
              widget.alarmService.setAlarmVolume(value);
            },
            divisions: 10,
            label: '${(_alarmVolume * 100).round()}%',
          ),

          const SizedBox(height: 16),

          // Ringtone selection
          Text(
            'Ringtone',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: DropdownButton<String>(
                  value: widget.alarmService.selectedRingtone,
                  isExpanded: true,
                  items: widget.alarmService.availableRingtones.map((ringtone) {
                    return DropdownMenuItem<String>(
                      value: ringtone,
                      child: Text(_getRingtoneName(ringtone)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      widget.alarmService.setSelectedRingtone(value);
                      setState(() {});
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: () {
                  widget.alarmService.testRingtone(
                    widget.alarmService.selectedRingtone,
                  );
                },
                tooltip: 'Test Ringtone',
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.alarmService.testAlarm(context);
            Navigator.pop(context);
          },
          child: const Text('Test Alarm'),
        ),
      ],
    );
  }

  // Helper method to get user-friendly ringtone names
  String _getRingtoneName(String filename) {
    switch (filename) {
      case 'default_alarm.mp3':
        return 'Default Alarm';
      case 'urgent_beep.mp3':
        return 'Urgent Beep';
      case 'notification_bell.mp3':
        return 'Notification Bell';
      case 'classic_ring.mp3':
        return 'Classic Ring';
      case 'modern_chime.mp3':
        return 'Modern Chime';
      default:
        return filename.replaceAll('.mp3', '').replaceAll('_', ' ');
    }
  }
}

// Performance Analytics Page
class PerformanceAnalyticsPage extends StatefulWidget {
  final RiderMode currentMode;

  const PerformanceAnalyticsPage({super.key, required this.currentMode});

  @override
  State<PerformanceAnalyticsPage> createState() =>
      _PerformanceAnalyticsPageState();
}

class _PerformanceAnalyticsPageState extends State<PerformanceAnalyticsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'Today';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.currentMode == RiderMode.foodDelivery
              ? 'Delivery Analytics'
              : 'Ride Analytics',
        ),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Earnings'),
            Tab(text: 'Performance'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildEarningsTab(),
          _buildPerformanceTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    final analytics = widget.currentMode == RiderMode.foodDelivery
        ? RiderDemoData.performanceAnalytics
        : RiderDemoData.rideAnalytics;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Period selector
          Row(
            children: [
              Text(
                'Period: ',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              DropdownButton<String>(
                value: _selectedPeriod,
                items: ['Today', 'Week', 'Month'].map((period) {
                  return DropdownMenuItem(value: period, child: Text(period));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedPeriod = value!;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Key metrics cards
          _buildMetricsGrid(analytics),
          const SizedBox(height: 24),

          // Charts section
          Text(
            'Performance Trends',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildPerformanceChart(),
          const SizedBox(height: 24),

          // Top routes/restaurants
          Text(
            widget.currentMode == RiderMode.foodDelivery
                ? 'Top Restaurants'
                : 'Popular Routes',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildTopItemsList(),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid(Map<String, dynamic> analytics) {
    final periodKey = _selectedPeriod.toLowerCase();
    final data = analytics[periodKey];

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildMetricCard(
          widget.currentMode == RiderMode.foodDelivery ? 'Deliveries' : 'Rides',
          widget.currentMode == RiderMode.foodDelivery
              ? '${data['tripsCompleted']}'
              : '${data['ridesCompleted']}',
          Icons.local_shipping,
          Colors.blue,
        ),
        _buildMetricCard(
          'Total Earnings',
          '₹${data['totalEarnings']}',
          Icons.currency_rupee,
          Colors.green,
        ),
        _buildMetricCard(
          'Distance',
          '${data['totalDistance']} km',
          Icons.route,
          Colors.orange,
        ),
        _buildMetricCard(
          'Avg Rating',
          '${data['averageRating']}⭐',
          Icons.star,
          Colors.amber,
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Weekly Performance',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.bar_chart, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 8),
                    Text(
                      'Performance Chart',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      'Chart visualization coming soon',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopItemsList() {
    final items = widget.currentMode == RiderMode.foodDelivery
        ? RiderDemoData.availableOrders.take(5).toList()
        : RiderDemoData.popularRoutes.take(5).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            if (widget.currentMode == RiderMode.foodDelivery)
              ...items.map(
                (order) => _buildRestaurantItem(order as AvailableOrder),
              )
            else
              ...items.map(
                (route) => _buildRouteItem(route as Map<String, dynamic>),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRestaurantItem(AvailableOrder order) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.green[100],
        child: Icon(Icons.restaurant, color: Colors.green),
      ),
      title: Text(
        order.restaurantName,
        style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
      ),
      subtitle: Text('${order.distanceKm} km away'),
      trailing: Text(
        '₹${order.deliveryFee}',
        style: GoogleFonts.poppins(
          fontWeight: FontWeight.w600,
          color: Colors.green,
        ),
      ),
    );
  }

  Widget _buildRouteItem(Map<String, dynamic> route) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.blue[100],
        child: Icon(Icons.directions_car, color: Colors.blue),
      ),
      title: Text(
        route['routeName'],
        style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
      ),
      subtitle: Text('${route['distance']} km • ${route['estimatedTime']} min'),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '₹${route['baseFare']}',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w600,
              color: Colors.green,
            ),
          ),
          Text(
            '${route['popularity']}%',
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.currency_rupee, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Detailed Earnings',
            style: GoogleFonts.poppins(fontSize: 18, color: Colors.grey[600]),
          ),
          Text(
            'Coming soon',
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Performance Metrics',
            style: GoogleFonts.poppins(fontSize: 18, color: Colors.grey[600]),
          ),
          Text(
            'Coming soon',
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }
}

// Earnings Page
class EarningsPage extends StatelessWidget {
  final RiderMode currentMode;

  const EarningsPage({super.key, required this.currentMode});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Earnings'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.currency_rupee, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Earnings Page',
              style: GoogleFonts.poppins(fontSize: 18, color: Colors.grey[600]),
            ),
            Text(
              'Coming soon',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }
}

// Trip History Page
class TripHistoryPage extends StatelessWidget {
  final RiderMode currentMode;

  const TripHistoryPage({super.key, required this.currentMode});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trip History'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Trip History',
              style: GoogleFonts.poppins(fontSize: 18, color: Colors.grey[600]),
            ),
            Text(
              'Coming soon',
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }
}
