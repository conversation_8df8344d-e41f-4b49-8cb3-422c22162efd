{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css|woff2|woff|ttf|eot|svg|png|jpg|jpeg|gif|ico|webp)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "**/*.@(html|json)", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}}