import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../rideshare/services/otp_service.dart';
import '../../../rideshare/services/commission_service.dart';
import '../../../rideshare/services/ride_completion_service.dart';

class OTPInputWidget extends StatefulWidget {
  final String rideId;
  final Function(bool isValid, CommissionBreakdown? commission) onOTPVerified;
  final VoidCallback? onCancel;

  const OTPInputWidget({
    super.key,
    required this.rideId,
    required this.onOTPVerified,
    this.onCancel,
  });

  @override
  State<OTPInputWidget> createState() => _OTPInputWidgetState();
}

class _OTPInputWidgetState extends State<OTPInputWidget> {
  final List<TextEditingController> _controllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());

  bool _isLoading = false;
  String? _errorMessage;
  int _remainingAttempts = 3;

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  String get _otpValue => _controllers.map((c) => c.text).join();

  void _onOTPChanged(int index, String value) {
    if (value.isNotEmpty && index < 5) {
      _focusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _focusNodes[index - 1].requestFocus();
    }

    // Clear error when user starts typing
    if (_errorMessage != null) {
      setState(() {
        _errorMessage = null;
      });
    }

    // Auto-verify when all digits are entered
    if (_otpValue.length == 6) {
      _verifyOTP();
    }
  }

  void _verifyOTP() async {
    if (_otpValue.length != 6) {
      setState(() {
        _errorMessage = 'Please enter complete 6-digit OTP';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Use the ride completion service for better error handling
      final rideData = {
        'rideId': widget.rideId,
        'rideType': 'economy', // This should come from actual ride data
        'totalFare': 180.0, // This should come from actual ride data
        'userRating': 5.0, // This should come from user rating
      };

      final result = await RideCompletionService.completeRideWithOTP(
        rideId: widget.rideId,
        otp: _otpValue,
        rideData: rideData,
      );

      if (result.success) {
        widget.onOTPVerified(true, result.commission);

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    result.message ?? 'Ride completed successfully!',
                    style: GoogleFonts.poppins(fontSize: 14),
                  ),
                ],
              ),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = result.error;
          _remainingAttempts = result.remainingAttempts ?? 0;
        });

        // Handle specific error types
        if (result.errorType == RideCompletionErrorType.networkError) {
          _showNetworkErrorDialog(result);
        } else if (result.errorType == RideCompletionErrorType.otpExpired) {
          _showOTPExpiredDialog(result);
        } else {
          // Clear OTP fields on error
          for (var controller in _controllers) {
            controller.clear();
          }
          _focusNodes[0].requestFocus();
        }

        widget.onOTPVerified(false, null);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to verify OTP. Please try again.';
      });

      // Show error dialog with retry options
      _showGenericErrorDialog();

      widget.onOTPVerified(false, null);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearOTP() {
    for (var controller in _controllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
    setState(() {
      _errorMessage = null;
    });
  }

  void _showNetworkErrorDialog(RideCompletionResult result) {
    RideCompletionService.showErrorDialog(
      context: context,
      result: result,
      onRetry: () {
        _verifyOTP();
      },
      onManualComplete: () {
        _handleManualCompletion();
      },
    );
  }

  void _showOTPExpiredDialog(RideCompletionResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.timer_off, color: Colors.blue, size: 24),
            const SizedBox(width: 8),
            Text(
              'OTP Expired',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'The OTP has expired. A new OTP has been generated.',
              style: GoogleFonts.poppins(fontSize: 14),
            ),
            if (result.newOTPData != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.userPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'New OTP: ${result.newOTPData!.otp}',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.userPrimary,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearOTP();
            },
            child: Text(
              'OK',
              style: GoogleFonts.poppins(color: AppColors.userPrimary),
            ),
          ),
        ],
      ),
    );
  }

  void _showGenericErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error_outline, color: AppColors.error, size: 24),
            const SizedBox(width: 8),
            Text(
              'Error',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Text(
          'An unexpected error occurred. Please try again or complete the ride manually.',
          style: GoogleFonts.poppins(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(color: Colors.grey[600]),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _verifyOTP();
            },
            child: Text(
              'Retry',
              style: GoogleFonts.poppins(color: AppColors.userPrimary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _handleManualCompletion();
            },
            child: Text(
              'Manual Complete',
              style: GoogleFonts.poppins(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _handleManualCompletion() async {
    // Show manual completion dialog
    final reason = await _showManualCompletionDialog();
    if (reason != null) {
      final rideData = {
        'rideId': widget.rideId,
        'rideType': 'economy',
        'totalFare': 180.0,
        'userRating': 4.0, // Lower rating for manual completion
      };

      final result = await RideCompletionService.completeRideManually(
        rideId: widget.rideId,
        rideData: rideData,
        reason: reason,
      );

      if (result.success) {
        widget.onOTPVerified(true, result.commission);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    result.message ?? 'Ride completed manually',
                    style: GoogleFonts.poppins(fontSize: 14),
                  ),
                ],
              ),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    }
  }

  Future<String?> _showManualCompletionDialog() async {
    String selectedReason = 'Customer unavailable';
    final reasons = [
      'Customer unavailable',
      'OTP system failure',
      'Network connectivity issues',
      'Emergency situation',
      'Other technical issues',
    ];

    return showDialog<String>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'Manual Completion',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Please select a reason for manual completion:',
                style: GoogleFonts.poppins(fontSize: 14),
              ),
              const SizedBox(height: 16),
              ...reasons.map((reason) {
                return RadioListTile<String>(
                  title: Text(reason, style: GoogleFonts.poppins(fontSize: 13)),
                  value: reason,
                  groupValue: selectedReason,
                  onChanged: (value) {
                    setState(() {
                      selectedReason = value!;
                    });
                  },
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                );
              }),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.grey[600]),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, selectedReason),
              child: Text(
                'Complete',
                style: GoogleFonts.poppins(color: AppColors.error),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.userPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.security,
                  color: AppColors.userPrimary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Enter Trip Completion OTP',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      'Ask the customer for their 6-digit OTP',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // OTP Input Fields
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(6, (index) {
              return SizedBox(
                width: 45,
                height: 55,
                child: TextField(
                  controller: _controllers[index],
                  focusNode: _focusNodes[index],
                  textAlign: TextAlign.center,
                  keyboardType: TextInputType.number,
                  maxLength: 1,
                  enabled: !_isLoading,
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.userPrimary,
                  ),
                  decoration: InputDecoration(
                    counterText: '',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: _errorMessage != null
                            ? AppColors.error
                            : Colors.grey[300]!,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: _errorMessage != null
                            ? AppColors.error
                            : AppColors.userPrimary,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: _errorMessage != null
                        ? AppColors.error.withValues(alpha: 0.05)
                        : Colors.grey[50],
                  ),
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (value) => _onOTPChanged(index, value),
                ),
              );
            }),
          ),

          const SizedBox(height: 16),

          // Error Message
          if (_errorMessage != null)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, size: 16, color: AppColors.error),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 20),

          // Action Buttons
          Row(
            children: [
              // Clear button
              Expanded(
                child: OutlinedButton(
                  onPressed: _isLoading ? null : _clearOTP,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.grey[600],
                    side: BorderSide(color: Colors.grey[300]!),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Clear',
                    style: GoogleFonts.poppins(fontSize: 14),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Verify button
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _isLoading || _otpValue.length != 6
                      ? null
                      : _verifyOTP,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.userPrimary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : Text(
                          'Verify OTP',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),

          // Remaining attempts info
          if (_remainingAttempts < 3)
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Text(
                'Remaining attempts: $_remainingAttempts',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: _remainingAttempts <= 1
                      ? AppColors.error
                      : Colors.orange,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
