import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../domain/models/loyalty_models.dart';

class LoyaltyService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const Uuid _uuid = Uuid();

  static const String _loyaltyAccountsCollection = 'loyalty_accounts';
  static const String _rewardTransactionsCollection = 'reward_transactions';
  static const String _referralProgramsCollection = 'referral_programs';
  static const String _rewardOffersCollection = 'reward_offers';
  static const String _rewardRedemptionsCollection = 'reward_redemptions';

  // Initialize loyalty account for new user
  static Future<LoyaltyAccount> initializeLoyaltyAccount(String userId) async {
    try {
      final accountId = _uuid.v4();
      final now = DateTime.now();

      final account = LoyaltyAccount(
        id: accountId,
        userId: userId,
        createdAt: now,
        updatedAt: now,
        tierBenefits: TierConfig.getTierConfig(UserTier.bronze) ?? {},
      );

      await _firestore.collection(_loyaltyAccountsCollection).doc(accountId).set(account.toJson());

      // Give welcome bonus
      await _addWelcomeBonus(userId);

      return account;
    } catch (e) {
      debugPrint('❌ Error initializing loyalty account: $e');
      throw Exception('Failed to initialize loyalty account: ${e.toString()}');
    }
  }

  // Get user's loyalty account
  static Future<LoyaltyAccount?> getLoyaltyAccount(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(_loyaltyAccountsCollection)
          .where('userId', isEqualTo: userId)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return LoyaltyAccount.fromJson(snapshot.docs.first.data());
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting loyalty account: $e');
      return null;
    }
  }

  // Add points for order completion
  static Future<void> addPointsForOrder({
    required String userId,
    required String orderId,
    required double orderAmount,
  }) async {
    try {
      final account = await getLoyaltyAccount(userId);
      if (account == null) return;

      // Calculate points based on tier multiplier
      final basePoints = (orderAmount * 0.1).round(); // 1 point per ₹10
      final multiplier = TierConfig.getPointsMultiplier(account.currentTier);
      final earnedPoints = (basePoints * multiplier).round();

      // Calculate cashback
      final cashbackRate = TierConfig.getCashbackRate(account.currentTier);
      final earnedCashback = (orderAmount * cashbackRate) / 100;

      // Add points transaction
      await _addRewardTransaction(
        userId: userId,
        type: TransactionType.earned,
        rewardType: RewardType.points,
        points: earnedPoints,
        description: 'Points earned from order #${orderId.substring(0, 8)}',
        orderId: orderId,
      );

      // Add cashback transaction
      await _addRewardTransaction(
        userId: userId,
        type: TransactionType.earned,
        rewardType: RewardType.cashback,
        cashback: earnedCashback,
        description: 'Cashback earned from order #${orderId.substring(0, 8)}',
        orderId: orderId,
      );

      // Update loyalty account
      await _updateLoyaltyAccount(userId, orderAmount);
    } catch (e) {
      debugPrint('❌ Error adding points for order: $e');
    }
  }

  // Redeem points for reward
  static Future<RewardRedemption?> redeemReward({
    required String userId,
    required String offerId,
  }) async {
    try {
      final account = await getLoyaltyAccount(userId);
      final offer = await getRewardOffer(offerId);
      
      if (account == null || offer == null) return null;

      // Check if user has enough points
      if (account.availablePoints < offer.pointsCost) {
        throw Exception('Insufficient points for redemption');
      }

      // Check if offer is available
      if (!offer.isAvailable) {
        throw Exception('Offer is no longer available');
      }

      // Check tier eligibility
      if (offer.eligibleTiers.isNotEmpty && !offer.eligibleTiers.contains(account.currentTier)) {
        throw Exception('You are not eligible for this offer');
      }

      final redemptionId = _uuid.v4();
      final redemptionCode = _generateRedemptionCode();
      final now = DateTime.now();

      final redemption = RewardRedemption(
        id: redemptionId,
        userId: userId,
        offerId: offerId,
        offerTitle: offer.title,
        pointsUsed: offer.pointsCost,
        cashbackUsed: offer.cashbackValue,
        redeemedAt: now,
        expiresAt: now.add(const Duration(days: 30)), // 30 days validity
        redemptionCode: redemptionCode,
      );

      // Save redemption
      await _firestore.collection(_rewardRedemptionsCollection).doc(redemptionId).set(redemption.toJson());

      // Deduct points
      await _addRewardTransaction(
        userId: userId,
        type: TransactionType.redeemed,
        rewardType: RewardType.points,
        points: -offer.pointsCost,
        description: 'Points redeemed for ${offer.title}',
        referenceId: redemptionId,
      );

      // Update offer redemption count
      await _firestore.collection(_rewardOffersCollection).doc(offerId).update({
        'currentRedemptions': FieldValue.increment(1),
      });

      // Update loyalty account
      await _updateLoyaltyAccount(userId, 0);

      return redemption;
    } catch (e) {
      debugPrint('❌ Error redeeming reward: $e');
      throw Exception('Failed to redeem reward: ${e.toString()}');
    }
  }

  // Create referral
  static Future<ReferralProgram> createReferral({
    required String referrerId,
    required String refereeId,
  }) async {
    try {
      final referralId = _uuid.v4();
      final referralCode = _generateReferralCode();
      final now = DateTime.now();

      final referral = ReferralProgram(
        id: referralId,
        referrerId: referrerId,
        refereeId: refereeId,
        referralCode: referralCode,
        referredAt: now,
      );

      await _firestore.collection(_referralProgramsCollection).doc(referralId).set(referral.toJson());

      return referral;
    } catch (e) {
      debugPrint('❌ Error creating referral: $e');
      throw Exception('Failed to create referral: ${e.toString()}');
    }
  }

  // Complete referral (when referee makes first order)
  static Future<void> completeReferral({
    required String referralId,
    required String orderId,
  }) async {
    try {
      final referralDoc = await _firestore.collection(_referralProgramsCollection).doc(referralId).get();
      if (!referralDoc.exists) return;

      final referral = ReferralProgram.fromJson(referralDoc.data()!);
      if (referral.isCompleted) return;

      final now = DateTime.now();

      // Update referral as completed
      await _firestore.collection(_referralProgramsCollection).doc(referralId).update({
        'isCompleted': true,
        'rewardedAt': now,
        'completionOrderId': orderId,
      });

      // Add rewards for referrer
      await _addRewardTransaction(
        userId: referral.referrerId,
        type: TransactionType.bonus,
        rewardType: RewardType.referralBonus,
        points: referral.referrerRewardPoints,
        cashback: referral.referrerRewardCashback,
        description: 'Referral bonus for inviting a friend',
        referenceId: referralId,
      );

      // Add rewards for referee
      await _addRewardTransaction(
        userId: referral.refereeId,
        type: TransactionType.bonus,
        rewardType: RewardType.referralBonus,
        points: referral.refereeRewardPoints,
        cashback: referral.refereeRewardCashback,
        description: 'Welcome bonus for joining through referral',
        referenceId: referralId,
      );

      // Update both loyalty accounts
      await _updateLoyaltyAccount(referral.referrerId, 0);
      await _updateLoyaltyAccount(referral.refereeId, 0);
    } catch (e) {
      debugPrint('❌ Error completing referral: $e');
    }
  }

  // Get reward transactions
  static Stream<List<RewardTransaction>> getRewardTransactions({
    required String userId,
    int limit = 50,
  }) {
    try {
      return _firestore
          .collection(_rewardTransactionsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .snapshots()
          .map((snapshot) {
        return snapshot.docs.map((doc) {
          return RewardTransaction.fromJson(doc.data());
        }).toList();
      });
    } catch (e) {
      debugPrint('❌ Error getting reward transactions: $e');
      return Stream.value([]);
    }
  }

  // Get available reward offers
  static Future<List<RewardOffer>> getAvailableRewardOffers({
    UserTier? userTier,
    List<String>? categories,
  }) async {
    try {
      Query query = _firestore
          .collection(_rewardOffersCollection)
          .where('isActive', isEqualTo: true)
          .where('validUntil', isGreaterThan: DateTime.now());

      if (categories != null && categories.isNotEmpty) {
        query = query.where('categories', arrayContainsAny: categories);
      }

      final snapshot = await query.get();
      
      var offers = snapshot.docs.map((doc) {
        return RewardOffer.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();

      // Filter by tier eligibility
      if (userTier != null) {
        offers = offers.where((offer) {
          return offer.eligibleTiers.isEmpty || offer.eligibleTiers.contains(userTier);
        }).toList();
      }

      // Sort by points cost
      offers.sort((a, b) => a.pointsCost.compareTo(b.pointsCost));

      return offers;
    } catch (e) {
      debugPrint('❌ Error getting reward offers: $e');
      return [];
    }
  }

  // Get specific reward offer
  static Future<RewardOffer?> getRewardOffer(String offerId) async {
    try {
      final doc = await _firestore.collection(_rewardOffersCollection).doc(offerId).get();
      if (doc.exists) {
        return RewardOffer.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting reward offer: $e');
      return null;
    }
  }

  // Get user's redemptions
  static Future<List<RewardRedemption>> getUserRedemptions(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(_rewardRedemptionsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('redeemedAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        return RewardRedemption.fromJson(doc.data());
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting user redemptions: $e');
      return [];
    }
  }

  // Private helper methods
  static Future<void> _addWelcomeBonus(String userId) async {
    await _addRewardTransaction(
      userId: userId,
      type: TransactionType.bonus,
      rewardType: RewardType.points,
      points: 100,
      description: 'Welcome bonus for joining Projek!',
    );
  }

  static Future<void> _addRewardTransaction({
    required String userId,
    required TransactionType type,
    required RewardType rewardType,
    int points = 0,
    double cashback = 0.0,
    required String description,
    String? orderId,
    String? referenceId,
  }) async {
    try {
      final transactionId = _uuid.v4();
      final now = DateTime.now();

      final transaction = RewardTransaction(
        id: transactionId,
        userId: userId,
        type: type,
        rewardType: rewardType,
        points: points,
        cashback: cashback,
        description: description,
        orderId: orderId,
        referenceId: referenceId,
        createdAt: now,
        expiresAt: type == TransactionType.earned ? now.add(const Duration(days: 365)) : null,
      );

      await _firestore.collection(_rewardTransactionsCollection).doc(transactionId).set(transaction.toJson());
    } catch (e) {
      debugPrint('❌ Error adding reward transaction: $e');
    }
  }

  static Future<void> _updateLoyaltyAccount(String userId, double orderAmount) async {
    try {
      final account = await getLoyaltyAccount(userId);
      if (account == null) return;

      // Calculate totals from transactions
      final transactions = await _firestore
          .collection(_rewardTransactionsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      int totalPoints = 0;
      int availablePoints = 0;
      double totalCashback = 0.0;
      double availableCashback = 0.0;

      for (final doc in transactions.docs) {
        final transaction = RewardTransaction.fromJson(doc.data());
        
        totalPoints += transaction.points;
        totalCashback += transaction.cashback;
        
        if (transaction.isActive) {
          availablePoints += transaction.points;
          availableCashback += transaction.cashback;
        }
      }

      // Update lifetime spending
      final newLifetimeSpending = account.lifetimeSpending + orderAmount;
      
      // Calculate new tier
      final newTier = TierConfig.calculateTier(newLifetimeSpending);
      final tierConfig = TierConfig.getTierConfig(newTier);
      final nextTierThreshold = tierConfig?['nextTierThreshold'] ?? -1;
      
      int tierProgress = 0;
      if (nextTierThreshold > 0) {
        tierProgress = (newLifetimeSpending - (tierConfig?['minSpending'] ?? 0)).round();
      }

      final updatedAccount = LoyaltyAccount(
        id: account.id,
        userId: userId,
        totalPoints: totalPoints,
        availablePoints: availablePoints,
        totalCashback: totalCashback,
        availableCashback: availableCashback,
        currentTier: newTier,
        tierProgress: tierProgress,
        tierThreshold: nextTierThreshold.toInt(),
        lifetimeSpending: newLifetimeSpending,
        totalReferrals: account.totalReferrals,
        createdAt: account.createdAt,
        updatedAt: DateTime.now(),
        tierBenefits: TierConfig.getTierConfig(newTier) ?? {},
      );

      await _firestore.collection(_loyaltyAccountsCollection).doc(account.id).update(updatedAccount.toJson());
    } catch (e) {
      debugPrint('❌ Error updating loyalty account: $e');
    }
  }

  static String _generateReferralCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(8, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  static String _generateRedemptionCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(12, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }
}
