import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'safety_models.g.dart';

@HiveType(typeId: 70)
enum EmergencyType {
  @HiveField(0)
  accident,
  @HiveField(1)
  medical,
  @HiveField(2)
  theft,
  @HiveField(3)
  harassment,
  @HiveField(4)
  vehicleBreakdown,
  @HiveField(5)
  other,
}

@HiveType(typeId: 71)
enum IncidentStatus {
  @HiveField(0)
  reported,
  @HiveField(1)
  acknowledged,
  @HiveField(2)
  investigating,
  @HiveField(3)
  resolved,
  @HiveField(4)
  closed,
}

@HiveType(typeId: 72)
enum SafetyCheckStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  completed,
  @HiveField(2)
  missed,
  @HiveField(3)
  overdue,
}

@HiveType(typeId: 73)
@JsonSerializable()
class EmergencyContact extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String name;
  
  @HiveField(3)
  final String phoneNumber;
  
  @HiveField(4)
  final String relationship;
  
  @HiveField(5)
  final bool isPrimary;
  
  @HiveField(6)
  final bool isActive;
  
  @HiveField(7)
  final DateTime createdAt;
  
  @HiveField(8)
  final DateTime updatedAt;

  const EmergencyContact({
    required this.id,
    required this.userId,
    required this.name,
    required this.phoneNumber,
    required this.relationship,
    this.isPrimary = false,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) => _$EmergencyContactFromJson(json);
  Map<String, dynamic> toJson() => _$EmergencyContactToJson(this);

  @override
  List<Object?> get props => [
    id, userId, name, phoneNumber, relationship, isPrimary, isActive, createdAt, updatedAt,
  ];
}

@HiveType(typeId: 74)
@JsonSerializable()
class SOSAlert extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String userName;
  
  @HiveField(3)
  final String userPhone;
  
  @HiveField(4)
  final EmergencyType type;
  
  @HiveField(5)
  final double latitude;
  
  @HiveField(6)
  final double longitude;
  
  @HiveField(7)
  final String address;
  
  @HiveField(8)
  final String? message;
  
  @HiveField(9)
  final List<String> imageUrls;
  
  @HiveField(10)
  final List<String> audioUrls;
  
  @HiveField(11)
  final DateTime triggeredAt;
  
  @HiveField(12)
  final DateTime? acknowledgedAt;
  
  @HiveField(13)
  final DateTime? resolvedAt;
  
  @HiveField(14)
  final String status;
  
  @HiveField(15)
  final List<String> notifiedContacts;
  
  @HiveField(16)
  final String? orderId;
  
  @HiveField(17)
  final Map<String, dynamic> metadata;

  const SOSAlert({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userPhone,
    required this.type,
    required this.latitude,
    required this.longitude,
    required this.address,
    this.message,
    this.imageUrls = const [],
    this.audioUrls = const [],
    required this.triggeredAt,
    this.acknowledgedAt,
    this.resolvedAt,
    this.status = 'active',
    this.notifiedContacts = const [],
    this.orderId,
    this.metadata = const {},
  });

  factory SOSAlert.fromJson(Map<String, dynamic> json) => _$SOSAlertFromJson(json);
  Map<String, dynamic> toJson() => _$SOSAlertToJson(this);

  bool get isActive => status == 'active';
  bool get isResolved => status == 'resolved';
  Duration get responseTime => acknowledgedAt != null 
      ? acknowledgedAt!.difference(triggeredAt) 
      : Duration.zero;

  @override
  List<Object?> get props => [
    id, userId, userName, userPhone, type, latitude, longitude, address,
    message, imageUrls, audioUrls, triggeredAt, acknowledgedAt, resolvedAt,
    status, notifiedContacts, orderId, metadata,
  ];
}

@HiveType(typeId: 75)
@JsonSerializable()
class SafetyCheckIn extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String? orderId;
  
  @HiveField(3)
  final double latitude;
  
  @HiveField(4)
  final double longitude;
  
  @HiveField(5)
  final String address;
  
  @HiveField(6)
  final DateTime scheduledAt;
  
  @HiveField(7)
  final DateTime? completedAt;
  
  @HiveField(8)
  final SafetyCheckStatus status;
  
  @HiveField(9)
  final String? note;
  
  @HiveField(10)
  final bool isAutomatic;
  
  @HiveField(11)
  final int reminderCount;
  
  @HiveField(12)
  final DateTime createdAt;

  const SafetyCheckIn({
    required this.id,
    required this.userId,
    this.orderId,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.scheduledAt,
    this.completedAt,
    this.status = SafetyCheckStatus.pending,
    this.note,
    this.isAutomatic = false,
    this.reminderCount = 0,
    required this.createdAt,
  });

  factory SafetyCheckIn.fromJson(Map<String, dynamic> json) => _$SafetyCheckInFromJson(json);
  Map<String, dynamic> toJson() => _$SafetyCheckInToJson(this);

  bool get isOverdue => status == SafetyCheckStatus.pending && 
                       DateTime.now().isAfter(scheduledAt.add(const Duration(minutes: 15)));
  
  bool get isCompleted => status == SafetyCheckStatus.completed;
  
  Duration get delayDuration => completedAt != null 
      ? completedAt!.difference(scheduledAt)
      : DateTime.now().difference(scheduledAt);

  @override
  List<Object?> get props => [
    id, userId, orderId, latitude, longitude, address, scheduledAt,
    completedAt, status, note, isAutomatic, reminderCount, createdAt,
  ];
}

@HiveType(typeId: 76)
@JsonSerializable()
class IncidentReport extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String reporterId;
  
  @HiveField(2)
  final String reporterName;
  
  @HiveField(3)
  final String reporterType; // 'rider', 'user', 'seller'
  
  @HiveField(4)
  final EmergencyType incidentType;
  
  @HiveField(5)
  final String title;
  
  @HiveField(6)
  final String description;
  
  @HiveField(7)
  final double? latitude;
  
  @HiveField(8)
  final double? longitude;
  
  @HiveField(9)
  final String? address;
  
  @HiveField(10)
  final List<String> imageUrls;
  
  @HiveField(11)
  final List<String> videoUrls;
  
  @HiveField(12)
  final String? orderId;
  
  @HiveField(13)
  final String? involvedUserId;
  
  @HiveField(14)
  final IncidentStatus status;
  
  @HiveField(15)
  final DateTime reportedAt;
  
  @HiveField(16)
  final DateTime? acknowledgedAt;
  
  @HiveField(17)
  final DateTime? resolvedAt;
  
  @HiveField(18)
  final String? assignedTo;
  
  @HiveField(19)
  final String? resolution;
  
  @HiveField(20)
  final Map<String, dynamic> metadata;

  const IncidentReport({
    required this.id,
    required this.reporterId,
    required this.reporterName,
    required this.reporterType,
    required this.incidentType,
    required this.title,
    required this.description,
    this.latitude,
    this.longitude,
    this.address,
    this.imageUrls = const [],
    this.videoUrls = const [],
    this.orderId,
    this.involvedUserId,
    this.status = IncidentStatus.reported,
    required this.reportedAt,
    this.acknowledgedAt,
    this.resolvedAt,
    this.assignedTo,
    this.resolution,
    this.metadata = const {},
  });

  factory IncidentReport.fromJson(Map<String, dynamic> json) => _$IncidentReportFromJson(json);
  Map<String, dynamic> toJson() => _$IncidentReportToJson(this);

  bool get hasLocation => latitude != null && longitude != null;
  bool get hasMedia => imageUrls.isNotEmpty || videoUrls.isNotEmpty;
  bool get isResolved => status == IncidentStatus.resolved || status == IncidentStatus.closed;
  
  Duration? get responseTime => acknowledgedAt != null 
      ? acknowledgedAt!.difference(reportedAt)
      : null;
  
  Duration? get resolutionTime => resolvedAt != null 
      ? resolvedAt!.difference(reportedAt)
      : null;

  @override
  List<Object?> get props => [
    id, reporterId, reporterName, reporterType, incidentType, title, description,
    latitude, longitude, address, imageUrls, videoUrls, orderId, involvedUserId,
    status, reportedAt, acknowledgedAt, resolvedAt, assignedTo, resolution, metadata,
  ];
}

@HiveType(typeId: 77)
@JsonSerializable()
class SafetySettings extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final bool sosEnabled;
  
  @HiveField(3)
  final bool autoCheckInEnabled;
  
  @HiveField(4)
  final int checkInIntervalMinutes;
  
  @HiveField(5)
  final bool locationSharingEnabled;
  
  @HiveField(6)
  final bool emergencyContactsNotification;
  
  @HiveField(7)
  final bool adminNotification;
  
  @HiveField(8)
  final List<String> trustedContacts;
  
  @HiveField(9)
  final Map<String, bool> notificationPreferences;
  
  @HiveField(10)
  final DateTime updatedAt;

  const SafetySettings({
    required this.id,
    required this.userId,
    this.sosEnabled = true,
    this.autoCheckInEnabled = true,
    this.checkInIntervalMinutes = 30,
    this.locationSharingEnabled = true,
    this.emergencyContactsNotification = true,
    this.adminNotification = true,
    this.trustedContacts = const [],
    this.notificationPreferences = const {},
    required this.updatedAt,
  });

  factory SafetySettings.fromJson(Map<String, dynamic> json) => _$SafetySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$SafetySettingsToJson(this);

  @override
  List<Object?> get props => [
    id, userId, sosEnabled, autoCheckInEnabled, checkInIntervalMinutes,
    locationSharingEnabled, emergencyContactsNotification, adminNotification,
    trustedContacts, notificationPreferences, updatedAt,
  ];
}

// Safety configuration
class SafetyConfig {
  static const Map<EmergencyType, Map<String, dynamic>> emergencyTypeConfigs = {
    EmergencyType.accident: {
      'name': 'Accident',
      'icon': 'car_crash',
      'color': 0xFFFF5722,
      'priority': 'high',
      'autoNotifyPolice': true,
      'autoNotifyMedical': true,
    },
    EmergencyType.medical: {
      'name': 'Medical Emergency',
      'icon': 'medical_services',
      'color': 0xFFE91E63,
      'priority': 'critical',
      'autoNotifyPolice': false,
      'autoNotifyMedical': true,
    },
    EmergencyType.theft: {
      'name': 'Theft/Robbery',
      'icon': 'security',
      'color': 0xFF9C27B0,
      'priority': 'high',
      'autoNotifyPolice': true,
      'autoNotifyMedical': false,
    },
    EmergencyType.harassment: {
      'name': 'Harassment',
      'icon': 'report_problem',
      'color': 0xFFFF9800,
      'priority': 'high',
      'autoNotifyPolice': true,
      'autoNotifyMedical': false,
    },
    EmergencyType.vehicleBreakdown: {
      'name': 'Vehicle Breakdown',
      'icon': 'build',
      'color': 0xFF607D8B,
      'priority': 'medium',
      'autoNotifyPolice': false,
      'autoNotifyMedical': false,
    },
    EmergencyType.other: {
      'name': 'Other Emergency',
      'icon': 'warning',
      'color': 0xFF795548,
      'priority': 'medium',
      'autoNotifyPolice': false,
      'autoNotifyMedical': false,
    },
  };

  static Map<String, dynamic>? getEmergencyTypeConfig(EmergencyType type) {
    return emergencyTypeConfigs[type];
  }

  static String getEmergencyTypeName(EmergencyType type) {
    return emergencyTypeConfigs[type]?['name'] ?? type.toString();
  }

  static int getEmergencyTypeColor(EmergencyType type) {
    return emergencyTypeConfigs[type]?['color'] ?? 0xFF9E9E9E;
  }

  static String getEmergencyTypePriority(EmergencyType type) {
    return emergencyTypeConfigs[type]?['priority'] ?? 'medium';
  }

  // Safety check intervals
  static const Map<String, int> checkInIntervals = {
    'Every 15 minutes': 15,
    'Every 30 minutes': 30,
    'Every 45 minutes': 45,
    'Every hour': 60,
    'Every 2 hours': 120,
  };

  // Emergency response times (in minutes)
  static const Map<String, int> responseTimeTargets = {
    'critical': 2,
    'high': 5,
    'medium': 15,
    'low': 30,
  };
}
