import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/utils/uid_generator.dart';
import '../../../auth/data/services/uid_service.dart';

class UIDRegistrationPage extends ConsumerStatefulWidget {
  const UIDRegistrationPage({super.key});

  @override
  ConsumerState<UIDRegistrationPage> createState() => _UIDRegistrationPageState();
}

class _UIDRegistrationPageState extends ConsumerState<UIDRegistrationPage> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  
  DateTime? _selectedDate;
  bool _isLoading = false;
  String? _generatedUID;
  List<String> _uidSuggestions = [];

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime(2000),
      firstDate: DateTime(1950),
      lastDate: DateTime.now().subtract(const Duration(days: 365 * 13)), // Min 13 years old
      helpText: 'Select Date of Birth',
    );
    
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _generateUID() async {
    if (!_formKey.currentState!.validate() || _selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill all required fields')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final uidService = ref.read(uidServiceProvider);
      
      // Generate UID suggestions
      final suggestions = await uidService.generateUIDSuggestions(
        fullName: _fullNameController.text.trim(),
        dateOfBirth: _selectedDate!,
        phoneNumber: _phoneController.text.trim(),
      );

      setState(() {
        _uidSuggestions = suggestions;
        _generatedUID = suggestions.isNotEmpty ? suggestions.first : null;
      });

      if (_generatedUID != null) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('UID Generated: $_generatedUID'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Copy',
              onPressed: () {
                Clipboard.setData(ClipboardData(text: _generatedUID!));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('UID copied to clipboard')),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _registerUID(String uid) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final uidService = ref.read(uidServiceProvider);
      
      final userUID = await uidService.createUserUID(
        fullName: _fullNameController.text.trim(),
        dateOfBirth: _selectedDate!,
        phoneNumber: _phoneController.text.trim(),
        additionalData: {
          'registrationSource': 'ProjekApp',
          'sessionId': '9c7d58e8-9e5b-4573-abed-a3d7773c9ec3',
        },
      );

      // Show success dialog
      _showSuccessDialog(userUID);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Registration failed: $e'), backgroundColor: Colors.red),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog(UserUID userUID) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 UID Registered Successfully!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Your unique ID: ${userUID.uid}'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Name: ${userUID.fullName}'),
                  Text('DOB: ${userUID.dateOfBirth.day}/${userUID.dateOfBirth.month}/${userUID.dateOfBirth.year}'),
                  Text('Created: ${userUID.createdAt.day}/${userUID.createdAt.month}/${userUID.createdAt.year}'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Please save this UID safely. You will need it for verification.',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: userUID.uid));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('UID copied to clipboard')),
              );
            },
            child: const Text('Copy UID'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/home');
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Your UID'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [theme.colorScheme.primary, theme.colorScheme.secondary],
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    const Icon(Icons.fingerprint, size: 48, color: Colors.white),
                    const SizedBox(height: 12),
                    const Text(
                      'Create Your Unique ID',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Combine your name + date of birth + 567',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Full Name Field
              TextFormField(
                controller: _fullNameController,
                decoration: const InputDecoration(
                  labelText: 'Full Name *',
                  hintText: 'Enter your full name',
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your full name';
                  }
                  if (value.trim().length < 3) {
                    return 'Name must be at least 3 characters';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Date of Birth Field
              InkWell(
                onTap: _selectDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Date of Birth *',
                    hintText: 'Select your date of birth',
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _selectedDate != null
                        ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                        : 'Tap to select date',
                    style: TextStyle(
                      color: _selectedDate != null ? null : Colors.grey,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Phone Number Field
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  hintText: '+91 9876543210',
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (value.length < 10) {
                      return 'Please enter a valid phone number';
                    }
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 32),
              
              // Generate UID Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _generateUID,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator()
                      : const Text('Generate UID'),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // UID Suggestions
              if (_uidSuggestions.isNotEmpty) ...[
                const Text(
                  'Available UID Suggestions:',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                
                ...(_uidSuggestions.map((uid) => Card(
                  child: ListTile(
                    title: Text(
                      uid,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(_parseUIDInfo(uid)),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: () {
                            Clipboard.setData(ClipboardData(text: uid));
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('UID copied')),
                            );
                          },
                          icon: const Icon(Icons.copy),
                        ),
                        ElevatedButton(
                          onPressed: () => _registerUID(uid),
                          child: const Text('Register'),
                        ),
                      ],
                    ),
                  ),
                ))),
              ],
              
              const SizedBox(height: 32),
              
              // Info Card
              Card(
                color: Colors.blue[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'UID Format Information:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      const Text('• First 6 characters: Name code'),
                      const Text('• Next 6 characters: Date of birth (DDMMYY)'),
                      const Text('• Next 3 characters: Magic number (567)'),
                      const Text('• Last 4 characters: Unique hash'),
                      const SizedBox(height: 8),
                      const Text(
                        'Example: RAHKUM150895567A1B2',
                        style: TextStyle(
                          fontFamily: 'monospace',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _parseUIDInfo(String uid) {
    try {
      final info = UIDGenerator.parseUID(uid);
      final dob = info['dateOfBirth'] as DateTime;
      return 'DOB: ${dob.day}/${dob.month}/${dob.year} | Magic: ${info['magicNumber']}';
    } catch (e) {
      return 'Invalid UID format';
    }
  }
}
