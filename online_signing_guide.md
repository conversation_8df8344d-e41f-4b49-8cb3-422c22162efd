# Online APK Signing Guide for Projek Marketplace

## 🌐 Online APK Signing Services

### Option 1: APK Signer Online
1. Visit: https://www.apksigner.io/
2. Upload: `APK_RELEASE/app-arm64-v8a-prod-release.apk`
3. Select: "Sign with test certificate"
4. Download: Signed APK
5. Transfer to Vivo V23 5G

### Option 2: APK Online Signer
1. Visit: https://apkonline.net/apk-signer
2. Upload your unsigned APK
3. Choose "Debug signing"
4. Download signed APK

### Option 3: GitHub Actions (Automated)
Create `.github/workflows/sign-apk.yml`:
```yaml
name: Sign APK
on: workflow_dispatch
jobs:
  sign:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Sign APK
        run: |
          keytool -genkey -v -keystore debug.keystore -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000 -dname "CN=Debug,O=Debug,C=US"
          jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore debug.keystore -storepass android -keypass android APK_RELEASE/app-arm64-v8a-prod-release.apk androiddebugkey
```

## ⚠️ Security Note
Only use online services for testing. For production, always sign locally with your own keystore.

## 📱 Installation Steps for Vivo V23 5G
1. Download signed APK to device
2. Enable "Install from Unknown Sources"
3. Install APK
4. Test all marketplace features
