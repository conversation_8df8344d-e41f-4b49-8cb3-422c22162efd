import 'package:flutter/material.dart';

class FoodDemoData {
  // Food categories with subcategories
  static const Map<String, List<String>> foodCategories = {
    'Regional Cuisine': [
      'Assamese Thali',
      'Bengali Thali', 
      'North Indian Cuisine',
      'South Indian Cuisine',
      'Other Regional Specialties'
    ],
    'Food Type': [
      'Fast Food',
      'Chicken Dishes',
      'Vegetarian Items',
      'Seafood',
      'Desserts & Sweets',
      'Beverages'
    ],
  };

  // Sample food items with Indian pricing and details
  static List<Map<String, dynamic>> getAllFoodItems() {
    return [
      // Assamese Thali
      {
        'id': 'assamese_1',
        'name': 'Traditional Assamese Thali',
        'description': 'Complete Assamese meal with rice, dal, fish curry, and vegetables',
        'price': 299.0,
        'originalPrice': 349.0,
        'category': 'Regional Cuisine',
        'subcategory': 'Assamese Thali',
        'rating': 4.6,
        'reviewCount': 234,
        'image': 'assets/images/food/assamese_thali.jpg',
        'isAvailable': true,
        'preparationTime': '25-30 mins',
        'serves': '1 person',
        'isVeg': false,
        'tags': ['Traditional', 'Complete Meal', 'Authentic'],
      },
      {
        'id': 'assamese_2', 
        'name': 'Assamese Fish Tenga',
        'description': 'Tangy fish curry with tomatoes and herbs',
        'price': 189.0,
        'originalPrice': 219.0,
        'category': 'Regional Cuisine',
        'subcategory': 'Assamese Thali',
        'rating': 4.4,
        'reviewCount': 156,
        'image': 'assets/images/food/fish_tenga.jpg',
        'isAvailable': true,
        'preparationTime': '20-25 mins',
        'serves': '1-2 persons',
        'isVeg': false,
        'tags': ['Tangy', 'Fish', 'Traditional'],
      },

      // Bengali Thali
      {
        'id': 'bengali_1',
        'name': 'Bengali Fish Thali',
        'description': 'Rice, fish curry, dal, vegetables, and sweets',
        'price': 329.0,
        'originalPrice': 379.0,
        'category': 'Regional Cuisine',
        'subcategory': 'Bengali Thali',
        'rating': 4.7,
        'reviewCount': 312,
        'image': 'assets/images/food/bengali_thali.jpg',
        'isAvailable': true,
        'preparationTime': '30-35 mins',
        'serves': '1 person',
        'isVeg': false,
        'tags': ['Fish', 'Complete Meal', 'Sweet'],
      },
      {
        'id': 'bengali_2',
        'name': 'Mishti Doi',
        'description': 'Traditional Bengali sweet yogurt',
        'price': 89.0,
        'originalPrice': 109.0,
        'category': 'Regional Cuisine',
        'subcategory': 'Bengali Thali',
        'rating': 4.8,
        'reviewCount': 445,
        'image': 'assets/images/food/mishti_doi.jpg',
        'isAvailable': true,
        'preparationTime': '5 mins',
        'serves': '1 person',
        'isVeg': true,
        'tags': ['Sweet', 'Dessert', 'Traditional'],
      },

      // North Indian Cuisine
      {
        'id': 'north_1',
        'name': 'Butter Chicken',
        'description': 'Creamy tomato-based chicken curry with butter naan',
        'price': 349.0,
        'originalPrice': 399.0,
        'category': 'Regional Cuisine',
        'subcategory': 'North Indian Cuisine',
        'rating': 4.9,
        'reviewCount': 567,
        'image': 'assets/images/food/butter_chicken.jpg',
        'isAvailable': true,
        'preparationTime': '25-30 mins',
        'serves': '2 persons',
        'isVeg': false,
        'tags': ['Creamy', 'Popular', 'Rich'],
      },
      {
        'id': 'north_2',
        'name': 'Dal Makhani',
        'description': 'Rich black lentils cooked with butter and cream',
        'price': 249.0,
        'originalPrice': 289.0,
        'category': 'Regional Cuisine',
        'subcategory': 'North Indian Cuisine',
        'rating': 4.5,
        'reviewCount': 234,
        'image': 'assets/images/food/dal_makhani.jpg',
        'isAvailable': true,
        'preparationTime': '20-25 mins',
        'serves': '2-3 persons',
        'isVeg': true,
        'tags': ['Creamy', 'Vegetarian', 'Rich'],
      },
      {
        'id': 'north_3',
        'name': 'Garlic Naan',
        'description': 'Soft bread with garlic and butter',
        'price': 89.0,
        'originalPrice': 109.0,
        'category': 'Regional Cuisine',
        'subcategory': 'North Indian Cuisine',
        'rating': 4.6,
        'reviewCount': 189,
        'image': 'assets/images/food/garlic_naan.jpg',
        'isAvailable': true,
        'preparationTime': '10-15 mins',
        'serves': '1-2 persons',
        'isVeg': true,
        'tags': ['Bread', 'Garlic', 'Soft'],
      },

      // South Indian Cuisine
      {
        'id': 'south_1',
        'name': 'Masala Dosa',
        'description': 'Crispy rice crepe with spiced potato filling',
        'price': 149.0,
        'originalPrice': 179.0,
        'category': 'Regional Cuisine',
        'subcategory': 'South Indian Cuisine',
        'rating': 4.7,
        'reviewCount': 423,
        'image': 'assets/images/food/masala_dosa.jpg',
        'isAvailable': true,
        'preparationTime': '15-20 mins',
        'serves': '1 person',
        'isVeg': true,
        'tags': ['Crispy', 'Traditional', 'Healthy'],
      },
      {
        'id': 'south_2',
        'name': 'Idli Sambar',
        'description': 'Steamed rice cakes with lentil curry',
        'price': 119.0,
        'originalPrice': 139.0,
        'category': 'Regional Cuisine',
        'subcategory': 'South Indian Cuisine',
        'rating': 4.5,
        'reviewCount': 312,
        'image': 'assets/images/food/idli_sambar.jpg',
        'isAvailable': true,
        'preparationTime': '10-15 mins',
        'serves': '1 person',
        'isVeg': true,
        'tags': ['Healthy', 'Light', 'Traditional'],
      },
      {
        'id': 'south_3',
        'name': 'Filter Coffee',
        'description': 'Traditional South Indian coffee',
        'price': 59.0,
        'originalPrice': 79.0,
        'category': 'Regional Cuisine',
        'subcategory': 'South Indian Cuisine',
        'rating': 4.8,
        'reviewCount': 234,
        'image': 'assets/images/food/filter_coffee.jpg',
        'isAvailable': true,
        'preparationTime': '5 mins',
        'serves': '1 person',
        'isVeg': true,
        'tags': ['Coffee', 'Traditional', 'Strong'],
      },

      // Fast Food
      {
        'id': 'fast_1',
        'name': 'Chicken Burger',
        'description': 'Grilled chicken patty with fresh vegetables',
        'price': 199.0,
        'originalPrice': 229.0,
        'category': 'Food Type',
        'subcategory': 'Fast Food',
        'rating': 4.4,
        'reviewCount': 567,
        'image': 'assets/images/food/chicken_burger.jpg',
        'isAvailable': true,
        'preparationTime': '15-20 mins',
        'serves': '1 person',
        'isVeg': false,
        'tags': ['Burger', 'Chicken', 'Fast'],
      },
      {
        'id': 'fast_2',
        'name': 'Margherita Pizza',
        'description': 'Classic pizza with tomato, mozzarella, and basil',
        'price': 299.0,
        'originalPrice': 349.0,
        'category': 'Food Type',
        'subcategory': 'Fast Food',
        'rating': 4.6,
        'reviewCount': 445,
        'image': 'assets/images/food/margherita_pizza.jpg',
        'isAvailable': true,
        'preparationTime': '20-25 mins',
        'serves': '2-3 persons',
        'isVeg': true,
        'tags': ['Pizza', 'Cheese', 'Italian'],
      },
      {
        'id': 'fast_3',
        'name': 'Chicken Momos',
        'description': 'Steamed dumplings with spiced chicken filling',
        'price': 149.0,
        'originalPrice': 179.0,
        'category': 'Food Type',
        'subcategory': 'Fast Food',
        'rating': 4.5,
        'reviewCount': 234,
        'image': 'assets/images/food/chicken_momos.jpg',
        'isAvailable': true,
        'preparationTime': '15-20 mins',
        'serves': '1 person',
        'isVeg': false,
        'tags': ['Momos', 'Steamed', 'Spicy'],
      },

      // Chicken Dishes
      {
        'id': 'chicken_1',
        'name': 'Tandoori Chicken',
        'description': 'Marinated chicken cooked in tandoor oven',
        'price': 399.0,
        'originalPrice': 449.0,
        'category': 'Food Type',
        'subcategory': 'Chicken Dishes',
        'rating': 4.8,
        'reviewCount': 678,
        'image': 'assets/images/food/tandoori_chicken.jpg',
        'isAvailable': true,
        'preparationTime': '30-35 mins',
        'serves': '2-3 persons',
        'isVeg': false,
        'tags': ['Tandoor', 'Marinated', 'Smoky'],
      },
      {
        'id': 'chicken_2',
        'name': 'Chicken Biryani',
        'description': 'Fragrant basmati rice with spiced chicken',
        'price': 329.0,
        'originalPrice': 379.0,
        'category': 'Food Type',
        'subcategory': 'Chicken Dishes',
        'rating': 4.7,
        'reviewCount': 523,
        'image': 'assets/images/food/chicken_biryani.jpg',
        'isAvailable': true,
        'preparationTime': '35-40 mins',
        'serves': '2 persons',
        'isVeg': false,
        'tags': ['Biryani', 'Fragrant', 'Spiced'],
      },

      // Vegetarian Items
      {
        'id': 'veg_1',
        'name': 'Paneer Butter Masala',
        'description': 'Cottage cheese in rich tomato-butter gravy',
        'price': 279.0,
        'originalPrice': 319.0,
        'category': 'Food Type',
        'subcategory': 'Vegetarian Items',
        'rating': 4.6,
        'reviewCount': 445,
        'image': 'assets/images/food/paneer_butter_masala.jpg',
        'isAvailable': true,
        'preparationTime': '20-25 mins',
        'serves': '2 persons',
        'isVeg': true,
        'tags': ['Paneer', 'Creamy', 'Rich'],
      },
      {
        'id': 'veg_2',
        'name': 'Chole Bhature',
        'description': 'Spiced chickpeas with fried bread',
        'price': 199.0,
        'originalPrice': 229.0,
        'category': 'Food Type',
        'subcategory': 'Vegetarian Items',
        'rating': 4.5,
        'reviewCount': 312,
        'image': 'assets/images/food/chole_bhature.jpg',
        'isAvailable': true,
        'preparationTime': '25-30 mins',
        'serves': '1-2 persons',
        'isVeg': true,
        'tags': ['Chickpeas', 'Spicy', 'Traditional'],
      },

      // Seafood
      {
        'id': 'seafood_1',
        'name': 'Fish Curry',
        'description': 'Fresh fish in coconut-based curry',
        'price': 349.0,
        'originalPrice': 399.0,
        'category': 'Food Type',
        'subcategory': 'Seafood',
        'rating': 4.7,
        'reviewCount': 234,
        'image': 'assets/images/food/fish_curry.jpg',
        'isAvailable': true,
        'preparationTime': '25-30 mins',
        'serves': '2 persons',
        'isVeg': false,
        'tags': ['Fish', 'Coconut', 'Curry'],
      },
      {
        'id': 'seafood_2',
        'name': 'Prawn Fry',
        'description': 'Crispy fried prawns with spices',
        'price': 429.0,
        'originalPrice': 479.0,
        'category': 'Food Type',
        'subcategory': 'Seafood',
        'rating': 4.6,
        'reviewCount': 189,
        'image': 'assets/images/food/prawn_fry.jpg',
        'isAvailable': true,
        'preparationTime': '20-25 mins',
        'serves': '2 persons',
        'isVeg': false,
        'tags': ['Prawn', 'Crispy', 'Spiced'],
      },

      // Desserts & Sweets
      {
        'id': 'dessert_1',
        'name': 'Rasgulla',
        'description': 'Soft cottage cheese balls in sugar syrup',
        'price': 129.0,
        'originalPrice': 149.0,
        'category': 'Food Type',
        'subcategory': 'Desserts & Sweets',
        'rating': 4.8,
        'reviewCount': 567,
        'image': 'assets/images/food/rasgulla.jpg',
        'isAvailable': true,
        'preparationTime': '5 mins',
        'serves': '2-3 persons',
        'isVeg': true,
        'tags': ['Sweet', 'Soft', 'Traditional'],
      },
      {
        'id': 'dessert_2',
        'name': 'Gulab Jamun',
        'description': 'Deep-fried milk balls in sugar syrup',
        'price': 149.0,
        'originalPrice': 169.0,
        'category': 'Food Type',
        'subcategory': 'Desserts & Sweets',
        'rating': 4.7,
        'reviewCount': 445,
        'image': 'assets/images/food/gulab_jamun.jpg',
        'isAvailable': true,
        'preparationTime': '5 mins',
        'serves': '2-3 persons',
        'isVeg': true,
        'tags': ['Sweet', 'Rich', 'Popular'],
      },

      // Beverages
      {
        'id': 'beverage_1',
        'name': 'Masala Chai',
        'description': 'Spiced Indian tea with milk',
        'price': 39.0,
        'originalPrice': 49.0,
        'category': 'Food Type',
        'subcategory': 'Beverages',
        'rating': 4.6,
        'reviewCount': 789,
        'image': 'assets/images/food/masala_chai.jpg',
        'isAvailable': true,
        'preparationTime': '5-10 mins',
        'serves': '1 person',
        'isVeg': true,
        'tags': ['Tea', 'Spiced', 'Hot'],
      },
      {
        'id': 'beverage_2',
        'name': 'Fresh Lime Soda',
        'description': 'Refreshing lime drink with soda',
        'price': 79.0,
        'originalPrice': 99.0,
        'category': 'Food Type',
        'subcategory': 'Beverages',
        'rating': 4.4,
        'reviewCount': 234,
        'image': 'assets/images/food/lime_soda.jpg',
        'isAvailable': true,
        'preparationTime': '5 mins',
        'serves': '1 person',
        'isVeg': true,
        'tags': ['Refreshing', 'Citrus', 'Cold'],
      },
      {
        'id': 'beverage_3',
        'name': 'Mango Lassi',
        'description': 'Sweet yogurt drink with mango',
        'price': 119.0,
        'originalPrice': 139.0,
        'category': 'Food Type',
        'subcategory': 'Beverages',
        'rating': 4.8,
        'reviewCount': 456,
        'image': 'assets/images/food/mango_lassi.jpg',
        'isAvailable': true,
        'preparationTime': '5 mins',
        'serves': '1 person',
        'isVeg': true,
        'tags': ['Mango', 'Sweet', 'Creamy'],
      },
    ];
  }

  // Get items by category
  static List<Map<String, dynamic>> getItemsByCategory(String category) {
    return getAllFoodItems()
        .where((item) => item['category'] == category)
        .toList();
  }

  // Get items by subcategory
  static List<Map<String, dynamic>> getItemsBySubcategory(String subcategory) {
    return getAllFoodItems()
        .where((item) => item['subcategory'] == subcategory)
        .toList();
  }

  // Search items
  static List<Map<String, dynamic>> searchItems(String query) {
    final lowercaseQuery = query.toLowerCase();
    return getAllFoodItems().where((item) {
      return item['name'].toString().toLowerCase().contains(lowercaseQuery) ||
          item['description'].toString().toLowerCase().contains(lowercaseQuery) ||
          item['tags'].any((tag) => tag.toString().toLowerCase().contains(lowercaseQuery));
    }).toList();
  }
}
