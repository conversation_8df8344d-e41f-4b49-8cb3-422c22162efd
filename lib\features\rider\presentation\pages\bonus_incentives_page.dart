import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/config/app_config.dart';
import '../../../../demo/rider_demo_data.dart';
import '../../domain/models/rider_kyc.dart';

class BonusIncentivesPage extends ConsumerStatefulWidget {
  const BonusIncentivesPage({super.key});

  @override
  ConsumerState<BonusIncentivesPage> createState() =>
      _BonusIncentivesPageState();
}

class _BonusIncentivesPageState extends ConsumerState<BonusIncentivesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final String _riderId = 'rider_001';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bonuses = RiderDemoData.getRiderBonuses(_riderId);
    final activeBonuses = bonuses
        .where((b) => b.status == BonusStatus.active)
        .toList();
    final earnedBonuses = bonuses
        .where((b) => b.status == BonusStatus.earned)
        .toList();

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'Bonuses & Incentives',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConfig.primaryColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Summary Header
          _buildSummaryHeader(bonuses),

          // Tab Bar
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppConfig.primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppConfig.primaryColor,
              labelStyle: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              tabs: const [
                Tab(text: 'Active'),
                Tab(text: 'Earned'),
                Tab(text: 'All'),
              ],
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildActiveBonusesTab(activeBonuses),
                _buildEarnedBonusesTab(earnedBonuses),
                _buildAllBonusesTab(bonuses),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryHeader(List<BonusIncentive> bonuses) {
    final totalEarned = bonuses
        .where((b) => b.status == BonusStatus.earned)
        .fold(0.0, (sum, b) => sum + b.amount);

    final activeBonusesCount = bonuses
        .where((b) => b.status == BonusStatus.active)
        .length;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppConfig.primaryColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Total Earned',
                  '₹${totalEarned.toStringAsFixed(2)}',
                  Icons.star,
                  Colors.yellow[300]!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'Active Bonuses',
                  '$activeBonusesCount',
                  Icons.trending_up,
                  Colors.green[300]!,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Complete rides during peak hours and maintain high ratings to earn more bonuses!',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color iconColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: iconColor, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActiveBonusesTab(List<BonusIncentive> activeBonuses) {
    if (activeBonuses.isEmpty) {
      return _buildEmptyState(
        'No Active Bonuses',
        'Complete more rides to unlock new bonus opportunities!',
        Icons.star_outline,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: activeBonuses.length,
      itemBuilder: (context, index) {
        return _buildBonusCard(activeBonuses[index]);
      },
    );
  }

  Widget _buildEarnedBonusesTab(List<BonusIncentive> earnedBonuses) {
    if (earnedBonuses.isEmpty) {
      return _buildEmptyState(
        'No Earned Bonuses',
        'Start completing rides to earn your first bonus!',
        Icons.emoji_events_outlined,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: earnedBonuses.length,
      itemBuilder: (context, index) {
        return _buildBonusCard(earnedBonuses[index]);
      },
    );
  }

  Widget _buildAllBonusesTab(List<BonusIncentive> allBonuses) {
    if (allBonuses.isEmpty) {
      return _buildEmptyState(
        'No Bonuses Available',
        'Check back later for new bonus opportunities!',
        Icons.card_giftcard_outlined,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: allBonuses.length,
      itemBuilder: (context, index) {
        return _buildBonusCard(allBonuses[index]);
      },
    );
  }

  Widget _buildEmptyState(String title, String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBonusCard(BonusIncentive bonus) {
    final statusColor = _getBonusStatusColor(bonus.status);
    final statusText = _getBonusStatusText(bonus.status);
    final typeIcon = _getBonusTypeIcon(bonus.type);
    final typeColor = _getBonusTypeColor(bonus.type);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: typeColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: typeColor.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(typeIcon, color: typeColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        bonus.title,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        bonus.description,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    statusText,
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: statusColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Amount and Progress
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Bonus Amount',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                    Text(
                      '₹${bonus.amount.toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),

                if (bonus.status == BonusStatus.active) ...[
                  const SizedBox(height: 16),
                  // Progress Bar
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Progress',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.black54,
                            ),
                          ),
                          Text(
                            '${bonus.progress.toStringAsFixed(1)}%',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppConfig.primaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: bonus.progress / 100,
                        backgroundColor: Colors.grey[200],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppConfig.primaryColor,
                        ),
                        minHeight: 6,
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 16),

                // Validity
                Row(
                  children: [
                    Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      bonus.status == BonusStatus.earned
                          ? 'Earned on ${_formatDate(bonus.earnedAt!)}'
                          : 'Valid until ${_formatDate(bonus.validUntil)}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getBonusStatusColor(BonusStatus status) {
    switch (status) {
      case BonusStatus.active:
        return Colors.blue;
      case BonusStatus.earned:
        return Colors.green;
      case BonusStatus.expired:
        return Colors.red;
      case BonusStatus.cancelled:
        return Colors.grey;
    }
  }

  String _getBonusStatusText(BonusStatus status) {
    switch (status) {
      case BonusStatus.active:
        return 'Active';
      case BonusStatus.earned:
        return 'Earned';
      case BonusStatus.expired:
        return 'Expired';
      case BonusStatus.cancelled:
        return 'Cancelled';
    }
  }

  IconData _getBonusTypeIcon(BonusType type) {
    switch (type) {
      case BonusType.peakHour:
        return Icons.access_time;
      case BonusType.dailyTarget:
        return Icons.today;
      case BonusType.weeklyTarget:
        return Icons.date_range;
      case BonusType.ratingBonus:
        return Icons.star;
      case BonusType.referral:
        return Icons.people;
      case BonusType.loyalty:
        return Icons.favorite;
      case BonusType.surge:
        return Icons.trending_up;
      case BonusType.special:
        return Icons.card_giftcard;
    }
  }

  Color _getBonusTypeColor(BonusType type) {
    switch (type) {
      case BonusType.peakHour:
        return Colors.orange;
      case BonusType.dailyTarget:
        return Colors.blue;
      case BonusType.weeklyTarget:
        return Colors.purple;
      case BonusType.ratingBonus:
        return Colors.yellow[700]!;
      case BonusType.referral:
        return Colors.green;
      case BonusType.loyalty:
        return Colors.pink;
      case BonusType.surge:
        return Colors.red;
      case BonusType.special:
        return Colors.indigo;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
