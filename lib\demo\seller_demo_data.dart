class SellerDemoData {
  // Restaurant/Seller Info
  static const String sellerName = "Spice Garden Restaurant";
  static const String sellerAddress = "123 Food Street, Guwahati, Assam";
  static const String sellerPhone = "+91 98765 43210";
  static const String sellerEmail = "<EMAIL>";
  static const double sellerRating = 4.5;
  static const int totalReviews = 1250;

  // Today's Summary
  static const int todayOrders = 28;
  static const double todayEarnings = 4250.0;
  static const int pendingOrders = 5;
  static const int completedOrders = 23;

  // Products/Menu Items
  static final List<Map<String, dynamic>> products = [
    {
      'id': 'p1',
      'name': 'Chicken Biryani',
      'description': 'Aromatic basmati rice with tender chicken pieces',
      'price': 280.0,
      'category': 'Main Course',
      'image': 'assets/images/biryani.jpg',
      'isAvailable': true,
      'stock': 25,
      'preparationTime': 30,
      'isVeg': false,
    },
    {
      'id': 'p2',
      'name': 'Paneer Butter Masala',
      'description': 'Creamy tomato curry with cottage cheese',
      'price': 220.0,
      'category': 'Main Course',
      'image': 'assets/images/paneer.jpg',
      'isAvailable': true,
      'stock': 15,
      'preparationTime': 20,
      'isVeg': true,
    },
    {
      'id': 'p3',
      'name': 'Masala Dosa',
      'description': 'Crispy rice crepe with spiced potato filling',
      'price': 120.0,
      'category': 'South Indian',
      'image': 'assets/images/dosa.jpg',
      'isAvailable': true,
      'stock': 30,
      'preparationTime': 15,
      'isVeg': true,
    },
    {
      'id': 'p4',
      'name': 'Fish Curry',
      'description': 'Traditional Assamese fish curry with rice',
      'price': 320.0,
      'category': 'Regional',
      'image': 'assets/images/fish_curry.jpg',
      'isAvailable': false,
      'stock': 0,
      'preparationTime': 25,
      'isVeg': false,
    },
    {
      'id': 'p5',
      'name': 'Veg Thali',
      'description': 'Complete vegetarian meal with variety',
      'price': 180.0,
      'category': 'Thali',
      'image': 'assets/images/veg_thali.jpg',
      'isAvailable': true,
      'stock': 20,
      'preparationTime': 25,
      'isVeg': true,
    },
  ];

  // Orders
  static final List<Map<String, dynamic>> orders = [
    {
      'id': 'ORD001',
      'customerName': 'Rahul Sharma',
      'customerPhone': '+91 98765 12345',
      'customerAddress': '45 MG Road, Guwahati',
      'items': [
        {'name': 'Chicken Biryani', 'quantity': 2, 'price': 280.0},
        {'name': 'Paneer Butter Masala', 'quantity': 1, 'price': 220.0},
      ],
      'totalAmount': 780.0,
      'status': 'pending',
      'orderTime': '2024-01-26 14:30',
      'estimatedTime': 35,
      'paymentMethod': 'Online',
      'isPaid': true,
    },
    {
      'id': 'ORD002',
      'customerName': 'Priya Das',
      'customerPhone': '+91 87654 32109',
      'customerAddress': '78 Zoo Road, Guwahati',
      'items': [
        {'name': 'Masala Dosa', 'quantity': 3, 'price': 120.0},
        {'name': 'Veg Thali', 'quantity': 1, 'price': 180.0},
      ],
      'totalAmount': 540.0,
      'status': 'preparing',
      'orderTime': '2024-01-26 14:45',
      'estimatedTime': 20,
      'paymentMethod': 'Cash on Delivery',
      'isPaid': false,
    },
    {
      'id': 'ORD003',
      'customerName': 'Amit Kumar',
      'customerPhone': '+91 76543 21098',
      'customerAddress': '12 Fancy Bazaar, Guwahati',
      'items': [
        {'name': 'Fish Curry', 'quantity': 1, 'price': 320.0},
      ],
      'totalAmount': 320.0,
      'status': 'ready',
      'orderTime': '2024-01-26 15:00',
      'estimatedTime': 0,
      'paymentMethod': 'Online',
      'isPaid': true,
    },
    {
      'id': 'ORD004',
      'customerName': 'Sneha Gogoi',
      'customerPhone': '+91 65432 10987',
      'customerAddress': '89 Ulubari, Guwahati',
      'items': [
        {'name': 'Chicken Biryani', 'quantity': 1, 'price': 280.0},
        {'name': 'Paneer Butter Masala', 'quantity': 1, 'price': 220.0},
      ],
      'totalAmount': 500.0,
      'status': 'completed',
      'orderTime': '2024-01-26 13:15',
      'estimatedTime': 0,
      'paymentMethod': 'Online',
      'isPaid': true,
    },
  ];

  // Earnings Data
  static final List<Map<String, dynamic>> earningsData = [
    {
      'date': '2024-01-26',
      'orders': 28,
      'earnings': 4250.0,
      'commission': 425.0,
      'netEarnings': 3825.0,
    },
    {
      'date': '2024-01-25',
      'orders': 32,
      'earnings': 4800.0,
      'commission': 480.0,
      'netEarnings': 4320.0,
    },
    {
      'date': '2024-01-24',
      'orders': 25,
      'earnings': 3750.0,
      'commission': 375.0,
      'netEarnings': 3375.0,
    },
    {
      'date': '2024-01-23',
      'orders': 30,
      'earnings': 4500.0,
      'commission': 450.0,
      'netEarnings': 4050.0,
    },
  ];

  // Categories
  static final List<String> categories = [
    'Main Course',
    'South Indian',
    'Regional',
    'Thali',
    'Appetizers',
    'Desserts',
    'Beverages',
  ];

  // Quick Stats
  static const Map<String, dynamic> quickStats = {
    'totalProducts': 45,
    'activeProducts': 38,
    'outOfStock': 7,
    'lowStock': 12,
    'avgRating': 4.5,
    'totalOrders': 1250,
    'monthlyEarnings': 125000.0,
  };

  // Notifications
  static final List<Map<String, dynamic>> notifications = [
    {
      'id': 'n1',
      'title': 'New Order Received',
      'message': 'Order #ORD001 from Rahul Sharma',
      'time': '2 minutes ago',
      'type': 'order',
      'isRead': false,
    },
    {
      'id': 'n2',
      'title': 'Low Stock Alert',
      'message': 'Fish Curry is out of stock',
      'time': '1 hour ago',
      'type': 'inventory',
      'isRead': false,
    },
    {
      'id': 'n3',
      'title': 'Payment Received',
      'message': '₹780 received for Order #ORD001',
      'time': '3 hours ago',
      'type': 'payment',
      'isRead': true,
    },
  ];
}
