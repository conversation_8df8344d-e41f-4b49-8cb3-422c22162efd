import 'package:flutter/material.dart';
import '../features/rider/domain/models/rider_profile.dart';
import '../features/rider/domain/models/rider_earnings.dart';
import '../features/rider/domain/models/rider_kyc.dart';
import '../features/rider/domain/models/rider_analytics.dart';

// Enhanced models for competitive order assignment system
enum OrderAssignmentStatus { broadcasted, assigned, expired, reassigned }

enum DeliveryStatus {
  pending,
  accepted,
  enRoutePickup,
  arrivedPickup,
  pickedUp,
  enRouteDelivery,
  arrivedDelivery,
  deliveredConfirmed,
  completed,
  cancelled,
}

class AvailableOrder {
  final String id;
  final String restaurantName;
  final String restaurantAddress;
  final String restaurantPhone;
  final String customerName;
  final String customerAddress;
  final String customerPhone;
  final double distanceKm;
  final double deliveryFee;
  final double orderValue;
  final DateTime broadcastTime;
  final int acceptanceTimeoutSeconds;
  final List<OrderItem> items;
  final String specialInstructions;
  final bool isCashOnDelivery;
  final double? cashAmount;
  final String otp;
  final double restaurantLat;
  final double restaurantLng;
  final double customerLat;
  final double customerLng;
  final int estimatedCompletionMinutes;
  final OrderAssignmentStatus assignmentStatus;

  AvailableOrder({
    required this.id,
    required this.restaurantName,
    required this.restaurantAddress,
    required this.restaurantPhone,
    required this.customerName,
    required this.customerAddress,
    required this.customerPhone,
    required this.distanceKm,
    required this.deliveryFee,
    required this.orderValue,
    required this.broadcastTime,
    required this.acceptanceTimeoutSeconds,
    required this.items,
    required this.specialInstructions,
    required this.isCashOnDelivery,
    this.cashAmount,
    required this.otp,
    required this.restaurantLat,
    required this.restaurantLng,
    required this.customerLat,
    required this.customerLng,
    required this.estimatedCompletionMinutes,
    required this.assignmentStatus,
  });
}

class OrderItem {
  final String name;
  final int quantity;
  final double price;
  final bool isVeg;

  OrderItem({
    required this.name,
    required this.quantity,
    required this.price,
    required this.isVeg,
  });
}

class ActiveDelivery {
  final String id;
  final AvailableOrder order;
  final DeliveryStatus status;
  final DateTime acceptedTime;
  final DateTime? pickedUpTime;
  final DateTime? deliveredTime;
  final List<String> photoProofs;
  final List<GPSPoint> routeHistory;
  final String? customerSignature;
  final double? customerRating;
  final String? customerFeedback;
  final bool otpVerified;

  ActiveDelivery({
    required this.id,
    required this.order,
    required this.status,
    required this.acceptedTime,
    this.pickedUpTime,
    this.deliveredTime,
    required this.photoProofs,
    required this.routeHistory,
    this.customerSignature,
    this.customerRating,
    this.customerFeedback,
    required this.otpVerified,
  });
}

class GPSPoint {
  final double latitude;
  final double longitude;
  final DateTime timestamp;
  final double? accuracy;
  final double? speed;

  GPSPoint({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    this.accuracy,
    this.speed,
  });
}

class TripHistory {
  final String id;
  final AvailableOrder order;
  final DateTime startTime;
  final DateTime endTime;
  final double totalDistance;
  final double earnings;
  final double customerRating;
  final String customerFeedback;
  final List<String> photoProofs;
  final List<GPSPoint> completeRoute;
  final Duration totalDuration;
  final Duration pickupDuration;
  final Duration deliveryDuration;

  TripHistory({
    required this.id,
    required this.order,
    required this.startTime,
    required this.endTime,
    required this.totalDistance,
    required this.earnings,
    required this.customerRating,
    required this.customerFeedback,
    required this.photoProofs,
    required this.completeRoute,
    required this.totalDuration,
    required this.pickupDuration,
    required this.deliveryDuration,
  });
}

// Dual-mode system support
enum RiderMode { foodDelivery, rideSharing }

// Ride-sharing specific models
class RideRequest {
  final String id;
  final String passengerId;
  final String passengerName;
  final String passengerPhone;
  final String pickupLocation;
  final String dropoffLocation;
  final double pickupLat;
  final double pickupLng;
  final double dropoffLat;
  final double dropoffLng;
  final double distance;
  final double fare;
  final DateTime requestTime;
  final int acceptanceTimeoutSeconds;
  final String specialInstructions;
  final bool isScheduled;
  final DateTime? scheduledTime;
  final String routeName;

  const RideRequest({
    required this.id,
    required this.passengerId,
    required this.passengerName,
    required this.passengerPhone,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.pickupLat,
    required this.pickupLng,
    required this.dropoffLat,
    required this.dropoffLng,
    required this.distance,
    required this.fare,
    required this.requestTime,
    required this.acceptanceTimeoutSeconds,
    required this.specialInstructions,
    required this.isScheduled,
    this.scheduledTime,
    required this.routeName,
  });
}

enum RideStatus {
  requested,
  accepted,
  enRoutePickup,
  arrivedPickup,
  passengerPickedUp,
  enRouteDropoff,
  arrivedDropoff,
  completed,
  cancelled,
}

class ActiveRide {
  final String id;
  final RideRequest request;
  final RideStatus status;
  final DateTime acceptedTime;
  final List<GPSPoint> routeHistory;
  final bool passengerConfirmed;
  final String? completionCode;

  const ActiveRide({
    required this.id,
    required this.request,
    required this.status,
    required this.acceptedTime,
    required this.routeHistory,
    required this.passengerConfirmed,
    this.completionCode,
  });
}

class RiderDemoData {
  // Current rider location (Guwahati coordinates)
  static const double currentRiderLat = 26.1445;
  static const double currentRiderLng = 91.7362;

  // Available orders for competitive assignment
  static final List<AvailableOrder> availableOrders = [
    AvailableOrder(
      id: 'ORD001',
      restaurantName: 'Spice Garden Restaurant',
      restaurantAddress: '123 Food Street, Paltan Bazaar, Guwahati',
      restaurantPhone: '+91 98765 43210',
      customerName: 'Rahul Sharma',
      customerAddress: '45 MG Road, Fancy Bazaar, Guwahati',
      customerPhone: '+91 98765 12345',
      distanceKm: 2.3,
      deliveryFee: 35.0,
      orderValue: 450.0,
      broadcastTime: DateTime.now().subtract(const Duration(seconds: 15)),
      acceptanceTimeoutSeconds: 60,
      items: [
        OrderItem(
          name: 'Chicken Biryani',
          quantity: 2,
          price: 280.0,
          isVeg: false,
        ),
        OrderItem(
          name: 'Paneer Butter Masala',
          quantity: 1,
          price: 220.0,
          isVeg: true,
        ),
      ],
      specialInstructions: 'Extra spicy, no onions',
      isCashOnDelivery: false,
      otp: '8834',
      restaurantLat: 26.1547,
      restaurantLng: 91.7417,
      customerLat: 26.1584,
      customerLng: 91.7539,
      estimatedCompletionMinutes: 35,
      assignmentStatus: OrderAssignmentStatus.broadcasted,
    ),
    AvailableOrder(
      id: 'ORD002',
      restaurantName: 'Assam Kitchen',
      restaurantAddress: '78 Zoo Road, Guwahati',
      restaurantPhone: '+91 87654 32109',
      customerName: 'Priya Das',
      customerAddress: '12 Ulubari, Guwahati',
      customerPhone: '+91 87654 21098',
      distanceKm: 3.7,
      deliveryFee: 45.0,
      orderValue: 320.0,
      broadcastTime: DateTime.now().subtract(const Duration(seconds: 8)),
      acceptanceTimeoutSeconds: 60,
      items: [
        OrderItem(name: 'Fish Curry', quantity: 1, price: 320.0, isVeg: false),
      ],
      specialInstructions: 'Call before delivery',
      isCashOnDelivery: true,
      cashAmount: 365.0,
      otp: '5521',
      restaurantLat: 26.1693,
      restaurantLng: 91.7479,
      customerLat: 26.1809,
      customerLng: 91.7540,
      estimatedCompletionMinutes: 28,
      assignmentStatus: OrderAssignmentStatus.broadcasted,
    ),
    AvailableOrder(
      id: 'ORD003',
      restaurantName: 'South Indian Corner',
      restaurantAddress: '56 GS Road, Guwahati',
      restaurantPhone: '+91 76543 21098',
      customerName: 'Amit Kumar',
      customerAddress: '89 Christian Basti, Guwahati',
      customerPhone: '+91 76543 10987',
      distanceKm: 4.2,
      deliveryFee: 50.0,
      orderValue: 280.0,
      broadcastTime: DateTime.now().subtract(const Duration(seconds: 25)),
      acceptanceTimeoutSeconds: 60,
      items: [
        OrderItem(name: 'Masala Dosa', quantity: 3, price: 120.0, isVeg: true),
        OrderItem(name: 'Filter Coffee', quantity: 2, price: 40.0, isVeg: true),
      ],
      specialInstructions: 'Ring doorbell twice',
      isCashOnDelivery: false,
      otp: '7743',
      restaurantLat: 26.1433,
      restaurantLng: 91.7898,
      customerLat: 26.1891,
      customerLng: 91.7649,
      estimatedCompletionMinutes: 42,
      assignmentStatus: OrderAssignmentStatus.broadcasted,
    ),
  ];

  // Active deliveries
  static final List<ActiveDelivery> activeDeliveries = [
    ActiveDelivery(
      id: 'DEL001',
      order: AvailableOrder(
        id: 'ORD004',
        restaurantName: 'Pizza Palace',
        restaurantAddress: '34 Hatigaon, Guwahati',
        restaurantPhone: '+91 65432 10987',
        customerName: 'Sneha Gogoi',
        customerAddress: '67 Beltola, Guwahati',
        customerPhone: '+91 65432 09876',
        distanceKm: 5.1,
        deliveryFee: 60.0,
        orderValue: 680.0,
        broadcastTime: DateTime.now().subtract(const Duration(minutes: 15)),
        acceptanceTimeoutSeconds: 60,
        items: [
          OrderItem(
            name: 'Margherita Pizza',
            quantity: 1,
            price: 350.0,
            isVeg: true,
          ),
          OrderItem(
            name: 'Chicken Wings',
            quantity: 1,
            price: 280.0,
            isVeg: false,
          ),
          OrderItem(
            name: 'Garlic Bread',
            quantity: 1,
            price: 120.0,
            isVeg: true,
          ),
        ],
        specialInstructions: 'Extra cheese on pizza',
        isCashOnDelivery: true,
        cashAmount: 740.0,
        otp: '9876',
        restaurantLat: 26.1298,
        restaurantLng: 91.7509,
        customerLat: 26.1067,
        customerLng: 91.7791,
        estimatedCompletionMinutes: 45,
        assignmentStatus: OrderAssignmentStatus.assigned,
      ),
      status: DeliveryStatus.enRouteDelivery,
      acceptedTime: DateTime.now().subtract(const Duration(minutes: 15)),
      pickedUpTime: DateTime.now().subtract(const Duration(minutes: 8)),
      photoProofs: ['pickup_photo_001.jpg'],
      routeHistory: [
        GPSPoint(
          latitude: 26.1445,
          longitude: 91.7362,
          timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
          accuracy: 5.0,
          speed: 0.0,
        ),
        GPSPoint(
          latitude: 26.1298,
          longitude: 91.7509,
          timestamp: DateTime.now().subtract(const Duration(minutes: 8)),
          accuracy: 3.0,
          speed: 25.0,
        ),
      ],
      otpVerified: false,
    ),
  ];

  // Demo Rider Profiles
  static final List<RiderProfile> riderProfiles = [
    RiderProfile(
      id: 'rider_001',
      firstName: 'Rajesh',
      lastName: 'Kumar',
      phoneNumber: '+91 ********10',
      email: '<EMAIL>',
      profileImageUrl:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      dateOfBirth: DateTime(1985, 6, 15),
      address: 'House No. 123, Sector 15',
      city: 'Guwahati',
      state: 'Assam',
      pincode: '781001',
      status: RiderStatus.active,
      kycStatus: KYCStatus.approved,
      vehicleInfo: VehicleInfo(
        vehicleType: 'Motorcycle',
        make: 'Honda',
        model: 'Activa 6G',
        year: '2022',
        registrationNumber: 'AS01AB1234',
        color: 'Red',
        vehicleImages: [
          'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300',
          'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300',
        ],
        insuranceNumber: 'INS123456789',
        insuranceExpiry: DateTime(2024, 12, 31),
        rcNumber: 'RC123456789',
        rcExpiry: DateTime(2025, 6, 15),
        pollutionCertNumber: 'PUC123456',
        pollutionExpiry: DateTime(2024, 3, 15),
      ),
      bankDetails: BankDetails(
        accountHolderName: 'Rajesh Kumar',
        accountNumber: '****************',
        ifscCode: 'SBIN0001234',
        bankName: 'State Bank of India',
        branchName: 'Guwahati Main Branch',
        isVerified: true,
        upiId: 'rajesh@paytm',
      ),
      rating: 4.8,
      totalRides: 1247,
      joinedDate: DateTime(2023, 1, 15),
      isOnline: true,
      currentLocation: 'Fancy Bazaar, Guwahati',
      languages: ['Hindi', 'Assamese', 'English'],
      preferences: RiderPreferences(
        acceptSharedRides: true,
        acceptLongDistanceRides: false,
        preferredAreas: ['Fancy Bazaar', 'Pan Bazaar', 'Paltan Bazaar'],
        workingHoursStart: '08:00',
        workingHoursEnd: '22:00',
        workingDays: [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
        ],
        enableNotifications: true,
        enableLocationTracking: true,
        preferredLanguage: 'Assamese',
      ),
    ),
    RiderProfile(
      id: 'rider_002',
      firstName: 'Priya',
      lastName: 'Sharma',
      phoneNumber: '+91 ********11',
      email: '<EMAIL>',
      profileImageUrl:
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      dateOfBirth: DateTime(1990, 3, 22),
      address: 'Flat 4B, Green Valley Apartments',
      city: 'Guwahati',
      state: 'Assam',
      pincode: '781005',
      status: RiderStatus.active,
      kycStatus: KYCStatus.approved,
      vehicleInfo: VehicleInfo(
        vehicleType: 'Scooter',
        make: 'TVS',
        model: 'Jupiter',
        year: '2023',
        registrationNumber: 'AS01CD5678',
        color: 'Blue',
        vehicleImages: [
          'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300',
        ],
        insuranceNumber: 'INS********1',
        insuranceExpiry: DateTime(2025, 1, 31),
        rcNumber: 'RC********1',
        rcExpiry: DateTime(2025, 3, 22),
        pollutionCertNumber: 'PUC987654',
        pollutionExpiry: DateTime(2024, 6, 22),
      ),
      bankDetails: BankDetails(
        accountHolderName: 'Priya Sharma',
        accountNumber: '********10987654',
        ifscCode: 'HDFC0001234',
        bankName: 'HDFC Bank',
        branchName: 'Guwahati Branch',
        isVerified: true,
        upiId: 'priya@phonepe',
      ),
      rating: 4.9,
      totalRides: 892,
      joinedDate: DateTime(2023, 3, 10),
      isOnline: false,
      currentLocation: 'Hatigaon, Guwahati',
      languages: ['Hindi', 'Assamese', 'English', 'Bengali'],
      preferences: RiderPreferences(
        acceptSharedRides: true,
        acceptLongDistanceRides: true,
        preferredAreas: ['Hatigaon', 'Beltola', 'Garchuk'],
        workingHoursStart: '09:00',
        workingHoursEnd: '21:00',
        workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        enableNotifications: true,
        enableLocationTracking: true,
        preferredLanguage: 'Hindi',
      ),
    ),
  ];

  // Demo Earnings Data
  static final Map<String, RiderEarnings> riderEarnings = {
    'rider_001': RiderEarnings(
      riderId: 'rider_001',
      totalEarnings: 45678.50,
      todayEarnings: 1250.75,
      weeklyEarnings: 8750.25,
      monthlyEarnings: 35420.80,
      availableBalance: 12450.30,
      pendingAmount: 2340.50,
      totalWithdrawn: 30887.70,
      transactions: [
        EarningsTransaction(
          id: 'txn_001',
          rideId: 'ride_001',
          type: TransactionType.rideEarning,
          amount: 145.50,
          commission: 32.01,
          netAmount: 113.49,
          timestamp: DateTime.now().subtract(Duration(hours: 2)),
          description: 'Ride from Fancy Bazaar to Airport',
          status: TransactionStatus.completed,
          metadata: {'rideType': 'Economy', 'distance': '12.5 km'},
        ),
        EarningsTransaction(
          id: 'txn_002',
          rideId: 'ride_002',
          type: TransactionType.bonus,
          amount: 50.00,
          commission: 0.00,
          netAmount: 50.00,
          timestamp: DateTime.now().subtract(Duration(hours: 4)),
          description: 'Peak hour bonus',
          status: TransactionStatus.completed,
          metadata: {'bonusType': 'peakHour', 'multiplier': '1.5x'},
        ),
      ],
      commissionHistory: [
        CommissionBreakdown(
          rideId: 'ride_001',
          baseFare: 50.00,
          distanceCharge: 75.00,
          timeCharge: 20.50,
          surgeMultiplier: 1.0,
          totalFare: 145.50,
          platformCommission: 32.01,
          riderEarning: 113.49,
          bonus: 0.00,
          incentive: 0.00,
          penalty: 0.00,
          netEarning: 113.49,
          timestamp: DateTime.now().subtract(Duration(hours: 2)),
          rideType: 'Economy',
        ),
      ],
      taxInfo: TaxInfo(
        totalTaxableIncome: 45678.50,
        tdsDeducted: 4567.85,
        gstCollected: 8221.13,
        panNumber: '**********',
        isTaxExempt: false,
        taxDocuments: [
          TaxDocument(
            id: 'tax_001',
            type: 'Form 16',
            url: 'https://example.com/form16.pdf',
            generatedDate: DateTime(2024, 3, 31),
            financialYear: '2023-24',
          ),
        ],
      ),
      lastUpdated: DateTime.now(),
      stats: EarningsStats(
        averageEarningPerRide: 36.64,
        averageEarningPerHour: 125.50,
        averageEarningPerDay: 1420.80,
        totalRidesCompleted: 1247,
        totalDistanceCovered: 15678.5,
        totalTimeOnline: 364.2,
        peakHourEarnings: 18567.40,
        offPeakEarnings: 27111.10,
        earningsByRideType: {
          'Economy': 25678.30,
          'Premium': 15432.20,
          'Shared': 4568.00,
        },
        earningsByDay: {
          'Monday': 6543.20,
          'Tuesday': 7234.50,
          'Wednesday': 6789.30,
          'Thursday': 7456.80,
          'Friday': 8234.70,
          'Saturday': 9420.00,
          'Sunday': 0.00,
        },
      ),
    ),
  };

  // Demo KYC Data
  static final Map<String, RiderKYC> riderKYCData = {
    'rider_001': RiderKYC(
      riderId: 'rider_001',
      status: KYCStatus.approved,
      documents: [
        KYCDocument(
          id: 'doc_001',
          type: DocumentType.aadhaar,
          documentNumber: '1234-5678-9012',
          imageUrls: [
            'https://example.com/aadhaar_front.jpg',
            'https://example.com/aadhaar_back.jpg',
          ],
          status: DocumentStatus.verified,
          uploadedAt: DateTime(2023, 1, 16),
          verifiedAt: DateTime(2023, 1, 17),
          extractedData: {
            'name': 'Rajesh Kumar',
            'dob': '15/06/1985',
            'address': 'House No. 123, Sector 15, Guwahati, Assam - 781001',
          },
          confidenceScore: 0.98,
        ),
        KYCDocument(
          id: 'doc_002',
          type: DocumentType.pan,
          documentNumber: '**********',
          imageUrls: ['https://example.com/pan.jpg'],
          status: DocumentStatus.verified,
          uploadedAt: DateTime(2023, 1, 16),
          verifiedAt: DateTime(2023, 1, 17),
          extractedData: {
            'name': 'RAJESH KUMAR',
            'panNumber': '**********',
            'dob': '15/06/1985',
          },
          confidenceScore: 0.95,
        ),
        KYCDocument(
          id: 'doc_003',
          type: DocumentType.drivingLicense,
          documentNumber: 'AS0120230001234',
          imageUrls: [
            'https://example.com/dl_front.jpg',
            'https://example.com/dl_back.jpg',
          ],
          status: DocumentStatus.verified,
          uploadedAt: DateTime(2023, 1, 16),
          verifiedAt: DateTime(2023, 1, 18),
          extractedData: {
            'name': 'Rajesh Kumar',
            'licenseNumber': 'AS0120230001234',
            'validUpto': '14/06/2043',
            'vehicleClass': 'MCWG',
          },
          confidenceScore: 0.92,
        ),
      ],
      submittedAt: DateTime(2023, 1, 16),
      reviewedAt: DateTime(2023, 1, 18),
      approvedAt: DateTime(2023, 1, 18),
      pendingDocuments: [],
      completionPercentage: 100.0,
      reviewer: KYCReviewer(
        id: 'reviewer_001',
        name: 'Admin User',
        email: '<EMAIL>',
        reviewedAt: DateTime(2023, 1, 18),
      ),
      comments: [
        KYCComment(
          id: 'comment_001',
          message: 'All documents verified successfully. Welcome to Projek!',
          authorId: 'reviewer_001',
          authorName: 'Admin User',
          timestamp: DateTime(2023, 1, 18),
          type: CommentType.approval,
        ),
      ],
    ),
  };

  // Demo Withdrawal Requests
  static final List<WithdrawalRequest> withdrawalRequests = [
    WithdrawalRequest(
      id: 'withdrawal_001',
      riderId: 'rider_001',
      amount: 5000.00,
      method: WithdrawalMethod.bankTransfer,
      status: WithdrawalStatus.completed,
      requestedAt: DateTime.now().subtract(Duration(days: 2)),
      processedAt: DateTime.now().subtract(Duration(days: 1)),
      transactionId: 'TXN123456789',
      processingFee: 25.00,
      netAmount: 4975.00,
      paymentDetails: {
        'accountNumber': '****6789',
        'bankName': 'State Bank of India',
        'ifscCode': 'SBIN0001234',
      },
    ),
    WithdrawalRequest(
      id: 'withdrawal_002',
      riderId: 'rider_001',
      amount: 2500.00,
      method: WithdrawalMethod.upi,
      status: WithdrawalStatus.processing,
      requestedAt: DateTime.now().subtract(Duration(hours: 6)),
      processingFee: 10.00,
      netAmount: 2490.00,
      paymentDetails: {'upiId': 'rajesh@paytm'},
    ),
  ];

  // Demo Bonus & Incentives
  static final List<BonusIncentive> bonusIncentives = [
    BonusIncentive(
      id: 'bonus_001',
      riderId: 'rider_001',
      type: BonusType.peakHour,
      title: 'Peak Hour Bonus',
      description: 'Extra earnings during high-demand hours (6-10 AM, 6-10 PM)',
      amount: 50.00,
      status: BonusStatus.earned,
      validFrom: DateTime.now().subtract(Duration(hours: 8)),
      validUntil: DateTime.now().add(Duration(hours: 16)),
      criteria: {
        'timeSlots': ['06:00-10:00', '18:00-22:00'],
        'minimumRides': 3,
      },
      progress: 100.0,
      earnedAt: DateTime.now().subtract(Duration(hours: 2)),
    ),
    BonusIncentive(
      id: 'bonus_002',
      riderId: 'rider_001',
      type: BonusType.dailyTarget,
      title: 'Daily Target Bonus',
      description: 'Complete 15 rides today to earn ₹200 bonus',
      amount: 200.00,
      status: BonusStatus.active,
      validFrom: DateTime.now().subtract(Duration(hours: 12)),
      validUntil: DateTime.now().add(Duration(hours: 12)),
      criteria: {'targetRides': 15, 'currentRides': 8},
      progress: 53.3,
    ),
    BonusIncentive(
      id: 'bonus_003',
      riderId: 'rider_001',
      type: BonusType.ratingBonus,
      title: 'Rating Excellence Bonus',
      description: 'Maintain 4.8+ rating for a week to earn ₹500',
      amount: 500.00,
      status: BonusStatus.active,
      validFrom: DateTime.now().subtract(Duration(days: 5)),
      validUntil: DateTime.now().add(Duration(days: 2)),
      criteria: {
        'minimumRating': 4.8,
        'currentRating': 4.8,
        'daysCompleted': 5,
        'targetDays': 7,
      },
      progress: 71.4,
    ),
  ];

  // Demo Analytics Data
  static final Map<String, RiderAnalytics> riderAnalytics = {
    'rider_001': RiderAnalytics(
      riderId: 'rider_001',
      performance: PerformanceMetrics(
        overallRating: 4.8,
        acceptanceRate: 92.5,
        completionRate: 98.2,
        cancellationRate: 1.8,
        onTimePercentage: 94.7,
        averageResponseTime: 45.2,
        totalRidesCompleted: 1247,
        totalRidesCancelled: 23,
        customerSatisfactionScore: 4.7,
        trends: [
          PerformanceTrend(
            date: DateTime.now().subtract(Duration(days: 6)),
            rating: 4.7,
            ridesCompleted: 18,
            earnings: 1420.50,
            acceptanceRate: 90.0,
          ),
          PerformanceTrend(
            date: DateTime.now().subtract(Duration(days: 5)),
            rating: 4.8,
            ridesCompleted: 22,
            earnings: 1680.75,
            acceptanceRate: 95.0,
          ),
        ],
      ),
      rideStats: RideStatistics(
        totalRides: 1247,
        todayRides: 8,
        weeklyRides: 142,
        monthlyRides: 567,
        totalDistance: 15678.5,
        averageRideDistance: 12.6,
        totalDuration: 2456.8,
        averageRideDuration: 18.5,
        ridesByType: {'Economy': 856, 'Premium': 234, 'Shared': 157},
        ridesByHour: {
          '08': 45,
          '09': 67,
          '10': 52,
          '18': 89,
          '19': 78,
          '20': 65,
        },
        ridesByDay: {
          'Monday': 178,
          'Tuesday': 189,
          'Wednesday': 167,
          'Thursday': 201,
          'Friday': 234,
          'Saturday': 278,
          'Sunday': 0,
        },
        popularRoutes: [
          PopularRoute(
            fromLocation: 'Fancy Bazaar',
            toLocation: 'Airport',
            rideCount: 45,
            averageEarning: 145.50,
            averageDuration: 35.2,
          ),
          PopularRoute(
            fromLocation: 'Railway Station',
            toLocation: 'ISBT',
            rideCount: 38,
            averageEarning: 89.25,
            averageDuration: 22.8,
          ),
        ],
      ),
      earnings: EarningsAnalytics(
        totalEarnings: 45678.50,
        averageEarningPerRide: 36.64,
        averageEarningPerHour: 125.50,
        peakHourEarnings: 18567.40,
        offPeakEarnings: 27111.10,
        earningsByHour: {
          '08': 2345.60,
          '09': 3456.70,
          '18': 4567.80,
          '19': 3890.45,
        },
        earningsByDay: {
          'Monday': 6543.20,
          'Tuesday': 7234.50,
          'Wednesday': 6789.30,
        },
        earningsByRideType: {
          'Economy': 25678.30,
          'Premium': 15432.20,
          'Shared': 4568.00,
        },
        trends: [
          EarningsTrend(
            date: DateTime.now().subtract(Duration(days: 6)),
            earnings: 1420.50,
            rides: 18,
            hoursOnline: 11.5,
          ),
        ],
      ),
      feedback: CustomerFeedback(
        averageRating: 4.8,
        totalReviews: 1156,
        ratingDistribution: {5: 789, 4: 267, 3: 78, 2: 15, 1: 7},
        positiveComments: [
          'Very punctual and polite driver',
          'Clean vehicle and safe driving',
          'Knows all the shortcuts in the city',
        ],
        negativeComments: ['Took a longer route', 'Vehicle was not very clean'],
        feedbackCategories: {
          'Punctuality': 945,
          'Cleanliness': 823,
          'Behavior': 967,
          'Driving': 889,
        },
        recentReviews: [
          CustomerReview(
            id: 'review_001',
            customerId: 'user_001',
            customerName: 'Amit Singh',
            rating: 5,
            comment: 'Excellent service! Very professional driver.',
            timestamp: DateTime.now().subtract(Duration(hours: 2)),
            rideId: 'ride_001',
          ),
        ],
      ),
      location: LocationAnalytics(
        hotspots: [
          HotspotArea(
            name: 'Fancy Bazaar',
            latitude: 26.1833,
            longitude: 91.7333,
            radius: 2.0,
            rideCount: 234,
            averageEarning: 125.50,
            peakHours: ['08:00-10:00', '18:00-20:00'],
          ),
        ],
        ridesByArea: {
          'Fancy Bazaar': 234,
          'Pan Bazaar': 189,
          'Paltan Bazaar': 156,
        },
        earningsByArea: {
          'Fancy Bazaar': 12456.80,
          'Pan Bazaar': 9876.50,
          'Paltan Bazaar': 7654.30,
        },
        recommendedAreas: ['Fancy Bazaar', 'Airport Road', 'GS Road'],
        totalDistanceCovered: 15678.5,
      ),
      timeAnalytics: TimeAnalytics(
        totalOnlineHours: 364.2,
        averageOnlineHoursPerDay: 8.5,
        onlineHoursByDay: {'Monday': 8.5, 'Tuesday': 9.2, 'Wednesday': 8.0},
        onlineHoursByHour: {'08': 25.5, '09': 28.7, '18': 32.4},
        mostActiveHours: ['08:00-10:00', '18:00-20:00'],
        leastActiveHours: ['02:00-06:00'],
        utilizationRate: 78.5,
      ),
      lastUpdated: DateTime.now(),
    ),
  };

  // Helper methods
  static RiderProfile? getRiderProfile(String riderId) {
    return riderProfiles.firstWhere(
      (profile) => profile.id == riderId,
      orElse: () => riderProfiles.first,
    );
  }

  static RiderEarnings? getRiderEarnings(String riderId) {
    return riderEarnings[riderId];
  }

  static RiderKYC? getRiderKYC(String riderId) {
    return riderKYCData[riderId];
  }

  static RiderAnalytics? getRiderAnalytics(String riderId) {
    return riderAnalytics[riderId];
  }

  static List<WithdrawalRequest> getRiderWithdrawals(String riderId) {
    return withdrawalRequests.where((w) => w.riderId == riderId).toList();
  }

  static List<BonusIncentive> getRiderBonuses(String riderId) {
    return bonusIncentives.where((b) => b.riderId == riderId).toList();
  }

  // Vehicle Types for Registration
  static const List<Map<String, dynamic>> vehicleTypes = [
    {
      'id': 'motorcycle',
      'name': 'Motorcycle',
      'icon': Icons.motorcycle,
      'description': 'Two-wheeler delivery vehicle',
      'capacity': '1 passenger',
      'fuelType': 'Petrol',
    },
    {
      'id': 'scooter',
      'name': 'Scooter',
      'icon': Icons.electric_scooter,
      'description': 'Automatic scooter for city rides',
      'capacity': '1 passenger',
      'fuelType': 'Petrol/Electric',
    },
    {
      'id': 'auto',
      'name': 'Auto Rickshaw',
      'icon': Icons.directions_car,
      'description': 'Three-wheeler for short distances',
      'capacity': '3 passengers',
      'fuelType': 'CNG/Petrol',
    },
    {
      'id': 'car',
      'name': 'Car',
      'icon': Icons.car_rental,
      'description': 'Four-wheeler for comfortable rides',
      'capacity': '4 passengers',
      'fuelType': 'Petrol/Diesel/CNG',
    },
  ];

  // Popular Vehicle Brands
  static const Map<String, List<String>> vehicleBrands = {
    'motorcycle': ['Honda', 'Hero', 'Bajaj', 'TVS', 'Yamaha', 'Royal Enfield'],
    'scooter': ['Honda', 'TVS', 'Suzuki', 'Hero', 'Yamaha', 'Ather'],
    'auto': ['Bajaj', 'Mahindra', 'Piaggio', 'Force Motors'],
    'car': ['Maruti Suzuki', 'Hyundai', 'Tata', 'Honda', 'Toyota', 'Mahindra'],
  };

  // Working Areas in Guwahati
  static const List<String> workingAreas = [
    'Fancy Bazaar',
    'Pan Bazaar',
    'Paltan Bazaar',
    'Uzan Bazaar',
    'Hatigaon',
    'Beltola',
    'Garchuk',
    'Khanapara',
    'Dispur',
    'Ganeshguri',
    'Sixmile',
    'Jalukbari',
    'Adabari',
    'Maligaon',
    'Chandmari',
    'Zoo Road',
    'GS Road',
    'Airport Road',
    'NH Road',
    'Rajgarh Road',
  ];

  // Languages supported
  static const List<Map<String, String>> supportedLanguages = [
    {'code': 'en', 'name': 'English', 'nativeName': 'English'},
    {'code': 'hi', 'name': 'Hindi', 'nativeName': 'हिन्दी'},
    {'code': 'as', 'name': 'Assamese', 'nativeName': 'অসমীয়া'},
    {'code': 'bn', 'name': 'Bengali', 'nativeName': 'বাংলা'},
  ];

  // Bank List for India
  static const List<Map<String, String>> indianBanks = [
    {'name': 'State Bank of India', 'code': 'SBI'},
    {'name': 'HDFC Bank', 'code': 'HDFC'},
    {'name': 'ICICI Bank', 'code': 'ICICI'},
    {'name': 'Axis Bank', 'code': 'AXIS'},
    {'name': 'Punjab National Bank', 'code': 'PNB'},
    {'name': 'Bank of Baroda', 'code': 'BOB'},
    {'name': 'Canara Bank', 'code': 'CANARA'},
    {'name': 'Union Bank of India', 'code': 'UNION'},
    {'name': 'Bank of India', 'code': 'BOI'},
    {'name': 'Indian Bank', 'code': 'INDIAN'},
    {'name': 'Central Bank of India', 'code': 'CENTRAL'},
    {'name': 'Indian Overseas Bank', 'code': 'IOB'},
    {'name': 'UCO Bank', 'code': 'UCO'},
    {'name': 'Bank of Maharashtra', 'code': 'BOM'},
    {'name': 'Punjab & Sind Bank', 'code': 'PSB'},
  ];

  // Trip history data
  static final List<TripHistory> tripHistory = [
    TripHistory(
      id: 'TRIP001',
      order: AvailableOrder(
        id: 'ORD005',
        restaurantName: 'Burger Junction',
        restaurantAddress: '12 Pan Bazaar, Guwahati',
        restaurantPhone: '+91 98765 11111',
        customerName: 'Ravi Sharma',
        customerAddress: '45 Rehabari, Guwahati',
        customerPhone: '+91 98765 22222',
        distanceKm: 3.2,
        deliveryFee: 40.0,
        orderValue: 380.0,
        broadcastTime: DateTime.now().subtract(const Duration(hours: 2)),
        acceptanceTimeoutSeconds: 60,
        items: [
          OrderItem(
            name: 'Chicken Burger',
            quantity: 2,
            price: 180.0,
            isVeg: false,
          ),
          OrderItem(
            name: 'French Fries',
            quantity: 1,
            price: 80.0,
            isVeg: true,
          ),
        ],
        specialInstructions: 'No mayo',
        isCashOnDelivery: false,
        otp: '1234',
        restaurantLat: 26.1584,
        restaurantLng: 91.7539,
        customerLat: 26.1693,
        customerLng: 91.7479,
        estimatedCompletionMinutes: 30,
        assignmentStatus: OrderAssignmentStatus.assigned,
      ),
      startTime: DateTime.now().subtract(const Duration(hours: 2)),
      endTime: DateTime.now().subtract(const Duration(hours: 1, minutes: 32)),
      totalDistance: 6.4,
      earnings: 40.0,
      customerRating: 4.8,
      customerFeedback: 'Very fast delivery, food was hot!',
      photoProofs: ['pickup_001.jpg', 'delivery_001.jpg'],
      completeRoute: [
        GPSPoint(
          latitude: 26.1445,
          longitude: 91.7362,
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        GPSPoint(
          latitude: 26.1584,
          longitude: 91.7539,
          timestamp: DateTime.now().subtract(
            const Duration(hours: 1, minutes: 45),
          ),
        ),
        GPSPoint(
          latitude: 26.1693,
          longitude: 91.7479,
          timestamp: DateTime.now().subtract(
            const Duration(hours: 1, minutes: 32),
          ),
        ),
      ],
      totalDuration: const Duration(minutes: 28),
      pickupDuration: const Duration(minutes: 15),
      deliveryDuration: const Duration(minutes: 13),
    ),
  ];

  // Performance analytics
  static final Map<String, dynamic> performanceAnalytics = {
    'today': {
      'tripsCompleted': 8,
      'totalDistance': 45.6,
      'totalEarnings': 420.0,
      'averageRating': 4.6,
      'onTimeDeliveries': 7,
      'acceptanceRate': 85.0,
      'averageResponseTime': 12.5, // seconds
    },
    'thisWeek': {
      'tripsCompleted': 42,
      'totalDistance': 234.8,
      'totalEarnings': 2180.0,
      'averageRating': 4.5,
      'onTimeDeliveries': 38,
      'acceptanceRate': 82.0,
      'averageResponseTime': 14.2,
    },
    'peakHours': ['12:00-14:00', '19:00-21:00'],
    'averageDeliveryTime': 28.5, // minutes
  };

  // Emergency contacts
  static final List<Map<String, String>> emergencyContacts = [
    {
      'name': 'Customer Support',
      'number': '+91 1800 123 4567',
      'type': 'support',
    },
    {'name': 'Emergency Services', 'number': '112', 'type': 'emergency'},
    {
      'name': 'Rider Support',
      'number': '+91 1800 987 6543',
      'type': 'rider_support',
    },
  ];

  // Call history
  static final List<Map<String, dynamic>> callHistory = [
    {
      'id': 'CALL001',
      'orderId': 'ORD004',
      'contactName': 'Sneha Gogoi',
      'contactNumber': '+91 65432 09876',
      'callType': 'customer',
      'timestamp': DateTime.now().subtract(const Duration(minutes: 5)),
      'duration': const Duration(minutes: 2, seconds: 30),
      'status': 'completed',
    },
    {
      'id': 'CALL002',
      'orderId': 'ORD004',
      'contactName': 'Pizza Palace',
      'contactNumber': '+91 65432 10987',
      'callType': 'restaurant',
      'timestamp': DateTime.now().subtract(const Duration(minutes: 12)),
      'duration': const Duration(minutes: 1, seconds: 45),
      'status': 'completed',
    },
  ];

  // ========== RIDE-SHARING DATA ==========

  // Available ride requests for common Guwahati routes
  static final List<RideRequest> availableRideRequests = [
    RideRequest(
      id: 'RIDE001',
      passengerId: 'PASS001',
      passengerName: 'Priya Sharma',
      passengerPhone: '+91 98765 43210',
      pickupLocation: 'Guwahati Zoo Main Gate',
      dropoffLocation: 'Paltan Bazaar Bus Stand',
      pickupLat: 26.1733,
      pickupLng: 91.7667,
      dropoffLat: 26.1445,
      dropoffLng: 91.7362,
      distance: 4.2,
      fare: 85.0,
      requestTime: DateTime.now().subtract(const Duration(seconds: 25)),
      acceptanceTimeoutSeconds: 60,
      specialInstructions: 'Please wait near the main entrance',
      isScheduled: false,
      routeName: 'Zoo to Paltan Bazaar',
    ),
    RideRequest(
      id: 'RIDE002',
      passengerId: 'PASS002',
      passengerName: 'Rahul Das',
      passengerPhone: '+91 87654 32109',
      pickupLocation: 'Fancy Bazaar Metro Station',
      dropoffLocation: 'Ulubari Railway Station',
      pickupLat: 26.1667,
      pickupLng: 91.7500,
      dropoffLat: 26.1833,
      dropoffLng: 91.7500,
      distance: 2.8,
      fare: 65.0,
      requestTime: DateTime.now().subtract(const Duration(seconds: 45)),
      acceptanceTimeoutSeconds: 60,
      specialInstructions: 'Carrying one small bag',
      isScheduled: false,
      routeName: 'Fancy Bazaar to Ulubari',
    ),
    RideRequest(
      id: 'RIDE003',
      passengerId: 'PASS003',
      passengerName: 'Anita Gogoi',
      passengerPhone: '+91 76543 21098',
      pickupLocation: 'Hatigaon Chariali',
      dropoffLocation: 'Christian Basti',
      pickupLat: 26.1167,
      pickupLng: 91.8000,
      dropoffLat: 26.1500,
      dropoffLng: 91.7333,
      distance: 6.5,
      fare: 120.0,
      requestTime: DateTime.now().subtract(const Duration(seconds: 15)),
      acceptanceTimeoutSeconds: 60,
      specialInstructions: 'Need to reach by 3:30 PM',
      isScheduled: true,
      scheduledTime: DateTime.now().add(const Duration(minutes: 45)),
      routeName: 'Hatigaon to Christian Basti',
    ),
  ];

  // Active ride deliveries
  static final List<ActiveRide> activeRides = [
    ActiveRide(
      id: 'RIDE_ACTIVE001',
      request: RideRequest(
        id: 'RIDE004',
        passengerId: 'PASS004',
        passengerName: 'Bikash Kalita',
        passengerPhone: '+91 65432 10987',
        pickupLocation: 'GS Road, Bhangagarh',
        dropoffLocation: 'Zoo Road, Pan Bazaar',
        pickupLat: 26.1400,
        pickupLng: 91.7800,
        dropoffLat: 26.1700,
        dropoffLng: 91.7600,
        distance: 5.2,
        fare: 95.0,
        requestTime: DateTime.now().subtract(const Duration(minutes: 8)),
        acceptanceTimeoutSeconds: 60,
        specialInstructions: 'Office pickup - formal dress',
        isScheduled: false,
        routeName: 'GS Road to Zoo Road',
      ),
      status: RideStatus.enRoutePickup,
      acceptedTime: DateTime.now().subtract(const Duration(minutes: 7)),
      routeHistory: [
        GPSPoint(
          latitude: 26.1445,
          longitude: 91.7362,
          timestamp: DateTime.now().subtract(const Duration(minutes: 7)),
          accuracy: 5.0,
          speed: 0.0,
        ),
        GPSPoint(
          latitude: 26.1420,
          longitude: 91.7580,
          timestamp: DateTime.now().subtract(const Duration(minutes: 4)),
          accuracy: 4.2,
          speed: 25.5,
        ),
      ],
      passengerConfirmed: false,
    ),
  ];

  // Common Guwahati routes for ride-sharing
  static final List<Map<String, dynamic>> popularRoutes = [
    {
      'routeName': 'Zoo to Paltan Bazaar',
      'distance': 4.2,
      'estimatedTime': 15,
      'baseFare': 85.0,
      'popularity': 95,
    },
    {
      'routeName': 'Fancy Bazaar to Ulubari',
      'distance': 2.8,
      'estimatedTime': 12,
      'baseFare': 65.0,
      'popularity': 88,
    },
    {
      'routeName': 'Hatigaon to Christian Basti',
      'distance': 6.5,
      'estimatedTime': 22,
      'baseFare': 120.0,
      'popularity': 75,
    },
    {
      'routeName': 'GS Road to Zoo Road',
      'distance': 5.2,
      'estimatedTime': 18,
      'baseFare': 95.0,
      'popularity': 82,
    },
    {
      'routeName': 'Silpukhuri to Maligaon',
      'distance': 8.1,
      'estimatedTime': 28,
      'baseFare': 145.0,
      'popularity': 68,
    },
  ];

  // Ride-sharing performance analytics
  static final Map<String, dynamic> rideAnalytics = {
    'today': {
      'ridesCompleted': 12,
      'totalEarnings': 1240.0,
      'totalDistance': 58.4,
      'averageRating': 4.7,
      'totalRideTime': 185, // minutes
    },
    'week': {
      'ridesCompleted': 89,
      'totalEarnings': 8950.0,
      'totalDistance': 425.6,
      'averageRating': 4.6,
      'totalRideTime': 1340, // minutes
    },
    'month': {
      'ridesCompleted': 342,
      'totalEarnings': 34200.0,
      'totalDistance': 1680.2,
      'averageRating': 4.5,
      'totalRideTime': 5120, // minutes
    },
  };

  // Combined performance analytics (delivery + rides)
  static final Map<String, dynamic> combinedAnalytics = {
    'today': {
      'totalTrips':
          performanceAnalytics['today']['tripsCompleted'] +
          rideAnalytics['today']['ridesCompleted'],
      'totalEarnings':
          performanceAnalytics['today']['totalEarnings'] +
          rideAnalytics['today']['totalEarnings'],
      'totalDistance':
          performanceAnalytics['today']['totalDistance'] +
          rideAnalytics['today']['totalDistance'],
      'averageRating':
          ((performanceAnalytics['today']['averageRating'] +
              rideAnalytics['today']['averageRating']) /
          2),
    },
    'week': {
      'totalTrips':
          performanceAnalytics['week']['tripsCompleted'] +
          rideAnalytics['week']['ridesCompleted'],
      'totalEarnings':
          performanceAnalytics['week']['totalEarnings'] +
          rideAnalytics['week']['totalEarnings'],
      'totalDistance':
          performanceAnalytics['week']['totalDistance'] +
          rideAnalytics['week']['totalDistance'],
      'averageRating':
          ((performanceAnalytics['week']['averageRating'] +
              rideAnalytics['week']['averageRating']) /
          2),
    },
    'month': {
      'totalTrips':
          performanceAnalytics['month']['tripsCompleted'] +
          rideAnalytics['month']['ridesCompleted'],
      'totalEarnings':
          performanceAnalytics['month']['totalEarnings'] +
          rideAnalytics['month']['totalEarnings'],
      'totalDistance':
          performanceAnalytics['month']['totalDistance'] +
          rideAnalytics['month']['totalDistance'],
      'averageRating':
          ((performanceAnalytics['month']['averageRating'] +
              rideAnalytics['month']['averageRating']) /
          2),
    },
  };
}
