import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../features/auth/presentation/pages/phone_auth_page.dart';
import '../../features/auth/presentation/pages/uid_registration_page.dart';
import '../../features/auth/presentation/pages/modern_login_page.dart';
import '../../features/auth/presentation/pages/modern_register_page.dart';
// Removed unused imports for deleted features
import '../../shared/widgets/main_wrapper.dart';

import '../../features/marketplace/presentation/pages/categories_page.dart';
import '../../features/marketplace/presentation/pages/filtered_categories_page.dart';
import '../../features/marketplace/presentation/pages/product_detail_page.dart';
import '../../features/payment/presentation/pages/payment_page.dart';
import '../../features/auth/presentation/pages/profile_page.dart';
import '../../features/auth/presentation/pages/edit_profile_page.dart';
import '../../features/user/presentation/pages/enhanced_dashboard.dart';
import '../../features/user/presentation/pages/splash_page.dart';
import '../../features/user/presentation/pages/onboarding_page.dart';
// Removed help_center.dart import

// New Feature Imports
import '../../features/loyalty/presentation/pages/loyalty_dashboard_page.dart';
import '../../features/booking/presentation/pages/advanced_booking_dashboard_page.dart';
import '../../features/search/presentation/pages/advanced_search_page.dart';
import '../../features/search/presentation/pages/search_results_page.dart';
import '../../features/marketplace/presentation/pages/category_detail_page.dart';
import '../../features/user/presentation/pages/food_category_page.dart';
import '../../features/marketplace/presentation/pages/item_detail_page.dart';
import '../../features/user/presentation/pages/food_item_detail_page.dart';
import '../../features/user/presentation/pages/profile/user_profile_page.dart';
import '../../features/rating/presentation/pages/rating_dashboard_page.dart';
import '../../features/user/presentation/pages/rideshare/rideshare_page.dart';
import '../../features/user/presentation/pages/rideshare/ride_booking_page.dart';

// Route names - Core features only (User app focuses on booking)
class AppRoutes {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String home = '/home';
  static const String categories = '/categories';
  static const String categoriesFiltered = '/categories/filtered';
  static const String productDetail = '/product/:id';
  static const String payment = '/payment';
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String orders = '/orders';
  static const String orderDetail = '/orders/:id';
  static const String loyalty = '/loyalty';
  static const String advancedBooking = '/advanced-booking';
  static const String search = '/search';
  static const String searchResults = '/search/results';
  static const String categoryDetail = '/category/:name';
  static const String itemDetail = '/item/:id';
  static const String restaurantDetail = '/restaurant/:id';
  static const String serviceDetail = '/service/:id';
  static const String ratings = '/ratings';
  static const String tracking = '/tracking/:id';
  static const String notifications = '/notifications';
  static const String settings = '/settings';
  static const String help = '/help';
  static const String about = '/about';
  static const String uidRegistration = '/uid-registration';
  static const String rideshare = '/rideshare';
  static const String rideshareBooking = '/rideshare/booking/:rideId';
}

// Router provider
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: false,
    routes: [
      // Splash & Onboarding
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const UserSplashPage(),
      ),
      GoRoute(
        path: AppRoutes.onboarding,
        name: 'onboarding',
        builder: (context, state) => const UserOnboardingPage(),
      ),

      // Authentication
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const ModernLoginPage(),
      ),
      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const ModernRegisterPage(),
      ),
      GoRoute(
        path: AppRoutes.forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),
      GoRoute(
        path: '/auth/phone',
        name: 'phone-auth',
        builder: (context, state) => const PhoneAuthPage(),
      ),

      // Removed dashboard, wallet QR scanner, and games routes

      // UID Registration
      GoRoute(
        path: AppRoutes.uidRegistration,
        name: 'uid-registration',
        builder: (context, state) => const UIDRegistrationPage(),
      ),

      // Removed enhanced help center route

      // Main App with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainWrapper(child: child),
        routes: [
          GoRoute(
            path: AppRoutes.home,
            name: 'home',
            builder: (context, state) => const EnhancedUserDashboard(),
          ),
          GoRoute(
            path: AppRoutes.categories,
            name: 'categories',
            builder: (context, state) => const CategoriesPage(),
          ),
          GoRoute(
            path: AppRoutes.categoriesFiltered,
            name: 'categories-filtered',
            builder: (context, state) {
              final filterType = state.uri.queryParameters['type'] ?? '';
              final title = state.uri.queryParameters['title'] ?? 'Categories';
              return FilteredCategoriesPage(
                filterType: filterType,
                title: title,
              );
            },
          ),
          // Cart route (for compatibility)
          GoRoute(
            path: '/cart',
            name: 'cart',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('Cart feature coming soon!')),
            ),
          ),
          GoRoute(
            path: AppRoutes.profile,
            name: 'profile',
            builder: (context, state) => const UserProfilePage(),
          ),
          // Rideshare routes
          GoRoute(
            path: AppRoutes.rideshare,
            name: 'rideshare',
            builder: (context, state) => const RideSharePage(),
          ),
        ],
      ),

      // Product Detail
      GoRoute(
        path: AppRoutes.productDetail,
        name: 'product-detail',
        builder: (context, state) {
          final productId = state.pathParameters['id']!;
          return ProductDetailPage(productId: productId);
        },
      ),

      // Payment
      GoRoute(
        path: AppRoutes.payment,
        name: 'payment',
        builder: (context, state) {
          final totalAmount =
              double.tryParse(state.uri.queryParameters['amount'] ?? '0') ??
              0.0;
          final orderId = state.uri.queryParameters['orderId'] ?? '';
          return PaymentPage(totalAmount: totalAmount, orderId: orderId);
        },
      ),

      // Edit Profile
      GoRoute(
        path: AppRoutes.editProfile,
        name: 'edit-profile',
        builder: (context, state) => const EditProfilePage(),
      ),

      // Rideshare Booking
      GoRoute(
        path: AppRoutes.rideshareBooking,
        name: 'rideshare-booking',
        builder: (context, state) {
          final rideId = state.pathParameters['rideId']!;
          return RideBookingPage(rideId: rideId);
        },
      ),

      // Search Routes
      GoRoute(
        path: '/search/results',
        name: 'search-results',
        builder: (context, state) {
          final query = state.uri.queryParameters['q'] ?? '';
          final category = state.uri.queryParameters['category'];
          return SearchResultsPage(query: query, category: category);
        },
      ),

      // Category Detail
      GoRoute(
        path: '/category/:name',
        name: 'category-detail',
        builder: (context, state) {
          final categoryName = state.pathParameters['name']!;
          final decodedName = Uri.decodeComponent(categoryName);

          // Special handling for Food category
          if (decodedName.toLowerCase() == 'food') {
            return const FoodCategoryPage();
          }

          return CategoryDetailPage(categoryName: decodedName);
        },
      ),

      // Item Detail Routes
      GoRoute(
        path: '/item/:id',
        name: 'item-detail',
        builder: (context, state) {
          final itemId = state.pathParameters['id']!;
          final itemType = state.uri.queryParameters['type'];

          // Use specialized food item detail page for food items
          if (itemType == 'food') {
            return FoodItemDetailPage(itemId: itemId);
          }

          return ItemDetailPage(itemId: itemId, itemType: itemType);
        },
      ),
      GoRoute(
        path: '/restaurant/:id',
        name: 'restaurant-detail',
        builder: (context, state) {
          final itemId = state.pathParameters['id']!;
          return ItemDetailPage(itemId: itemId, itemType: 'restaurant');
        },
      ),
      GoRoute(
        path: '/service/:id',
        name: 'service-detail',
        builder: (context, state) {
          final itemId = state.pathParameters['id']!;
          return ItemDetailPage(itemId: itemId, itemType: 'service');
        },
      ),

      // Orders
      GoRoute(
        path: AppRoutes.orders,
        name: 'orders',
        builder: (context, state) => const OrdersPage(),
      ),
      GoRoute(
        path: AppRoutes.orderDetail,
        name: 'order-detail',
        builder: (context, state) {
          final orderId = state.pathParameters['id']!;
          return OrderDetailPage(orderId: orderId);
        },
      ),

      // Tracking
      GoRoute(
        path: AppRoutes.tracking,
        name: 'tracking',
        builder: (context, state) {
          final orderId = state.pathParameters['id']!;
          return TrackingPage(orderId: orderId);
        },
      ),

      // Other Pages
      GoRoute(
        path: AppRoutes.notifications,
        name: 'notifications',
        builder: (context, state) => const NotificationsPage(),
      ),
      GoRoute(
        path: AppRoutes.settings,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
      ),
      GoRoute(
        path: AppRoutes.help,
        name: 'help',
        builder: (context, state) => const HelpPage(),
      ),
      // Removed help center route
      GoRoute(
        path: AppRoutes.about,
        name: 'about',
        builder: (context, state) => const AboutPage(),
      ),

      // New Feature Routes
      GoRoute(
        path: AppRoutes.loyalty,
        name: 'loyalty',
        builder: (context, state) => const LoyaltyDashboardPage(),
      ),
      GoRoute(
        path: AppRoutes.advancedBooking,
        name: 'advanced-booking',
        builder: (context, state) => const AdvancedBookingDashboardPage(),
      ),
      GoRoute(
        path: AppRoutes.search,
        name: 'advanced-search',
        builder: (context, state) => const AdvancedSearchPage(),
      ),
      GoRoute(
        path: AppRoutes.ratings,
        name: 'ratings',
        builder: (context, state) => const RatingDashboardPage(),
      ),
    ],
    errorBuilder: (context, state) => ErrorPage(error: state.error),
  );
});

// Placeholder pages (to be implemented)

class OrdersPage extends StatelessWidget {
  const OrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Orders')),
      body: const Center(child: Text('Orders Page')),
    );
  }
}

class OrderDetailPage extends StatelessWidget {
  final String orderId;

  const OrderDetailPage({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Order Detail')),
      body: Center(child: Text('Order ID: $orderId')),
    );
  }
}

class TrackingPage extends StatelessWidget {
  final String orderId;

  const TrackingPage({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Tracking')),
      body: Center(child: Text('Tracking Order: $orderId')),
    );
  }
}

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notifications')),
      body: const Center(child: Text('Notifications Page')),
    );
  }
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: const Center(child: Text('Settings Page')),
    );
  }
}

class HelpPage extends StatelessWidget {
  const HelpPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Help')),
      body: const Center(child: Text('Help Page')),
    );
  }
}

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('About')),
      body: const Center(child: Text('About Page')),
    );
  }
}

class ErrorPage extends StatelessWidget {
  final Exception? error;

  const ErrorPage({super.key, this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Something went wrong: ${error?.toString() ?? 'Unknown error'}',
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}
