@echo off
echo ========================================
echo Flutter Plugin Path Issue Fix
echo ========================================
echo.
echo This script fixes the flutter_plugin_android_lifecycle path conflict
echo between project directory and pub cache.
echo.

echo [1/6] Stopping Gradle daemons...
call gradle --stop 2>nul
call .\android\gradlew --stop 2>nul

echo [2/6] Removing problematic build artifacts...
if exist "build\flutter_plugin_android_lifecycle" (
    echo Removing conflicting flutter_plugin_android_lifecycle build...
    rmdir /s /q "build\flutter_plugin_android_lifecycle" 2>nul
)

if exist ".dart_tool\flutter_build" (
    echo Removing Flutter build cache...
    rmdir /s /q ".dart_tool\flutter_build" 2>nul
)

echo [3/6] Cleaning pub cache for specific plugin...
echo Removing flutter_plugin_android_lifecycle from pub cache...
call flutter pub cache repair

echo [4/6] Refreshing dependencies...
call flutter pub get

echo [5/6] Rebuilding generated files...
call flutter packages get
call dart run build_runner clean
call dart run build_runner build --delete-conflicting-outputs

echo [6/6] Testing build with clean environment...
echo Attempting debug build...
call flutter build apk --debug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ SUCCESS: Plugin path issue resolved!
    echo Debug APK built successfully.
    echo.
    echo Now attempting release build...
    call flutter build apk --release
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Release APK also built successfully!
    ) else (
        echo ⚠️  Release build failed, but debug works.
        echo You can use the debug APK for testing.
    )
) else (
    echo.
    echo ❌ Build still failing. Trying alternative approach...
    echo.
    echo Clearing all Flutter caches...
    call flutter clean
    call flutter pub cache clean
    call flutter pub get
    echo.
    echo Try running: flutter build apk --debug --verbose
    echo to see detailed error information.
)

echo.
echo Fix attempt completed.
pause
