// Shared models for order flow between <PERSON><PERSON>, <PERSON><PERSON>, and Rider apps

enum OrderStatus {
  pending,      // User placed order, waiting for seller acceptance
  accepted,     // Se<PERSON> accepted order
  preparing,    // Se<PERSON> is preparing the order
  ready,        // Order is ready for pickup
  assigned,     // Order assigned to rider
  pickedUp,     // Rider picked up the order
  inTransit,    // Rider is delivering the order
  delivered,    // Order delivered to customer
  cancelled,    // Order cancelled
  rejected,     // <PERSON><PERSON> rejected the order
}

class OrderItem {
  final String id;
  final String name;
  final double price;
  final int quantity;
  final String? specialInstructions;
  final bool isVeg;

  OrderItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    this.specialInstructions,
    required this.isVeg,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'price': price,
    'quantity': quantity,
    'specialInstructions': specialInstructions,
    'isVeg': isVeg,
  };

  factory OrderItem.fromJson(Map<String, dynamic> json) => OrderItem(
    id: json['id'],
    name: json['name'],
    price: json['price'].toDouble(),
    quantity: json['quantity'],
    specialInstructions: json['specialInstructions'],
    isVeg: json['isVeg'],
  );
}

class Address {
  final String street;
  final String city;
  final String state;
  final String pincode;
  final String? landmark;
  final double? latitude;
  final double? longitude;

  Address({
    required this.street,
    required this.city,
    required this.state,
    required this.pincode,
    this.landmark,
    this.latitude,
    this.longitude,
  });

  Map<String, dynamic> toJson() => {
    'street': street,
    'city': city,
    'state': state,
    'pincode': pincode,
    'landmark': landmark,
    'latitude': latitude,
    'longitude': longitude,
  };

  factory Address.fromJson(Map<String, dynamic> json) => Address(
    street: json['street'],
    city: json['city'],
    state: json['state'],
    pincode: json['pincode'],
    landmark: json['landmark'],
    latitude: json['latitude']?.toDouble(),
    longitude: json['longitude']?.toDouble(),
  );

  @override
  String toString() {
    return '$street, $city, $state - $pincode';
  }
}

class Customer {
  final String id;
  final String name;
  final String phone;
  final String email;
  final Address address;

  Customer({
    required this.id,
    required this.name,
    required this.phone,
    required this.email,
    required this.address,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'phone': phone,
    'email': email,
    'address': address.toJson(),
  };

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
    id: json['id'],
    name: json['name'],
    phone: json['phone'],
    email: json['email'],
    address: Address.fromJson(json['address']),
  );
}

class Seller {
  final String id;
  final String name;
  final String phone;
  final String email;
  final Address address;
  final double rating;
  final int totalOrders;

  Seller({
    required this.id,
    required this.name,
    required this.phone,
    required this.email,
    required this.address,
    required this.rating,
    required this.totalOrders,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'phone': phone,
    'email': email,
    'address': address.toJson(),
    'rating': rating,
    'totalOrders': totalOrders,
  };

  factory Seller.fromJson(Map<String, dynamic> json) => Seller(
    id: json['id'],
    name: json['name'],
    phone: json['phone'],
    email: json['email'],
    address: Address.fromJson(json['address']),
    rating: json['rating'].toDouble(),
    totalOrders: json['totalOrders'],
  );
}

class Rider {
  final String id;
  final String name;
  final String phone;
  final String vehicleNumber;
  final String vehicleType;
  final double rating;
  final bool isOnline;
  final double? currentLatitude;
  final double? currentLongitude;

  Rider({
    required this.id,
    required this.name,
    required this.phone,
    required this.vehicleNumber,
    required this.vehicleType,
    required this.rating,
    required this.isOnline,
    this.currentLatitude,
    this.currentLongitude,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'phone': phone,
    'vehicleNumber': vehicleNumber,
    'vehicleType': vehicleType,
    'rating': rating,
    'isOnline': isOnline,
    'currentLatitude': currentLatitude,
    'currentLongitude': currentLongitude,
  };

  factory Rider.fromJson(Map<String, dynamic> json) => Rider(
    id: json['id'],
    name: json['name'],
    phone: json['phone'],
    vehicleNumber: json['vehicleNumber'],
    vehicleType: json['vehicleType'],
    rating: json['rating'].toDouble(),
    isOnline: json['isOnline'],
    currentLatitude: json['currentLatitude']?.toDouble(),
    currentLongitude: json['currentLongitude']?.toDouble(),
  );
}

class Order {
  final String id;
  final Customer customer;
  final Seller seller;
  final List<OrderItem> items;
  final double subtotal;
  final double deliveryFee;
  final double tax;
  final double total;
  final OrderStatus status;
  final DateTime orderTime;
  final DateTime? acceptedTime;
  final DateTime? readyTime;
  final DateTime? assignedTime;
  final DateTime? pickedUpTime;
  final DateTime? deliveredTime;
  final String paymentMethod;
  final bool isPaid;
  final Rider? assignedRider;
  final String? specialInstructions;
  final int estimatedPreparationTime; // in minutes

  Order({
    required this.id,
    required this.customer,
    required this.seller,
    required this.items,
    required this.subtotal,
    required this.deliveryFee,
    required this.tax,
    required this.total,
    required this.status,
    required this.orderTime,
    this.acceptedTime,
    this.readyTime,
    this.assignedTime,
    this.pickedUpTime,
    this.deliveredTime,
    required this.paymentMethod,
    required this.isPaid,
    this.assignedRider,
    this.specialInstructions,
    required this.estimatedPreparationTime,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'customer': customer.toJson(),
    'seller': seller.toJson(),
    'items': items.map((item) => item.toJson()).toList(),
    'subtotal': subtotal,
    'deliveryFee': deliveryFee,
    'tax': tax,
    'total': total,
    'status': status.name,
    'orderTime': orderTime.toIso8601String(),
    'acceptedTime': acceptedTime?.toIso8601String(),
    'readyTime': readyTime?.toIso8601String(),
    'assignedTime': assignedTime?.toIso8601String(),
    'pickedUpTime': pickedUpTime?.toIso8601String(),
    'deliveredTime': deliveredTime?.toIso8601String(),
    'paymentMethod': paymentMethod,
    'isPaid': isPaid,
    'assignedRider': assignedRider?.toJson(),
    'specialInstructions': specialInstructions,
    'estimatedPreparationTime': estimatedPreparationTime,
  };

  factory Order.fromJson(Map<String, dynamic> json) => Order(
    id: json['id'],
    customer: Customer.fromJson(json['customer']),
    seller: Seller.fromJson(json['seller']),
    items: (json['items'] as List).map((item) => OrderItem.fromJson(item)).toList(),
    subtotal: json['subtotal'].toDouble(),
    deliveryFee: json['deliveryFee'].toDouble(),
    tax: json['tax'].toDouble(),
    total: json['total'].toDouble(),
    status: OrderStatus.values.firstWhere((e) => e.name == json['status']),
    orderTime: DateTime.parse(json['orderTime']),
    acceptedTime: json['acceptedTime'] != null ? DateTime.parse(json['acceptedTime']) : null,
    readyTime: json['readyTime'] != null ? DateTime.parse(json['readyTime']) : null,
    assignedTime: json['assignedTime'] != null ? DateTime.parse(json['assignedTime']) : null,
    pickedUpTime: json['pickedUpTime'] != null ? DateTime.parse(json['pickedUpTime']) : null,
    deliveredTime: json['deliveredTime'] != null ? DateTime.parse(json['deliveredTime']) : null,
    paymentMethod: json['paymentMethod'],
    isPaid: json['isPaid'],
    assignedRider: json['assignedRider'] != null ? Rider.fromJson(json['assignedRider']) : null,
    specialInstructions: json['specialInstructions'],
    estimatedPreparationTime: json['estimatedPreparationTime'],
  );

  Order copyWith({
    OrderStatus? status,
    DateTime? acceptedTime,
    DateTime? readyTime,
    DateTime? assignedTime,
    DateTime? pickedUpTime,
    DateTime? deliveredTime,
    Rider? assignedRider,
  }) {
    return Order(
      id: id,
      customer: customer,
      seller: seller,
      items: items,
      subtotal: subtotal,
      deliveryFee: deliveryFee,
      tax: tax,
      total: total,
      status: status ?? this.status,
      orderTime: orderTime,
      acceptedTime: acceptedTime ?? this.acceptedTime,
      readyTime: readyTime ?? this.readyTime,
      assignedTime: assignedTime ?? this.assignedTime,
      pickedUpTime: pickedUpTime ?? this.pickedUpTime,
      deliveredTime: deliveredTime ?? this.deliveredTime,
      paymentMethod: paymentMethod,
      isPaid: isPaid,
      assignedRider: assignedRider ?? this.assignedRider,
      specialInstructions: specialInstructions,
      estimatedPreparationTime: estimatedPreparationTime,
    );
  }
}
