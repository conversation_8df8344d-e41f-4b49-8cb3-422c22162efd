android.useAndroidX=true
android.enableJetifier=true

# Gradle daemon and performance settings (OPTIMIZED FOR 8GB RAM)
org.gradle.daemon=true
org.gradle.parallel=false
org.gradle.configureondemand=true
org.gradle.caching=true

# Network and connectivity settings (REDUCED MEMORY FOR 8GB RAM)
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Djava.net.useSystemProxies=true
systemProp.http.keepAlive=false
systemProp.http.socketTimeout=60000
systemProp.http.connectionTimeout=60000

# Android build settings
android.nonTransitiveRClass=false

# Fix DEX merging issues
android.enableR8.fullMode=false

# Gradle cache location
org.gradle.user.home=C:\Users\<USER>\.gradle

# Fix file locking on Windows (OPTIMIZED FOR 8GB RAM + SLOW SSD)
org.gradle.workers.max=2

# Kotlin JVM target for java 17 compatibility
kotlin.jvm.target=17

# Fix path resolution issues
org.gradle.unsafe.configuration-cache=false

# Disable incremental compilation to prevent file locking
kotlin.incremental=false
kotlin.incremental.android=false

# Additional stability settings
android.builder.sdkDownload=true

# Suppress Gradle deprecation warnings
org.gradle.warning.mode=none

# Suppress specific Android Gradle Plugin warnings
android.suppressUnsupportedCompileSdk=true
android.suppressUnsupportedOptionWarnings=true

# Fix flutter_plugin_android_lifecycle path conflict
org.gradle.configuration-cache=false
