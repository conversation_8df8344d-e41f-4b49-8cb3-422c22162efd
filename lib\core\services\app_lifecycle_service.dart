import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_logger.dart';
import 'analytics_service.dart';
import 'notification_service.dart';

class AppLifecycleService extends ChangeNotifier {
  AppLifecycleState _currentState = AppLifecycleState.resumed;
  bool _isInitialized = false;
  DateTime? _backgroundTime;
  DateTime? _foregroundTime;

  AppLifecycleState get currentState => _currentState;
  bool get isInitialized => _isInitialized;
  bool get isInBackground => _currentState != AppLifecycleState.resumed;
  bool get isInForeground => _currentState == AppLifecycleState.resumed;

  /// Initialize the app lifecycle service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('Initializing App Lifecycle Service');

      // Set initial state
      _currentState = AppLifecycleState.resumed;
      _foregroundTime = DateTime.now();

      // Mark as initialized
      _isInitialized = true;

      // Log analytics
      await AnalyticsService.logEvent('app_lifecycle_initialized', {
        'timestamp': DateTime.now().toIso8601String(),
        'initial_state': _currentState.toString(),
      });

      AppLogger.info('App Lifecycle Service initialized successfully');
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to initialize App Lifecycle Service',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Handle app lifecycle state changes
  Future<void> handleLifecycleChange(AppLifecycleState state) async {
    if (!_isInitialized) {
      AppLogger.warning(
        'App Lifecycle Service not initialized, ignoring state change',
      );
      return;
    }

    final previousState = _currentState;
    _currentState = state;

    AppLogger.info('App lifecycle changed: $previousState -> $state');

    try {
      switch (state) {
        case AppLifecycleState.resumed:
          await _handleAppResumed(previousState);
          break;
        case AppLifecycleState.paused:
          await _handleAppPaused();
          break;
        case AppLifecycleState.inactive:
          await _handleAppInactive();
          break;
        case AppLifecycleState.detached:
          await _handleAppDetached();
          break;
        case AppLifecycleState.hidden:
          await _handleAppHidden();
          break;
      }

      // Log analytics for state changes
      await AnalyticsService.logEvent('app_lifecycle_changed', {
        'previous_state': previousState.toString(),
        'current_state': state.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      });

      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('Error handling lifecycle change', e, stackTrace);
    }
  }

  /// Handle app resumed (foreground)
  Future<void> _handleAppResumed(AppLifecycleState previousState) async {
    _foregroundTime = DateTime.now();

    // Calculate background duration if coming from background
    if (_backgroundTime != null && previousState == AppLifecycleState.paused) {
      final backgroundDuration = _foregroundTime!.difference(_backgroundTime!);
      AppLogger.info(
        'App was in background for: ${backgroundDuration.inSeconds} seconds',
      );

      // Log background duration
      await AnalyticsService.logEvent('app_background_duration', {
        'duration_seconds': backgroundDuration.inSeconds,
        'duration_minutes': backgroundDuration.inMinutes,
      });

      // Handle long background periods (e.g., refresh data)
      if (backgroundDuration.inMinutes > 5) {
        await _handleLongBackgroundReturn();
      }
    }

    // Note: Notification refresh logic can be added here if needed
    // await NotificationService.someRefreshMethod();

    AppLogger.info('App resumed successfully');
  }

  /// Handle app paused (background)
  Future<void> _handleAppPaused() async {
    _backgroundTime = DateTime.now();

    // Save any pending data
    await _savePendingData();

    // Schedule background tasks if needed
    await _scheduleBackgroundTasks();

    AppLogger.info('App paused successfully');
  }

  /// Handle app inactive
  Future<void> _handleAppInactive() async {
    // App is temporarily inactive (e.g., phone call, notification panel)
    AppLogger.info('App became inactive');
  }

  /// Handle app detached
  Future<void> _handleAppDetached() async {
    // App is being terminated
    await _handleAppTermination();
    AppLogger.info('App detached');
  }

  /// Handle app hidden
  Future<void> _handleAppHidden() async {
    // App is hidden but still running
    AppLogger.info('App hidden');
  }

  /// Handle long background return (refresh data, check updates)
  Future<void> _handleLongBackgroundReturn() async {
    try {
      AppLogger.info('Handling long background return - refreshing data');

      // Add any data refresh logic here
      // Example: refresh user data, check for updates, sync offline changes

      await AnalyticsService.logEvent('app_long_background_return', {
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e, stackTrace) {
      AppLogger.error('Error handling long background return', e, stackTrace);
    }
  }

  /// Save any pending data before going to background
  Future<void> _savePendingData() async {
    try {
      // Add logic to save any unsaved data
      AppLogger.info('Saving pending data before background');
    } catch (e, stackTrace) {
      AppLogger.error('Error saving pending data', e, stackTrace);
    }
  }

  /// Schedule background tasks
  Future<void> _scheduleBackgroundTasks() async {
    try {
      // Add logic for background tasks
      AppLogger.info('Scheduling background tasks');
    } catch (e, stackTrace) {
      AppLogger.error('Error scheduling background tasks', e, stackTrace);
    }
  }

  /// Handle app termination
  Future<void> _handleAppTermination() async {
    try {
      // Clean up resources, save critical data
      await _savePendingData();

      await AnalyticsService.logEvent('app_terminated', {
        'timestamp': DateTime.now().toIso8601String(),
        'session_duration': _getSessionDuration(),
      });

      AppLogger.info('App termination handled');
    } catch (e, stackTrace) {
      AppLogger.error('Error handling app termination', e, stackTrace);
    }
  }

  /// Get current session duration
  int _getSessionDuration() {
    if (_foregroundTime == null) return 0;
    return DateTime.now().difference(_foregroundTime!).inSeconds;
  }

  /// Get app usage statistics
  Map<String, dynamic> getUsageStats() {
    return {
      'current_state': _currentState.toString(),
      'is_initialized': _isInitialized,
      'session_duration_seconds': _getSessionDuration(),
      'foreground_time': _foregroundTime?.toIso8601String(),
      'background_time': _backgroundTime?.toIso8601String(),
    };
  }

  @override
  void dispose() {
    AppLogger.info('App Lifecycle Service disposed');
    super.dispose();
  }
}

// Riverpod provider for App Lifecycle Service
final appLifecycleServiceProvider = ChangeNotifierProvider<AppLifecycleService>(
  (ref) {
    return AppLifecycleService();
  },
);
