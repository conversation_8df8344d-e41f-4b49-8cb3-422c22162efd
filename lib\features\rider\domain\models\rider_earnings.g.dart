// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rider_earnings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RiderEarningsAdapter extends TypeAdapter<RiderEarnings> {
  @override
  final int typeId = 26;

  @override
  RiderEarnings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RiderEarnings(
      riderId: fields[0] as String,
      totalEarnings: fields[1] as double,
      todayEarnings: fields[2] as double,
      weeklyEarnings: fields[3] as double,
      monthlyEarnings: fields[4] as double,
      availableBalance: fields[5] as double,
      pendingAmount: fields[6] as double,
      totalWithdrawn: fields[7] as double,
      transactions: (fields[8] as List).cast<EarningsTransaction>(),
      commissionHistory: (fields[9] as List).cast<CommissionBreakdown>(),
      taxInfo: fields[10] as TaxInfo,
      lastUpdated: fields[11] as DateTime,
      stats: fields[12] as EarningsStats,
    );
  }

  @override
  void write(BinaryWriter writer, RiderEarnings obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.riderId)
      ..writeByte(1)
      ..write(obj.totalEarnings)
      ..writeByte(2)
      ..write(obj.todayEarnings)
      ..writeByte(3)
      ..write(obj.weeklyEarnings)
      ..writeByte(4)
      ..write(obj.monthlyEarnings)
      ..writeByte(5)
      ..write(obj.availableBalance)
      ..writeByte(6)
      ..write(obj.pendingAmount)
      ..writeByte(7)
      ..write(obj.totalWithdrawn)
      ..writeByte(8)
      ..write(obj.transactions)
      ..writeByte(9)
      ..write(obj.commissionHistory)
      ..writeByte(10)
      ..write(obj.taxInfo)
      ..writeByte(11)
      ..write(obj.lastUpdated)
      ..writeByte(12)
      ..write(obj.stats);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RiderEarningsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EarningsTransactionAdapter extends TypeAdapter<EarningsTransaction> {
  @override
  final int typeId = 27;

  @override
  EarningsTransaction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return EarningsTransaction(
      id: fields[0] as String,
      rideId: fields[1] as String,
      type: fields[2] as TransactionType,
      amount: fields[3] as double,
      commission: fields[4] as double,
      netAmount: fields[5] as double,
      timestamp: fields[6] as DateTime,
      description: fields[7] as String,
      status: fields[8] as TransactionStatus,
      metadata: (fields[9] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, EarningsTransaction obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.rideId)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.commission)
      ..writeByte(5)
      ..write(obj.netAmount)
      ..writeByte(6)
      ..write(obj.timestamp)
      ..writeByte(7)
      ..write(obj.description)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EarningsTransactionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CommissionBreakdownAdapter extends TypeAdapter<CommissionBreakdown> {
  @override
  final int typeId = 30;

  @override
  CommissionBreakdown read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CommissionBreakdown(
      rideId: fields[0] as String,
      baseFare: fields[1] as double,
      distanceCharge: fields[2] as double,
      timeCharge: fields[3] as double,
      surgeMultiplier: fields[4] as double,
      totalFare: fields[5] as double,
      platformCommission: fields[6] as double,
      riderEarning: fields[7] as double,
      bonus: fields[8] as double,
      incentive: fields[9] as double,
      penalty: fields[10] as double,
      netEarning: fields[11] as double,
      timestamp: fields[12] as DateTime,
      rideType: fields[13] as String,
    );
  }

  @override
  void write(BinaryWriter writer, CommissionBreakdown obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.rideId)
      ..writeByte(1)
      ..write(obj.baseFare)
      ..writeByte(2)
      ..write(obj.distanceCharge)
      ..writeByte(3)
      ..write(obj.timeCharge)
      ..writeByte(4)
      ..write(obj.surgeMultiplier)
      ..writeByte(5)
      ..write(obj.totalFare)
      ..writeByte(6)
      ..write(obj.platformCommission)
      ..writeByte(7)
      ..write(obj.riderEarning)
      ..writeByte(8)
      ..write(obj.bonus)
      ..writeByte(9)
      ..write(obj.incentive)
      ..writeByte(10)
      ..write(obj.penalty)
      ..writeByte(11)
      ..write(obj.netEarning)
      ..writeByte(12)
      ..write(obj.timestamp)
      ..writeByte(13)
      ..write(obj.rideType);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommissionBreakdownAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TaxInfoAdapter extends TypeAdapter<TaxInfo> {
  @override
  final int typeId = 31;

  @override
  TaxInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TaxInfo(
      totalTaxableIncome: fields[0] as double,
      tdsDeducted: fields[1] as double,
      gstCollected: fields[2] as double,
      panNumber: fields[3] as String,
      isTaxExempt: fields[4] as bool,
      taxDocuments: (fields[5] as List).cast<TaxDocument>(),
    );
  }

  @override
  void write(BinaryWriter writer, TaxInfo obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.totalTaxableIncome)
      ..writeByte(1)
      ..write(obj.tdsDeducted)
      ..writeByte(2)
      ..write(obj.gstCollected)
      ..writeByte(3)
      ..write(obj.panNumber)
      ..writeByte(4)
      ..write(obj.isTaxExempt)
      ..writeByte(5)
      ..write(obj.taxDocuments);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaxInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TaxDocumentAdapter extends TypeAdapter<TaxDocument> {
  @override
  final int typeId = 32;

  @override
  TaxDocument read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TaxDocument(
      id: fields[0] as String,
      type: fields[1] as String,
      url: fields[2] as String,
      generatedDate: fields[3] as DateTime,
      financialYear: fields[4] as String,
    );
  }

  @override
  void write(BinaryWriter writer, TaxDocument obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.url)
      ..writeByte(3)
      ..write(obj.generatedDate)
      ..writeByte(4)
      ..write(obj.financialYear);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaxDocumentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EarningsStatsAdapter extends TypeAdapter<EarningsStats> {
  @override
  final int typeId = 33;

  @override
  EarningsStats read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return EarningsStats(
      averageEarningPerRide: fields[0] as double,
      averageEarningPerHour: fields[1] as double,
      averageEarningPerDay: fields[2] as double,
      totalRidesCompleted: fields[3] as int,
      totalDistanceCovered: fields[4] as double,
      totalTimeOnline: fields[5] as double,
      peakHourEarnings: fields[6] as double,
      offPeakEarnings: fields[7] as double,
      earningsByRideType: (fields[8] as Map).cast<String, double>(),
      earningsByDay: (fields[9] as Map).cast<String, double>(),
    );
  }

  @override
  void write(BinaryWriter writer, EarningsStats obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.averageEarningPerRide)
      ..writeByte(1)
      ..write(obj.averageEarningPerHour)
      ..writeByte(2)
      ..write(obj.averageEarningPerDay)
      ..writeByte(3)
      ..write(obj.totalRidesCompleted)
      ..writeByte(4)
      ..write(obj.totalDistanceCovered)
      ..writeByte(5)
      ..write(obj.totalTimeOnline)
      ..writeByte(6)
      ..write(obj.peakHourEarnings)
      ..writeByte(7)
      ..write(obj.offPeakEarnings)
      ..writeByte(8)
      ..write(obj.earningsByRideType)
      ..writeByte(9)
      ..write(obj.earningsByDay);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EarningsStatsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransactionTypeAdapter extends TypeAdapter<TransactionType> {
  @override
  final int typeId = 28;

  @override
  TransactionType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransactionType.rideEarning;
      case 1:
        return TransactionType.bonus;
      case 2:
        return TransactionType.incentive;
      case 3:
        return TransactionType.penalty;
      case 4:
        return TransactionType.withdrawal;
      case 5:
        return TransactionType.refund;
      case 6:
        return TransactionType.adjustment;
      default:
        return TransactionType.rideEarning;
    }
  }

  @override
  void write(BinaryWriter writer, TransactionType obj) {
    switch (obj) {
      case TransactionType.rideEarning:
        writer.writeByte(0);
        break;
      case TransactionType.bonus:
        writer.writeByte(1);
        break;
      case TransactionType.incentive:
        writer.writeByte(2);
        break;
      case TransactionType.penalty:
        writer.writeByte(3);
        break;
      case TransactionType.withdrawal:
        writer.writeByte(4);
        break;
      case TransactionType.refund:
        writer.writeByte(5);
        break;
      case TransactionType.adjustment:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransactionStatusAdapter extends TypeAdapter<TransactionStatus> {
  @override
  final int typeId = 29;

  @override
  TransactionStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransactionStatus.pending;
      case 1:
        return TransactionStatus.completed;
      case 2:
        return TransactionStatus.failed;
      case 3:
        return TransactionStatus.cancelled;
      default:
        return TransactionStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, TransactionStatus obj) {
    switch (obj) {
      case TransactionStatus.pending:
        writer.writeByte(0);
        break;
      case TransactionStatus.completed:
        writer.writeByte(1);
        break;
      case TransactionStatus.failed:
        writer.writeByte(2);
        break;
      case TransactionStatus.cancelled:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RiderEarnings _$RiderEarningsFromJson(Map<String, dynamic> json) =>
    RiderEarnings(
      riderId: json['riderId'] as String,
      totalEarnings: (json['totalEarnings'] as num).toDouble(),
      todayEarnings: (json['todayEarnings'] as num).toDouble(),
      weeklyEarnings: (json['weeklyEarnings'] as num).toDouble(),
      monthlyEarnings: (json['monthlyEarnings'] as num).toDouble(),
      availableBalance: (json['availableBalance'] as num).toDouble(),
      pendingAmount: (json['pendingAmount'] as num).toDouble(),
      totalWithdrawn: (json['totalWithdrawn'] as num).toDouble(),
      transactions: (json['transactions'] as List<dynamic>)
          .map((e) => EarningsTransaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      commissionHistory: (json['commissionHistory'] as List<dynamic>)
          .map((e) => CommissionBreakdown.fromJson(e as Map<String, dynamic>))
          .toList(),
      taxInfo: TaxInfo.fromJson(json['taxInfo'] as Map<String, dynamic>),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      stats: EarningsStats.fromJson(json['stats'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RiderEarningsToJson(RiderEarnings instance) =>
    <String, dynamic>{
      'riderId': instance.riderId,
      'totalEarnings': instance.totalEarnings,
      'todayEarnings': instance.todayEarnings,
      'weeklyEarnings': instance.weeklyEarnings,
      'monthlyEarnings': instance.monthlyEarnings,
      'availableBalance': instance.availableBalance,
      'pendingAmount': instance.pendingAmount,
      'totalWithdrawn': instance.totalWithdrawn,
      'transactions': instance.transactions,
      'commissionHistory': instance.commissionHistory,
      'taxInfo': instance.taxInfo,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'stats': instance.stats,
    };

EarningsTransaction _$EarningsTransactionFromJson(Map<String, dynamic> json) =>
    EarningsTransaction(
      id: json['id'] as String,
      rideId: json['rideId'] as String,
      type: $enumDecode(_$TransactionTypeEnumMap, json['type']),
      amount: (json['amount'] as num).toDouble(),
      commission: (json['commission'] as num).toDouble(),
      netAmount: (json['netAmount'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      description: json['description'] as String,
      status: $enumDecode(_$TransactionStatusEnumMap, json['status']),
      metadata: json['metadata'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$EarningsTransactionToJson(
        EarningsTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'rideId': instance.rideId,
      'type': _$TransactionTypeEnumMap[instance.type]!,
      'amount': instance.amount,
      'commission': instance.commission,
      'netAmount': instance.netAmount,
      'timestamp': instance.timestamp.toIso8601String(),
      'description': instance.description,
      'status': _$TransactionStatusEnumMap[instance.status]!,
      'metadata': instance.metadata,
    };

const _$TransactionTypeEnumMap = {
  TransactionType.rideEarning: 'rideEarning',
  TransactionType.bonus: 'bonus',
  TransactionType.incentive: 'incentive',
  TransactionType.penalty: 'penalty',
  TransactionType.withdrawal: 'withdrawal',
  TransactionType.refund: 'refund',
  TransactionType.adjustment: 'adjustment',
};

const _$TransactionStatusEnumMap = {
  TransactionStatus.pending: 'pending',
  TransactionStatus.completed: 'completed',
  TransactionStatus.failed: 'failed',
  TransactionStatus.cancelled: 'cancelled',
};

CommissionBreakdown _$CommissionBreakdownFromJson(Map<String, dynamic> json) =>
    CommissionBreakdown(
      rideId: json['rideId'] as String,
      baseFare: (json['baseFare'] as num).toDouble(),
      distanceCharge: (json['distanceCharge'] as num).toDouble(),
      timeCharge: (json['timeCharge'] as num).toDouble(),
      surgeMultiplier: (json['surgeMultiplier'] as num).toDouble(),
      totalFare: (json['totalFare'] as num).toDouble(),
      platformCommission: (json['platformCommission'] as num).toDouble(),
      riderEarning: (json['riderEarning'] as num).toDouble(),
      bonus: (json['bonus'] as num).toDouble(),
      incentive: (json['incentive'] as num).toDouble(),
      penalty: (json['penalty'] as num).toDouble(),
      netEarning: (json['netEarning'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      rideType: json['rideType'] as String,
    );

Map<String, dynamic> _$CommissionBreakdownToJson(
        CommissionBreakdown instance) =>
    <String, dynamic>{
      'rideId': instance.rideId,
      'baseFare': instance.baseFare,
      'distanceCharge': instance.distanceCharge,
      'timeCharge': instance.timeCharge,
      'surgeMultiplier': instance.surgeMultiplier,
      'totalFare': instance.totalFare,
      'platformCommission': instance.platformCommission,
      'riderEarning': instance.riderEarning,
      'bonus': instance.bonus,
      'incentive': instance.incentive,
      'penalty': instance.penalty,
      'netEarning': instance.netEarning,
      'timestamp': instance.timestamp.toIso8601String(),
      'rideType': instance.rideType,
    };

TaxInfo _$TaxInfoFromJson(Map<String, dynamic> json) => TaxInfo(
      totalTaxableIncome: (json['totalTaxableIncome'] as num).toDouble(),
      tdsDeducted: (json['tdsDeducted'] as num).toDouble(),
      gstCollected: (json['gstCollected'] as num).toDouble(),
      panNumber: json['panNumber'] as String,
      isTaxExempt: json['isTaxExempt'] as bool,
      taxDocuments: (json['taxDocuments'] as List<dynamic>)
          .map((e) => TaxDocument.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TaxInfoToJson(TaxInfo instance) => <String, dynamic>{
      'totalTaxableIncome': instance.totalTaxableIncome,
      'tdsDeducted': instance.tdsDeducted,
      'gstCollected': instance.gstCollected,
      'panNumber': instance.panNumber,
      'isTaxExempt': instance.isTaxExempt,
      'taxDocuments': instance.taxDocuments,
    };

TaxDocument _$TaxDocumentFromJson(Map<String, dynamic> json) => TaxDocument(
      id: json['id'] as String,
      type: json['type'] as String,
      url: json['url'] as String,
      generatedDate: DateTime.parse(json['generatedDate'] as String),
      financialYear: json['financialYear'] as String,
    );

Map<String, dynamic> _$TaxDocumentToJson(TaxDocument instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'url': instance.url,
      'generatedDate': instance.generatedDate.toIso8601String(),
      'financialYear': instance.financialYear,
    };

EarningsStats _$EarningsStatsFromJson(Map<String, dynamic> json) =>
    EarningsStats(
      averageEarningPerRide: (json['averageEarningPerRide'] as num).toDouble(),
      averageEarningPerHour: (json['averageEarningPerHour'] as num).toDouble(),
      averageEarningPerDay: (json['averageEarningPerDay'] as num).toDouble(),
      totalRidesCompleted: (json['totalRidesCompleted'] as num).toInt(),
      totalDistanceCovered: (json['totalDistanceCovered'] as num).toDouble(),
      totalTimeOnline: (json['totalTimeOnline'] as num).toDouble(),
      peakHourEarnings: (json['peakHourEarnings'] as num).toDouble(),
      offPeakEarnings: (json['offPeakEarnings'] as num).toDouble(),
      earningsByRideType:
          (json['earningsByRideType'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      earningsByDay: (json['earningsByDay'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$EarningsStatsToJson(EarningsStats instance) =>
    <String, dynamic>{
      'averageEarningPerRide': instance.averageEarningPerRide,
      'averageEarningPerHour': instance.averageEarningPerHour,
      'averageEarningPerDay': instance.averageEarningPerDay,
      'totalRidesCompleted': instance.totalRidesCompleted,
      'totalDistanceCovered': instance.totalDistanceCovered,
      'totalTimeOnline': instance.totalTimeOnline,
      'peakHourEarnings': instance.peakHourEarnings,
      'offPeakEarnings': instance.offPeakEarnings,
      'earningsByRideType': instance.earningsByRideType,
      'earningsByDay': instance.earningsByDay,
    };
