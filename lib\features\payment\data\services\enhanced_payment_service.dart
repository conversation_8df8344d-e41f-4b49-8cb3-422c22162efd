import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
// import '../../../core/services/analytics_service.dart'; // Commented out for now
// import '../../../core/services/notification_service.dart'; // Commented out for now
import '../../domain/models/enhanced_payment_models.dart';

class EnhancedPaymentService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const Uuid _uuid = Uuid();

  static const String _paymentsCollection = 'enhanced_payments';
  static const String _paymentCardsCollection = 'payment_cards';
  static const String _upiAccountsCollection = 'upi_accounts';
  static const String _paymentRefundsCollection = 'payment_refunds';
  static const String _paymentHistoryCollection = 'payment_history';

  // Create a new payment
  static Future<EnhancedPayment> createPayment({
    required String orderId,
    required double totalAmount,
    String currency = 'INR',
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final paymentId = _uuid.v4();
      final now = DateTime.now();

      final payment = EnhancedPayment(
        id: paymentId,
        userId: currentUser.uid,
        orderId: orderId,
        totalAmount: totalAmount,
        currency: currency,
        status: PaymentStatus.pending,
        metadata: metadata,
        createdAt: now,
        updatedAt: now,
      );

      await _firestore
          .collection(_paymentsCollection)
          .doc(paymentId)
          .set(payment.toJson());

      // Log payment creation
      await _addPaymentHistory(
        paymentId: paymentId,
        fromStatus: PaymentStatus.pending,
        toStatus: PaymentStatus.pending,
        note: 'Payment created',
      );

      // await AnalyticsService.logEvent('payment_created', {
      //   'payment_id': paymentId,
      //   'order_id': orderId,
      //   'amount': totalAmount,
      //   'currency': currency,
      // });

      return payment;
    } catch (e) {
      debugPrint('❌ Error creating payment: $e');
      throw Exception('Failed to create payment: ${e.toString()}');
    }
  }

  // Process payment with single method
  static Future<EnhancedPayment> processPayment({
    required String paymentId,
    required PaymentMethod method,
    required double amount,
    String? cardId,
    String? upiId,
    Map<String, dynamic> additionalData = const {},
  }) async {
    try {
      final paymentDoc = await _firestore
          .collection(_paymentsCollection)
          .doc(paymentId)
          .get();
      if (!paymentDoc.exists) throw Exception('Payment not found');

      final payment = EnhancedPayment.fromJson(paymentDoc.data()!);

      // Create payment split
      final split = PaymentSplit(
        id: _uuid.v4(),
        paymentId: paymentId,
        method: method,
        amount: amount,
        percentage: (amount / payment.totalAmount) * 100,
        cardId: cardId,
        upiId: upiId,
        status: PaymentStatus.processing,
        createdAt: DateTime.now(),
      );

      // Simulate payment processing
      final success = await _simulatePaymentProcessing(method, amount);

      final updatedSplit = PaymentSplit(
        id: split.id,
        paymentId: split.paymentId,
        method: split.method,
        amount: split.amount,
        percentage: split.percentage,
        cardId: split.cardId,
        upiId: split.upiId,
        status: success ? PaymentStatus.completed : PaymentStatus.failed,
        transactionId: success ? _generateTransactionId() : null,
        createdAt: split.createdAt,
      );

      // Update payment
      final updatedSplits = [...payment.splits, updatedSplit];
      final totalPaid = updatedSplits
          .where((s) => s.status == PaymentStatus.completed)
          .fold(0.0, (sum, s) => sum + s.amount);

      final updatedPayment = EnhancedPayment(
        id: payment.id,
        userId: payment.userId,
        orderId: payment.orderId,
        totalAmount: payment.totalAmount,
        paidAmount: totalPaid,
        refundedAmount: payment.refundedAmount,
        currency: payment.currency,
        status: _calculatePaymentStatus(payment.totalAmount, totalPaid),
        splits: updatedSplits,
        metadata: payment.metadata,
        createdAt: payment.createdAt,
        updatedAt: DateTime.now(),
        failureReason: success ? null : 'Payment processing failed',
        retryCount: success ? payment.retryCount : payment.retryCount + 1,
      );

      await _firestore
          .collection(_paymentsCollection)
          .doc(paymentId)
          .update(updatedPayment.toJson());

      // Add to payment history
      await _addPaymentHistory(
        paymentId: paymentId,
        fromStatus: payment.status,
        toStatus: updatedPayment.status,
        note: success
            ? 'Payment processed successfully'
            : 'Payment processing failed',
      );

      // Send notification
      await _sendPaymentNotification(updatedPayment, success);

      // await AnalyticsService.logEvent('payment_processed', {
      //   'payment_id': paymentId,
      //   'method': method.toString(),
      //   'amount': amount,
      //   'success': success,
      // });

      return updatedPayment;
    } catch (e) {
      debugPrint('❌ Error processing payment: $e');
      throw Exception('Failed to process payment: ${e.toString()}');
    }
  }

  // Process split payment
  static Future<EnhancedPayment> processSplitPayment({
    required String paymentId,
    required List<Map<String, dynamic>> paymentSplits,
  }) async {
    try {
      final paymentDoc = await _firestore
          .collection(_paymentsCollection)
          .doc(paymentId)
          .get();
      if (!paymentDoc.exists) throw Exception('Payment not found');

      final payment = EnhancedPayment.fromJson(paymentDoc.data()!);
      final List<PaymentSplit> processedSplits = [];

      for (final splitData in paymentSplits) {
        final method = PaymentMethod.values.firstWhere(
          (m) => m.toString() == splitData['method'],
        );

        final split = PaymentSplit(
          id: _uuid.v4(),
          paymentId: paymentId,
          method: method,
          amount: splitData['amount'].toDouble(),
          percentage: (splitData['amount'] / payment.totalAmount) * 100,
          cardId: splitData['cardId'],
          upiId: splitData['upiId'],
          status: PaymentStatus.processing,
          createdAt: DateTime.now(),
        );

        // Process each split
        final success = await _simulatePaymentProcessing(method, split.amount);

        final processedSplit = PaymentSplit(
          id: split.id,
          paymentId: split.paymentId,
          method: split.method,
          amount: split.amount,
          percentage: split.percentage,
          cardId: split.cardId,
          upiId: split.upiId,
          status: success ? PaymentStatus.completed : PaymentStatus.failed,
          transactionId: success ? _generateTransactionId() : null,
          createdAt: split.createdAt,
        );

        processedSplits.add(processedSplit);
      }

      // Update payment with all splits
      final allSplits = [...payment.splits, ...processedSplits];
      final totalPaid = allSplits
          .where((s) => s.status == PaymentStatus.completed)
          .fold(0.0, (sum, s) => sum + s.amount);

      final updatedPayment = EnhancedPayment(
        id: payment.id,
        userId: payment.userId,
        orderId: payment.orderId,
        totalAmount: payment.totalAmount,
        paidAmount: totalPaid,
        refundedAmount: payment.refundedAmount,
        currency: payment.currency,
        status: _calculatePaymentStatus(payment.totalAmount, totalPaid),
        splits: allSplits,
        metadata: payment.metadata,
        createdAt: payment.createdAt,
        updatedAt: DateTime.now(),
        retryCount: payment.retryCount,
      );

      await _firestore
          .collection(_paymentsCollection)
          .doc(paymentId)
          .update(updatedPayment.toJson());

      // await AnalyticsService.logEvent('split_payment_processed', {
      //   'payment_id': paymentId,
      //   'splits_count': processedSplits.length,
      //   'total_amount': totalPaid,
      // });

      return updatedPayment;
    } catch (e) {
      debugPrint('❌ Error processing split payment: $e');
      throw Exception('Failed to process split payment: ${e.toString()}');
    }
  }

  // Initiate refund
  static Future<PaymentRefund> initiateRefund({
    required String paymentId,
    required double amount,
    required String reason,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final paymentDoc = await _firestore
          .collection(_paymentsCollection)
          .doc(paymentId)
          .get();
      if (!paymentDoc.exists) throw Exception('Payment not found');

      final payment = EnhancedPayment.fromJson(paymentDoc.data()!);

      if (amount > payment.refundableAmount) {
        throw Exception('Refund amount exceeds refundable amount');
      }

      final refundId = _uuid.v4();
      final refund = PaymentRefund(
        id: refundId,
        paymentId: paymentId,
        userId: currentUser.uid,
        amount: amount,
        reason: reason,
        status: PaymentStatus.processing,
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(_paymentRefundsCollection)
          .doc(refundId)
          .set(refund.toJson());

      // Simulate refund processing
      final success = await _simulateRefundProcessing(amount);

      final processedRefund = PaymentRefund(
        id: refund.id,
        paymentId: refund.paymentId,
        userId: refund.userId,
        amount: refund.amount,
        reason: refund.reason,
        status: success ? PaymentStatus.completed : PaymentStatus.failed,
        transactionId: success ? _generateTransactionId() : null,
        createdAt: refund.createdAt,
        processedAt: success ? DateTime.now() : null,
        processingNote: success
            ? 'Refund processed successfully'
            : 'Refund processing failed',
      );

      await _firestore
          .collection(_paymentRefundsCollection)
          .doc(refundId)
          .update(processedRefund.toJson());

      if (success) {
        // Update payment refunded amount
        final updatedPayment = EnhancedPayment(
          id: payment.id,
          userId: payment.userId,
          orderId: payment.orderId,
          totalAmount: payment.totalAmount,
          paidAmount: payment.paidAmount,
          refundedAmount: payment.refundedAmount + amount,
          currency: payment.currency,
          status: payment.refundedAmount + amount >= payment.paidAmount
              ? PaymentStatus.refunded
              : PaymentStatus.partiallyRefunded,
          splits: payment.splits,
          metadata: payment.metadata,
          createdAt: payment.createdAt,
          updatedAt: DateTime.now(),
          retryCount: payment.retryCount,
        );

        await _firestore
            .collection(_paymentsCollection)
            .doc(paymentId)
            .update(updatedPayment.toJson());
      }

      // await AnalyticsService.logEvent('refund_initiated', {
      //   'payment_id': paymentId,
      //   'refund_id': refundId,
      //   'amount': amount,
      //   'success': success,
      // });

      return processedRefund;
    } catch (e) {
      debugPrint('❌ Error initiating refund: $e');
      throw Exception('Failed to initiate refund: ${e.toString()}');
    }
  }

  // Get payment details
  static Future<EnhancedPayment?> getPayment(String paymentId) async {
    try {
      final doc = await _firestore
          .collection(_paymentsCollection)
          .doc(paymentId)
          .get();
      if (doc.exists) {
        return EnhancedPayment.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting payment: $e');
      return null;
    }
  }

  // Get user payments
  static Stream<List<EnhancedPayment>> getUserPayments({
    String? userId,
    int limit = 20,
  }) {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return Stream.value([]);

      return _firestore
          .collection(_paymentsCollection)
          .where('userId', isEqualTo: targetUserId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs.map((doc) {
              return EnhancedPayment.fromJson(doc.data());
            }).toList();
          });
    } catch (e) {
      debugPrint('❌ Error getting user payments: $e');
      return Stream.value([]);
    }
  }

  // Get payment history
  static Future<List<PaymentHistory>> getPaymentHistory(
    String paymentId,
  ) async {
    try {
      final snapshot = await _firestore
          .collection(_paymentHistoryCollection)
          .where('paymentId', isEqualTo: paymentId)
          .orderBy('timestamp', descending: false)
          .get();

      return snapshot.docs.map((doc) {
        return PaymentHistory.fromJson(doc.data());
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting payment history: $e');
      return [];
    }
  }

  // Private helper methods
  static Future<bool> _simulatePaymentProcessing(
    PaymentMethod method,
    double amount,
  ) async {
    // Simulate processing delay
    await Future.delayed(Duration(seconds: Random().nextInt(3) + 1));

    // Simulate success rate based on payment method
    final successRate = _getSuccessRate(method);
    return Random().nextDouble() < successRate;
  }

  static Future<bool> _simulateRefundProcessing(double amount) async {
    await Future.delayed(Duration(seconds: Random().nextInt(2) + 1));
    return Random().nextDouble() < 0.95; // 95% success rate for refunds
  }

  static double _getSuccessRate(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.upi:
        return 0.98;
      case PaymentMethod.wallet:
        return 0.99;
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return 0.95;
      case PaymentMethod.netBanking:
        return 0.92;
      case PaymentMethod.cashOnDelivery:
        return 1.0;
      default:
        return 0.90;
    }
  }

  static PaymentStatus _calculatePaymentStatus(
    double totalAmount,
    double paidAmount,
  ) {
    if (paidAmount >= totalAmount) {
      return PaymentStatus.completed;
    } else if (paidAmount > 0) {
      return PaymentStatus.processing;
    } else {
      return PaymentStatus.pending;
    }
  }

  static String _generateTransactionId() {
    return 'TXN${DateTime.now().millisecondsSinceEpoch}${Random().nextInt(1000)}';
  }

  static Future<void> _addPaymentHistory({
    required String paymentId,
    required PaymentStatus fromStatus,
    required PaymentStatus toStatus,
    String? note,
  }) async {
    try {
      final history = PaymentHistory(
        id: _uuid.v4(),
        paymentId: paymentId,
        fromStatus: fromStatus,
        toStatus: toStatus,
        note: note,
        timestamp: DateTime.now(),
      );

      await _firestore
          .collection(_paymentHistoryCollection)
          .add(history.toJson());
    } catch (e) {
      debugPrint('❌ Error adding payment history: $e');
    }
  }

  static Future<void> _sendPaymentNotification(
    EnhancedPayment payment,
    bool success,
  ) async {
    try {
      // await NotificationService.sendNotificationToUser(
      //   userId: payment.userId,
      //   title: success ? 'Payment Successful' : 'Payment Failed',
      //   body: success
      //       ? 'Your payment of ₹${payment.paidAmount.toStringAsFixed(2)} was successful'
      //       : 'Your payment failed. Please try again.',
      //   data: {
      //     'type': 'payment',
      //     'payment_id': payment.id,
      //     'status': payment.status.toString(),
      //   },
      // );
    } catch (e) {
      debugPrint('❌ Error sending payment notification: $e');
    }
  }
}
