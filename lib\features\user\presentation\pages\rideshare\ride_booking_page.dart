import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../demo/rideshare_demo_data.dart';
import '../../../../rideshare/services/otp_service.dart';
import '../../../../rideshare/services/commission_service.dart';
import 'widgets/driver_info_card.dart';
import 'widgets/ride_tracking_widget.dart';
import 'widgets/fare_breakdown_widget.dart';

class RideBookingPage extends ConsumerStatefulWidget {
  final String rideId;

  const RideBookingPage({super.key, required this.rideId});

  @override
  ConsumerState<RideBookingPage> createState() => _RideBookingPageState();
}

class _RideBookingPageState extends ConsumerState<RideBookingPage> {
  String rideStatus = 'driver_assigned';
  Map<String, dynamic>? driver;
  Map<String, dynamic>? fareDetails;
  String selectedPaymentMethod = 'upi';
  OTPData? otpData;
  bool showOTP = false;

  @override
  void initState() {
    super.initState();
    _loadRideDetails();
    _simulateRideProgress();
  }

  void _loadRideDetails() {
    // Load driver details
    driver = RideShareDemoData.getDriverById(widget.rideId);

    // Load fare details
    fareDetails = RideShareDemoData.calculateFare('Economy', 8.5, 25);
  }

  void _simulateRideProgress() {
    // Simulate ride status changes
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          rideStatus = 'driver_arriving';
        });
      }
    });

    Future.delayed(const Duration(seconds: 8), () {
      if (mounted) {
        setState(() {
          rideStatus = 'in_progress';
        });
      }
    });

    Future.delayed(const Duration(seconds: 15), () {
      if (mounted) {
        setState(() {
          rideStatus = 'arrived_destination';
          // Generate OTP when ride reaches destination
          otpData = OTPService.generateOTP(widget.rideId);
          showOTP = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (driver == null || fareDetails == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'Ride Tracking',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (rideStatus != 'completed')
            IconButton(
              onPressed: _showCancelDialog,
              icon: const Icon(Icons.close),
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Map section
            _buildMapSection(),

            // Ride tracking widget
            Container(
              margin: const EdgeInsets.all(16),
              child: RideTrackingWidget(
                rideStatus: rideStatus,
                driver: driver!,
                pickupLocation: 'Paltan Bazaar, Guwahati',
                dropLocation: 'Kamakhya Temple, Guwahati',
                estimatedTime: _getEstimatedTime(),
                distance: 8.5,
              ),
            ),

            // OTP Display Section
            if (showOTP && otpData != null) _buildOTPSection(),

            // Driver info card
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: DriverInfoCard(
                driver: driver!,
                onCallTap: _callDriver,
                onMessageTap: _messageDriver,
              ),
            ),

            // Fare breakdown
            Container(
              margin: const EdgeInsets.all(16),
              child: FareBreakdownWidget(
                fareDetails: fareDetails!,
                showPaymentOptions: rideStatus == 'completed',
                selectedPaymentMethod: selectedPaymentMethod,
                onPaymentMethodChanged: (method) {
                  setState(() {
                    selectedPaymentMethod = method;
                  });
                },
              ),
            ),

            // Action buttons
            if (rideStatus == 'completed') _buildCompletionActions(),

            const SizedBox(height: 100), // Space for bottom navigation
          ],
        ),
      ),
    );
  }

  Widget _buildMapSection() {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Map placeholder
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.grey[300],
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.navigation, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text(
                      'Live Tracking',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Status overlay
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor().withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getStatusText(),
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOTPSection() {
    if (otpData == null) return const SizedBox.shrink();

    final remainingTime = otpData!.remainingTime;
    final minutes = remainingTime.inMinutes;
    final seconds = remainingTime.inSeconds % 60;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.userPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.security,
                  color: AppColors.userPrimary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Trip Completion OTP',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      'Share this OTP with your driver',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // OTP Display
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            decoration: BoxDecoration(
              color: AppColors.userPrimary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.userPrimary.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  otpData!.otp,
                  style: GoogleFonts.poppins(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: AppColors.userPrimary,
                    letterSpacing: 8,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Timer and Actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Timer
              Row(
                children: [
                  Icon(Icons.timer, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Expires in ${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),

              // Share button
              TextButton.icon(
                onPressed: _shareOTP,
                icon: const Icon(Icons.share, size: 16),
                label: Text(
                  'Share OTP',
                  style: GoogleFonts.poppins(fontSize: 12),
                ),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.userPrimary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Instructions
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.blue[700]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Your ride will be marked as completed only after the driver enters this OTP',
                    style: GoogleFonts.poppins(
                      fontSize: 11,
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionActions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Rate ride button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showRatingDialog,
              icon: const Icon(Icons.star, size: 20),
              label: Text(
                'Rate Your Ride',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.userPrimary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 0,
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Book another ride button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => context.go('/rideshare'),
              icon: const Icon(Icons.refresh, size: 20),
              label: Text(
                'Book Another Ride',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.userPrimary,
                side: const BorderSide(color: AppColors.userPrimary),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  double? _getEstimatedTime() {
    switch (rideStatus) {
      case 'driver_assigned':
        return 5.0;
      case 'driver_arriving':
        return 3.0;
      case 'in_progress':
        return 15.0;
      default:
        return null;
    }
  }

  Color _getStatusColor() {
    switch (rideStatus.toLowerCase()) {
      case 'driver_assigned':
        return AppColors.userPrimary;
      case 'driver_arriving':
        return Colors.blue;
      case 'in_progress':
        return AppColors.accentGreen;
      case 'arrived_destination':
        return Colors.orange;
      case 'completed':
        return AppColors.success;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText() {
    switch (rideStatus.toLowerCase()) {
      case 'driver_assigned':
        return 'Driver Assigned';
      case 'driver_arriving':
        return 'Driver Arriving';
      case 'in_progress':
        return 'Trip in Progress';
      case 'arrived_destination':
        return 'Arrived at Destination';
      case 'completed':
        return 'Trip Completed';
      default:
        return 'Unknown';
    }
  }

  void _callDriver() {
    // Handle call driver
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling ${driver!['name']}...'),
        backgroundColor: AppColors.userPrimary,
      ),
    );
  }

  void _messageDriver() {
    // Handle message driver
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening chat with ${driver!['name']}...'),
        backgroundColor: AppColors.userPrimary,
      ),
    );
  }

  void _showCancelDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Cancel Ride?',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        content: Text(
          'Are you sure you want to cancel this ride? Cancellation charges may apply.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'No',
              style: GoogleFonts.poppins(color: Colors.grey[600]),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.go('/home');
            },
            child: Text(
              'Yes, Cancel',
              style: GoogleFonts.poppins(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _showRatingDialog() {
    int rating = 5;
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'Rate Your Ride',
            style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'How was your ride with ${driver!['name']}?',
                style: GoogleFonts.poppins(),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return GestureDetector(
                    onTap: () => setState(() => rating = index + 1),
                    child: Icon(
                      Icons.star,
                      size: 32,
                      color: index < rating ? Colors.amber : Colors.grey[300],
                    ),
                  );
                }),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Skip',
                style: GoogleFonts.poppins(color: Colors.grey[600]),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Thank you for rating!'),
                    backgroundColor: AppColors.success,
                  ),
                );
              },
              child: Text(
                'Submit',
                style: GoogleFonts.poppins(color: AppColors.userPrimary),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _shareOTP() {
    if (otpData != null) {
      // Copy OTP to clipboard
      Clipboard.setData(ClipboardData(text: otpData!.otp));

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                'OTP copied to clipboard: ${otpData!.otp}',
                style: GoogleFonts.poppins(fontSize: 14),
              ),
            ],
          ),
          backgroundColor: AppColors.success,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    }
  }
}
