import 'package:cloud_firestore/cloud_firestore.dart';

enum ChatType {
  support, // User <-> Support Agent
  userSeller, // User <-> Seller (product inquiries)
  userRider, // User <-> Rider (delivery coordination)
  sellerRider, // Seller <-> Rider (pickup coordination)
  groupOrder, // User + Seller + Rider (complex orders)
  family, // Family members sharing orders
  business, // B2B communication
}

enum MessageType {
  text,
  image,
  file,
  location,
  voice,
  system, // System messages (order updates, etc.)
}

enum UserType { user, seller, rider, support, admin }

class Chat {
  final String id;
  final List<String> participants;
  final ChatType chatType;
  final String? orderId;
  final String? productId;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String lastMessage;
  final DateTime? lastMessageTime;
  final String lastMessageSender;
  final bool isActive;
  final Map<String, int> unreadCounts;

  Chat({
    required this.id,
    required this.participants,
    required this.chatType,
    this.orderId,
    this.productId,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    required this.lastMessage,
    this.lastMessageTime,
    required this.lastMessageSender,
    required this.isActive,
    required this.unreadCounts,
  });

  factory Chat.fromFirestore(String id, Map<String, dynamic> data) {
    return Chat(
      id: id,
      participants: List<String>.from(data['participants'] ?? []),
      chatType: ChatType.values.firstWhere(
        (type) => type.toString() == data['chatType'],
        orElse: () => ChatType.support,
      ),
      orderId: data['orderId'],
      productId: data['productId'],
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastMessage: data['lastMessage'] ?? '',
      lastMessageTime: (data['lastMessageTime'] as Timestamp?)?.toDate(),
      lastMessageSender: data['lastMessageSender'] ?? '',
      isActive: data['isActive'] ?? true,
      unreadCounts: Map<String, int>.from(
        (data['unreadCounts'] as Map<String, dynamic>?)?.map(
              (key, value) => MapEntry(key, value as int),
            ) ??
            {},
      ),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'participants': participants,
      'chatType': chatType.toString(),
      'orderId': orderId,
      'productId': productId,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'lastMessage': lastMessage,
      'lastMessageTime': lastMessageTime != null
          ? Timestamp.fromDate(lastMessageTime!)
          : null,
      'lastMessageSender': lastMessageSender,
      'isActive': isActive,
      'unreadCounts': unreadCounts,
    };
  }

  int getUnreadCount(String userId) {
    return unreadCounts[userId] ?? 0;
  }

  String getChatTitle(
    String currentUserId,
    List<ChatParticipant> participants,
  ) {
    switch (chatType) {
      case ChatType.support:
        return 'Support Chat';
      case ChatType.userSeller:
        final seller = participants.firstWhere(
          (p) => p.userType == UserType.seller,
          orElse: () => ChatParticipant(
            id: '',
            name: 'Seller',
            userType: UserType.seller,
          ),
        );
        return seller.name;
      case ChatType.userRider:
        final rider = participants.firstWhere(
          (p) => p.userType == UserType.rider,
          orElse: () =>
              ChatParticipant(id: '', name: 'Rider', userType: UserType.rider),
        );
        return '🚗 ${rider.name}';
      case ChatType.sellerRider:
        return 'Pickup Coordination';
      case ChatType.groupOrder:
        return 'Order Chat';
      case ChatType.family:
        return 'Family Chat';
      case ChatType.business:
        return 'Business Chat';
    }
  }

  String getChatSubtitle() {
    switch (chatType) {
      case ChatType.support:
        return 'Get help from our support team';
      case ChatType.userSeller:
        return productId != null ? 'Product inquiry' : 'General inquiry';
      case ChatType.userRider:
        return orderId != null
            ? 'Order #${orderId?.substring(0, 8)}'
            : 'Delivery chat';
      case ChatType.sellerRider:
        return 'Pickup coordination';
      case ChatType.groupOrder:
        return orderId != null
            ? 'Order #${orderId?.substring(0, 8)}'
            : 'Group order';
      case ChatType.family:
        return 'Family sharing';
      case ChatType.business:
        return 'Business communication';
    }
  }
}

class ChatMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String message;
  final MessageType messageType;
  final String? imageUrl;
  final String? fileUrl;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;
  final bool isRead;
  final bool isEdited;
  final bool isDeleted;

  ChatMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.messageType,
    this.imageUrl,
    this.fileUrl,
    required this.metadata,
    required this.timestamp,
    required this.isRead,
    required this.isEdited,
    required this.isDeleted,
  });

  factory ChatMessage.fromFirestore(String id, Map<String, dynamic> data) {
    return ChatMessage(
      id: id,
      senderId: data['senderId'] ?? '',
      senderName: data['senderName'] ?? 'User',
      message: data['message'] ?? '',
      messageType: MessageType.values.firstWhere(
        (type) => type.toString() == data['messageType'],
        orElse: () => MessageType.text,
      ),
      imageUrl: data['imageUrl'],
      fileUrl: data['fileUrl'],
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false,
      isEdited: data['isEdited'] ?? false,
      isDeleted: data['isDeleted'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'senderId': senderId,
      'senderName': senderName,
      'message': message,
      'messageType': messageType.toString(),
      'imageUrl': imageUrl,
      'fileUrl': fileUrl,
      'metadata': metadata,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'isEdited': isEdited,
      'isDeleted': isDeleted,
    };
  }

  String getDisplayMessage() {
    if (isDeleted) return 'This message was deleted';

    switch (messageType) {
      case MessageType.image:
        return '📷 Image';
      case MessageType.file:
        return '📎 File';
      case MessageType.location:
        return '📍 Location';
      case MessageType.voice:
        return '🎤 Voice message';
      case MessageType.system:
        return message;
      default:
        return message;
    }
  }

  bool isFromCurrentUser(String currentUserId) {
    return senderId == currentUserId;
  }
}

class ChatParticipant {
  final String id;
  final String name;
  final String? avatar;
  final UserType userType;
  final bool isOnline;
  final DateTime? lastSeen;

  ChatParticipant({
    required this.id,
    required this.name,
    this.avatar,
    required this.userType,
    this.isOnline = false,
    this.lastSeen,
  });

  factory ChatParticipant.fromFirestore(String id, Map<String, dynamic> data) {
    return ChatParticipant(
      id: id,
      name: data['name'] ?? 'User',
      avatar: data['avatar'],
      userType: UserType.values.firstWhere(
        (type) => type.toString() == data['userType'],
        orElse: () => UserType.user,
      ),
      isOnline: data['isOnline'] ?? false,
      lastSeen: (data['lastSeen'] as Timestamp?)?.toDate(),
    );
  }

  String getDisplayName() {
    switch (userType) {
      case UserType.seller:
        return '🏪 $name';
      case UserType.rider:
        return '🚗 $name';
      case UserType.support:
        return '🆘 $name';
      case UserType.admin:
        return '👑 $name';
      default:
        return name;
    }
  }

  String getOnlineStatus() {
    if (isOnline) return 'Online';
    if (lastSeen == null) return 'Offline';

    final now = DateTime.now();
    final difference = now.difference(lastSeen!);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class ChatNotification {
  final String chatId;
  final String senderId;
  final String senderName;
  final String message;
  final ChatType chatType;
  final DateTime timestamp;

  ChatNotification({
    required this.chatId,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.chatType,
    required this.timestamp,
  });

  String getNotificationTitle() {
    switch (chatType) {
      case ChatType.support:
        return 'Support Message';
      case ChatType.userSeller:
        return 'Seller Message';
      case ChatType.userRider:
        return 'Rider Message';
      case ChatType.sellerRider:
        return 'Pickup Update';
      case ChatType.groupOrder:
        return 'Order Update';
      default:
        return 'New Message';
    }
  }

  String getNotificationBody() {
    return '$senderName: $message';
  }
}
