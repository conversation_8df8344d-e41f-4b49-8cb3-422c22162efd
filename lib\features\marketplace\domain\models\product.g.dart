// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProductAdapter extends TypeAdapter<Product> {
  @override
  final int typeId = 0;

  @override
  Product read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Product(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      price: fields[3] as double,
      originalPrice: fields[4] as double,
      currency: fields[5] as String,
      images: (fields[6] as List).cast<String>(),
      category: fields[7] as String,
      subcategory: fields[8] as String,
      brand: fields[9] as String,
      rating: fields[10] as double,
      reviewCount: fields[11] as int,
      inStock: fields[12] as bool,
      stockQuantity: fields[13] as int,
      vendorId: fields[14] as String,
      vendorName: fields[15] as String,
      specifications: (fields[16] as Map).cast<String, dynamic>(),
      tags: (fields[17] as List).cast<String>(),
      createdAt: fields[18] as DateTime,
      updatedAt: fields[19] as DateTime,
      isFeatured: fields[20] as bool,
      discountPercentage: fields[21] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, Product obj) {
    writer
      ..writeByte(22)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.price)
      ..writeByte(4)
      ..write(obj.originalPrice)
      ..writeByte(5)
      ..write(obj.currency)
      ..writeByte(6)
      ..write(obj.images)
      ..writeByte(7)
      ..write(obj.category)
      ..writeByte(8)
      ..write(obj.subcategory)
      ..writeByte(9)
      ..write(obj.brand)
      ..writeByte(10)
      ..write(obj.rating)
      ..writeByte(11)
      ..write(obj.reviewCount)
      ..writeByte(12)
      ..write(obj.inStock)
      ..writeByte(13)
      ..write(obj.stockQuantity)
      ..writeByte(14)
      ..write(obj.vendorId)
      ..writeByte(15)
      ..write(obj.vendorName)
      ..writeByte(16)
      ..write(obj.specifications)
      ..writeByte(17)
      ..write(obj.tags)
      ..writeByte(18)
      ..write(obj.createdAt)
      ..writeByte(19)
      ..write(obj.updatedAt)
      ..writeByte(20)
      ..write(obj.isFeatured)
      ..writeByte(21)
      ..write(obj.discountPercentage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CategoryAdapter extends TypeAdapter<Category> {
  @override
  final int typeId = 3;

  @override
  Category read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Category(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      imageUrl: fields[3] as String,
      iconName: fields[4] as String,
      colorHex: fields[5] as String,
      itemCount: fields[6] as int,
      isActive: fields[7] as bool,
      sortOrder: fields[8] as int,
      subcategories: (fields[9] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, Category obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.imageUrl)
      ..writeByte(4)
      ..write(obj.iconName)
      ..writeByte(5)
      ..write(obj.colorHex)
      ..writeByte(6)
      ..write(obj.itemCount)
      ..writeByte(7)
      ..write(obj.isActive)
      ..writeByte(8)
      ..write(obj.sortOrder)
      ..writeByte(9)
      ..write(obj.subcategories);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      originalPrice: (json['originalPrice'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'INR',
      images:
          (json['images'] as List<dynamic>).map((e) => e as String).toList(),
      category: json['category'] as String,
      subcategory: json['subcategory'] as String,
      brand: json['brand'] as String,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: (json['reviewCount'] as num?)?.toInt() ?? 0,
      inStock: json['inStock'] as bool? ?? true,
      stockQuantity: (json['stockQuantity'] as num?)?.toInt() ?? 0,
      vendorId: json['vendorId'] as String,
      vendorName: json['vendorName'] as String,
      specifications:
          json['specifications'] as Map<String, dynamic>? ?? const {},
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isFeatured: json['isFeatured'] as bool? ?? false,
      discountPercentage: (json['discountPercentage'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'currency': instance.currency,
      'images': instance.images,
      'category': instance.category,
      'subcategory': instance.subcategory,
      'brand': instance.brand,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'inStock': instance.inStock,
      'stockQuantity': instance.stockQuantity,
      'vendorId': instance.vendorId,
      'vendorName': instance.vendorName,
      'specifications': instance.specifications,
      'tags': instance.tags,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isFeatured': instance.isFeatured,
      'discountPercentage': instance.discountPercentage,
    };

Category _$CategoryFromJson(Map<String, dynamic> json) => Category(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String,
      iconName: json['iconName'] as String,
      colorHex: json['colorHex'] as String,
      itemCount: (json['itemCount'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      subcategories: (json['subcategories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$CategoryToJson(Category instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'iconName': instance.iconName,
      'colorHex': instance.colorHex,
      'itemCount': instance.itemCount,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'subcategories': instance.subcategories,
    };
