// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_rider.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptionsRider.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptionsRider {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'No Web API Key for this project',
    appId: '1:682359797670:web:your_web_app_id_here',
    messagingSenderId: '682359797670',
    projectId: 'projek-rider-575d2',
    authDomain: 'projek-rider-575d2.firebaseapp.com',
    storageBucket: 'projek-rider-575d2.appspot.com',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:682359797670:android:55b4babc039eedd70e8167',
    messagingSenderId: '682359797670',
    projectId: 'projek-rider-575d2',
    storageBucket: 'projek-rider-575d2.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:682359797670:ios:your_ios_app_id_here',
    messagingSenderId: '682359797670',
    projectId: 'projek-rider-575d2',
    storageBucket: 'projek-rider-575d2.appspot.com',
    iosBundleId: 'com.projek.rider',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:682359797670:macos:your_macos_app_id_here',
    messagingSenderId: '682359797670',
    projectId: 'projek-rider-575d2',
    storageBucket: 'projek-rider-575d2.appspot.com',
    iosBundleId: 'com.projek.rider',
  );
}
