rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() && request.auth.token.admin == true;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Users collection - users can only read/write their own profile
    match /users/{userId} {
      allow read, write: if isOwner(userId);
    }

    // Riders collection - riders can only access their own data
    match /riders/{userId} {
      allow read, write: if isOwner(userId);

      // Allow creation during registration
      allow create: if isAuthenticated() &&
                   request.auth.uid == userId &&
                   isValidRiderData(request.resource.data);

      // Allow updates to own profile
      allow update: if isOwner(userId) &&
                   isValidRiderUpdate(request.resource.data, resource.data);
    }

    // Orders collection - riders can only see orders assigned to them
    match /orders/{orderId} {
      allow read: if isAuthenticated() &&
                 (resource.data.riderId == request.auth.uid ||
                  resource.data.status == 'available');

      allow update: if isAuthenticated() &&
                   resource.data.riderId == request.auth.uid &&
                   isValidOrderUpdate(request.resource.data, resource.data);
    }

    // Trips collection - riders can only see their own trips
    match /trips/{tripId} {
      allow read, write: if isAuthenticated() &&
                        resource.data.riderId == request.auth.uid;
    }

    // Earnings collection - riders can only see their own earnings
    match /earnings/{earningId} {
      allow read: if isAuthenticated() &&
                 resource.data.riderId == request.auth.uid;
    }

    // Messages collection - authenticated users can read and create their own messages
    match /messages/{messageId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated()
        && request.auth.uid == request.resource.data.userId
        && request.auth.token.email == request.resource.data.userEmail;
      allow update, delete: if false;
    }

    // Support chats collection
    match /support_chats/{chatId} {
      allow read, write: if isAuthenticated() &&
        (isOwner(resource.data.userId) || isAdmin());

      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId;

      // Messages subcollection inside support chats
      match /messages/{messageId} {
        function getChatOwner() {
          return get(/databases/$(database)/documents/support_chats/$(chatId)).data.userId;
        }

        function isChatOwner() {
          return isAuthenticated() && request.auth.uid == getChatOwner();
        }

        allow read: if isAuthenticated() && (isChatOwner() || isAdmin());

        allow create: if isAuthenticated() &&
          ((isChatOwner() && request.auth.uid == request.resource.data.senderId) ||
           (isAdmin() && request.resource.data.senderType == 'agent'));

        allow update: if isAuthenticated() && (isChatOwner() || isAdmin());
        allow delete: if false;
      }
    }

    // Support agents collection - admin only
    match /support_agents/{agentId} {
      allow read, write: if isAdmin();
    }

    // Validation functions for rider data
    function isValidRiderData(data) {
      return data.keys().hasAll(['fullName', 'phoneNumber', 'email', 'dateOfBirth',
                                'vehicleType', 'createdAt', 'isVerified', 'isActive']) &&
             data.fullName is string && data.fullName.size() > 0 &&
             data.phoneNumber is string && data.phoneNumber.matches('^[6-9][0-9]{9}$') &&
             data.email is string && data.email.matches('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$') &&
             data.dateOfBirth is timestamp &&
             data.vehicleType is string &&
             data.createdAt is timestamp &&
             data.isVerified is bool &&
             data.isActive is bool;
    }

    function isValidRiderUpdate(newData, oldData) {
      // Users can update most fields but not verification status or creation date
      return newData.createdAt == oldData.createdAt &&
             newData.isVerified == oldData.isVerified &&
             isValidRiderData(newData);
    }

    function isValidOrderUpdate(newData, oldData) {
      // Riders can only update order status and location
      return newData.riderId == oldData.riderId &&
             newData.customerId == oldData.customerId &&
             newData.createdAt == oldData.createdAt &&
             (newData.status in ['accepted', 'picked_up', 'delivered', 'cancelled']);
    }
  }
}