// import 'dart:math'; // Commented out for now
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../domain/models/advanced_booking_models.dart';

class AdvancedBookingService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const Uuid _uuid = Uuid();

  static const String _bookingsCollection = 'advanced_bookings';
  static const String _templatesCollection = 'booking_templates';
  static const String _groupBookingsCollection = 'group_bookings';

  // Create single booking
  static Future<AdvancedBooking> createBooking({
    required String title,
    required String serviceType,
    required DateTime scheduledAt,
    required String pickupAddress,
    required double pickupLatitude,
    required double pickupLongitude,
    required String deliveryAddress,
    required double deliveryLatitude,
    required double deliveryLongitude,
    List<BookingItem> items = const [],
    String description = '',
    Map<String, dynamic> preferences = const {},
    String? notes,
    String? templateId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final bookingId = _uuid.v4();
      final now = DateTime.now();

      // Validate advance booking limit
      final advanceLimit = BookingConfig.getAdvanceBookingLimit(serviceType);
      final maxAdvanceDate = now.add(Duration(days: advanceLimit));
      if (scheduledAt.isAfter(maxAdvanceDate)) {
        throw Exception(
          'Booking cannot be scheduled more than $advanceLimit days in advance',
        );
      }

      final estimatedCost = items.fold(
        0.0,
        (sum, item) => sum + item.totalPrice,
      );

      final booking = AdvancedBooking(
        id: bookingId,
        userId: currentUser.uid,
        title: title,
        description: description,
        serviceType: serviceType,
        scheduledAt: scheduledAt,
        pickupAddress: pickupAddress,
        pickupLatitude: pickupLatitude,
        pickupLongitude: pickupLongitude,
        deliveryAddress: deliveryAddress,
        deliveryLatitude: deliveryLatitude,
        deliveryLongitude: deliveryLongitude,
        items: items,
        preferences: preferences,
        estimatedCost: estimatedCost,
        createdAt: now,
        updatedAt: now,
        notes: notes,
        templateId: templateId,
      );

      await _firestore
          .collection(_bookingsCollection)
          .doc(bookingId)
          .set(booking.toJson());

      // Update template usage count if created from template
      if (templateId != null) {
        await _incrementTemplateUsage(templateId);
      }

      return booking;
    } catch (e) {
      debugPrint('❌ Error creating booking: $e');
      throw Exception('Failed to create booking: ${e.toString()}');
    }
  }

  // Create recurring booking
  static Future<AdvancedBooking> createRecurringBooking({
    required String title,
    required String serviceType,
    required DateTime startDate,
    required DateTime endDate,
    required RecurrencePattern pattern,
    required String pickupAddress,
    required double pickupLatitude,
    required double pickupLongitude,
    required String deliveryAddress,
    required double deliveryLatitude,
    required double deliveryLongitude,
    List<BookingItem> items = const [],
    String description = '',
    Map<String, dynamic> preferences = const {},
    Map<String, dynamic> recurrenceConfig = const {},
    String? notes,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final bookingId = _uuid.v4();
      final now = DateTime.now();

      final estimatedCost = items.fold(
        0.0,
        (sum, item) => sum + item.totalPrice,
      );

      final booking = AdvancedBooking(
        id: bookingId,
        userId: currentUser.uid,
        type: BookingType.recurring,
        title: title,
        description: description,
        serviceType: serviceType,
        scheduledAt: startDate,
        endDate: endDate,
        recurrencePattern: pattern,
        recurrenceConfig: recurrenceConfig,
        pickupAddress: pickupAddress,
        pickupLatitude: pickupLatitude,
        pickupLongitude: pickupLongitude,
        deliveryAddress: deliveryAddress,
        deliveryLatitude: deliveryLatitude,
        deliveryLongitude: deliveryLongitude,
        items: items,
        preferences: preferences,
        estimatedCost: estimatedCost,
        createdAt: now,
        updatedAt: now,
        notes: notes,
      );

      await _firestore
          .collection(_bookingsCollection)
          .doc(bookingId)
          .set(booking.toJson());

      // Generate individual bookings for the recurrence
      await _generateRecurringBookings(booking);

      return booking;
    } catch (e) {
      debugPrint('❌ Error creating recurring booking: $e');
      throw Exception('Failed to create recurring booking: ${e.toString()}');
    }
  }

  // Create group booking
  static Future<GroupBooking> createGroupBooking({
    required String title,
    required String serviceType,
    required DateTime scheduledAt,
    required String pickupAddress,
    required double pickupLatitude,
    required double pickupLongitude,
    required String deliveryAddress,
    required double deliveryLatitude,
    required double deliveryLongitude,
    required List<String> participantEmails,
    String description = '',
    String costSplitMethod = 'equal',
    String? notes,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      if (participantEmails.length < BookingConfig.minGroupParticipants) {
        throw Exception(
          'Group booking requires at least ${BookingConfig.minGroupParticipants} participants',
        );
      }

      if (participantEmails.length > BookingConfig.maxGroupParticipants) {
        throw Exception(
          'Group booking cannot have more than ${BookingConfig.maxGroupParticipants} participants',
        );
      }

      final groupBookingId = _uuid.v4();
      final now = DateTime.now();

      // Create participants (in real app, would look up users by email)
      final participants = participantEmails.map((email) {
        return GroupParticipant(
          userId: _uuid.v4(), // In real app, would be actual user ID
          name: email.split('@')[0], // Simplified for demo
          email: email,
          joinedAt: now,
        );
      }).toList();

      final groupBooking = GroupBooking(
        id: groupBookingId,
        organizerId: currentUser.uid,
        title: title,
        description: description,
        serviceType: serviceType,
        scheduledAt: scheduledAt,
        participants: participants,
        costSplitMethod: costSplitMethod,
        pickupAddress: pickupAddress,
        pickupLatitude: pickupLatitude,
        pickupLongitude: pickupLongitude,
        deliveryAddress: deliveryAddress,
        deliveryLatitude: deliveryLatitude,
        deliveryLongitude: deliveryLongitude,
        createdAt: now,
        updatedAt: now,
        notes: notes,
      );

      await _firestore
          .collection(_groupBookingsCollection)
          .doc(groupBookingId)
          .set(groupBooking.toJson());

      // Send invitations to participants (in real app)
      await _sendGroupBookingInvitations(groupBooking);

      return groupBooking;
    } catch (e) {
      debugPrint('❌ Error creating group booking: $e');
      throw Exception('Failed to create group booking: ${e.toString()}');
    }
  }

  // Create booking template
  static Future<BookingTemplate> createTemplate({
    required String name,
    required String serviceType,
    required String pickupAddress,
    required double pickupLatitude,
    required double pickupLongitude,
    required String deliveryAddress,
    required double deliveryLatitude,
    required double deliveryLongitude,
    List<BookingItem> items = const [],
    String description = '',
    Map<String, dynamic> preferences = const {},
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final templateId = _uuid.v4();
      final now = DateTime.now();

      final template = BookingTemplate(
        id: templateId,
        userId: currentUser.uid,
        name: name,
        description: description,
        serviceType: serviceType,
        pickupAddress: pickupAddress,
        pickupLatitude: pickupLatitude,
        pickupLongitude: pickupLongitude,
        deliveryAddress: deliveryAddress,
        deliveryLatitude: deliveryLatitude,
        deliveryLongitude: deliveryLongitude,
        items: items,
        preferences: preferences,
        createdAt: now,
        updatedAt: now,
      );

      await _firestore
          .collection(_templatesCollection)
          .doc(templateId)
          .set(template.toJson());

      return template;
    } catch (e) {
      debugPrint('❌ Error creating template: $e');
      throw Exception('Failed to create template: ${e.toString()}');
    }
  }

  // Get user bookings
  static Stream<List<AdvancedBooking>> getUserBookings({
    String? userId,
    BookingType? type,
    BookingStatus? status,
    int limit = 50,
  }) {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return Stream.value([]);

      Query query = _firestore
          .collection(_bookingsCollection)
          .where('userId', isEqualTo: targetUserId);

      if (type != null) {
        query = query.where('type', isEqualTo: type.toString());
      }

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString());
      }

      return query
          .orderBy('scheduledAt', descending: true)
          .limit(limit)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs.map((doc) {
              return AdvancedBooking.fromJson(
                doc.data() as Map<String, dynamic>,
              );
            }).toList();
          });
    } catch (e) {
      debugPrint('❌ Error getting user bookings: $e');
      return Stream.value([]);
    }
  }

  // Get user templates
  static Future<List<BookingTemplate>> getUserTemplates({
    String? userId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return [];

      final snapshot = await _firestore
          .collection(_templatesCollection)
          .where('userId', isEqualTo: targetUserId)
          .where('isActive', isEqualTo: true)
          .orderBy('usageCount', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        return BookingTemplate.fromJson(doc.data());
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting user templates: $e');
      return [];
    }
  }

  // Get user group bookings
  static Stream<List<GroupBooking>> getUserGroupBookings({String? userId}) {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return Stream.value([]);

      return _firestore
          .collection(_groupBookingsCollection)
          .where('organizerId', isEqualTo: targetUserId)
          .orderBy('scheduledAt', descending: true)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs.map((doc) {
              return GroupBooking.fromJson(doc.data());
            }).toList();
          });
    } catch (e) {
      debugPrint('❌ Error getting user group bookings: $e');
      return Stream.value([]);
    }
  }

  // Update booking status
  static Future<void> updateBookingStatus({
    required String bookingId,
    required BookingStatus status,
  }) async {
    try {
      await _firestore.collection(_bookingsCollection).doc(bookingId).update({
        'status': status.toString(),
        'updatedAt': DateTime.now(),
      });
    } catch (e) {
      debugPrint('❌ Error updating booking status: $e');
    }
  }

  // Cancel booking
  static Future<void> cancelBooking(String bookingId) async {
    try {
      await updateBookingStatus(
        bookingId: bookingId,
        status: BookingStatus.cancelled,
      );
    } catch (e) {
      debugPrint('❌ Error cancelling booking: $e');
      throw Exception('Failed to cancel booking: ${e.toString()}');
    }
  }

  // Confirm group booking participation
  static Future<void> confirmGroupBookingParticipation({
    required String groupBookingId,
    required String participantId,
  }) async {
    try {
      final doc = await _firestore
          .collection(_groupBookingsCollection)
          .doc(groupBookingId)
          .get();
      if (!doc.exists) throw Exception('Group booking not found');

      final groupBooking = GroupBooking.fromJson(doc.data()!);
      final updatedParticipants = groupBooking.participants.map((participant) {
        if (participant.userId == participantId) {
          return GroupParticipant(
            userId: participant.userId,
            name: participant.name,
            email: participant.email,
            phoneNumber: participant.phoneNumber,
            hasConfirmed: true,
            shareAmount: participant.shareAmount,
            items: participant.items,
            joinedAt: participant.joinedAt,
            confirmedAt: DateTime.now(),
          );
        }
        return participant;
      }).toList();

      await _firestore
          .collection(_groupBookingsCollection)
          .doc(groupBookingId)
          .update({
            'participants': updatedParticipants.map((p) => p.toJson()).toList(),
            'updatedAt': DateTime.now(),
          });
    } catch (e) {
      debugPrint('❌ Error confirming group booking participation: $e');
      throw Exception('Failed to confirm participation: ${e.toString()}');
    }
  }

  // Private helper methods
  static Future<void> _generateRecurringBookings(
    AdvancedBooking recurringBooking,
  ) async {
    try {
      final config = BookingConfig.getRecurrenceConfig(
        recurringBooking.recurrencePattern!,
      );
      final intervalDays = config?['intervalDays'] ?? 1;
      final maxOccurrences = config?['maxOccurrences'] ?? 10;

      final generatedIds = <String>[];
      var currentDate = recurringBooking.scheduledAt;
      var occurrenceCount = 0;

      while (currentDate.isBefore(recurringBooking.endDate!) &&
          occurrenceCount < maxOccurrences) {
        final individualBookingId = _uuid.v4();

        final individualBooking = AdvancedBooking(
          id: individualBookingId,
          userId: recurringBooking.userId,
          type: BookingType.single,
          title: '${recurringBooking.title} (${occurrenceCount + 1})',
          description: recurringBooking.description,
          serviceType: recurringBooking.serviceType,
          scheduledAt: currentDate,
          pickupAddress: recurringBooking.pickupAddress,
          pickupLatitude: recurringBooking.pickupLatitude,
          pickupLongitude: recurringBooking.pickupLongitude,
          deliveryAddress: recurringBooking.deliveryAddress,
          deliveryLatitude: recurringBooking.deliveryLatitude,
          deliveryLongitude: recurringBooking.deliveryLongitude,
          items: recurringBooking.items,
          preferences: recurringBooking.preferences,
          estimatedCost: recurringBooking.estimatedCost,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          notes: recurringBooking.notes,
        );

        await _firestore
            .collection(_bookingsCollection)
            .doc(individualBookingId)
            .set(individualBooking.toJson());
        generatedIds.add(individualBookingId);

        currentDate = currentDate.add(Duration(days: intervalDays));
        occurrenceCount++;
      }

      // Update the recurring booking with generated IDs
      await _firestore
          .collection(_bookingsCollection)
          .doc(recurringBooking.id)
          .update({
            'generatedBookingIds': generatedIds,
            'updatedAt': DateTime.now(),
          });
    } catch (e) {
      debugPrint('❌ Error generating recurring bookings: $e');
    }
  }

  static Future<void> _incrementTemplateUsage(String templateId) async {
    try {
      await _firestore.collection(_templatesCollection).doc(templateId).update({
        'usageCount': FieldValue.increment(1),
        'updatedAt': DateTime.now(),
      });
    } catch (e) {
      debugPrint('❌ Error incrementing template usage: $e');
    }
  }

  static Future<void> _sendGroupBookingInvitations(
    GroupBooking groupBooking,
  ) async {
    try {
      // In a real app, this would send email/SMS invitations to participants
      debugPrint(
        '📧 Sending group booking invitations to ${groupBooking.participants.length} participants',
      );
    } catch (e) {
      debugPrint('❌ Error sending group booking invitations: $e');
    }
  }
}
