// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'safety_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class EmergencyContactAdapter extends TypeAdapter<EmergencyContact> {
  @override
  final int typeId = 73;

  @override
  EmergencyContact read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return EmergencyContact(
      id: fields[0] as String,
      userId: fields[1] as String,
      name: fields[2] as String,
      phoneNumber: fields[3] as String,
      relationship: fields[4] as String,
      isPrimary: fields[5] as bool,
      isActive: fields[6] as bool,
      createdAt: fields[7] as DateTime,
      updatedAt: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, EmergencyContact obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.phoneNumber)
      ..writeByte(4)
      ..write(obj.relationship)
      ..writeByte(5)
      ..write(obj.isPrimary)
      ..writeByte(6)
      ..write(obj.isActive)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EmergencyContactAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SOSAlertAdapter extends TypeAdapter<SOSAlert> {
  @override
  final int typeId = 74;

  @override
  SOSAlert read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SOSAlert(
      id: fields[0] as String,
      userId: fields[1] as String,
      userName: fields[2] as String,
      userPhone: fields[3] as String,
      type: fields[4] as EmergencyType,
      latitude: fields[5] as double,
      longitude: fields[6] as double,
      address: fields[7] as String,
      message: fields[8] as String?,
      imageUrls: (fields[9] as List).cast<String>(),
      audioUrls: (fields[10] as List).cast<String>(),
      triggeredAt: fields[11] as DateTime,
      acknowledgedAt: fields[12] as DateTime?,
      resolvedAt: fields[13] as DateTime?,
      status: fields[14] as String,
      notifiedContacts: (fields[15] as List).cast<String>(),
      orderId: fields[16] as String?,
      metadata: (fields[17] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, SOSAlert obj) {
    writer
      ..writeByte(18)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.userName)
      ..writeByte(3)
      ..write(obj.userPhone)
      ..writeByte(4)
      ..write(obj.type)
      ..writeByte(5)
      ..write(obj.latitude)
      ..writeByte(6)
      ..write(obj.longitude)
      ..writeByte(7)
      ..write(obj.address)
      ..writeByte(8)
      ..write(obj.message)
      ..writeByte(9)
      ..write(obj.imageUrls)
      ..writeByte(10)
      ..write(obj.audioUrls)
      ..writeByte(11)
      ..write(obj.triggeredAt)
      ..writeByte(12)
      ..write(obj.acknowledgedAt)
      ..writeByte(13)
      ..write(obj.resolvedAt)
      ..writeByte(14)
      ..write(obj.status)
      ..writeByte(15)
      ..write(obj.notifiedContacts)
      ..writeByte(16)
      ..write(obj.orderId)
      ..writeByte(17)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SOSAlertAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SafetyCheckInAdapter extends TypeAdapter<SafetyCheckIn> {
  @override
  final int typeId = 75;

  @override
  SafetyCheckIn read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SafetyCheckIn(
      id: fields[0] as String,
      userId: fields[1] as String,
      orderId: fields[2] as String?,
      latitude: fields[3] as double,
      longitude: fields[4] as double,
      address: fields[5] as String,
      scheduledAt: fields[6] as DateTime,
      completedAt: fields[7] as DateTime?,
      status: fields[8] as SafetyCheckStatus,
      note: fields[9] as String?,
      isAutomatic: fields[10] as bool,
      reminderCount: fields[11] as int,
      createdAt: fields[12] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SafetyCheckIn obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.orderId)
      ..writeByte(3)
      ..write(obj.latitude)
      ..writeByte(4)
      ..write(obj.longitude)
      ..writeByte(5)
      ..write(obj.address)
      ..writeByte(6)
      ..write(obj.scheduledAt)
      ..writeByte(7)
      ..write(obj.completedAt)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.note)
      ..writeByte(10)
      ..write(obj.isAutomatic)
      ..writeByte(11)
      ..write(obj.reminderCount)
      ..writeByte(12)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SafetyCheckInAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class IncidentReportAdapter extends TypeAdapter<IncidentReport> {
  @override
  final int typeId = 76;

  @override
  IncidentReport read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return IncidentReport(
      id: fields[0] as String,
      reporterId: fields[1] as String,
      reporterName: fields[2] as String,
      reporterType: fields[3] as String,
      incidentType: fields[4] as EmergencyType,
      title: fields[5] as String,
      description: fields[6] as String,
      latitude: fields[7] as double?,
      longitude: fields[8] as double?,
      address: fields[9] as String?,
      imageUrls: (fields[10] as List).cast<String>(),
      videoUrls: (fields[11] as List).cast<String>(),
      orderId: fields[12] as String?,
      involvedUserId: fields[13] as String?,
      status: fields[14] as IncidentStatus,
      reportedAt: fields[15] as DateTime,
      acknowledgedAt: fields[16] as DateTime?,
      resolvedAt: fields[17] as DateTime?,
      assignedTo: fields[18] as String?,
      resolution: fields[19] as String?,
      metadata: (fields[20] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, IncidentReport obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.reporterId)
      ..writeByte(2)
      ..write(obj.reporterName)
      ..writeByte(3)
      ..write(obj.reporterType)
      ..writeByte(4)
      ..write(obj.incidentType)
      ..writeByte(5)
      ..write(obj.title)
      ..writeByte(6)
      ..write(obj.description)
      ..writeByte(7)
      ..write(obj.latitude)
      ..writeByte(8)
      ..write(obj.longitude)
      ..writeByte(9)
      ..write(obj.address)
      ..writeByte(10)
      ..write(obj.imageUrls)
      ..writeByte(11)
      ..write(obj.videoUrls)
      ..writeByte(12)
      ..write(obj.orderId)
      ..writeByte(13)
      ..write(obj.involvedUserId)
      ..writeByte(14)
      ..write(obj.status)
      ..writeByte(15)
      ..write(obj.reportedAt)
      ..writeByte(16)
      ..write(obj.acknowledgedAt)
      ..writeByte(17)
      ..write(obj.resolvedAt)
      ..writeByte(18)
      ..write(obj.assignedTo)
      ..writeByte(19)
      ..write(obj.resolution)
      ..writeByte(20)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IncidentReportAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SafetySettingsAdapter extends TypeAdapter<SafetySettings> {
  @override
  final int typeId = 77;

  @override
  SafetySettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SafetySettings(
      id: fields[0] as String,
      userId: fields[1] as String,
      sosEnabled: fields[2] as bool,
      autoCheckInEnabled: fields[3] as bool,
      checkInIntervalMinutes: fields[4] as int,
      locationSharingEnabled: fields[5] as bool,
      emergencyContactsNotification: fields[6] as bool,
      adminNotification: fields[7] as bool,
      trustedContacts: (fields[8] as List).cast<String>(),
      notificationPreferences: (fields[9] as Map).cast<String, bool>(),
      updatedAt: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SafetySettings obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.sosEnabled)
      ..writeByte(3)
      ..write(obj.autoCheckInEnabled)
      ..writeByte(4)
      ..write(obj.checkInIntervalMinutes)
      ..writeByte(5)
      ..write(obj.locationSharingEnabled)
      ..writeByte(6)
      ..write(obj.emergencyContactsNotification)
      ..writeByte(7)
      ..write(obj.adminNotification)
      ..writeByte(8)
      ..write(obj.trustedContacts)
      ..writeByte(9)
      ..write(obj.notificationPreferences)
      ..writeByte(10)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SafetySettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EmergencyTypeAdapter extends TypeAdapter<EmergencyType> {
  @override
  final int typeId = 70;

  @override
  EmergencyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return EmergencyType.accident;
      case 1:
        return EmergencyType.medical;
      case 2:
        return EmergencyType.theft;
      case 3:
        return EmergencyType.harassment;
      case 4:
        return EmergencyType.vehicleBreakdown;
      case 5:
        return EmergencyType.other;
      default:
        return EmergencyType.accident;
    }
  }

  @override
  void write(BinaryWriter writer, EmergencyType obj) {
    switch (obj) {
      case EmergencyType.accident:
        writer.writeByte(0);
        break;
      case EmergencyType.medical:
        writer.writeByte(1);
        break;
      case EmergencyType.theft:
        writer.writeByte(2);
        break;
      case EmergencyType.harassment:
        writer.writeByte(3);
        break;
      case EmergencyType.vehicleBreakdown:
        writer.writeByte(4);
        break;
      case EmergencyType.other:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EmergencyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class IncidentStatusAdapter extends TypeAdapter<IncidentStatus> {
  @override
  final int typeId = 71;

  @override
  IncidentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return IncidentStatus.reported;
      case 1:
        return IncidentStatus.acknowledged;
      case 2:
        return IncidentStatus.investigating;
      case 3:
        return IncidentStatus.resolved;
      case 4:
        return IncidentStatus.closed;
      default:
        return IncidentStatus.reported;
    }
  }

  @override
  void write(BinaryWriter writer, IncidentStatus obj) {
    switch (obj) {
      case IncidentStatus.reported:
        writer.writeByte(0);
        break;
      case IncidentStatus.acknowledged:
        writer.writeByte(1);
        break;
      case IncidentStatus.investigating:
        writer.writeByte(2);
        break;
      case IncidentStatus.resolved:
        writer.writeByte(3);
        break;
      case IncidentStatus.closed:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IncidentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SafetyCheckStatusAdapter extends TypeAdapter<SafetyCheckStatus> {
  @override
  final int typeId = 72;

  @override
  SafetyCheckStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return SafetyCheckStatus.pending;
      case 1:
        return SafetyCheckStatus.completed;
      case 2:
        return SafetyCheckStatus.missed;
      case 3:
        return SafetyCheckStatus.overdue;
      default:
        return SafetyCheckStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, SafetyCheckStatus obj) {
    switch (obj) {
      case SafetyCheckStatus.pending:
        writer.writeByte(0);
        break;
      case SafetyCheckStatus.completed:
        writer.writeByte(1);
        break;
      case SafetyCheckStatus.missed:
        writer.writeByte(2);
        break;
      case SafetyCheckStatus.overdue:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SafetyCheckStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmergencyContact _$EmergencyContactFromJson(Map<String, dynamic> json) =>
    EmergencyContact(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String,
      relationship: json['relationship'] as String,
      isPrimary: json['isPrimary'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$EmergencyContactToJson(EmergencyContact instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'phoneNumber': instance.phoneNumber,
      'relationship': instance.relationship,
      'isPrimary': instance.isPrimary,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

SOSAlert _$SOSAlertFromJson(Map<String, dynamic> json) => SOSAlert(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userPhone: json['userPhone'] as String,
      type: $enumDecode(_$EmergencyTypeEnumMap, json['type']),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      message: json['message'] as String?,
      imageUrls: (json['imageUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      audioUrls: (json['audioUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      triggeredAt: DateTime.parse(json['triggeredAt'] as String),
      acknowledgedAt: json['acknowledgedAt'] == null
          ? null
          : DateTime.parse(json['acknowledgedAt'] as String),
      resolvedAt: json['resolvedAt'] == null
          ? null
          : DateTime.parse(json['resolvedAt'] as String),
      status: json['status'] as String? ?? 'active',
      notifiedContacts: (json['notifiedContacts'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      orderId: json['orderId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$SOSAlertToJson(SOSAlert instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'userPhone': instance.userPhone,
      'type': _$EmergencyTypeEnumMap[instance.type]!,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'message': instance.message,
      'imageUrls': instance.imageUrls,
      'audioUrls': instance.audioUrls,
      'triggeredAt': instance.triggeredAt.toIso8601String(),
      'acknowledgedAt': instance.acknowledgedAt?.toIso8601String(),
      'resolvedAt': instance.resolvedAt?.toIso8601String(),
      'status': instance.status,
      'notifiedContacts': instance.notifiedContacts,
      'orderId': instance.orderId,
      'metadata': instance.metadata,
    };

const _$EmergencyTypeEnumMap = {
  EmergencyType.accident: 'accident',
  EmergencyType.medical: 'medical',
  EmergencyType.theft: 'theft',
  EmergencyType.harassment: 'harassment',
  EmergencyType.vehicleBreakdown: 'vehicleBreakdown',
  EmergencyType.other: 'other',
};

SafetyCheckIn _$SafetyCheckInFromJson(Map<String, dynamic> json) =>
    SafetyCheckIn(
      id: json['id'] as String,
      userId: json['userId'] as String,
      orderId: json['orderId'] as String?,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      scheduledAt: DateTime.parse(json['scheduledAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      status: $enumDecodeNullable(_$SafetyCheckStatusEnumMap, json['status']) ??
          SafetyCheckStatus.pending,
      note: json['note'] as String?,
      isAutomatic: json['isAutomatic'] as bool? ?? false,
      reminderCount: (json['reminderCount'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$SafetyCheckInToJson(SafetyCheckIn instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'orderId': instance.orderId,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'scheduledAt': instance.scheduledAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'status': _$SafetyCheckStatusEnumMap[instance.status]!,
      'note': instance.note,
      'isAutomatic': instance.isAutomatic,
      'reminderCount': instance.reminderCount,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$SafetyCheckStatusEnumMap = {
  SafetyCheckStatus.pending: 'pending',
  SafetyCheckStatus.completed: 'completed',
  SafetyCheckStatus.missed: 'missed',
  SafetyCheckStatus.overdue: 'overdue',
};

IncidentReport _$IncidentReportFromJson(Map<String, dynamic> json) =>
    IncidentReport(
      id: json['id'] as String,
      reporterId: json['reporterId'] as String,
      reporterName: json['reporterName'] as String,
      reporterType: json['reporterType'] as String,
      incidentType: $enumDecode(_$EmergencyTypeEnumMap, json['incidentType']),
      title: json['title'] as String,
      description: json['description'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      address: json['address'] as String?,
      imageUrls: (json['imageUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      videoUrls: (json['videoUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      orderId: json['orderId'] as String?,
      involvedUserId: json['involvedUserId'] as String?,
      status: $enumDecodeNullable(_$IncidentStatusEnumMap, json['status']) ??
          IncidentStatus.reported,
      reportedAt: DateTime.parse(json['reportedAt'] as String),
      acknowledgedAt: json['acknowledgedAt'] == null
          ? null
          : DateTime.parse(json['acknowledgedAt'] as String),
      resolvedAt: json['resolvedAt'] == null
          ? null
          : DateTime.parse(json['resolvedAt'] as String),
      assignedTo: json['assignedTo'] as String?,
      resolution: json['resolution'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$IncidentReportToJson(IncidentReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'reporterId': instance.reporterId,
      'reporterName': instance.reporterName,
      'reporterType': instance.reporterType,
      'incidentType': _$EmergencyTypeEnumMap[instance.incidentType]!,
      'title': instance.title,
      'description': instance.description,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'imageUrls': instance.imageUrls,
      'videoUrls': instance.videoUrls,
      'orderId': instance.orderId,
      'involvedUserId': instance.involvedUserId,
      'status': _$IncidentStatusEnumMap[instance.status]!,
      'reportedAt': instance.reportedAt.toIso8601String(),
      'acknowledgedAt': instance.acknowledgedAt?.toIso8601String(),
      'resolvedAt': instance.resolvedAt?.toIso8601String(),
      'assignedTo': instance.assignedTo,
      'resolution': instance.resolution,
      'metadata': instance.metadata,
    };

const _$IncidentStatusEnumMap = {
  IncidentStatus.reported: 'reported',
  IncidentStatus.acknowledged: 'acknowledged',
  IncidentStatus.investigating: 'investigating',
  IncidentStatus.resolved: 'resolved',
  IncidentStatus.closed: 'closed',
};

SafetySettings _$SafetySettingsFromJson(Map<String, dynamic> json) =>
    SafetySettings(
      id: json['id'] as String,
      userId: json['userId'] as String,
      sosEnabled: json['sosEnabled'] as bool? ?? true,
      autoCheckInEnabled: json['autoCheckInEnabled'] as bool? ?? true,
      checkInIntervalMinutes:
          (json['checkInIntervalMinutes'] as num?)?.toInt() ?? 30,
      locationSharingEnabled: json['locationSharingEnabled'] as bool? ?? true,
      emergencyContactsNotification:
          json['emergencyContactsNotification'] as bool? ?? true,
      adminNotification: json['adminNotification'] as bool? ?? true,
      trustedContacts: (json['trustedContacts'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      notificationPreferences:
          (json['notificationPreferences'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, e as bool),
              ) ??
              const {},
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$SafetySettingsToJson(SafetySettings instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'sosEnabled': instance.sosEnabled,
      'autoCheckInEnabled': instance.autoCheckInEnabled,
      'checkInIntervalMinutes': instance.checkInIntervalMinutes,
      'locationSharingEnabled': instance.locationSharingEnabled,
      'emergencyContactsNotification': instance.emergencyContactsNotification,
      'adminNotification': instance.adminNotification,
      'trustedContacts': instance.trustedContacts,
      'notificationPreferences': instance.notificationPreferences,
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
