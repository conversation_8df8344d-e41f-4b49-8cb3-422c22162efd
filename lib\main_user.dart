import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'core/config/app_config.dart';
import 'core/config/environment.dart';
import 'core/services/notification_service.dart';
import 'core/services/analytics_service.dart';
import 'core/database/hive_service.dart';
import 'core/utils/app_logger.dart';
import 'firebase_options_user.dart';
import 'src/app.dart';

void main() async {
  await _initializeUserApp();
  runApp(const ProviderScope(child: ProjekUserApp()));
}

Future<void> _initializeUserApp() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // System UI configuration
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
      ),
    );

    // Environment setup
    EnvironmentConfig.setEnvironment(Environment.production);
    AppConfig.setAppType(AppType.user);

    // Core services initialization
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptionsUser.currentPlatform,
      );
    } catch (e) {
      // Firebase already initialized (hot restart case)
      AppLogger.info('Firebase already initialized: $e');
    }

    // Initialize Hive first, then other services
    try {
      await Hive.initFlutter();
      await HiveService.initialize();
    } catch (e) {
      AppLogger.warning(
        'Hive initialization failed, continuing without local storage: $e',
      );
    }

    // Initialize remaining services in parallel
    await Future.wait([
      NotificationService.initialize(),
      AnalyticsService.initialize(),
    ]);

    AppLogger.info('User app initialized successfully');
  } catch (e, stackTrace) {
    AppLogger.error('Failed to initialize user app', e, stackTrace);
    rethrow;
  }
}

class ProjekUserApp extends ConsumerWidget {
  const ProjekUserApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Set app type for user app
    AppConfig.setAppType(AppType.user);

    return const ProjekApp();
  }
}

class UserLifecycleWrapper extends StatefulWidget {
  final Widget child;

  const UserLifecycleWrapper({super.key, required this.child});

  @override
  State<UserLifecycleWrapper> createState() => _UserLifecycleWrapperState();
}

class _UserLifecycleWrapperState extends State<UserLifecycleWrapper>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // Initialize app lifecycle service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // This will be handled by the provider when needed
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Handle lifecycle changes here if needed
  }

  @override
  Widget build(BuildContext context) => widget.child;
}
