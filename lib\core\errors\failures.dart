import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final int? code;

  const Failure({
    required this.message,
    this.code,
  });

  @override
  List<Object?> get props => [message, code];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
  });
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
  });
}

class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
  });
}

class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code,
  });
}

// Auth failures
class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.code,
  });
}

class UnauthorizedFailure extends AuthFailure {
  const UnauthorizedFailure({
    super.message = 'Unauthorized access',
    super.code = 401,
  });
}

class ForbiddenFailure extends AuthFailure {
  const ForbiddenFailure({
    super.message = 'Access forbidden',
    super.code = 403,
  });
}

// Data failures
class NotFoundFailure extends Failure {
  const NotFoundFailure({
    super.message = 'Resource not found',
    super.code = 404,
  });
}

class ConflictFailure extends Failure {
  const ConflictFailure({
    super.message = 'Resource conflict',
    super.code = 409,
  });
}

// Payment failures
class PaymentFailure extends Failure {
  const PaymentFailure({
    required super.message,
    super.code,
  });
}

class InsufficientFundsFailure extends PaymentFailure {
  const InsufficientFundsFailure({
    super.message = 'Insufficient funds',
    super.code = 402,
  });
}

// Location failures
class LocationFailure extends Failure {
  const LocationFailure({
    required super.message,
    super.code,
  });
}

class LocationPermissionFailure extends LocationFailure {
  const LocationPermissionFailure({
    super.message = 'Location permission denied',
    super.code,
  });
}

class LocationServiceFailure extends LocationFailure {
  const LocationServiceFailure({
    super.message = 'Location service disabled',
    super.code,
  });
}

// File failures
class FileFailure extends Failure {
  const FileFailure({
    required super.message,
    super.code,
  });
}

class FileSizeFailure extends FileFailure {
  const FileSizeFailure({
    super.message = 'File size exceeds limit',
    super.code,
  });
}

class FileTypeFailure extends FileFailure {
  const FileTypeFailure({
    super.message = 'Invalid file type',
    super.code,
  });
}
