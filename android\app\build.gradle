plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    // Google Services plugin for Firebase
    id "com.google.gms.google-services"
}

// Load keystore properties
def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.projek.user"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    flavorDimensions "app", "environment"

    productFlavors {
        // App flavors
        user {
            dimension "app"
            applicationId = "com.projek.user"
            versionNameSuffix "-user"
            manifestPlaceholders = [appName: "Projek User"]
        }

        rider {
            dimension "app"
            applicationId = "com.projek.rider"
            versionNameSuffix "-rider"
            manifestPlaceholders = [appName: "Projek Rider"]
        }

        seller {
            dimension "app"
            applicationId = "com.projek.seller"
            versionNameSuffix "-seller"
            manifestPlaceholders = [appName: "Projek Seller"]
        }

        // Environment flavors
        dev {
            dimension "environment"
            versionNameSuffix "-dev"
            manifestPlaceholders = [usesCleartextTraffic: "true"]
        }

        prod {
            dimension "environment"
            manifestPlaceholders = [usesCleartextTraffic: "false"]
        }
    }

    signingConfigs {
        if (keystorePropertiesFile.exists()) {
            release {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
            }
        } else {
            release {
                // Default debug keystore settings
                keyAlias 'androiddebugkey'
                keyPassword 'android'
                storeFile file('debug.keystore')
                storePassword 'android'
            }
        }
    }

    defaultConfig {
        // Application ID is defined in product flavors
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = 34  // Android 14 - Compatible with Vivo V23 5G
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        // Enable multidex to handle large number of methods
        multiDexEnabled = true

        // Ensure compatibility with modern Android devices and emulators
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64', 'x86'
        }
    }

    buildFeatures {
        buildConfig = true
    }

    // Create universal APKs for better device compatibility
    splits {
        abi {
            enable false  // Disabled for universal APK compatibility
            reset()
            include 'arm64-v8a', 'armeabi-v7a', 'x86_64', 'x86'
            universalApk true  // Create universal APK for all devices
        }
    }

    buildTypes {
        debug {
            debuggable = true
            minifyEnabled = false
            shrinkResources = false
            manifestPlaceholders = [usesCleartextTraffic: "true"]
        }

        release {
            // Enable size optimization for smaller APK
            minifyEnabled = true
            shrinkResources = true
            manifestPlaceholders = [usesCleartextTraffic: "false"]
            if (keystorePropertiesFile.exists()) {
                signingConfig = signingConfigs.release
            }
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // Ensure APK outputs are in the expected location
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def outputFile = output.outputFile
            if (outputFile != null && outputFile.name.endsWith('.apk')) {
                // Copy APK to flutter-apk directory for Flutter to find
                def flutterApkDir = new File(project.rootDir.parent, "build/app/outputs/flutter-apk")
                flutterApkDir.mkdirs()

                def targetFile = new File(flutterApkDir, "app-debug.apk")

                // Create a task to copy the APK
                def copyTask = tasks.create("copy${variant.name.capitalize()}Apk", Copy) {
                    from outputFile
                    into flutterApkDir
                    rename { String fileName ->
                        "app-debug.apk"
                    }
                }

                // Make the copy task depend on the assemble task
                copyTask.dependsOn variant.assembleProvider

                // Make the assemble task depend on the copy task
                tasks.named("assemble${variant.name.capitalize()}").configure {
                    finalizedBy copyTask
                }
            }
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Import the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:34.0.0')

    // Add the dependency for the Firebase Authentication library
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-auth'

    // MultiDex support for large apps
    implementation 'androidx.multidex:multidex:2.0.1'

    // Core library desugaring for Java 8+ features
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
}
