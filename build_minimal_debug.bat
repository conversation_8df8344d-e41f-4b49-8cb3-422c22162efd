@echo off
echo ========================================
echo   MINIMAL DEBUG BUILD FOR VIVO V23 5G
echo ========================================
echo.

echo Step 1: Cleaning previous builds...
flutter clean

echo.
echo Step 2: Getting dependencies...
flutter pub get

echo.
echo Step 3: Building minimal debug APK (ARM64 only for Vivo V23 5G)...
flutter build apk --debug --target-platform android-arm64

echo.
echo Step 4: Checking build output...
if exist "build\app\outputs\flutter-apk\app-debug.apk" (
    echo SUCCESS: Debug APK created!
    echo Location: build\app\outputs\flutter-apk\app-debug.apk
    echo.
    echo Copying to APK_RELEASE directory...
    if not exist "APK_RELEASE" mkdir "APK_RELEASE"
    copy "build\app\outputs\flutter-apk\app-debug.apk" "APK_RELEASE\app-debug-signed.apk"
    echo.
    echo File size:
    dir "APK_RELEASE\app-debug-signed.apk" | findstr "app-debug"
) else (
    echo ERROR: APK build failed or file not found
)

echo.
echo ========================================
echo   BUILD COMPLETED
echo ========================================
echo.
echo Transfer app-debug-signed.apk to your Vivo V23 5G and install!
pause
