import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../config/app_config.dart';
import '../services/auth_service.dart';
import '../services/notification_service.dart';
import '../services/analytics_service.dart';

enum AppType { user, rider, seller }

enum CrossAppEventType {
  orderCreated,
  orderAccepted,
  orderPickedUp,
  orderDelivered,
  serviceBooked,
  serviceConfirmed,
  serviceStarted,
  serviceCompleted,
  paymentProcessed,
  userLocationUpdate,
  riderLocationUpdate,
  chatMessage,
  emergencyAlert,
}

class CrossAppEvent {
  final String id;
  final CrossAppEventType type;
  final String sourceApp;
  final String targetApp;
  final String userId;
  final String? riderId;
  final String? sellerId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final bool isUrgent;

  const CrossAppEvent({
    required this.id,
    required this.type,
    required this.sourceApp,
    required this.targetApp,
    required this.userId,
    this.riderId,
    this.sellerId,
    required this.data,
    required this.timestamp,
    this.isUrgent = false,
  });

  factory CrossAppEvent.fromJson(Map<String, dynamic> json) {
    return CrossAppEvent(
      id: json['id'],
      type: CrossAppEventType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      sourceApp: json['sourceApp'],
      targetApp: json['targetApp'],
      userId: json['userId'],
      riderId: json['riderId'],
      sellerId: json['sellerId'],
      data: Map<String, dynamic>.from(json['data']),
      timestamp: DateTime.parse(json['timestamp']),
      isUrgent: json['isUrgent'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'sourceApp': sourceApp,
      'targetApp': targetApp,
      'userId': userId,
      'riderId': riderId,
      'sellerId': sellerId,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'isUrgent': isUrgent,
    };
  }
}

class MultiAppIntegrationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  static const String _eventsCollection = 'cross_app_events';
  static const String _ordersCollection = 'unified_orders';
  static const String _trackingCollection = 'real_time_tracking';
  
  static StreamSubscription<QuerySnapshot>? _eventSubscription;
  static StreamSubscription<QuerySnapshot>? _trackingSubscription;
  
  // Event handlers
  static final Map<CrossAppEventType, List<Function(CrossAppEvent)>> _eventHandlers = {};
  
  // Initialize multi-app integration
  static Future<void> initialize() async {
    try {
      await _setupEventListeners();
      await _setupCrossAppMessaging();
      await _initializeLocationTracking();
      
      debugPrint('✅ Multi-App Integration Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Multi-App Integration: $e');
    }
  }

  // Setup real-time event listeners
  static Future<void> _setupEventListeners() async {
    final currentUserId = AuthService.currentUserId;
    if (currentUserId == null) return;

    // Listen to events targeted at current user/app
    _eventSubscription = _firestore
        .collection(_eventsCollection)
        .where('userId', isEqualTo: currentUserId)
        .where('targetApp', isEqualTo: AppConfig.currentAppType.toString())
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .listen(_handleIncomingEvent);
  }

  // Handle incoming cross-app events
  static void _handleIncomingEvent(QuerySnapshot snapshot) {
    for (final doc in snapshot.docChanges) {
      if (doc.type == DocumentChangeType.added) {
        try {
          final event = CrossAppEvent.fromJson(
            doc.doc.data() as Map<String, dynamic>,
          );
          _processEvent(event);
        } catch (e) {
          debugPrint('Error processing event: $e');
        }
      }
    }
  }

  // Process cross-app event
  static void _processEvent(CrossAppEvent event) {
    // Call registered handlers
    final handlers = _eventHandlers[event.type] ?? [];
    for (final handler in handlers) {
      try {
        handler(event);
      } catch (e) {
        debugPrint('Error in event handler: $e');
      }
    }

    // Show notification if urgent
    if (event.isUrgent) {
      _showUrgentNotification(event);
    }

    // Log analytics
    AnalyticsService.logEvent('cross_app_event_received', {
      'event_type': event.type.toString(),
      'source_app': event.sourceApp,
      'is_urgent': event.isUrgent,
    });
  }

  // Send cross-app event
  static Future<void> sendEvent(CrossAppEvent event) async {
    try {
      await _firestore.collection(_eventsCollection).add(event.toJson());
      
      // Send push notification to target app
      await _sendCrossAppNotification(event);
      
      debugPrint('✅ Cross-app event sent: ${event.type}');
    } catch (e) {
      debugPrint('❌ Error sending cross-app event: $e');
      throw Exception('Failed to send cross-app event');
    }
  }

  // Register event handler
  static void registerEventHandler(
    CrossAppEventType eventType,
    Function(CrossAppEvent) handler,
  ) {
    _eventHandlers[eventType] ??= [];
    _eventHandlers[eventType]!.add(handler);
  }

  // Unregister event handler
  static void unregisterEventHandler(
    CrossAppEventType eventType,
    Function(CrossAppEvent) handler,
  ) {
    _eventHandlers[eventType]?.remove(handler);
  }

  // Create unified order (links user order with rider delivery)
  static Future<String> createUnifiedOrder({
    required String orderId,
    required String userId,
    required String sellerId,
    String? riderId,
    required Map<String, dynamic> orderData,
    required Map<String, dynamic> deliveryData,
  }) async {
    try {
      final unifiedOrder = {
        'id': orderId,
        'userId': userId,
        'sellerId': sellerId,
        'riderId': riderId,
        'orderData': orderData,
        'deliveryData': deliveryData,
        'status': 'created',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'tracking': {
          'currentLocation': null,
          'estimatedDeliveryTime': null,
          'route': [],
          'milestones': [],
        },
      };

      await _firestore.collection(_ordersCollection).doc(orderId).set(unifiedOrder);

      // Send events to all relevant apps
      await _notifyOrderCreation(orderId, userId, sellerId, riderId);

      return orderId;
    } catch (e) {
      debugPrint('❌ Error creating unified order: $e');
      throw Exception('Failed to create unified order');
    }
  }

  // Update order status across apps
  static Future<void> updateOrderStatus({
    required String orderId,
    required String newStatus,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final Map<String, dynamic> updateData = {
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (additionalData != null) {
        updateData.addAll(additionalData);
      }

      await _firestore.collection(_ordersCollection).doc(orderId).update(updateData);

      // Get order details for notifications
      final orderDoc = await _firestore.collection(_ordersCollection).doc(orderId).get();
      if (orderDoc.exists) {
        final orderData = orderDoc.data()!;
        await _notifyOrderStatusUpdate(orderId, newStatus, orderData);
      }
    } catch (e) {
      debugPrint('❌ Error updating order status: $e');
      throw Exception('Failed to update order status');
    }
  }

  // Real-time location tracking
  static Future<void> updateLocation({
    required String orderId,
    required double latitude,
    required double longitude,
    String? riderId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final locationData = {
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': FieldValue.serverTimestamp(),
        'riderId': riderId,
        ...?additionalData,
      };

      // Update in tracking collection
      await _firestore
          .collection(_trackingCollection)
          .doc(orderId)
          .set(locationData, SetOptions(merge: true));

      // Update in unified order
      await _firestore.collection(_ordersCollection).doc(orderId).update({
        'tracking.currentLocation': {
          'latitude': latitude,
          'longitude': longitude,
          'timestamp': FieldValue.serverTimestamp(),
        },
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Send real-time update to user app
      await sendEvent(CrossAppEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: CrossAppEventType.riderLocationUpdate,
        sourceApp: 'rider',
        targetApp: 'user',
        userId: '', // Will be filled from order data
        riderId: riderId,
        data: locationData,
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      debugPrint('❌ Error updating location: $e');
    }
  }

  // Get real-time tracking stream
  static Stream<DocumentSnapshot> getTrackingStream(String orderId) {
    return _firestore.collection(_trackingCollection).doc(orderId).snapshots();
  }

  // Get unified order stream
  static Stream<DocumentSnapshot> getUnifiedOrderStream(String orderId) {
    return _firestore.collection(_ordersCollection).doc(orderId).snapshots();
  }

  // Setup cross-app messaging
  static Future<void> _setupCrossAppMessaging() async {
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle message taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageTap);
  }

  // Handle background message
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Background message: ${message.messageId}');
    // Process cross-app message
  }

  // Handle foreground message
  static void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Foreground message: ${message.messageId}');
    
    // Show in-app notification
    if (message.data.containsKey('cross_app_event')) {
      final eventData = jsonDecode(message.data['cross_app_event']);
      final event = CrossAppEvent.fromJson(eventData);
      _processEvent(event);
    }
  }

  // Handle message tap
  static void _handleMessageTap(RemoteMessage message) {
    debugPrint('Message tapped: ${message.messageId}');
    // Navigate to relevant screen based on message data
  }

  // Send cross-app notification
  static Future<void> _sendCrossAppNotification(CrossAppEvent event) async {
    // This would integrate with your push notification service
    // to send notifications to the target app
    debugPrint('Sending cross-app notification for ${event.type}');
  }

  // Show urgent notification
  static void _showUrgentNotification(CrossAppEvent event) {
    NotificationService.showLocalNotification(
      title: _getEventTitle(event.type),
      body: _getEventBody(event.type, event.data),
      payload: event.id,
    );
  }

  // Notify order creation
  static Future<void> _notifyOrderCreation(
    String orderId,
    String userId,
    String sellerId,
    String? riderId,
  ) async {
    // Notify seller app
    await sendEvent(CrossAppEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: CrossAppEventType.orderCreated,
      sourceApp: 'user',
      targetApp: 'seller',
      userId: userId,
      sellerId: sellerId,
      data: {'orderId': orderId},
      timestamp: DateTime.now(),
      isUrgent: true,
    ));

    // Notify rider app if assigned
    if (riderId != null) {
      await sendEvent(CrossAppEvent(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: CrossAppEventType.orderCreated,
        sourceApp: 'user',
        targetApp: 'rider',
        userId: userId,
        riderId: riderId,
        data: {'orderId': orderId},
        timestamp: DateTime.now(),
        isUrgent: true,
      ));
    }
  }

  // Notify order status update
  static Future<void> _notifyOrderStatusUpdate(
    String orderId,
    String newStatus,
    Map<String, dynamic> orderData,
  ) async {
    final userId = orderData['userId'];
    final sellerId = orderData['sellerId'];
    final riderId = orderData['riderId'];

    // Notify user app
    await sendEvent(CrossAppEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: _getEventTypeFromStatus(newStatus),
      sourceApp: _getSourceAppFromStatus(newStatus),
      targetApp: 'user',
      userId: userId,
      sellerId: sellerId,
      riderId: riderId,
      data: {'orderId': orderId, 'status': newStatus},
      timestamp: DateTime.now(),
      isUrgent: _isStatusUrgent(newStatus),
    ));
  }

  // Initialize location tracking
  static Future<void> _initializeLocationTracking() async {
    // Setup location tracking for current app type
    if (AppConfig.currentAppType.toString() == AppType.rider.toString()) {
      // Initialize rider location tracking
      debugPrint('🚗 Rider location tracking initialized');
    }
  }

  // Helper methods
  static String _getEventTitle(CrossAppEventType type) {
    switch (type) {
      case CrossAppEventType.orderCreated:
        return 'New Order';
      case CrossAppEventType.orderAccepted:
        return 'Order Accepted';
      case CrossAppEventType.orderPickedUp:
        return 'Order Picked Up';
      case CrossAppEventType.orderDelivered:
        return 'Order Delivered';
      case CrossAppEventType.serviceBooked:
        return 'Service Booked';
      case CrossAppEventType.serviceConfirmed:
        return 'Service Confirmed';
      case CrossAppEventType.riderLocationUpdate:
        return 'Location Update';
      default:
        return 'Update';
    }
  }

  static String _getEventBody(CrossAppEventType type, Map<String, dynamic> data) {
    switch (type) {
      case CrossAppEventType.orderCreated:
        return 'A new order has been placed';
      case CrossAppEventType.orderAccepted:
        return 'Your order has been accepted';
      case CrossAppEventType.orderPickedUp:
        return 'Your order is on the way';
      case CrossAppEventType.orderDelivered:
        return 'Your order has been delivered';
      case CrossAppEventType.riderLocationUpdate:
        return 'Delivery location updated';
      default:
        return 'You have a new update';
    }
  }

  static CrossAppEventType _getEventTypeFromStatus(String status) {
    switch (status) {
      case 'accepted':
        return CrossAppEventType.orderAccepted;
      case 'picked_up':
        return CrossAppEventType.orderPickedUp;
      case 'delivered':
        return CrossAppEventType.orderDelivered;
      default:
        return CrossAppEventType.orderCreated;
    }
  }

  static String _getSourceAppFromStatus(String status) {
    switch (status) {
      case 'accepted':
        return 'seller';
      case 'picked_up':
      case 'delivered':
        return 'rider';
      default:
        return 'user';
    }
  }

  static bool _isStatusUrgent(String status) {
    return ['accepted', 'picked_up', 'delivered'].contains(status);
  }

  // Cleanup
  static void dispose() {
    _eventSubscription?.cancel();
    _trackingSubscription?.cancel();
    _eventHandlers.clear();
  }
}

