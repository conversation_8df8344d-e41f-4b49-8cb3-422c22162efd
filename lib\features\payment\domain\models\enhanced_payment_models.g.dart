// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enhanced_payment_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentCardAdapter extends TypeAdapter<PaymentCard> {
  @override
  final int typeId = 42;

  @override
  PaymentCard read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentCard(
      id: fields[0] as String,
      userId: fields[1] as String,
      cardNumber: fields[2] as String,
      cardHolderName: fields[3] as String,
      expiryMonth: fields[4] as String,
      expiryYear: fields[5] as String,
      cardType: fields[6] as String,
      bankName: fields[7] as String,
      isDefault: fields[8] as bool,
      createdAt: fields[9] as DateTime,
      updatedAt: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentCard obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.cardNumber)
      ..writeByte(3)
      ..write(obj.cardHolderName)
      ..writeByte(4)
      ..write(obj.expiryMonth)
      ..writeByte(5)
      ..write(obj.expiryYear)
      ..writeByte(6)
      ..write(obj.cardType)
      ..writeByte(7)
      ..write(obj.bankName)
      ..writeByte(8)
      ..write(obj.isDefault)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentCardAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UPIAccountAdapter extends TypeAdapter<UPIAccount> {
  @override
  final int typeId = 43;

  @override
  UPIAccount read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UPIAccount(
      id: fields[0] as String,
      userId: fields[1] as String,
      upiId: fields[2] as String,
      bankName: fields[3] as String,
      accountHolderName: fields[4] as String,
      isVerified: fields[5] as bool,
      isDefault: fields[6] as bool,
      createdAt: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, UPIAccount obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.upiId)
      ..writeByte(3)
      ..write(obj.bankName)
      ..writeByte(4)
      ..write(obj.accountHolderName)
      ..writeByte(5)
      ..write(obj.isVerified)
      ..writeByte(6)
      ..write(obj.isDefault)
      ..writeByte(7)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UPIAccountAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentSplitAdapter extends TypeAdapter<PaymentSplit> {
  @override
  final int typeId = 44;

  @override
  PaymentSplit read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentSplit(
      id: fields[0] as String,
      paymentId: fields[1] as String,
      method: fields[2] as PaymentMethod,
      amount: fields[3] as double,
      percentage: fields[4] as double,
      cardId: fields[5] as String?,
      upiId: fields[6] as String?,
      status: fields[7] as PaymentStatus,
      transactionId: fields[8] as String?,
      createdAt: fields[9] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentSplit obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.paymentId)
      ..writeByte(2)
      ..write(obj.method)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.percentage)
      ..writeByte(5)
      ..write(obj.cardId)
      ..writeByte(6)
      ..write(obj.upiId)
      ..writeByte(7)
      ..write(obj.status)
      ..writeByte(8)
      ..write(obj.transactionId)
      ..writeByte(9)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentSplitAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EnhancedPaymentAdapter extends TypeAdapter<EnhancedPayment> {
  @override
  final int typeId = 45;

  @override
  EnhancedPayment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return EnhancedPayment(
      id: fields[0] as String,
      userId: fields[1] as String,
      orderId: fields[2] as String,
      totalAmount: fields[3] as double,
      paidAmount: fields[4] as double,
      refundedAmount: fields[5] as double,
      currency: fields[6] as String,
      status: fields[7] as PaymentStatus,
      splits: (fields[8] as List).cast<PaymentSplit>(),
      metadata: (fields[9] as Map).cast<String, dynamic>(),
      createdAt: fields[10] as DateTime,
      updatedAt: fields[11] as DateTime,
      failureReason: fields[12] as String?,
      retryCount: fields[13] as int,
    );
  }

  @override
  void write(BinaryWriter writer, EnhancedPayment obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.orderId)
      ..writeByte(3)
      ..write(obj.totalAmount)
      ..writeByte(4)
      ..write(obj.paidAmount)
      ..writeByte(5)
      ..write(obj.refundedAmount)
      ..writeByte(6)
      ..write(obj.currency)
      ..writeByte(7)
      ..write(obj.status)
      ..writeByte(8)
      ..write(obj.splits)
      ..writeByte(9)
      ..write(obj.metadata)
      ..writeByte(10)
      ..write(obj.createdAt)
      ..writeByte(11)
      ..write(obj.updatedAt)
      ..writeByte(12)
      ..write(obj.failureReason)
      ..writeByte(13)
      ..write(obj.retryCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EnhancedPaymentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentRefundAdapter extends TypeAdapter<PaymentRefund> {
  @override
  final int typeId = 46;

  @override
  PaymentRefund read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentRefund(
      id: fields[0] as String,
      paymentId: fields[1] as String,
      userId: fields[2] as String,
      amount: fields[3] as double,
      reason: fields[4] as String,
      status: fields[5] as PaymentStatus,
      transactionId: fields[6] as String?,
      createdAt: fields[7] as DateTime,
      processedAt: fields[8] as DateTime?,
      processingNote: fields[9] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentRefund obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.paymentId)
      ..writeByte(2)
      ..write(obj.userId)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.reason)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.transactionId)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.processedAt)
      ..writeByte(9)
      ..write(obj.processingNote);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentRefundAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentHistoryAdapter extends TypeAdapter<PaymentHistory> {
  @override
  final int typeId = 47;

  @override
  PaymentHistory read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentHistory(
      id: fields[0] as String,
      paymentId: fields[1] as String,
      fromStatus: fields[2] as PaymentStatus,
      toStatus: fields[3] as PaymentStatus,
      note: fields[4] as String?,
      timestamp: fields[5] as DateTime,
      metadata: (fields[6] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, PaymentHistory obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.paymentId)
      ..writeByte(2)
      ..write(obj.fromStatus)
      ..writeByte(3)
      ..write(obj.toStatus)
      ..writeByte(4)
      ..write(obj.note)
      ..writeByte(5)
      ..write(obj.timestamp)
      ..writeByte(6)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentHistoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentMethodAdapter extends TypeAdapter<PaymentMethod> {
  @override
  final int typeId = 40;

  @override
  PaymentMethod read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentMethod.creditCard;
      case 1:
        return PaymentMethod.debitCard;
      case 2:
        return PaymentMethod.upi;
      case 3:
        return PaymentMethod.netBanking;
      case 4:
        return PaymentMethod.wallet;
      case 5:
        return PaymentMethod.cashOnDelivery;
      case 6:
        return PaymentMethod.emi;
      case 7:
        return PaymentMethod.buyNowPayLater;
      default:
        return PaymentMethod.creditCard;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentMethod obj) {
    switch (obj) {
      case PaymentMethod.creditCard:
        writer.writeByte(0);
        break;
      case PaymentMethod.debitCard:
        writer.writeByte(1);
        break;
      case PaymentMethod.upi:
        writer.writeByte(2);
        break;
      case PaymentMethod.netBanking:
        writer.writeByte(3);
        break;
      case PaymentMethod.wallet:
        writer.writeByte(4);
        break;
      case PaymentMethod.cashOnDelivery:
        writer.writeByte(5);
        break;
      case PaymentMethod.emi:
        writer.writeByte(6);
        break;
      case PaymentMethod.buyNowPayLater:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentStatusAdapter extends TypeAdapter<PaymentStatus> {
  @override
  final int typeId = 41;

  @override
  PaymentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentStatus.pending;
      case 1:
        return PaymentStatus.processing;
      case 2:
        return PaymentStatus.completed;
      case 3:
        return PaymentStatus.failed;
      case 4:
        return PaymentStatus.cancelled;
      case 5:
        return PaymentStatus.refunded;
      case 6:
        return PaymentStatus.partiallyRefunded;
      default:
        return PaymentStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentStatus obj) {
    switch (obj) {
      case PaymentStatus.pending:
        writer.writeByte(0);
        break;
      case PaymentStatus.processing:
        writer.writeByte(1);
        break;
      case PaymentStatus.completed:
        writer.writeByte(2);
        break;
      case PaymentStatus.failed:
        writer.writeByte(3);
        break;
      case PaymentStatus.cancelled:
        writer.writeByte(4);
        break;
      case PaymentStatus.refunded:
        writer.writeByte(5);
        break;
      case PaymentStatus.partiallyRefunded:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentCard _$PaymentCardFromJson(Map<String, dynamic> json) => PaymentCard(
      id: json['id'] as String,
      userId: json['userId'] as String,
      cardNumber: json['cardNumber'] as String,
      cardHolderName: json['cardHolderName'] as String,
      expiryMonth: json['expiryMonth'] as String,
      expiryYear: json['expiryYear'] as String,
      cardType: json['cardType'] as String,
      bankName: json['bankName'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PaymentCardToJson(PaymentCard instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'cardNumber': instance.cardNumber,
      'cardHolderName': instance.cardHolderName,
      'expiryMonth': instance.expiryMonth,
      'expiryYear': instance.expiryYear,
      'cardType': instance.cardType,
      'bankName': instance.bankName,
      'isDefault': instance.isDefault,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

UPIAccount _$UPIAccountFromJson(Map<String, dynamic> json) => UPIAccount(
      id: json['id'] as String,
      userId: json['userId'] as String,
      upiId: json['upiId'] as String,
      bankName: json['bankName'] as String,
      accountHolderName: json['accountHolderName'] as String,
      isVerified: json['isVerified'] as bool? ?? false,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$UPIAccountToJson(UPIAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'upiId': instance.upiId,
      'bankName': instance.bankName,
      'accountHolderName': instance.accountHolderName,
      'isVerified': instance.isVerified,
      'isDefault': instance.isDefault,
      'createdAt': instance.createdAt.toIso8601String(),
    };

PaymentSplit _$PaymentSplitFromJson(Map<String, dynamic> json) => PaymentSplit(
      id: json['id'] as String,
      paymentId: json['paymentId'] as String,
      method: $enumDecode(_$PaymentMethodEnumMap, json['method']),
      amount: (json['amount'] as num).toDouble(),
      percentage: (json['percentage'] as num).toDouble(),
      cardId: json['cardId'] as String?,
      upiId: json['upiId'] as String?,
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      transactionId: json['transactionId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$PaymentSplitToJson(PaymentSplit instance) =>
    <String, dynamic>{
      'id': instance.id,
      'paymentId': instance.paymentId,
      'method': _$PaymentMethodEnumMap[instance.method]!,
      'amount': instance.amount,
      'percentage': instance.percentage,
      'cardId': instance.cardId,
      'upiId': instance.upiId,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'transactionId': instance.transactionId,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$PaymentMethodEnumMap = {
  PaymentMethod.creditCard: 'creditCard',
  PaymentMethod.debitCard: 'debitCard',
  PaymentMethod.upi: 'upi',
  PaymentMethod.netBanking: 'netBanking',
  PaymentMethod.wallet: 'wallet',
  PaymentMethod.cashOnDelivery: 'cashOnDelivery',
  PaymentMethod.emi: 'emi',
  PaymentMethod.buyNowPayLater: 'buyNowPayLater',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.processing: 'processing',
  PaymentStatus.completed: 'completed',
  PaymentStatus.failed: 'failed',
  PaymentStatus.cancelled: 'cancelled',
  PaymentStatus.refunded: 'refunded',
  PaymentStatus.partiallyRefunded: 'partiallyRefunded',
};

EnhancedPayment _$EnhancedPaymentFromJson(Map<String, dynamic> json) =>
    EnhancedPayment(
      id: json['id'] as String,
      userId: json['userId'] as String,
      orderId: json['orderId'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      paidAmount: (json['paidAmount'] as num?)?.toDouble() ?? 0.0,
      refundedAmount: (json['refundedAmount'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] as String? ?? 'INR',
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      splits: (json['splits'] as List<dynamic>?)
              ?.map((e) => PaymentSplit.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      failureReason: json['failureReason'] as String?,
      retryCount: (json['retryCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$EnhancedPaymentToJson(EnhancedPayment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'orderId': instance.orderId,
      'totalAmount': instance.totalAmount,
      'paidAmount': instance.paidAmount,
      'refundedAmount': instance.refundedAmount,
      'currency': instance.currency,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'splits': instance.splits,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'failureReason': instance.failureReason,
      'retryCount': instance.retryCount,
    };

PaymentRefund _$PaymentRefundFromJson(Map<String, dynamic> json) =>
    PaymentRefund(
      id: json['id'] as String,
      paymentId: json['paymentId'] as String,
      userId: json['userId'] as String,
      amount: (json['amount'] as num).toDouble(),
      reason: json['reason'] as String,
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      transactionId: json['transactionId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      processingNote: json['processingNote'] as String?,
    );

Map<String, dynamic> _$PaymentRefundToJson(PaymentRefund instance) =>
    <String, dynamic>{
      'id': instance.id,
      'paymentId': instance.paymentId,
      'userId': instance.userId,
      'amount': instance.amount,
      'reason': instance.reason,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'transactionId': instance.transactionId,
      'createdAt': instance.createdAt.toIso8601String(),
      'processedAt': instance.processedAt?.toIso8601String(),
      'processingNote': instance.processingNote,
    };

PaymentHistory _$PaymentHistoryFromJson(Map<String, dynamic> json) =>
    PaymentHistory(
      id: json['id'] as String,
      paymentId: json['paymentId'] as String,
      fromStatus: $enumDecode(_$PaymentStatusEnumMap, json['fromStatus']),
      toStatus: $enumDecode(_$PaymentStatusEnumMap, json['toStatus']),
      note: json['note'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$PaymentHistoryToJson(PaymentHistory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'paymentId': instance.paymentId,
      'fromStatus': _$PaymentStatusEnumMap[instance.fromStatus]!,
      'toStatus': _$PaymentStatusEnumMap[instance.toStatus]!,
      'note': instance.note,
      'timestamp': instance.timestamp.toIso8601String(),
      'metadata': instance.metadata,
    };
