import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// User UID model for Projek Super App
class UserUID extends Equatable {
  final String uid;
  final String fullName;
  final DateTime dateOfBirth;
  final String? phoneNumber;
  final String? email;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;
  final bool isActive;
  final bool isCustomUUID;

  const UserUID({
    required this.uid,
    required this.fullName,
    required this.dateOfBirth,
    this.phoneNumber,
    this.email,
    required this.createdAt,
    this.metadata = const {},
    this.isActive = true,
    this.isCustomUUID = false,
  });

  /// Create UID using name + DOB + 567 format
  factory UserUID.create({
    required String fullName,
    required DateTime dateOfBirth,
    String? phoneNumber,
    String? email,
    Map<String, dynamic>? metadata,
  }) {
    // Generate UID using name + DOB + 567
    final nameCode = _generateNameCode(fullName);
    final dobCode = _generateDOBCode(dateOfBirth);
    final uid = '$nameCode$dobCode${567}';

    return UserUID(
      uid: uid,
      fullName: fullName,
      dateOfBirth: dateOfBirth,
      phoneNumber: phoneNumber,
      email: email,
      createdAt: DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Create from Firestore document
  factory UserUID.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserUID(
      uid: doc.id,
      fullName: data['fullName'] ?? '',
      dateOfBirth: (data['dateOfBirth'] as Timestamp).toDate(),
      phoneNumber: data['phoneNumber'],
      email: data['email'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      isActive: data['isActive'] ?? true,
      isCustomUUID: data['isCustomUUID'] ?? false,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'fullName': fullName,
      'dateOfBirth': Timestamp.fromDate(dateOfBirth),
      'phoneNumber': phoneNumber,
      'email': email,
      'createdAt': Timestamp.fromDate(createdAt),
      'metadata': metadata,
      'isActive': isActive,
      'isCustomUUID': isCustomUUID,
    };
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'fullName': fullName,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'phoneNumber': phoneNumber,
      'email': email,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
      'isActive': isActive,
      'isCustomUUID': isCustomUUID,
    };
  }

  /// Create from JSON
  factory UserUID.fromJson(Map<String, dynamic> json) {
    return UserUID(
      uid: json['uid'] ?? '',
      fullName: json['fullName'] ?? '',
      dateOfBirth: DateTime.parse(json['dateOfBirth']),
      phoneNumber: json['phoneNumber'],
      email: json['email'],
      createdAt: DateTime.parse(json['createdAt']),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      isActive: json['isActive'] ?? true,
      isCustomUUID: json['isCustomUUID'] ?? false,
    );
  }

  /// Generate name code from full name
  static String _generateNameCode(String fullName) {
    final words = fullName.trim().split(' ');
    if (words.length >= 2) {
      // First 2 letters of first name + first 2 letters of last name
      final firstName = words.first.toUpperCase();
      final lastName = words.last.toUpperCase();
      return '${firstName.substring(0, firstName.length >= 2 ? 2 : firstName.length)}'
             '${lastName.substring(0, lastName.length >= 2 ? 2 : lastName.length)}';
    } else {
      // If only one name, take first 4 letters
      final name = words.first.toUpperCase();
      return name.substring(0, name.length >= 4 ? 4 : name.length).padRight(4, 'X');
    }
  }

  /// Generate DOB code from date of birth
  static String _generateDOBCode(DateTime dateOfBirth) {
    final day = dateOfBirth.day.toString().padLeft(2, '0');
    final month = dateOfBirth.month.toString().padLeft(2, '0');
    final year = dateOfBirth.year.toString().substring(2); // Last 2 digits of year
    return '$day$month$year';
  }

  /// Copy with new values
  UserUID copyWith({
    String? uid,
    String? fullName,
    DateTime? dateOfBirth,
    String? phoneNumber,
    String? email,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
    bool? isActive,
    bool? isCustomUUID,
  }) {
    return UserUID(
      uid: uid ?? this.uid,
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
      isActive: isActive ?? this.isActive,
      isCustomUUID: isCustomUUID ?? this.isCustomUUID,
    );
  }

  @override
  List<Object?> get props => [
    uid, fullName, dateOfBirth, phoneNumber, email, 
    createdAt, metadata, isActive, isCustomUUID,
  ];

  @override
  String toString() {
    return 'UserUID(uid: $uid, fullName: $fullName, isCustomUUID: $isCustomUUID)';
  }
}
