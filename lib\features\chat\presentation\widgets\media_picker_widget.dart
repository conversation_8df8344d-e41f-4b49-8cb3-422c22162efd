import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class MediaPickerWidget extends StatelessWidget {
  final Function(File) onImageSelected;
  final Function(File) onVideoSelected;
  final Function(File) onDocumentSelected;
  final Function(double, double) onLocationSelected;

  const MediaPickerWidget({
    super.key,
    required this.onImageSelected,
    required this.onVideoSelected,
    required this.onDocumentSelected,
    required this.onLocationSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            _buildMediaOption(
              icon: Icons.camera_alt,
              label: 'Camera',
              color: AppColors.primaryBlue,
              onTap: () => _pickImageFromCamera(context),
            ),
            const SizedBox(width: 16),
            _buildMediaOption(
              icon: Icons.photo_library,
              label: 'Gallery',
              color: AppColors.success,
              onTap: () => _pickImageFromGallery(context),
            ),
            const SizedBox(width: 16),
            _buildMediaOption(
              icon: Icons.videocam,
              label: 'Video',
              color: AppColors.error,
              onTap: () => _pickVideo(context),
            ),
            const SizedBox(width: 16),
            _buildMediaOption(
              icon: Icons.insert_drive_file,
              label: 'Document',
              color: AppColors.warning,
              onTap: () => _pickDocument(context),
            ),
            const SizedBox(width: 16),
            _buildMediaOption(
              icon: Icons.location_on,
              label: 'Location',
              color: AppColors.info,
              onTap: () => _shareLocation(context),
            ),
            const SizedBox(width: 16),
            _buildMediaOption(
              icon: Icons.contact_phone,
              label: 'Contact',
              color: AppColors.secondaryOrange,
              onTap: () => _shareContact(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: color.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImageFromCamera(BuildContext context) async {
    try {
      // Check camera permission
      final cameraStatus = await Permission.camera.request();
      if (!cameraStatus.isGranted) {
        _showPermissionDialog(context, 'Camera');
        return;
      }

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        onImageSelected(File(image.path));
      }
    } catch (e) {
      _showErrorDialog(context, 'Failed to capture image: ${e.toString()}');
    }
  }

  Future<void> _pickImageFromGallery(BuildContext context) async {
    try {
      // Check storage permission
      final storageStatus = await Permission.storage.request();
      if (!storageStatus.isGranted) {
        _showPermissionDialog(context, 'Storage');
        return;
      }

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        onImageSelected(File(image.path));
      }
    } catch (e) {
      _showErrorDialog(context, 'Failed to pick image: ${e.toString()}');
    }
  }

  Future<void> _pickVideo(BuildContext context) async {
    try {
      // Check storage permission
      final storageStatus = await Permission.storage.request();
      if (!storageStatus.isGranted) {
        _showPermissionDialog(context, 'Storage');
        return;
      }

      final ImagePicker picker = ImagePicker();
      final XFile? video = await picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        onVideoSelected(File(video.path));
      }
    } catch (e) {
      _showErrorDialog(context, 'Failed to pick video: ${e.toString()}');
    }
  }

  Future<void> _pickDocument(BuildContext context) async {
    try {
      // Check storage permission
      final storageStatus = await Permission.storage.request();
      if (!storageStatus.isGranted) {
        _showPermissionDialog(context, 'Storage');
        return;
      }

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
          'txt', 'rtf', 'zip', 'rar'
        ],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        
        // Check file size (max 10MB)
        final fileSize = await file.length();
        if (fileSize > 10 * 1024 * 1024) {
          _showErrorDialog(context, 'File size must be less than 10MB');
          return;
        }

        onDocumentSelected(file);
      }
    } catch (e) {
      _showErrorDialog(context, 'Failed to pick document: ${e.toString()}');
    }
  }

  Future<void> _shareLocation(BuildContext context) async {
    try {
      // Check location permission
      final locationStatus = await Permission.location.request();
      if (!locationStatus.isGranted) {
        _showPermissionDialog(context, 'Location');
        return;
      }

      // Show location picker dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Icon(Icons.location_on, color: AppColors.info),
              const SizedBox(width: 8),
              const Text('Share Location'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.my_location, color: AppColors.primaryBlue),
                title: const Text('Current Location'),
                subtitle: const Text('Share your current location'),
                onTap: () {
                  Navigator.pop(context);
                  _getCurrentLocation(context);
                },
              ),
              ListTile(
                leading: Icon(Icons.map, color: AppColors.success),
                title: const Text('Choose on Map'),
                subtitle: const Text('Pick a location on map'),
                onTap: () {
                  Navigator.pop(context);
                  _openMapPicker(context);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    } catch (e) {
      _showErrorDialog(context, 'Failed to access location: ${e.toString()}');
    }
  }

  Future<void> _shareContact(BuildContext context) async {
    try {
      // Show contact picker dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Icon(Icons.contact_phone, color: AppColors.secondaryOrange),
              const SizedBox(width: 8),
              const Text('Share Contact'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.person, color: AppColors.primaryBlue),
                title: const Text('From Contacts'),
                subtitle: const Text('Choose from your contacts'),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromContacts(context);
                },
              ),
              ListTile(
                leading: Icon(Icons.person_add, color: AppColors.success),
                title: const Text('Create Contact'),
                subtitle: const Text('Create a new contact'),
                onTap: () {
                  Navigator.pop(context);
                  _createContact(context);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    } catch (e) {
      _showErrorDialog(context, 'Failed to share contact: ${e.toString()}');
    }
  }

  Future<void> _getCurrentLocation(BuildContext context) async {
    // Implement current location fetching
    // For demo purposes, using dummy coordinates
    onLocationSelected(28.6139, 77.2090); // New Delhi coordinates
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Current location shared'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Future<void> _openMapPicker(BuildContext context) async {
    // Implement map picker
    // For demo purposes, using dummy coordinates
    onLocationSelected(28.6139, 77.2090);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Location picked from map'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Future<void> _pickFromContacts(BuildContext context) async {
    // Implement contact picker
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Contact picker not implemented yet'),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Future<void> _createContact(BuildContext context) async {
    // Implement contact creation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Contact creation not implemented yet'),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showPermissionDialog(BuildContext context, String permission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text('$permission Permission Required'),
        content: Text(
          'This feature requires $permission permission. Please grant permission in app settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.error_outline, color: AppColors.error),
            const SizedBox(width: 8),
            const Text('Error'),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
