// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loyalty_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LoyaltyAccountAdapter extends TypeAdapter<LoyaltyAccount> {
  @override
  final int typeId = 63;

  @override
  LoyaltyAccount read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LoyaltyAccount(
      id: fields[0] as String,
      userId: fields[1] as String,
      totalPoints: fields[2] as int,
      availablePoints: fields[3] as int,
      totalCashback: fields[4] as double,
      availableCashback: fields[5] as double,
      currentTier: fields[6] as UserTier,
      tierProgress: fields[7] as int,
      tierThreshold: fields[8] as int,
      lifetimeSpending: fields[9] as double,
      totalReferrals: fields[10] as int,
      createdAt: fields[11] as DateTime,
      updatedAt: fields[12] as DateTime,
      tierBenefits: (fields[13] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, LoyaltyAccount obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.totalPoints)
      ..writeByte(3)
      ..write(obj.availablePoints)
      ..writeByte(4)
      ..write(obj.totalCashback)
      ..writeByte(5)
      ..write(obj.availableCashback)
      ..writeByte(6)
      ..write(obj.currentTier)
      ..writeByte(7)
      ..write(obj.tierProgress)
      ..writeByte(8)
      ..write(obj.tierThreshold)
      ..writeByte(9)
      ..write(obj.lifetimeSpending)
      ..writeByte(10)
      ..write(obj.totalReferrals)
      ..writeByte(11)
      ..write(obj.createdAt)
      ..writeByte(12)
      ..write(obj.updatedAt)
      ..writeByte(13)
      ..write(obj.tierBenefits);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoyaltyAccountAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RewardTransactionAdapter extends TypeAdapter<RewardTransaction> {
  @override
  final int typeId = 64;

  @override
  RewardTransaction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RewardTransaction(
      id: fields[0] as String,
      userId: fields[1] as String,
      type: fields[2] as TransactionType,
      rewardType: fields[3] as RewardType,
      points: fields[4] as int,
      cashback: fields[5] as double,
      description: fields[6] as String,
      orderId: fields[7] as String?,
      referenceId: fields[8] as String?,
      createdAt: fields[9] as DateTime,
      expiresAt: fields[10] as DateTime?,
      metadata: (fields[11] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, RewardTransaction obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.rewardType)
      ..writeByte(4)
      ..write(obj.points)
      ..writeByte(5)
      ..write(obj.cashback)
      ..writeByte(6)
      ..write(obj.description)
      ..writeByte(7)
      ..write(obj.orderId)
      ..writeByte(8)
      ..write(obj.referenceId)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.expiresAt)
      ..writeByte(11)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RewardTransactionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ReferralProgramAdapter extends TypeAdapter<ReferralProgram> {
  @override
  final int typeId = 65;

  @override
  ReferralProgram read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ReferralProgram(
      id: fields[0] as String,
      referrerId: fields[1] as String,
      refereeId: fields[2] as String,
      referralCode: fields[3] as String,
      referrerRewardPoints: fields[4] as int,
      referrerRewardCashback: fields[5] as double,
      refereeRewardPoints: fields[6] as int,
      refereeRewardCashback: fields[7] as double,
      referredAt: fields[8] as DateTime,
      rewardedAt: fields[9] as DateTime?,
      isCompleted: fields[10] as bool,
      completionOrderId: fields[11] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ReferralProgram obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.referrerId)
      ..writeByte(2)
      ..write(obj.refereeId)
      ..writeByte(3)
      ..write(obj.referralCode)
      ..writeByte(4)
      ..write(obj.referrerRewardPoints)
      ..writeByte(5)
      ..write(obj.referrerRewardCashback)
      ..writeByte(6)
      ..write(obj.refereeRewardPoints)
      ..writeByte(7)
      ..write(obj.refereeRewardCashback)
      ..writeByte(8)
      ..write(obj.referredAt)
      ..writeByte(9)
      ..write(obj.rewardedAt)
      ..writeByte(10)
      ..write(obj.isCompleted)
      ..writeByte(11)
      ..write(obj.completionOrderId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReferralProgramAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RewardOfferAdapter extends TypeAdapter<RewardOffer> {
  @override
  final int typeId = 66;

  @override
  RewardOffer read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RewardOffer(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String,
      type: fields[3] as RewardType,
      pointsCost: fields[4] as int,
      cashbackValue: fields[5] as double,
      discountPercentage: fields[6] as double,
      imageUrl: fields[7] as String?,
      eligibleTiers: (fields[8] as List).cast<UserTier>(),
      validFrom: fields[9] as DateTime,
      validUntil: fields[10] as DateTime,
      maxRedemptions: fields[11] as int,
      currentRedemptions: fields[12] as int,
      isActive: fields[13] as bool,
      categories: (fields[14] as List).cast<String>(),
      terms: (fields[15] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, RewardOffer obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.pointsCost)
      ..writeByte(5)
      ..write(obj.cashbackValue)
      ..writeByte(6)
      ..write(obj.discountPercentage)
      ..writeByte(7)
      ..write(obj.imageUrl)
      ..writeByte(8)
      ..write(obj.eligibleTiers)
      ..writeByte(9)
      ..write(obj.validFrom)
      ..writeByte(10)
      ..write(obj.validUntil)
      ..writeByte(11)
      ..write(obj.maxRedemptions)
      ..writeByte(12)
      ..write(obj.currentRedemptions)
      ..writeByte(13)
      ..write(obj.isActive)
      ..writeByte(14)
      ..write(obj.categories)
      ..writeByte(15)
      ..write(obj.terms);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RewardOfferAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RewardRedemptionAdapter extends TypeAdapter<RewardRedemption> {
  @override
  final int typeId = 67;

  @override
  RewardRedemption read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RewardRedemption(
      id: fields[0] as String,
      userId: fields[1] as String,
      offerId: fields[2] as String,
      offerTitle: fields[3] as String,
      pointsUsed: fields[4] as int,
      cashbackUsed: fields[5] as double,
      redeemedAt: fields[6] as DateTime,
      usedAt: fields[7] as DateTime?,
      expiresAt: fields[8] as DateTime,
      status: fields[9] as String,
      orderId: fields[10] as String?,
      redemptionCode: fields[11] as String,
    );
  }

  @override
  void write(BinaryWriter writer, RewardRedemption obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.offerId)
      ..writeByte(3)
      ..write(obj.offerTitle)
      ..writeByte(4)
      ..write(obj.pointsUsed)
      ..writeByte(5)
      ..write(obj.cashbackUsed)
      ..writeByte(6)
      ..write(obj.redeemedAt)
      ..writeByte(7)
      ..write(obj.usedAt)
      ..writeByte(8)
      ..write(obj.expiresAt)
      ..writeByte(9)
      ..write(obj.status)
      ..writeByte(10)
      ..write(obj.orderId)
      ..writeByte(11)
      ..write(obj.redemptionCode);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RewardRedemptionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserTierAdapter extends TypeAdapter<UserTier> {
  @override
  final int typeId = 60;

  @override
  UserTier read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return UserTier.bronze;
      case 1:
        return UserTier.silver;
      case 2:
        return UserTier.gold;
      case 3:
        return UserTier.platinum;
      case 4:
        return UserTier.diamond;
      default:
        return UserTier.bronze;
    }
  }

  @override
  void write(BinaryWriter writer, UserTier obj) {
    switch (obj) {
      case UserTier.bronze:
        writer.writeByte(0);
        break;
      case UserTier.silver:
        writer.writeByte(1);
        break;
      case UserTier.gold:
        writer.writeByte(2);
        break;
      case UserTier.platinum:
        writer.writeByte(3);
        break;
      case UserTier.diamond:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserTierAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RewardTypeAdapter extends TypeAdapter<RewardType> {
  @override
  final int typeId = 61;

  @override
  RewardType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RewardType.points;
      case 1:
        return RewardType.cashback;
      case 2:
        return RewardType.discount;
      case 3:
        return RewardType.freeDelivery;
      case 4:
        return RewardType.voucher;
      case 5:
        return RewardType.referralBonus;
      default:
        return RewardType.points;
    }
  }

  @override
  void write(BinaryWriter writer, RewardType obj) {
    switch (obj) {
      case RewardType.points:
        writer.writeByte(0);
        break;
      case RewardType.cashback:
        writer.writeByte(1);
        break;
      case RewardType.discount:
        writer.writeByte(2);
        break;
      case RewardType.freeDelivery:
        writer.writeByte(3);
        break;
      case RewardType.voucher:
        writer.writeByte(4);
        break;
      case RewardType.referralBonus:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RewardTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransactionTypeAdapter extends TypeAdapter<TransactionType> {
  @override
  final int typeId = 62;

  @override
  TransactionType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransactionType.earned;
      case 1:
        return TransactionType.redeemed;
      case 2:
        return TransactionType.expired;
      case 3:
        return TransactionType.bonus;
      case 4:
        return TransactionType.refunded;
      default:
        return TransactionType.earned;
    }
  }

  @override
  void write(BinaryWriter writer, TransactionType obj) {
    switch (obj) {
      case TransactionType.earned:
        writer.writeByte(0);
        break;
      case TransactionType.redeemed:
        writer.writeByte(1);
        break;
      case TransactionType.expired:
        writer.writeByte(2);
        break;
      case TransactionType.bonus:
        writer.writeByte(3);
        break;
      case TransactionType.refunded:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoyaltyAccount _$LoyaltyAccountFromJson(Map<String, dynamic> json) =>
    LoyaltyAccount(
      id: json['id'] as String,
      userId: json['userId'] as String,
      totalPoints: (json['totalPoints'] as num?)?.toInt() ?? 0,
      availablePoints: (json['availablePoints'] as num?)?.toInt() ?? 0,
      totalCashback: (json['totalCashback'] as num?)?.toDouble() ?? 0.0,
      availableCashback: (json['availableCashback'] as num?)?.toDouble() ?? 0.0,
      currentTier:
          $enumDecodeNullable(_$UserTierEnumMap, json['currentTier']) ??
              UserTier.bronze,
      tierProgress: (json['tierProgress'] as num?)?.toInt() ?? 0,
      tierThreshold: (json['tierThreshold'] as num?)?.toInt() ?? 1000,
      lifetimeSpending: (json['lifetimeSpending'] as num?)?.toDouble() ?? 0.0,
      totalReferrals: (json['totalReferrals'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      tierBenefits: json['tierBenefits'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$LoyaltyAccountToJson(LoyaltyAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'totalPoints': instance.totalPoints,
      'availablePoints': instance.availablePoints,
      'totalCashback': instance.totalCashback,
      'availableCashback': instance.availableCashback,
      'currentTier': _$UserTierEnumMap[instance.currentTier]!,
      'tierProgress': instance.tierProgress,
      'tierThreshold': instance.tierThreshold,
      'lifetimeSpending': instance.lifetimeSpending,
      'totalReferrals': instance.totalReferrals,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'tierBenefits': instance.tierBenefits,
    };

const _$UserTierEnumMap = {
  UserTier.bronze: 'bronze',
  UserTier.silver: 'silver',
  UserTier.gold: 'gold',
  UserTier.platinum: 'platinum',
  UserTier.diamond: 'diamond',
};

RewardTransaction _$RewardTransactionFromJson(Map<String, dynamic> json) =>
    RewardTransaction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$TransactionTypeEnumMap, json['type']),
      rewardType: $enumDecode(_$RewardTypeEnumMap, json['rewardType']),
      points: (json['points'] as num?)?.toInt() ?? 0,
      cashback: (json['cashback'] as num?)?.toDouble() ?? 0.0,
      description: json['description'] as String,
      orderId: json['orderId'] as String?,
      referenceId: json['referenceId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$RewardTransactionToJson(RewardTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$TransactionTypeEnumMap[instance.type]!,
      'rewardType': _$RewardTypeEnumMap[instance.rewardType]!,
      'points': instance.points,
      'cashback': instance.cashback,
      'description': instance.description,
      'orderId': instance.orderId,
      'referenceId': instance.referenceId,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$TransactionTypeEnumMap = {
  TransactionType.earned: 'earned',
  TransactionType.redeemed: 'redeemed',
  TransactionType.expired: 'expired',
  TransactionType.bonus: 'bonus',
  TransactionType.refunded: 'refunded',
};

const _$RewardTypeEnumMap = {
  RewardType.points: 'points',
  RewardType.cashback: 'cashback',
  RewardType.discount: 'discount',
  RewardType.freeDelivery: 'freeDelivery',
  RewardType.voucher: 'voucher',
  RewardType.referralBonus: 'referralBonus',
};

ReferralProgram _$ReferralProgramFromJson(Map<String, dynamic> json) =>
    ReferralProgram(
      id: json['id'] as String,
      referrerId: json['referrerId'] as String,
      refereeId: json['refereeId'] as String,
      referralCode: json['referralCode'] as String,
      referrerRewardPoints:
          (json['referrerRewardPoints'] as num?)?.toInt() ?? 500,
      referrerRewardCashback:
          (json['referrerRewardCashback'] as num?)?.toDouble() ?? 50.0,
      refereeRewardPoints:
          (json['refereeRewardPoints'] as num?)?.toInt() ?? 200,
      refereeRewardCashback:
          (json['refereeRewardCashback'] as num?)?.toDouble() ?? 20.0,
      referredAt: DateTime.parse(json['referredAt'] as String),
      rewardedAt: json['rewardedAt'] == null
          ? null
          : DateTime.parse(json['rewardedAt'] as String),
      isCompleted: json['isCompleted'] as bool? ?? false,
      completionOrderId: json['completionOrderId'] as String?,
    );

Map<String, dynamic> _$ReferralProgramToJson(ReferralProgram instance) =>
    <String, dynamic>{
      'id': instance.id,
      'referrerId': instance.referrerId,
      'refereeId': instance.refereeId,
      'referralCode': instance.referralCode,
      'referrerRewardPoints': instance.referrerRewardPoints,
      'referrerRewardCashback': instance.referrerRewardCashback,
      'refereeRewardPoints': instance.refereeRewardPoints,
      'refereeRewardCashback': instance.refereeRewardCashback,
      'referredAt': instance.referredAt.toIso8601String(),
      'rewardedAt': instance.rewardedAt?.toIso8601String(),
      'isCompleted': instance.isCompleted,
      'completionOrderId': instance.completionOrderId,
    };

RewardOffer _$RewardOfferFromJson(Map<String, dynamic> json) => RewardOffer(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$RewardTypeEnumMap, json['type']),
      pointsCost: (json['pointsCost'] as num?)?.toInt() ?? 0,
      cashbackValue: (json['cashbackValue'] as num?)?.toDouble() ?? 0.0,
      discountPercentage:
          (json['discountPercentage'] as num?)?.toDouble() ?? 0.0,
      imageUrl: json['imageUrl'] as String?,
      eligibleTiers: (json['eligibleTiers'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$UserTierEnumMap, e))
              .toList() ??
          const [],
      validFrom: DateTime.parse(json['validFrom'] as String),
      validUntil: DateTime.parse(json['validUntil'] as String),
      maxRedemptions: (json['maxRedemptions'] as num?)?.toInt() ?? -1,
      currentRedemptions: (json['currentRedemptions'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      terms: json['terms'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$RewardOfferToJson(RewardOffer instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$RewardTypeEnumMap[instance.type]!,
      'pointsCost': instance.pointsCost,
      'cashbackValue': instance.cashbackValue,
      'discountPercentage': instance.discountPercentage,
      'imageUrl': instance.imageUrl,
      'eligibleTiers':
          instance.eligibleTiers.map((e) => _$UserTierEnumMap[e]!).toList(),
      'validFrom': instance.validFrom.toIso8601String(),
      'validUntil': instance.validUntil.toIso8601String(),
      'maxRedemptions': instance.maxRedemptions,
      'currentRedemptions': instance.currentRedemptions,
      'isActive': instance.isActive,
      'categories': instance.categories,
      'terms': instance.terms,
    };

RewardRedemption _$RewardRedemptionFromJson(Map<String, dynamic> json) =>
    RewardRedemption(
      id: json['id'] as String,
      userId: json['userId'] as String,
      offerId: json['offerId'] as String,
      offerTitle: json['offerTitle'] as String,
      pointsUsed: (json['pointsUsed'] as num?)?.toInt() ?? 0,
      cashbackUsed: (json['cashbackUsed'] as num?)?.toDouble() ?? 0.0,
      redeemedAt: DateTime.parse(json['redeemedAt'] as String),
      usedAt: json['usedAt'] == null
          ? null
          : DateTime.parse(json['usedAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      status: json['status'] as String? ?? 'active',
      orderId: json['orderId'] as String?,
      redemptionCode: json['redemptionCode'] as String,
    );

Map<String, dynamic> _$RewardRedemptionToJson(RewardRedemption instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'offerId': instance.offerId,
      'offerTitle': instance.offerTitle,
      'pointsUsed': instance.pointsUsed,
      'cashbackUsed': instance.cashbackUsed,
      'redeemedAt': instance.redeemedAt.toIso8601String(),
      'usedAt': instance.usedAt?.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'status': instance.status,
      'orderId': instance.orderId,
      'redemptionCode': instance.redemptionCode,
    };
