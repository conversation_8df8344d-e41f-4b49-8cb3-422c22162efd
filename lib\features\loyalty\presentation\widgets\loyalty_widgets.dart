import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/models/loyalty_models.dart';

class LoyaltyAccountCard extends StatelessWidget {
  final LoyaltyAccount account;
  final VoidCallback? onTap;

  const LoyaltyAccountCard({super.key, required this.account, this.onTap});

  @override
  Widget build(BuildContext context) {
    final tierConfig = TierConfig.getTierConfig(account.currentTier);
    final tierColor = Color(tierConfig?['color'] ?? 0xFF9E9E9E);

    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [tierColor.withOpacity(0.1), tierColor.withOpacity(0.05)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with tier badge
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: tierColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      (tierConfig?['name'] ?? 'Unknown')
                          .toString()
                          .toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Icon(Icons.card_giftcard, color: tierColor, size: 28),
                ],
              ),

              const SizedBox(height: 20),

              // Points and Cashback
              Row(
                children: [
                  Expanded(
                    child: _buildMetric(
                      'Available Points',
                      account.availablePoints.toString(),
                      Icons.stars,
                      AppColors.warning,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildMetric(
                      'Cashback',
                      '₹${account.availableCashback.toStringAsFixed(0)}',
                      Icons.account_balance_wallet,
                      AppColors.success,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Tier Progress
              if (account.tierThreshold > 0) ...[
                Text(
                  'Progress to ${account.nextTierName}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: account.tierProgressPercentage / 100,
                  backgroundColor: AppColors.grey200,
                  valueColor: AlwaysStoppedAnimation<Color>(tierColor),
                  minHeight: 6,
                ),
                const SizedBox(height: 4),
                Text(
                  '₹${account.tierProgress} / ₹${account.tierThreshold}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ] else ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: tierColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.emoji_events, color: tierColor, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'You\'ve reached the highest tier!',
                        style: TextStyle(
                          color: tierColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetric(String label, String value, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}

class RewardOfferCard extends StatelessWidget {
  final RewardOffer offer;
  final bool canRedeem;
  final VoidCallback? onRedeem;

  const RewardOfferCard({
    super.key,
    required this.offer,
    this.canRedeem = true,
    this.onRedeem,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: canRedeem ? onRedeem : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with type badge
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getTypeColor(offer.type).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _getTypeColor(offer.type).withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      _getTypeLabel(offer.type),
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: _getTypeColor(offer.type),
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (!offer.isUnlimited)
                    Text(
                      '${offer.remainingRedemptions} left',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // Title and Description
              Text(
                offer.title,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              Text(
                offer.description,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Cost and Action
              Row(
                children: [
                  if (offer.pointsCost > 0) ...[
                    Icon(Icons.stars, size: 16, color: AppColors.warning),
                    const SizedBox(width: 4),
                    Text(
                      '${offer.pointsCost} points',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.warning,
                      ),
                    ),
                  ] else ...[
                    Text(
                      'FREE',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.success,
                      ),
                    ),
                  ],

                  const Spacer(),

                  ElevatedButton(
                    onPressed: canRedeem && offer.isAvailable ? onRedeem : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: canRedeem && offer.isAvailable
                          ? AppColors.primaryBlue
                          : AppColors.grey300,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      canRedeem && offer.isAvailable ? 'Redeem' : 'Unavailable',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),

              // Validity
              const SizedBox(height: 8),
              Text(
                'Valid until ${_formatDate(offer.validUntil)}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(RewardType type) {
    switch (type) {
      case RewardType.points:
        return AppColors.warning;
      case RewardType.cashback:
        return AppColors.success;
      case RewardType.discount:
        return AppColors.info;
      case RewardType.freeDelivery:
        return AppColors.primaryBlue;
      case RewardType.voucher:
        return AppColors.error;
      case RewardType.referralBonus:
        return Colors.purple;
    }
  }

  String _getTypeLabel(RewardType type) {
    switch (type) {
      case RewardType.points:
        return 'POINTS';
      case RewardType.cashback:
        return 'CASHBACK';
      case RewardType.discount:
        return 'DISCOUNT';
      case RewardType.freeDelivery:
        return 'FREE DELIVERY';
      case RewardType.voucher:
        return 'VOUCHER';
      case RewardType.referralBonus:
        return 'REFERRAL';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class RewardTransactionItem extends StatelessWidget {
  final RewardTransaction transaction;

  const RewardTransactionItem({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    final isPositive =
        transaction.type == TransactionType.earned ||
        transaction.type == TransactionType.bonus;
    final color = isPositive ? AppColors.success : AppColors.error;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getTransactionIcon(transaction.type),
                color: color,
                size: 20,
              ),
            ),

            const SizedBox(width: 12),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.description,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _formatDate(transaction.createdAt),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (transaction.points != 0) ...[
                  Text(
                    '${isPositive ? '+' : ''}${transaction.points}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  Text(
                    'points',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
                if (transaction.cashback != 0) ...[
                  Text(
                    '${isPositive ? '+' : ''}₹${transaction.cashback.toStringAsFixed(0)}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  Text(
                    'cashback',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.earned:
        return Icons.add_circle;
      case TransactionType.redeemed:
        return Icons.remove_circle;
      case TransactionType.expired:
        return Icons.schedule;
      case TransactionType.bonus:
        return Icons.card_giftcard;
      case TransactionType.refunded:
        return Icons.undo;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class TierBenefitsWidget extends StatelessWidget {
  final UserTier tier;

  const TierBenefitsWidget({super.key, required this.tier});

  @override
  Widget build(BuildContext context) {
    final benefits = TierConfig.getTierBenefits(tier);
    final tierColor = Color(TierConfig.getTierColor(tier));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.emoji_events, color: tierColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  '${TierConfig.getTierName(tier)} Benefits',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: tierColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            ...benefits.map(
              (benefit) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: AppColors.success,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(benefit, style: AppTextStyles.bodyMedium),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
