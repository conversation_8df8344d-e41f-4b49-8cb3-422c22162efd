import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class SearchResultsPage extends StatefulWidget {
  final String query;
  final String? category;

  const SearchResultsPage({
    super.key,
    required this.query,
    this.category,
  });

  @override
  State<SearchResultsPage> createState() => _SearchResultsPageState();
}

class _SearchResultsPageState extends State<SearchResultsPage> {
  final TextEditingController _searchController = TextEditingController();
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.query;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => context.pop(),
        ),
        title: Container(
          height: 40,
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search products and services...',
              hintStyle: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[600]),
              prefixIcon: const Icon(Icons.search, color: Colors.grey, size: 20),
              suffixIcon: IconButton(
                icon: const Icon(Icons.tune, color: Colors.grey, size: 20),
                onPressed: () => setState(() => _showFilters = !_showFilters),
              ),
              filled: true,
              fillColor: Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            onSubmitted: (value) {
              if (value.isNotEmpty) {
                context.pushReplacement('/search?q=${Uri.encodeComponent(value)}');
              }
            },
          ),
        ),
      ),
      body: Column(
        children: [
          if (_showFilters) _buildFilterSection(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSearchHeader(),
                  const SizedBox(height: 16),
                  _buildSearchResults(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFilterChip('All', true),
              _buildFilterChip('Products', false),
              _buildFilterChip('Services', false),
              _buildFilterChip('Restaurants', false),
              _buildFilterChip('Nearby', false),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.poppins(
          fontSize: 12,
          color: isSelected ? Colors.white : Colors.grey[700],
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        // Handle filter selection
      },
      backgroundColor: Colors.grey[200],
      selectedColor: Colors.blue,
      checkmarkColor: Colors.white,
    );
  }

  Widget _buildSearchHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search results for "${widget.query}"',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${_getResultCount()} results found',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults() {
    final results = _getSearchResults();
    
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: results.length,
      itemBuilder: (context, index) {
        final result = results[index];
        return _buildResultCard(result);
      },
    );
  }

  Widget _buildResultCard(Map<String, dynamic> result) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: result['color'] ?? Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            result['icon'] ?? Icons.search,
            color: Colors.white,
            size: 24,
          ),
        ),
        title: Text(
          result['title'] ?? '',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              result['description'] ?? '',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              result['category'] ?? '',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.blue,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
        onTap: () {
          // Navigate to detail page based on result type
          _navigateToDetail(result);
        },
      ),
    );
  }

  int _getResultCount() {
    return _getSearchResults().length;
  }

  List<Map<String, dynamic>> _getSearchResults() {
    // Mock search results - in real app, this would come from API
    return [
      {
        'title': 'Wireless Bluetooth Headphones',
        'description': 'High-quality wireless headphones with noise cancellation',
        'category': 'Electronics',
        'icon': Icons.headphones,
        'color': Colors.blue,
        'type': 'product',
        'id': '1',
      },
      {
        'title': 'Pizza Palace',
        'description': 'Delicious pizza and Italian cuisine',
        'category': 'Restaurant',
        'icon': Icons.restaurant,
        'color': Colors.red,
        'type': 'restaurant',
        'id': '2',
      },
      {
        'title': 'Home Cleaning Service',
        'description': 'Professional home cleaning and maintenance',
        'category': 'Services',
        'icon': Icons.cleaning_services,
        'color': Colors.green,
        'type': 'service',
        'id': '3',
      },
      {
        'title': 'Fashion Store',
        'description': 'Latest fashion trends and clothing',
        'category': 'Fashion',
        'icon': Icons.checkroom,
        'color': Colors.purple,
        'type': 'category',
        'id': '4',
      },
    ].where((result) {
      final title = result['title'].toString().toLowerCase();
      final description = result['description'].toString().toLowerCase();
      final category = result['category'].toString().toLowerCase();
      final query = widget.query.toLowerCase();
      
      return title.contains(query) || 
             description.contains(query) || 
             category.contains(query);
    }).toList();
  }

  void _navigateToDetail(Map<String, dynamic> result) {
    final type = result['type'];
    final id = result['id'];
    
    switch (type) {
      case 'product':
        context.push('/product/$id');
        break;
      case 'restaurant':
        context.push('/restaurant/$id');
        break;
      case 'service':
        context.push('/service/$id');
        break;
      case 'category':
        context.push('/categories/filtered?type=${result['category']}&title=${result['title']}');
        break;
      default:
        // Show detail dialog or navigate to generic detail page
        _showDetailDialog(result);
    }
  }

  void _showDetailDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(result['title']),
        content: Text(result['description']),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
