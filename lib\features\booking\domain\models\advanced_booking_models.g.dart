// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'advanced_booking_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AdvancedBookingAdapter extends TypeAdapter<AdvancedBooking> {
  @override
  final int typeId = 83;

  @override
  AdvancedBooking read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AdvancedBooking(
      id: fields[0] as String,
      userId: fields[1] as String,
      type: fields[2] as BookingType,
      title: fields[3] as String,
      description: fields[4] as String,
      serviceType: fields[5] as String,
      scheduledAt: fields[6] as DateTime,
      endDate: fields[7] as DateTime?,
      recurrencePattern: fields[8] as RecurrencePattern?,
      recurrenceConfig: (fields[9] as Map).cast<String, dynamic>(),
      participantIds: (fields[10] as List).cast<String>(),
      templateId: fields[11] as String?,
      status: fields[12] as BookingStatus,
      estimatedCost: fields[13] as double,
      pickupAddress: fields[14] as String,
      pickupLatitude: fields[15] as double,
      pickupLongitude: fields[16] as double,
      deliveryAddress: fields[17] as String,
      deliveryLatitude: fields[18] as double,
      deliveryLongitude: fields[19] as double,
      items: (fields[20] as List).cast<BookingItem>(),
      preferences: (fields[21] as Map).cast<String, dynamic>(),
      createdAt: fields[22] as DateTime,
      updatedAt: fields[23] as DateTime,
      notes: fields[24] as String?,
      generatedBookingIds: (fields[25] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, AdvancedBooking obj) {
    writer
      ..writeByte(26)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.title)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.serviceType)
      ..writeByte(6)
      ..write(obj.scheduledAt)
      ..writeByte(7)
      ..write(obj.endDate)
      ..writeByte(8)
      ..write(obj.recurrencePattern)
      ..writeByte(9)
      ..write(obj.recurrenceConfig)
      ..writeByte(10)
      ..write(obj.participantIds)
      ..writeByte(11)
      ..write(obj.templateId)
      ..writeByte(12)
      ..write(obj.status)
      ..writeByte(13)
      ..write(obj.estimatedCost)
      ..writeByte(14)
      ..write(obj.pickupAddress)
      ..writeByte(15)
      ..write(obj.pickupLatitude)
      ..writeByte(16)
      ..write(obj.pickupLongitude)
      ..writeByte(17)
      ..write(obj.deliveryAddress)
      ..writeByte(18)
      ..write(obj.deliveryLatitude)
      ..writeByte(19)
      ..write(obj.deliveryLongitude)
      ..writeByte(20)
      ..write(obj.items)
      ..writeByte(21)
      ..write(obj.preferences)
      ..writeByte(22)
      ..write(obj.createdAt)
      ..writeByte(23)
      ..write(obj.updatedAt)
      ..writeByte(24)
      ..write(obj.notes)
      ..writeByte(25)
      ..write(obj.generatedBookingIds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdvancedBookingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingItemAdapter extends TypeAdapter<BookingItem> {
  @override
  final int typeId = 84;

  @override
  BookingItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BookingItem(
      id: fields[0] as String,
      name: fields[1] as String,
      category: fields[2] as String,
      quantity: fields[3] as int,
      unitPrice: fields[4] as double,
      imageUrl: fields[5] as String?,
      description: fields[6] as String?,
      specifications: (fields[7] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, BookingItem obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.category)
      ..writeByte(3)
      ..write(obj.quantity)
      ..writeByte(4)
      ..write(obj.unitPrice)
      ..writeByte(5)
      ..write(obj.imageUrl)
      ..writeByte(6)
      ..write(obj.description)
      ..writeByte(7)
      ..write(obj.specifications);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingTemplateAdapter extends TypeAdapter<BookingTemplate> {
  @override
  final int typeId = 85;

  @override
  BookingTemplate read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BookingTemplate(
      id: fields[0] as String,
      userId: fields[1] as String,
      name: fields[2] as String,
      description: fields[3] as String,
      serviceType: fields[4] as String,
      pickupAddress: fields[5] as String,
      pickupLatitude: fields[6] as double,
      pickupLongitude: fields[7] as double,
      deliveryAddress: fields[8] as String,
      deliveryLatitude: fields[9] as double,
      deliveryLongitude: fields[10] as double,
      items: (fields[11] as List).cast<BookingItem>(),
      preferences: (fields[12] as Map).cast<String, dynamic>(),
      createdAt: fields[13] as DateTime,
      updatedAt: fields[14] as DateTime,
      usageCount: fields[15] as int,
      isActive: fields[16] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, BookingTemplate obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.serviceType)
      ..writeByte(5)
      ..write(obj.pickupAddress)
      ..writeByte(6)
      ..write(obj.pickupLatitude)
      ..writeByte(7)
      ..write(obj.pickupLongitude)
      ..writeByte(8)
      ..write(obj.deliveryAddress)
      ..writeByte(9)
      ..write(obj.deliveryLatitude)
      ..writeByte(10)
      ..write(obj.deliveryLongitude)
      ..writeByte(11)
      ..write(obj.items)
      ..writeByte(12)
      ..write(obj.preferences)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.updatedAt)
      ..writeByte(15)
      ..write(obj.usageCount)
      ..writeByte(16)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingTemplateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class GroupBookingAdapter extends TypeAdapter<GroupBooking> {
  @override
  final int typeId = 86;

  @override
  GroupBooking read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GroupBooking(
      id: fields[0] as String,
      organizerId: fields[1] as String,
      title: fields[2] as String,
      description: fields[3] as String,
      serviceType: fields[4] as String,
      scheduledAt: fields[5] as DateTime,
      participants: (fields[6] as List).cast<GroupParticipant>(),
      totalCost: fields[7] as double,
      costSplitMethod: fields[8] as String,
      pickupAddress: fields[9] as String,
      pickupLatitude: fields[10] as double,
      pickupLongitude: fields[11] as double,
      deliveryAddress: fields[12] as String,
      deliveryLatitude: fields[13] as double,
      deliveryLongitude: fields[14] as double,
      status: fields[15] as BookingStatus,
      createdAt: fields[16] as DateTime,
      updatedAt: fields[17] as DateTime,
      notes: fields[18] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, GroupBooking obj) {
    writer
      ..writeByte(19)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.organizerId)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.serviceType)
      ..writeByte(5)
      ..write(obj.scheduledAt)
      ..writeByte(6)
      ..write(obj.participants)
      ..writeByte(7)
      ..write(obj.totalCost)
      ..writeByte(8)
      ..write(obj.costSplitMethod)
      ..writeByte(9)
      ..write(obj.pickupAddress)
      ..writeByte(10)
      ..write(obj.pickupLatitude)
      ..writeByte(11)
      ..write(obj.pickupLongitude)
      ..writeByte(12)
      ..write(obj.deliveryAddress)
      ..writeByte(13)
      ..write(obj.deliveryLatitude)
      ..writeByte(14)
      ..write(obj.deliveryLongitude)
      ..writeByte(15)
      ..write(obj.status)
      ..writeByte(16)
      ..write(obj.createdAt)
      ..writeByte(17)
      ..write(obj.updatedAt)
      ..writeByte(18)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GroupBookingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class GroupParticipantAdapter extends TypeAdapter<GroupParticipant> {
  @override
  final int typeId = 87;

  @override
  GroupParticipant read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GroupParticipant(
      userId: fields[0] as String,
      name: fields[1] as String,
      email: fields[2] as String,
      phoneNumber: fields[3] as String?,
      hasConfirmed: fields[4] as bool,
      shareAmount: fields[5] as double,
      items: (fields[6] as List).cast<BookingItem>(),
      joinedAt: fields[7] as DateTime,
      confirmedAt: fields[8] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, GroupParticipant obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.phoneNumber)
      ..writeByte(4)
      ..write(obj.hasConfirmed)
      ..writeByte(5)
      ..write(obj.shareAmount)
      ..writeByte(6)
      ..write(obj.items)
      ..writeByte(7)
      ..write(obj.joinedAt)
      ..writeByte(8)
      ..write(obj.confirmedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GroupParticipantAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingTypeAdapter extends TypeAdapter<BookingType> {
  @override
  final int typeId = 80;

  @override
  BookingType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BookingType.single;
      case 1:
        return BookingType.recurring;
      case 2:
        return BookingType.group;
      case 3:
        return BookingType.template;
      default:
        return BookingType.single;
    }
  }

  @override
  void write(BinaryWriter writer, BookingType obj) {
    switch (obj) {
      case BookingType.single:
        writer.writeByte(0);
        break;
      case BookingType.recurring:
        writer.writeByte(1);
        break;
      case BookingType.group:
        writer.writeByte(2);
        break;
      case BookingType.template:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RecurrencePatternAdapter extends TypeAdapter<RecurrencePattern> {
  @override
  final int typeId = 81;

  @override
  RecurrencePattern read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RecurrencePattern.daily;
      case 1:
        return RecurrencePattern.weekly;
      case 2:
        return RecurrencePattern.biweekly;
      case 3:
        return RecurrencePattern.monthly;
      case 4:
        return RecurrencePattern.custom;
      default:
        return RecurrencePattern.daily;
    }
  }

  @override
  void write(BinaryWriter writer, RecurrencePattern obj) {
    switch (obj) {
      case RecurrencePattern.daily:
        writer.writeByte(0);
        break;
      case RecurrencePattern.weekly:
        writer.writeByte(1);
        break;
      case RecurrencePattern.biweekly:
        writer.writeByte(2);
        break;
      case RecurrencePattern.monthly:
        writer.writeByte(3);
        break;
      case RecurrencePattern.custom:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RecurrencePatternAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BookingStatusAdapter extends TypeAdapter<BookingStatus> {
  @override
  final int typeId = 82;

  @override
  BookingStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BookingStatus.scheduled;
      case 1:
        return BookingStatus.confirmed;
      case 2:
        return BookingStatus.inProgress;
      case 3:
        return BookingStatus.completed;
      case 4:
        return BookingStatus.cancelled;
      case 5:
        return BookingStatus.failed;
      default:
        return BookingStatus.scheduled;
    }
  }

  @override
  void write(BinaryWriter writer, BookingStatus obj) {
    switch (obj) {
      case BookingStatus.scheduled:
        writer.writeByte(0);
        break;
      case BookingStatus.confirmed:
        writer.writeByte(1);
        break;
      case BookingStatus.inProgress:
        writer.writeByte(2);
        break;
      case BookingStatus.completed:
        writer.writeByte(3);
        break;
      case BookingStatus.cancelled:
        writer.writeByte(4);
        break;
      case BookingStatus.failed:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookingStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AdvancedBooking _$AdvancedBookingFromJson(Map<String, dynamic> json) =>
    AdvancedBooking(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecodeNullable(_$BookingTypeEnumMap, json['type']) ??
          BookingType.single,
      title: json['title'] as String,
      description: json['description'] as String? ?? '',
      serviceType: json['serviceType'] as String,
      scheduledAt: DateTime.parse(json['scheduledAt'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      recurrencePattern: $enumDecodeNullable(
          _$RecurrencePatternEnumMap, json['recurrencePattern']),
      recurrenceConfig:
          json['recurrenceConfig'] as Map<String, dynamic>? ?? const {},
      participantIds: (json['participantIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      templateId: json['templateId'] as String?,
      status: $enumDecodeNullable(_$BookingStatusEnumMap, json['status']) ??
          BookingStatus.scheduled,
      estimatedCost: (json['estimatedCost'] as num?)?.toDouble() ?? 0.0,
      pickupAddress: json['pickupAddress'] as String,
      pickupLatitude: (json['pickupLatitude'] as num).toDouble(),
      pickupLongitude: (json['pickupLongitude'] as num).toDouble(),
      deliveryAddress: json['deliveryAddress'] as String,
      deliveryLatitude: (json['deliveryLatitude'] as num).toDouble(),
      deliveryLongitude: (json['deliveryLongitude'] as num).toDouble(),
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => BookingItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      preferences: json['preferences'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      notes: json['notes'] as String?,
      generatedBookingIds: (json['generatedBookingIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$AdvancedBookingToJson(AdvancedBooking instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$BookingTypeEnumMap[instance.type]!,
      'title': instance.title,
      'description': instance.description,
      'serviceType': instance.serviceType,
      'scheduledAt': instance.scheduledAt.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'recurrencePattern':
          _$RecurrencePatternEnumMap[instance.recurrencePattern],
      'recurrenceConfig': instance.recurrenceConfig,
      'participantIds': instance.participantIds,
      'templateId': instance.templateId,
      'status': _$BookingStatusEnumMap[instance.status]!,
      'estimatedCost': instance.estimatedCost,
      'pickupAddress': instance.pickupAddress,
      'pickupLatitude': instance.pickupLatitude,
      'pickupLongitude': instance.pickupLongitude,
      'deliveryAddress': instance.deliveryAddress,
      'deliveryLatitude': instance.deliveryLatitude,
      'deliveryLongitude': instance.deliveryLongitude,
      'items': instance.items,
      'preferences': instance.preferences,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'notes': instance.notes,
      'generatedBookingIds': instance.generatedBookingIds,
    };

const _$BookingTypeEnumMap = {
  BookingType.single: 'single',
  BookingType.recurring: 'recurring',
  BookingType.group: 'group',
  BookingType.template: 'template',
};

const _$RecurrencePatternEnumMap = {
  RecurrencePattern.daily: 'daily',
  RecurrencePattern.weekly: 'weekly',
  RecurrencePattern.biweekly: 'biweekly',
  RecurrencePattern.monthly: 'monthly',
  RecurrencePattern.custom: 'custom',
};

const _$BookingStatusEnumMap = {
  BookingStatus.scheduled: 'scheduled',
  BookingStatus.confirmed: 'confirmed',
  BookingStatus.inProgress: 'inProgress',
  BookingStatus.completed: 'completed',
  BookingStatus.cancelled: 'cancelled',
  BookingStatus.failed: 'failed',
};

BookingItem _$BookingItemFromJson(Map<String, dynamic> json) => BookingItem(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      quantity: (json['quantity'] as num?)?.toInt() ?? 1,
      unitPrice: (json['unitPrice'] as num?)?.toDouble() ?? 0.0,
      imageUrl: json['imageUrl'] as String?,
      description: json['description'] as String?,
      specifications:
          json['specifications'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$BookingItemToJson(BookingItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'category': instance.category,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'imageUrl': instance.imageUrl,
      'description': instance.description,
      'specifications': instance.specifications,
    };

BookingTemplate _$BookingTemplateFromJson(Map<String, dynamic> json) =>
    BookingTemplate(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      serviceType: json['serviceType'] as String,
      pickupAddress: json['pickupAddress'] as String,
      pickupLatitude: (json['pickupLatitude'] as num).toDouble(),
      pickupLongitude: (json['pickupLongitude'] as num).toDouble(),
      deliveryAddress: json['deliveryAddress'] as String,
      deliveryLatitude: (json['deliveryLatitude'] as num).toDouble(),
      deliveryLongitude: (json['deliveryLongitude'] as num).toDouble(),
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => BookingItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      preferences: json['preferences'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      usageCount: (json['usageCount'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$BookingTemplateToJson(BookingTemplate instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'description': instance.description,
      'serviceType': instance.serviceType,
      'pickupAddress': instance.pickupAddress,
      'pickupLatitude': instance.pickupLatitude,
      'pickupLongitude': instance.pickupLongitude,
      'deliveryAddress': instance.deliveryAddress,
      'deliveryLatitude': instance.deliveryLatitude,
      'deliveryLongitude': instance.deliveryLongitude,
      'items': instance.items,
      'preferences': instance.preferences,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'usageCount': instance.usageCount,
      'isActive': instance.isActive,
    };

GroupBooking _$GroupBookingFromJson(Map<String, dynamic> json) => GroupBooking(
      id: json['id'] as String,
      organizerId: json['organizerId'] as String,
      title: json['title'] as String,
      description: json['description'] as String? ?? '',
      serviceType: json['serviceType'] as String,
      scheduledAt: DateTime.parse(json['scheduledAt'] as String),
      participants: (json['participants'] as List<dynamic>?)
              ?.map((e) => GroupParticipant.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalCost: (json['totalCost'] as num?)?.toDouble() ?? 0.0,
      costSplitMethod: json['costSplitMethod'] as String? ?? 'equal',
      pickupAddress: json['pickupAddress'] as String,
      pickupLatitude: (json['pickupLatitude'] as num).toDouble(),
      pickupLongitude: (json['pickupLongitude'] as num).toDouble(),
      deliveryAddress: json['deliveryAddress'] as String,
      deliveryLatitude: (json['deliveryLatitude'] as num).toDouble(),
      deliveryLongitude: (json['deliveryLongitude'] as num).toDouble(),
      status: $enumDecodeNullable(_$BookingStatusEnumMap, json['status']) ??
          BookingStatus.scheduled,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$GroupBookingToJson(GroupBooking instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizerId': instance.organizerId,
      'title': instance.title,
      'description': instance.description,
      'serviceType': instance.serviceType,
      'scheduledAt': instance.scheduledAt.toIso8601String(),
      'participants': instance.participants,
      'totalCost': instance.totalCost,
      'costSplitMethod': instance.costSplitMethod,
      'pickupAddress': instance.pickupAddress,
      'pickupLatitude': instance.pickupLatitude,
      'pickupLongitude': instance.pickupLongitude,
      'deliveryAddress': instance.deliveryAddress,
      'deliveryLatitude': instance.deliveryLatitude,
      'deliveryLongitude': instance.deliveryLongitude,
      'status': _$BookingStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'notes': instance.notes,
    };

GroupParticipant _$GroupParticipantFromJson(Map<String, dynamic> json) =>
    GroupParticipant(
      userId: json['userId'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      hasConfirmed: json['hasConfirmed'] as bool? ?? false,
      shareAmount: (json['shareAmount'] as num?)?.toDouble() ?? 0.0,
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => BookingItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      confirmedAt: json['confirmedAt'] == null
          ? null
          : DateTime.parse(json['confirmedAt'] as String),
    );

Map<String, dynamic> _$GroupParticipantToJson(GroupParticipant instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'name': instance.name,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'hasConfirmed': instance.hasConfirmed,
      'shareAmount': instance.shareAmount,
      'items': instance.items,
      'joinedAt': instance.joinedAt.toIso8601String(),
      'confirmedAt': instance.confirmedAt?.toIso8601String(),
    };
