import '../../../rating/domain/models/rating_models.dart';

class DemoRatingData {
  // Demo users for ratings
  static const List<Map<String, dynamic>> demoUsers = [
    {
      'id': 'user_1',
      'name': '<PERSON><PERSON>',
      'avatar': 'assets/images/demo/user_1.jpg',
    },
    {
      'id': 'user_2',
      'name': '<PERSON><PERSON>',
      'avatar': 'assets/images/demo/user_2.jpg',
    },
    {
      'id': 'user_3',
      'name': '<PERSON><PERSON>',
      'avatar': 'assets/images/demo/user_3.jpg',
    },
    {
      'id': 'user_4',
      'name': '<PERSON><PERSON><PERSON>',
      'avatar': 'assets/images/demo/user_4.jpg',
    },
    {
      'id': 'user_5',
      'name': '<PERSON><PERSON><PERSON>',
      'avatar': 'assets/images/demo/user_5.jpg',
    },
  ];

  // Demo ratings for riders
  static List<Rating> getDemoRiderRatings(String riderId) {
    return [
      Rating(
        id: 'rating_rider_1',
        userId: 'user_1',
        targetId: riderId,
        targetType: 'rider',
        orderId: 'order_123',
        rating: 5.0,
        review: 'Excellent service! The rider was very professional and delivered my food hot and fresh. Highly recommended!',
        tags: ['Fast Delivery', 'Polite', 'Professional', 'On Time'],
        images: ['assets/images/demo/review_food_1.jpg'],
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        isVerified: true,
        helpfulCount: 12,
        userName: 'Rajesh Kumar',
        userAvatar: 'assets/images/demo/user_1.jpg',
      ),
      Rating(
        id: 'rating_rider_2',
        userId: 'user_2',
        targetId: riderId,
        targetType: 'rider',
        orderId: 'order_124',
        rating: 4.0,
        review: 'Good service overall. The rider was on time and the delivery was smooth. Food was still warm when it arrived.',
        tags: ['On Time', 'Good Communication', 'Careful Handling'],
        images: [],
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
        isVerified: true,
        helpfulCount: 8,
        userName: 'Priya Sharma',
        userAvatar: 'assets/images/demo/user_2.jpg',
      ),
      Rating(
        id: 'rating_rider_3',
        userId: 'user_3',
        targetId: riderId,
        targetType: 'rider',
        orderId: 'order_125',
        rating: 5.0,
        review: 'Amazing rider! Very polite and professional. Delivered exactly on time and even called to confirm the location. Will definitely order again!',
        tags: ['Fast Delivery', 'Polite', 'Professional', 'Good Communication', 'Helpful'],
        images: ['assets/images/demo/review_food_2.jpg'],
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        updatedAt: DateTime.now().subtract(const Duration(days: 7)),
        isVerified: true,
        helpfulCount: 15,
        userName: 'Amit Singh',
        userAvatar: 'assets/images/demo/user_3.jpg',
      ),
      Rating(
        id: 'rating_rider_4',
        userId: 'user_4',
        targetId: riderId,
        targetType: 'rider',
        orderId: 'order_126',
        rating: 3.0,
        review: 'Average service. The delivery was a bit delayed but the rider was polite when he arrived.',
        tags: ['Polite'],
        images: [],
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 10)),
        isVerified: true,
        helpfulCount: 3,
        userName: 'Sneha Patel',
        userAvatar: 'assets/images/demo/user_4.jpg',
      ),
      Rating(
        id: 'rating_rider_5',
        userId: 'user_5',
        targetId: riderId,
        targetType: 'rider',
        orderId: 'order_127',
        rating: 4.0,
        review: 'Good experience overall. The rider was professional and the food arrived in good condition.',
        tags: ['Professional', 'Careful Handling', 'Clean Vehicle'],
        images: [],
        createdAt: DateTime.now().subtract(const Duration(days: 12)),
        updatedAt: DateTime.now().subtract(const Duration(days: 12)),
        isVerified: true,
        helpfulCount: 6,
        userName: 'Vikram Gupta',
        userAvatar: 'assets/images/demo/user_5.jpg',
      ),
    ];
  }

  // Demo ratings for sellers
  static List<Rating> getDemoSellerRatings(String sellerId) {
    return [
      Rating(
        id: 'rating_seller_1',
        userId: 'user_1',
        targetId: sellerId,
        targetType: 'seller',
        orderId: 'order_201',
        rating: 5.0,
        review: 'Excellent restaurant! The food quality is outstanding and the packaging was perfect. Will definitely order again!',
        tags: ['Quality Products', 'Good Packaging', 'Professional Service', 'Fast Response'],
        images: ['assets/images/demo/review_product_1.jpg', 'assets/images/demo/review_product_2.jpg'],
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        isVerified: true,
        helpfulCount: 18,
        userName: 'Rajesh Kumar',
        userAvatar: 'assets/images/demo/user_1.jpg',
      ),
      Rating(
        id: 'rating_seller_2',
        userId: 'user_2',
        targetId: sellerId,
        targetType: 'seller',
        orderId: 'order_202',
        rating: 4.0,
        review: 'Great food and service. The restaurant responded quickly to my order and the food was delicious. Highly recommend their biryani!',
        tags: ['Quality Products', 'Fast Response', 'Good Communication'],
        images: [],
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
        isVerified: true,
        helpfulCount: 11,
        userName: 'Priya Sharma',
        userAvatar: 'assets/images/demo/user_2.jpg',
      ),
      Rating(
        id: 'rating_seller_3',
        userId: 'user_3',
        targetId: sellerId,
        targetType: 'seller',
        orderId: 'order_203',
        rating: 5.0,
        review: 'Outstanding service! The seller was very professional and the product quality exceeded my expectations. Perfect packaging and timely delivery.',
        tags: ['Quality Products', 'Professional Service', 'Good Packaging', 'Reliable', 'Honest Pricing'],
        images: ['assets/images/demo/review_service_1.jpg'],
        createdAt: DateTime.now().subtract(const Duration(days: 6)),
        updatedAt: DateTime.now().subtract(const Duration(days: 6)),
        isVerified: true,
        helpfulCount: 22,
        userName: 'Amit Singh',
        userAvatar: 'assets/images/demo/user_3.jpg',
      ),
      Rating(
        id: 'rating_seller_4',
        userId: 'user_4',
        targetId: sellerId,
        targetType: 'seller',
        orderId: 'order_204',
        rating: 4.0,
        review: 'Good experience with this seller. Products are of good quality and reasonably priced. Will buy again.',
        tags: ['Quality Products', 'Honest Pricing', 'Reliable'],
        images: [],
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        updatedAt: DateTime.now().subtract(const Duration(days: 8)),
        isVerified: true,
        helpfulCount: 7,
        userName: 'Sneha Patel',
        userAvatar: 'assets/images/demo/user_4.jpg',
      ),
    ];
  }

  // Demo ratings for services
  static List<Rating> getDemoServiceRatings(String serviceId) {
    return [
      Rating(
        id: 'rating_service_1',
        userId: 'user_1',
        targetId: serviceId,
        targetType: 'service',
        orderId: 'service_301',
        rating: 5.0,
        review: 'Excellent plumbing service! The technician was very skilled and professional. Fixed the issue quickly and cleaned up after the work. Highly recommended!',
        tags: ['High Quality', 'Professional', 'On Time', 'Skilled', 'Clean Work'],
        images: ['assets/images/demo/review_service_1.jpg', 'assets/images/demo/review_service_2.jpg'],
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
        isVerified: true,
        helpfulCount: 14,
        userName: 'Rajesh Kumar',
        userAvatar: 'assets/images/demo/user_1.jpg',
      ),
      Rating(
        id: 'rating_service_2',
        userId: 'user_2',
        targetId: serviceId,
        targetType: 'service',
        orderId: 'service_302',
        rating: 4.0,
        review: 'Good service overall. The technician arrived on time and completed the work efficiently. Fair pricing for the quality of work.',
        tags: ['On Time', 'Professional', 'Value for Money', 'Reliable'],
        images: [],
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        isVerified: true,
        helpfulCount: 9,
        userName: 'Priya Sharma',
        userAvatar: 'assets/images/demo/user_2.jpg',
      ),
      Rating(
        id: 'rating_service_3',
        userId: 'user_3',
        targetId: serviceId,
        targetType: 'service',
        orderId: 'service_303',
        rating: 5.0,
        review: 'Amazing service! Very professional and courteous. The work was completed to perfection and the pricing was very reasonable. Will definitely use again!',
        tags: ['High Quality', 'Professional', 'Courteous', 'Value for Money', 'Skilled', 'Reliable'],
        images: [],
        createdAt: DateTime.now().subtract(const Duration(days: 4)),
        updatedAt: DateTime.now().subtract(const Duration(days: 4)),
        isVerified: true,
        helpfulCount: 16,
        userName: 'Amit Singh',
        userAvatar: 'assets/images/demo/user_3.jpg',
      ),
    ];
  }

  // Generate demo rating summary
  static RatingSummary getDemoRatingSummary(String targetId, String targetType) {
    List<Rating> ratings;
    switch (targetType) {
      case 'rider':
        ratings = getDemoRiderRatings(targetId);
        break;
      case 'seller':
        ratings = getDemoSellerRatings(targetId);
        break;
      case 'service':
        ratings = getDemoServiceRatings(targetId);
        break;
      default:
        ratings = [];
    }

    if (ratings.isEmpty) {
      return RatingSummary(
        targetId: targetId,
        targetType: targetType,
        averageRating: 0.0,
        totalRatings: 0,
        ratingDistribution: {},
        topTags: [],
        lastUpdated: DateTime.now(),
      );
    }

    final totalRatings = ratings.length;
    final averageRating = ratings.map((r) => r.rating).reduce((a, b) => a + b) / totalRatings;
    
    final ratingDistribution = <int, int>{};
    for (int i = 1; i <= 5; i++) {
      ratingDistribution[i] = ratings.where((r) => r.rating.round() == i).length;
    }

    // Get top tags
    final allTags = <String>[];
    for (final rating in ratings) {
      allTags.addAll(rating.tags);
    }
    final tagCounts = <String, int>{};
    for (final tag in allTags) {
      tagCounts[tag] = (tagCounts[tag] ?? 0) + 1;
    }
    final topTags = tagCounts.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value));
    final topTagsList = topTags.take(5).map((e) => e.key).toList();

    return RatingSummary(
      targetId: targetId,
      targetType: targetType,
      averageRating: double.parse(averageRating.toStringAsFixed(1)),
      totalRatings: totalRatings,
      ratingDistribution: ratingDistribution,
      topTags: topTagsList,
      lastUpdated: DateTime.now(),
    );
  }
}
