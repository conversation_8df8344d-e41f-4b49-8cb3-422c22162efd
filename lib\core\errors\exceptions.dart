class ServerException implements Exception {
  final String message;
  final int? statusCode;

  const ServerException({
    required this.message,
    this.statusCode,
  });

  @override
  String toString() => 'ServerException: $message (Code: $statusCode)';
}

class NetworkException implements Exception {
  final String message;

  const NetworkException({
    required this.message,
  });

  @override
  String toString() => 'NetworkException: $message';
}

class CacheException implements Exception {
  final String message;

  const CacheException({
    required this.message,
  });

  @override
  String toString() => 'CacheException: $message';
}

class ValidationException implements Exception {
  final String message;
  final Map<String, String>? errors;

  const ValidationException({
    required this.message,
    this.errors,
  });

  @override
  String toString() => 'ValidationException: $message';
}

class AuthException implements Exception {
  final String message;
  final String? code;

  const AuthException({
    required this.message,
    this.code,
  });

  @override
  String toString() => 'AuthException: $message (Code: $code)';
}

class LocationException implements Exception {
  final String message;

  const LocationException({
    required this.message,
  });

  @override
  String toString() => 'LocationException: $message';
}

class PaymentException implements Exception {
  final String message;
  final String? code;

  const PaymentException({
    required this.message,
    this.code,
  });

  @override
  String toString() => 'PaymentException: $message (Code: $code)';
}

class FileException implements Exception {
  final String message;

  const FileException({
    required this.message,
  });

  @override
  String toString() => 'FileException: $message';
}
