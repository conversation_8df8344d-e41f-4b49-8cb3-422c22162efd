@echo off
title Device Connection Checker
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 DEVICE CONNECTION CHECKER                   ║
echo ║              Verify Android Device Setup                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Step 1: Checking ADB connection...
echo Running ADB devices command...
adb devices
echo.

echo 📱 Step 2: Checking Flutter devices...
echo Scanning for Flutter-compatible devices...
flutter devices
echo.

echo 🔧 Step 3: Checking USB debugging status...
echo Verifying USB debugging is enabled...
adb shell getprop ro.debuggable
echo.

echo 📊 Step 4: Device information...
echo Getting device details...
adb shell getprop ro.product.model
adb shell getprop ro.build.version.release
adb shell getprop ro.product.manufacturer
echo.

echo 🎯 Step 5: Connection test...
echo Testing connection stability...
adb shell echo "Connection test successful"
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    CONNECTION STATUS                        ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  📱 Expected Device: V2130 (1397182984001HG)               ║
echo ║  🔧 USB Debugging: Should be enabled                       ║
echo ║  ⚡ Developer Options: Should be enabled                   ║
echo ║                                                              ║
echo ║  ✅ If device appears above, you're ready!                 ║
echo ║  ❌ If no device shown, check:                             ║
echo ║     • USB cable connection                                  ║
echo ║     • USB debugging enabled                                 ║
echo ║     • Developer options enabled                             ║
echo ║     • Allow USB debugging popup                             ║
echo ║                                                              ║
echo ║  🚀 READY TO DEVELOP?                                      ║
echo ║     Run: .\dev_user_app.bat                                ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo Press any key to exit...
pause >nul
