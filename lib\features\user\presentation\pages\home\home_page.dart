import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/config/app_config.dart';
import '../games/spin_earn_page.dart';
import '../help/help_center_page.dart';
import '../chat/chat_list_page.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../enhanced_dashboard.dart';
import '../bookings/user_bookings_page.dart';

class UserHomePage extends ConsumerStatefulWidget {
  const UserHomePage({super.key});

  @override
  ConsumerState<UserHomePage> createState() => _UserHomePageState();
}

class _UserHomePageState extends ConsumerState<UserHomePage> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const EnhancedUserDashboard(), // Replaced with enhanced dashboard
    const UserMarketplaceTab(),
    const UserBookingsPage(),
    const UserWalletTab(),
    const ChatListPage(),
    const UserProfileTab(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        selectedItemColor: AppConfig.primaryColor,
        unselectedItemColor: AppColors.grey500,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_bag),
            label: 'Services',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'Bookings',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.payment), label: 'Payment'),
          BottomNavigationBarItem(icon: Icon(Icons.chat), label: 'Messages'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}

// Old UserDashboardTab removed - now using EnhancedUserDashboard

class UserMarketplaceTab extends ConsumerWidget {
  const UserMarketplaceTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Browse Services'),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.pushNamed(context, '/search');
            },
          ),
          IconButton(
            icon: const Icon(Icons.card_giftcard),
            onPressed: () {
              Navigator.pushNamed(context, '/loyalty');
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Categories Section
            Text('Service Categories', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 16),
            _buildCategoriesGrid(),

            const SizedBox(height: 24),

            // Featured Services
            Text('Featured Services', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 16),
            _buildFeaturedServices(),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    final categories = [
      {
        'name': 'Home Cleaning',
        'icon': Icons.cleaning_services,
        'color': Colors.blue,
      },
      {'name': 'Plumbing', 'icon': Icons.plumbing, 'color': Colors.orange},
      {
        'name': 'Electrical',
        'icon': Icons.electrical_services,
        'color': Colors.yellow,
      },
      {'name': 'Beauty & Spa', 'icon': Icons.spa, 'color': Colors.pink},
      {'name': 'Fitness', 'icon': Icons.fitness_center, 'color': Colors.green},
      {'name': 'Tutoring', 'icon': Icons.school, 'color': Colors.purple},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return Card(
          elevation: 2,
          child: InkWell(
            onTap: () {
              // Navigate to category services
            },
            borderRadius: BorderRadius.circular(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  category['icon'] as IconData,
                  size: 40,
                  color: category['color'] as Color,
                ),
                const SizedBox(height: 8),
                Text(
                  category['name'] as String,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeaturedServices() {
    // Mock featured services data
    final services = [
      {
        'name': 'Professional House Cleaning',
        'provider': 'CleanPro Services',
        'rating': 4.8,
        'price': 299,
        'image': 'assets/images/cleaning.jpg',
      },
      {
        'name': 'AC Repair & Maintenance',
        'provider': 'CoolTech Solutions',
        'rating': 4.6,
        'price': 499,
        'image': 'assets/images/ac_repair.jpg',
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: services.length,
      itemBuilder: (context, index) {
        final service = services[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: AppColors.grey200,
              ),
              child: const Icon(Icons.home_repair_service),
            ),
            title: Text(
              service['name'] as String,
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(service['provider'] as String),
                Row(
                  children: [
                    const Icon(Icons.star, size: 16, color: Colors.amber),
                    Text(' ${service['rating']}'),
                    const Spacer(),
                    Text(
                      '₹${service['price']}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppConfig.primaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: ElevatedButton(
              onPressed: () {
                // Navigate to booking page
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConfig.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Book'),
            ),
          ),
        );
      },
    );
  }
}

class UserWalletTab extends ConsumerWidget {
  const UserWalletTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment & Wallet'),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              // Navigate to payment history
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Payment Methods Section
            Text('Payment Methods', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 16),
            _buildPaymentMethods(),

            const SizedBox(height: 24),

            // Recent Transactions
            Text('Recent Transactions', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 16),
            _buildRecentTransactions(),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethods() {
    final paymentMethods = [
      {
        'name': 'Credit/Debit Card',
        'icon': Icons.credit_card,
        'description': 'Add your cards for quick payments',
        'color': Colors.blue,
      },
      {
        'name': 'UPI',
        'icon': Icons.account_balance_wallet,
        'description': 'Pay using UPI apps',
        'color': Colors.green,
      },
      {
        'name': 'Net Banking',
        'icon': Icons.account_balance,
        'description': 'Direct bank transfer',
        'color': Colors.orange,
      },
      {
        'name': 'Digital Wallets',
        'icon': Icons.wallet,
        'description': 'Paytm, PhonePe, Google Pay',
        'color': Colors.purple,
      },
    ];

    return Column(
      children: paymentMethods.map((method) {
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: (method['color'] as Color).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                method['icon'] as IconData,
                color: method['color'] as Color,
              ),
            ),
            title: Text(
              method['name'] as String,
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(method['description'] as String),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // Navigate to payment method setup
            },
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRecentTransactions() {
    // Mock transaction data
    final transactions = [
      {
        'title': 'House Cleaning Service',
        'amount': -299.0,
        'date': 'Today, 2:30 PM',
        'status': 'Completed',
        'type': 'payment',
      },
      {
        'title': 'AC Repair Service',
        'amount': -499.0,
        'date': 'Yesterday, 10:15 AM',
        'status': 'Completed',
        'type': 'payment',
      },
      {
        'title': 'Refund - Cancelled Booking',
        'amount': 199.0,
        'date': '2 days ago',
        'status': 'Refunded',
        'type': 'refund',
      },
    ];

    if (transactions.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              'No transactions yet',
              style: AppTextStyles.bodyLarge.copyWith(color: AppColors.grey600),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        final isPayment = transaction['type'] == 'payment';
        final amount = transaction['amount'] as double;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isPayment
                    ? Colors.red.withValues(alpha: 0.1)
                    : Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isPayment ? Icons.arrow_upward : Icons.arrow_downward,
                color: isPayment ? Colors.red : Colors.green,
              ),
            ),
            title: Text(
              transaction['title'] as String,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(transaction['date'] as String),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${amount > 0 ? '+' : ''}₹${amount.abs().toStringAsFixed(0)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: amount > 0 ? Colors.green : Colors.red,
                  ),
                ),
                Text(
                  transaction['status'] as String,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class UserProfileTab extends ConsumerWidget {
  const UserProfileTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(child: Text('Profile - Coming Soon!')),
    );
  }
}
