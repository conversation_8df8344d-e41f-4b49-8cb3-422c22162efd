import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'service.g.dart';

@HiveType(typeId: 30)
enum ServiceType {
  @HiveField(0)
  teaching,
  @HiveField(1)
  plumber,
  @HiveField(2)
  electrician,
  @HiveField(3)
  cleaning,
  @HiveField(4)
  beautyService,
  @HiveField(5)
  repairs,
  @HiveField(6)
  medicine,
  @HiveField(7)
  laundry,
  @HiveField(8)
  cooking,
  @HiveField(9)
  gardening,
  @HiveField(10)
  painting,
  @HiveField(11)
  carpentry,
  @HiveField(12)
  appliance,
  @HiveField(13)
  pest,
  @HiveField(14)
  security,
}

@HiveType(typeId: 31)
enum ServiceCategory {
  @HiveField(0)
  homeServices,
  @HiveField(1)
  education,
  @HiveField(2)
  healthcare,
  @HiveField(3)
  beauty,
  @HiveField(4)
  maintenance,
  @HiveField(5)
  emergency,
}

@HiveType(typeId: 32)
enum ServiceStatus {
  @HiveField(0)
  active,
  @HiveField(1)
  inactive,
  @HiveField(2)
  suspended,
  @HiveField(3)
  underReview,
}

@HiveType(typeId: 33)
enum PricingType {
  @HiveField(0)
  hourly,
  @HiveField(1)
  fixed,
  @HiveField(2)
  perSession,
  @HiveField(3)
  perProject,
  @HiveField(4)
  perVisit,
}

@HiveType(typeId: 34)
@JsonSerializable()
class ServiceAvailability extends Equatable {
  @HiveField(0)
  final int dayOfWeek; // 1-7 (Monday-Sunday)
  
  @HiveField(1)
  final String startTime; // "09:00"
  
  @HiveField(2)
  final String endTime; // "18:00"
  
  @HiveField(3)
  final bool isAvailable;

  const ServiceAvailability({
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.isAvailable,
  });

  factory ServiceAvailability.fromJson(Map<String, dynamic> json) =>
      _$ServiceAvailabilityFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceAvailabilityToJson(this);

  @override
  List<Object?> get props => [dayOfWeek, startTime, endTime, isAvailable];
}

@HiveType(typeId: 35)
@JsonSerializable()
class Service extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final ServiceType type;

  @HiveField(4)
  final ServiceCategory category;

  @HiveField(5)
  final String providerId;

  @HiveField(6)
  final String providerName;

  @HiveField(7)
  final String providerPhone;

  @HiveField(8)
  final String? providerEmail;

  @HiveField(9)
  final String? providerImageUrl;

  @HiveField(10)
  final double basePrice;

  @HiveField(11)
  final PricingType pricingType;

  @HiveField(12)
  final String currency;

  @HiveField(13)
  final double rating;

  @HiveField(14)
  final int reviewCount;

  @HiveField(15)
  final List<String> images;

  @HiveField(16)
  final List<String> skills;

  @HiveField(17)
  final List<String> certifications;

  @HiveField(18)
  final List<ServiceAvailability> availability;

  @HiveField(19)
  final int experienceYears;

  @HiveField(20)
  final String? location;

  @HiveField(21)
  final double? latitude;

  @HiveField(22)
  final double? longitude;

  @HiveField(23)
  final double serviceRadius; // in kilometers

  @HiveField(24)
  final ServiceStatus status;

  @HiveField(25)
  final bool isVerified;

  @HiveField(26)
  final bool isEmergencyService;

  @HiveField(27)
  final int minBookingHours;

  @HiveField(28)
  final int maxBookingHours;

  @HiveField(29)
  final List<String> languages;

  @HiveField(30)
  final Map<String, dynamic> additionalInfo;

  @HiveField(31)
  final DateTime createdAt;

  @HiveField(32)
  final DateTime updatedAt;

  @HiveField(33)
  final int completedBookings;

  @HiveField(34)
  final double? distanceFromUser;

  const Service({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.category,
    required this.providerId,
    required this.providerName,
    required this.providerPhone,
    this.providerEmail,
    this.providerImageUrl,
    required this.basePrice,
    required this.pricingType,
    this.currency = 'INR',
    this.rating = 0.0,
    this.reviewCount = 0,
    this.images = const [],
    this.skills = const [],
    this.certifications = const [],
    this.availability = const [],
    this.experienceYears = 0,
    this.location,
    this.latitude,
    this.longitude,
    this.serviceRadius = 10.0,
    this.status = ServiceStatus.active,
    this.isVerified = false,
    this.isEmergencyService = false,
    this.minBookingHours = 1,
    this.maxBookingHours = 8,
    this.languages = const ['Hindi', 'English'],
    this.additionalInfo = const {},
    required this.createdAt,
    required this.updatedAt,
    this.completedBookings = 0,
    this.distanceFromUser,
  });

  factory Service.fromJson(Map<String, dynamic> json) => _$ServiceFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceToJson(this);

  Service copyWith({
    String? id,
    String? title,
    String? description,
    ServiceType? type,
    ServiceCategory? category,
    String? providerId,
    String? providerName,
    String? providerPhone,
    String? providerEmail,
    String? providerImageUrl,
    double? basePrice,
    PricingType? pricingType,
    String? currency,
    double? rating,
    int? reviewCount,
    List<String>? images,
    List<String>? skills,
    List<String>? certifications,
    List<ServiceAvailability>? availability,
    int? experienceYears,
    String? location,
    double? latitude,
    double? longitude,
    double? serviceRadius,
    ServiceStatus? status,
    bool? isVerified,
    bool? isEmergencyService,
    int? minBookingHours,
    int? maxBookingHours,
    List<String>? languages,
    Map<String, dynamic>? additionalInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? completedBookings,
    double? distanceFromUser,
  }) {
    return Service(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      category: category ?? this.category,
      providerId: providerId ?? this.providerId,
      providerName: providerName ?? this.providerName,
      providerPhone: providerPhone ?? this.providerPhone,
      providerEmail: providerEmail ?? this.providerEmail,
      providerImageUrl: providerImageUrl ?? this.providerImageUrl,
      basePrice: basePrice ?? this.basePrice,
      pricingType: pricingType ?? this.pricingType,
      currency: currency ?? this.currency,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      images: images ?? this.images,
      skills: skills ?? this.skills,
      certifications: certifications ?? this.certifications,
      availability: availability ?? this.availability,
      experienceYears: experienceYears ?? this.experienceYears,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      serviceRadius: serviceRadius ?? this.serviceRadius,
      status: status ?? this.status,
      isVerified: isVerified ?? this.isVerified,
      isEmergencyService: isEmergencyService ?? this.isEmergencyService,
      minBookingHours: minBookingHours ?? this.minBookingHours,
      maxBookingHours: maxBookingHours ?? this.maxBookingHours,
      languages: languages ?? this.languages,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedBookings: completedBookings ?? this.completedBookings,
      distanceFromUser: distanceFromUser ?? this.distanceFromUser,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        category,
        providerId,
        providerName,
        providerPhone,
        providerEmail,
        providerImageUrl,
        basePrice,
        pricingType,
        currency,
        rating,
        reviewCount,
        images,
        skills,
        certifications,
        availability,
        experienceYears,
        location,
        latitude,
        longitude,
        serviceRadius,
        status,
        isVerified,
        isEmergencyService,
        minBookingHours,
        maxBookingHours,
        languages,
        additionalInfo,
        createdAt,
        updatedAt,
        completedBookings,
        distanceFromUser,
      ];
}
