#!/usr/bin/env pwsh

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    PROJEK APK BUILD FIX UTILITY" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to copy APK files
function Copy-ApkFiles {
    Write-Host "Creating flutter-apk directory..." -ForegroundColor Blue
    $flutterApkDir = "build\app\outputs\flutter-apk"
    if (!(Test-Path $flutterApkDir)) {
        New-Item -ItemType Directory -Path $flutterApkDir -Force | Out-Null
    }

    Write-Host "Searching for APK files..." -ForegroundColor Blue
    
    # Define APK mappings
    $apkMappings = @{
        "build\app\outputs\apk\userProd\debug\app-user-prod-debug.apk" = @("app-debug.apk", "app-userprod-debug.apk")
        "build\app\outputs\apk\riderProd\debug\app-rider-prod-debug.apk" = @("app-riderprod-debug.apk")
        "build\app\outputs\apk\sellerProd\debug\app-seller-prod-debug.apk" = @("app-sellerprod-debug.apk")
    }
    
    $apkFound = $false
    
    foreach ($sourcePath in $apkMappings.Keys) {
        if (Test-Path $sourcePath) {
            $targetNames = $apkMappings[$sourcePath]
            foreach ($targetName in $targetNames) {
                $targetPath = Join-Path $flutterApkDir $targetName
                Copy-Item $sourcePath $targetPath -Force
                Write-Host "✓ Copied: $targetName" -ForegroundColor Green
                $apkFound = $true
            }
        }
    }
    
    return $apkFound
}

# Function to build APKs
function Build-Apks {
    Write-Host "Building APK files..." -ForegroundColor Blue
    
    # Clean and get dependencies
    Write-Host "Cleaning project..." -ForegroundColor Yellow
    flutter clean | Out-Null
    
    Write-Host "Getting dependencies..." -ForegroundColor Yellow
    flutter pub get | Out-Null
    
    # Try building User App
    Write-Host "Building User App..." -ForegroundColor Yellow
    $userBuild = Start-Process -FilePath "flutter" -ArgumentList "build", "apk", "--debug", "--flavor", "userProd", "--target", "lib/main_user.dart" -Wait -PassThru -NoNewWindow
    
    if ($userBuild.ExitCode -eq 0) {
        Write-Host "✓ User App build successful" -ForegroundColor Green
    } else {
        Write-Host "⚠ User App build failed, trying Gradle..." -ForegroundColor Yellow
        Set-Location android
        .\gradlew assembleUserProdDebug
        Set-Location ..
    }
}

# Main execution
try {
    # First, check if APKs already exist
    $existingApks = Copy-ApkFiles
    
    if (!$existingApks) {
        Write-Host "No existing APKs found. Building..." -ForegroundColor Yellow
        Build-Apks
        $existingApks = Copy-ApkFiles
    }
    
    if ($existingApks) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "✓ SUCCESS! APK files are ready" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        
        Write-Host "Available APK files:" -ForegroundColor Cyan
        Get-ChildItem "build\app\outputs\flutter-apk\*.apk" | ForEach-Object {
            Write-Host "• $($_.Name)" -ForegroundColor White
        }
        
        Write-Host ""
        Write-Host "You can now run:" -ForegroundColor Blue
        Write-Host "  flutter install" -ForegroundColor White
        Write-Host "or install the APK files directly on your device." -ForegroundColor White
        
    } else {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Red
        Write-Host "✗ No APK files found" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please check the build output for errors." -ForegroundColor Yellow
        Write-Host "You may need to fix compilation errors first." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
