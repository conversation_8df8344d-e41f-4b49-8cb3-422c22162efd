import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../payment/domain/models/enhanced_payment_models.dart';

class PaymentMethodCard extends StatelessWidget {
  final PaymentMethod method;
  final bool isSelected;
  final VoidCallback onTap;
  final String? subtitle;

  const PaymentMethodCard({
    super.key,
    required this.method,
    required this.isSelected,
    required this.onTap,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final config = PaymentMethodConfig.getConfig(method);

    return Card(
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(color: AppColors.primaryBlue, width: 2)
                : null,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getMethodIcon(method),
                  color: AppColors.primaryBlue,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      PaymentMethodConfig.getMethodName(method),
                      style: AppTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle!,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                    if (config != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Processing fee: ${config['processingFee']}%',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (isSelected)
                Icon(Icons.check_circle, color: AppColors.success, size: 24),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return Icons.credit_card;
      case PaymentMethod.upi:
        return Icons.account_balance;
      case PaymentMethod.netBanking:
        return Icons.account_balance;
      case PaymentMethod.wallet:
        return Icons.account_balance_wallet;
      case PaymentMethod.cashOnDelivery:
        return Icons.local_shipping;
      case PaymentMethod.emi:
        return Icons.payment;
      case PaymentMethod.buyNowPayLater:
        return Icons.schedule;
    }
  }
}

class PaymentSplitWidget extends StatefulWidget {
  final double totalAmount;
  final ValueChanged<List<Map<String, dynamic>>> onSplitsChanged;

  const PaymentSplitWidget({
    super.key,
    required this.totalAmount,
    required this.onSplitsChanged,
  });

  @override
  State<PaymentSplitWidget> createState() => _PaymentSplitWidgetState();
}

class _PaymentSplitWidgetState extends State<PaymentSplitWidget> {
  final List<Map<String, dynamic>> _splits = [];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('Split Payment', style: AppTextStyles.headlineSmall),
            const Spacer(),
            TextButton.icon(
              onPressed: _addSplit,
              icon: const Icon(Icons.add),
              label: const Text('Add Split'),
            ),
          ],
        ),
        const SizedBox(height: 16),

        if (_splits.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.grey300,
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(Icons.call_split, size: 48, color: AppColors.grey400),
                  const SizedBox(height: 8),
                  Text(
                    'No payment splits added',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Tap "Add Split" to split your payment across multiple methods',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          )
        else
          Column(
            children: [
              ..._splits.asMap().entries.map((entry) {
                final index = entry.key;
                final split = entry.value;
                return _buildSplitItem(index, split);
              }),
              const SizedBox(height: 16),
              _buildSplitSummary(),
            ],
          ),
      ],
    );
  }

  Widget _buildSplitItem(int index, Map<String, dynamic> split) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Split ${index + 1}',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => _removeSplit(index),
                  icon: const Icon(Icons.delete_outline),
                  color: AppColors.error,
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Payment Method Selection
            DropdownButtonFormField<PaymentMethod>(
              value: split['method'],
              decoration: const InputDecoration(
                labelText: 'Payment Method',
                border: OutlineInputBorder(),
              ),
              items: PaymentMethod.values.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Text(PaymentMethodConfig.getMethodName(method)),
                );
              }).toList(),
              onChanged: (method) {
                setState(() {
                  _splits[index]['method'] = method;
                });
                _notifyChanges();
              },
            ),

            const SizedBox(height: 12),

            // Amount Input
            TextFormField(
              initialValue: split['amount']?.toString() ?? '',
              decoration: InputDecoration(
                labelText: 'Amount',
                prefixText: '₹ ',
                border: const OutlineInputBorder(),
                helperText: 'Max: ₹${widget.totalAmount.toStringAsFixed(2)}',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              onChanged: (value) {
                final amount = double.tryParse(value) ?? 0.0;
                setState(() {
                  _splits[index]['amount'] = amount;
                });
                _notifyChanges();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSplitSummary() {
    final totalSplitAmount = _splits.fold<double>(
      0.0,
      (sum, split) => sum + (split['amount'] ?? 0.0),
    );
    final remainingAmount = widget.totalAmount - totalSplitAmount;

    return Card(
      color: AppColors.primaryBlue.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Text('Total Amount:', style: AppTextStyles.bodyMedium),
                const Spacer(),
                Text(
                  '₹${widget.totalAmount.toStringAsFixed(2)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Text('Split Amount:', style: AppTextStyles.bodyMedium),
                const Spacer(),
                Text(
                  '₹${totalSplitAmount.toStringAsFixed(2)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: totalSplitAmount > widget.totalAmount
                        ? AppColors.error
                        : AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Text('Remaining:', style: AppTextStyles.bodyMedium),
                const Spacer(),
                Text(
                  '₹${remainingAmount.toStringAsFixed(2)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: remainingAmount < 0
                        ? AppColors.error
                        : remainingAmount > 0
                        ? AppColors.warning
                        : AppColors.success,
                  ),
                ),
              ],
            ),
            if (remainingAmount != 0) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: remainingAmount < 0
                      ? AppColors.error.withOpacity(0.1)
                      : AppColors.warning.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      remainingAmount < 0
                          ? Icons.error_outline
                          : Icons.warning_outlined,
                      size: 16,
                      color: remainingAmount < 0
                          ? AppColors.error
                          : AppColors.warning,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        remainingAmount < 0
                            ? 'Split amount exceeds total amount'
                            : 'Split amount is less than total amount',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: remainingAmount < 0
                              ? AppColors.error
                              : AppColors.warning,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _addSplit() {
    setState(() {
      _splits.add({'method': PaymentMethod.upi, 'amount': 0.0});
    });
    _notifyChanges();
  }

  void _removeSplit(int index) {
    setState(() {
      _splits.removeAt(index);
    });
    _notifyChanges();
  }

  void _notifyChanges() {
    widget.onSplitsChanged(_splits);
  }
}

class PaymentStatusWidget extends StatelessWidget {
  final PaymentStatus status;
  final double? amount;
  final DateTime? timestamp;

  const PaymentStatusWidget({
    super.key,
    required this.status,
    this.amount,
    this.timestamp,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getStatusColor(status).withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(status),
            size: 16,
            color: _getStatusColor(status),
          ),
          const SizedBox(width: 6),
          Text(
            _getStatusText(status),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: _getStatusColor(status),
            ),
          ),
          if (amount != null) ...[
            const SizedBox(width: 6),
            Text(
              '₹${amount!.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: _getStatusColor(status),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.completed:
        return AppColors.success;
      case PaymentStatus.pending:
      case PaymentStatus.processing:
        return AppColors.warning;
      case PaymentStatus.failed:
      case PaymentStatus.cancelled:
        return AppColors.error;
      case PaymentStatus.refunded:
      case PaymentStatus.partiallyRefunded:
        return AppColors.info;
    }
  }

  IconData _getStatusIcon(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.completed:
        return Icons.check_circle;
      case PaymentStatus.pending:
        return Icons.schedule;
      case PaymentStatus.processing:
        return Icons.sync;
      case PaymentStatus.failed:
        return Icons.error;
      case PaymentStatus.cancelled:
        return Icons.cancel;
      case PaymentStatus.refunded:
      case PaymentStatus.partiallyRefunded:
        return Icons.undo;
    }
  }

  String _getStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.cancelled:
        return 'Cancelled';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.partiallyRefunded:
        return 'Partially Refunded';
    }
  }
}

class PaymentSummaryCard extends StatelessWidget {
  final EnhancedPayment payment;
  final VoidCallback? onTap;

  const PaymentSummaryCard({super.key, required this.payment, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order #${payment.orderId.substring(0, 8)}',
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _formatDate(payment.createdAt),
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PaymentStatusWidget(
                    status: payment.status,
                    amount: payment.paidAmount,
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Text(
                    'Total: ₹${payment.totalAmount.toStringAsFixed(2)}',
                    style: AppTextStyles.bodyMedium,
                  ),
                  const Spacer(),
                  if (payment.splits.isNotEmpty)
                    Text(
                      '${payment.splits.length} method${payment.splits.length > 1 ? 's' : ''}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                ],
              ),

              if (payment.refundedAmount > 0) ...[
                const SizedBox(height: 4),
                Text(
                  'Refunded: ₹${payment.refundedAmount.toStringAsFixed(2)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.info,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }
}
