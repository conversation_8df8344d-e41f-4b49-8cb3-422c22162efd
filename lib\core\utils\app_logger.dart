import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

enum LogLevel { debug, info, warning, error, critical }

class AppLogger {
  static const String _appName = 'Projek';
  static bool _isEnabled = true;
  static LogLevel _minLogLevel = kDebugMode ? LogLevel.debug : LogLevel.info;

  /// Enable or disable logging
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// Set minimum log level
  static void setMinLogLevel(LogLevel level) {
    _minLogLevel = level;
  }

  /// Log debug message
  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    _log(LogLevel.debug, message, error, stackTrace);
  }

  /// Log info message
  static void info(String message, [Object? error, StackTrace? stackTrace]) {
    _log(LogLevel.info, message, error, stackTrace);
  }

  /// Log warning message
  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    _log(LogLevel.warning, message, error, stackTrace);
  }

  /// Log error message
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    _log(LogLevel.error, message, error, stackTrace);
  }

  /// Log critical message
  static void critical(
    String message, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    _log(LogLevel.critical, message, error, stackTrace);
  }

  /// Internal logging method
  static void _log(
    LogLevel level,
    String message,
    Object? error,
    StackTrace? stackTrace,
  ) {
    if (!_isEnabled || level.index < _minLogLevel.index) {
      return;
    }

    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.name.toUpperCase().padRight(8);
    final logMessage = '[$timestamp] [$levelStr] $_appName: $message';

    // Print to console in debug mode
    if (kDebugMode) {
      switch (level) {
        case LogLevel.debug:
          debugPrint('🐛 $logMessage');
          break;
        case LogLevel.info:
          debugPrint('ℹ️ $logMessage');
          break;
        case LogLevel.warning:
          debugPrint('⚠️ $logMessage');
          break;
        case LogLevel.error:
          debugPrint('❌ $logMessage');
          break;
        case LogLevel.critical:
          debugPrint('🚨 $logMessage');
          break;
      }

      // Print error details if provided
      if (error != null) {
        debugPrint('   Error: $error');
      }
      if (stackTrace != null) {
        debugPrint('   Stack trace: $stackTrace');
      }
    }

    // Use developer.log for better debugging tools integration
    developer.log(
      message,
      name: _appName,
      level: _getLevelValue(level),
      error: error,
      stackTrace: stackTrace,
      time: DateTime.now(),
    );
  }

  /// Get numeric level value for developer.log
  static int _getLevelValue(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
      case LogLevel.critical:
        return 1200;
    }
  }

  /// Log API request
  static void apiRequest(
    String method,
    String url, {
    Map<String, dynamic>? data,
  }) {
    debug('API Request: $method $url', data);
  }

  /// Log API response
  static void apiResponse(
    String method,
    String url,
    int statusCode, {
    dynamic data,
  }) {
    if (statusCode >= 200 && statusCode < 300) {
      debug('API Response: $method $url - $statusCode', data);
    } else {
      warning('API Response: $method $url - $statusCode', data);
    }
  }

  /// Log navigation events
  static void navigation(String from, String to) {
    info('Navigation: $from -> $to');
  }

  /// Log user actions
  static void userAction(String action, {Map<String, dynamic>? data}) {
    info('User Action: $action', data);
  }

  /// Log performance metrics
  static void performance(
    String operation,
    Duration duration, {
    Map<String, dynamic>? data,
  }) {
    info('Performance: $operation took ${duration.inMilliseconds}ms', data);
  }

  /// Log Firebase events
  static void firebase(String event, {Map<String, dynamic>? data}) {
    info('Firebase: $event', data);
  }

  /// Log payment events
  static void payment(String event, {Map<String, dynamic>? data}) {
    info('Payment: $event', data);
  }

  /// Log authentication events
  static void auth(String event, {Map<String, dynamic>? data}) {
    info('Auth: $event', data);
  }

  /// Log database operations
  static void database(String operation, {Map<String, dynamic>? data}) {
    debug('Database: $operation', data);
  }

  /// Log network events
  static void network(String event, {Map<String, dynamic>? data}) {
    debug('Network: $event', data);
  }

  /// Log cache operations
  static void cache(String operation, {Map<String, dynamic>? data}) {
    debug('Cache: $operation', data);
  }

  /// Log security events
  static void security(String event, {Map<String, dynamic>? data}) {
    warning('Security: $event', data);
  }

  /// Log business logic events
  static void business(String event, {Map<String, dynamic>? data}) {
    info('Business: $event', data);
  }

  /// Log UI events
  static void ui(String event, {Map<String, dynamic>? data}) {
    debug('UI: $event', data);
  }

  /// Log lifecycle events
  static void lifecycle(String event, {Map<String, dynamic>? data}) {
    info('Lifecycle: $event', data);
  }

  /// Create a timed operation logger
  static TimedLogger startTimer(String operation) {
    return TimedLogger(operation);
  }
}

/// Helper class for timing operations
class TimedLogger {
  final String operation;
  final DateTime startTime;

  TimedLogger(this.operation) : startTime = DateTime.now() {
    AppLogger.debug('Started: $operation');
  }

  /// End the timer and log the duration
  void end({Map<String, dynamic>? data}) {
    final duration = DateTime.now().difference(startTime);
    AppLogger.performance(operation, duration, data: data);
  }
}

/// Extension for easy logging on any object
extension LoggingExtension on Object {
  void logDebug(String message) => AppLogger.debug('$runtimeType: $message');
  void logInfo(String message) => AppLogger.info('$runtimeType: $message');
  void logWarning(String message) =>
      AppLogger.warning('$runtimeType: $message');
  void logError(String message, [Object? error, StackTrace? stackTrace]) {
    AppLogger.error('$runtimeType: $message', error, stackTrace);
  }
}
