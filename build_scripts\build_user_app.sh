#!/bin/bash

echo "Building User App..."
echo

# Clean previous builds
echo "Cleaning previous builds..."
flutter clean
flutter pub get

# Build User App Debug APK
echo "Building User App Debug APK..."
flutter build apk --debug --target=lib/main_user.dart --flavor=userDev --no-tree-shake-icons

if [ $? -eq 0 ]; then
    echo
    echo "✅ User App Debug APK built successfully!"
    echo "Location: build/app/outputs/flutter-apk/app-user-dev-debug.apk"
else
    echo
    echo "❌ User App Debug build failed!"
    exit 1
fi

echo
echo "Building User App Release APK..."
flutter build apk --release --target=lib/main_user.dart --flavor=userProd --no-tree-shake-icons

if [ $? -eq 0 ]; then
    echo
    echo "✅ User App Release APK built successfully!"
    echo "Location: build/app/outputs/flutter-apk/app-user-prod-release.apk"
else
    echo
    echo "❌ User App Release build failed!"
    exit 1
fi

echo
echo "🎉 User App build completed successfully!"
echo
echo "Debug APK: build/app/outputs/flutter-apk/app-user-dev-debug.apk"
echo "Release APK: build/app/outputs/flutter-apk/app-user-prod-release.apk"
