class AppConstants {
  // App Information
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  
  // API Configuration
  static const String baseUrl = 'https://api.projek.com';
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String ridersCollection = 'riders';
  static const String sellersCollection = 'sellers';
  static const String ordersCollection = 'orders';
  static const String productsCollection = 'products';
  static const String transactionsCollection = 'transactions';
  static const String notificationsCollection = 'notifications';
  static const String walletsCollection = 'wallets';
  static const String vehiclesCollection = 'vehicles';
  static const String coursesCollection = 'courses';
  static const String servicesCollection = 'services';
  
  // Storage Paths
  static const String profileImagesPath = 'profile_images';
  static const String productImagesPath = 'product_images';
  static const String documentsPath = 'documents';
  static const String vehicleImagesPath = 'vehicle_images';
  static const String courseImagesPath = 'course_images';
  
  // Shared Preferences Keys
  static const String userTokenKey = 'user_token';
  static const String userIdKey = 'user_id';
  static const String appTypeKey = 'app_type';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language_code';
  static const String onboardingKey = 'onboarding_completed';
  static const String locationPermissionKey = 'location_permission';
  
  // Notification Channels
  static const String orderNotificationChannel = 'order_notifications';
  static const String generalNotificationChannel = 'general_notifications';
  static const String promotionNotificationChannel = 'promotion_notifications';
  
  // Payment Configuration
  static const String razorpayKeyId = 'rzp_test_your_key_id';
  static const String razorpayKeySecret = 'your_key_secret';
  
  // Map Configuration
  static const double defaultLatitude = 28.6139; // Delhi
  static const double defaultLongitude = 77.2090;
  static const double mapZoom = 15.0;
  
  // Business Rules
  static const double minOrderAmount = 50.0;
  static const double maxOrderAmount = 10000.0;
  static const double deliveryFee = 30.0;
  static const double freeDeliveryThreshold = 500.0;
  static const double gstRate = 0.18; // 18%
  static const double platformFee = 0.05; // 5%
  
  // Wallet Configuration
  static const double minWalletTopup = 10.0;
  static const double maxWalletTopup = 10000.0;
  static const double maxWalletBalance = 50000.0;
  static const double withdrawalFee = 0.05; // 5%
  
  // File Upload Limits
  static const int maxImageSizeMB = 5;
  static const int maxDocumentSizeMB = 10;
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedDocumentFormats = ['pdf', 'doc', 'docx'];
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Cache Duration
  static const Duration cacheExpiry = Duration(hours: 24);
  static const Duration shortCacheExpiry = Duration(minutes: 30);
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Error Messages
  static const String networkError = 'Network connection error. Please try again.';
  static const String serverError = 'Server error. Please try again later.';
  static const String unknownError = 'Something went wrong. Please try again.';
  static const String authError = 'Authentication failed. Please login again.';
  static const String permissionError = 'Permission denied. Please grant required permissions.';
  
  // Success Messages
  static const String orderPlaced = 'Order placed successfully!';
  static const String paymentSuccess = 'Payment completed successfully!';
  static const String profileUpdated = 'Profile updated successfully!';
  static const String documentUploaded = 'Document uploaded successfully!';
  
  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int phoneNumberLength = 10;
  
  // Regular Expressions
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^[6-9]\d{9}$';
  static const String nameRegex = r'^[a-zA-Z\s]+$';
  static const String vehicleNumberRegex = r'^[A-Z]{2}[0-9]{1,2}[A-Z]{1,2}[0-9]{4}$';
  
  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String apiDateFormat = 'yyyy-MM-dd';
  
  // Currency
  static const String currencySymbol = '₹';
  static const String currencyCode = 'INR';
  
  // Social Media
  static const String facebookUrl = 'https://facebook.com/projek';
  static const String twitterUrl = 'https://twitter.com/projek';
  static const String instagramUrl = 'https://instagram.com/projek';
  static const String linkedinUrl = 'https://linkedin.com/company/projek';
  
  // Support
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+91-9876543210';
  static const String helpUrl = 'https://help.projek.com';
  static const String privacyPolicyUrl = 'https://projek.com/privacy';
  static const String termsOfServiceUrl = 'https://projek.com/terms';
}

class AssetPaths {
  // Images
  static const String logo = 'assets/images/shared/logo.png';
  static const String splashBg = 'assets/images/shared/splash_bg.png';
  static const String placeholder = 'assets/images/shared/placeholder.png';
  static const String noInternet = 'assets/images/shared/no_internet.png';
  static const String emptyCart = 'assets/images/shared/empty_cart.png';
  static const String emptyOrders = 'assets/images/shared/empty_orders.png';
  
  // Icons
  static const String userIcon = 'assets/icons/user/user.svg';
  static const String riderIcon = 'assets/icons/rider/rider.svg';
  static const String sellerIcon = 'assets/icons/seller/seller.svg';
  
  // Animations
  static const String loadingAnimation = 'assets/animations/loading.json';
  static const String successAnimation = 'assets/animations/success.json';
  static const String errorAnimation = 'assets/animations/error.json';
  static const String emptyStateAnimation = 'assets/animations/empty_state.json';
  
  // Sounds
  static const String alarmSound = 'assets/sounds/alarm.mp3';
  static const String notificationSound = 'assets/sounds/notification.mp3';
  static const String successSound = 'assets/sounds/success.mp3';
}
