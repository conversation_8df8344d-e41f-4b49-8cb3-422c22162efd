import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'seller_models.g.dart';

@HiveType(typeId: 100)
enum SellerStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  verified,
  @HiveField(2)
  suspended,
  @HiveField(3)
  rejected,
}

@HiveType(typeId: 106)
enum InventoryStatus {
  @HiveField(0)
  inStock,
  @HiveField(1)
  lowStock,
  @HiveField(2)
  outOfStock,
  @HiveField(3)
  discontinued,
}

@HiveType(typeId: 102)
enum OrderStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  confirmed,
  @HiveField(2)
  processing,
  @HiveField(3)
  shipped,
  @HiveField(4)
  delivered,
  @HiveField(5)
  cancelled,
  @HiveField(6)
  returned,
}

@HiveType(typeId: 103)
@JsonSerializable()
class SellerProfile extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String businessName;

  @HiveField(2)
  final String ownerName;

  @HiveField(3)
  final String email;

  @HiveField(4)
  final String phone;

  @HiveField(5)
  final String? businessAddress;

  @HiveField(6)
  final String? businessDescription;

  @HiveField(7)
  final String? logoUrl;

  @HiveField(8)
  final String? bannerUrl;

  @HiveField(9)
  final SellerStatus status;

  @HiveField(10)
  final double rating;

  @HiveField(11)
  final int reviewCount;

  @HiveField(12)
  final List<String> categories;

  @HiveField(13)
  final Map<String, dynamic> businessDocuments;

  @HiveField(14)
  final DateTime createdAt;

  @HiveField(15)
  final DateTime updatedAt;

  @HiveField(16)
  final bool isVerified;

  @HiveField(17)
  final Map<String, dynamic> settings;

  const SellerProfile({
    required this.id,
    required this.businessName,
    required this.ownerName,
    required this.email,
    required this.phone,
    this.businessAddress,
    this.businessDescription,
    this.logoUrl,
    this.bannerUrl,
    this.status = SellerStatus.pending,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.categories = const [],
    this.businessDocuments = const {},
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.settings = const {},
  });

  factory SellerProfile.fromJson(Map<String, dynamic> json) =>
      _$SellerProfileFromJson(json);

  Map<String, dynamic> toJson() => _$SellerProfileToJson(this);

  factory SellerProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SellerProfile.fromJson({
      'id': doc.id,
      ...data,
      'createdAt': (data['createdAt'] as Timestamp?)
          ?.toDate()
          .toIso8601String(),
      'updatedAt': (data['updatedAt'] as Timestamp?)
          ?.toDate()
          .toIso8601String(),
    });
  }

  String get formattedRating => rating.toStringAsFixed(1);
  String get statusDisplayText {
    switch (status) {
      case SellerStatus.pending:
        return 'Pending Verification';
      case SellerStatus.verified:
        return 'Verified Seller';
      case SellerStatus.suspended:
        return 'Account Suspended';
      case SellerStatus.rejected:
        return 'Verification Rejected';
    }
  }

  @override
  List<Object?> get props => [
    id,
    businessName,
    ownerName,
    email,
    phone,
    businessAddress,
    businessDescription,
    logoUrl,
    bannerUrl,
    status,
    rating,
    reviewCount,
    categories,
    businessDocuments,
    createdAt,
    updatedAt,
    isVerified,
    settings,
  ];
}

@HiveType(typeId: 104)
@JsonSerializable()
class InventoryItem extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String productId;

  @HiveField(2)
  final String sellerId;

  @HiveField(3)
  final String sku;

  @HiveField(4)
  final int currentStock;

  @HiveField(5)
  final int minStockLevel;

  @HiveField(6)
  final int maxStockLevel;

  @HiveField(7)
  final double costPrice;

  @HiveField(8)
  final double sellingPrice;

  @HiveField(9)
  final InventoryStatus status;

  @HiveField(10)
  final String? location;

  @HiveField(11)
  final DateTime? lastRestocked;

  @HiveField(12)
  final DateTime? expiryDate;

  @HiveField(13)
  final Map<String, dynamic> metadata;

  @HiveField(14)
  final DateTime createdAt;

  @HiveField(15)
  final DateTime updatedAt;

  const InventoryItem({
    required this.id,
    required this.productId,
    required this.sellerId,
    required this.sku,
    required this.currentStock,
    this.minStockLevel = 5,
    this.maxStockLevel = 100,
    required this.costPrice,
    required this.sellingPrice,
    this.status = InventoryStatus.inStock,
    this.location,
    this.lastRestocked,
    this.expiryDate,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
  });

  factory InventoryItem.fromJson(Map<String, dynamic> json) =>
      _$InventoryItemFromJson(json);

  Map<String, dynamic> toJson() => _$InventoryItemToJson(this);

  bool get isLowStock => currentStock <= minStockLevel;
  bool get isOutOfStock => currentStock <= 0;
  double get profitMargin => ((sellingPrice - costPrice) / costPrice) * 100;
  String get formattedProfitMargin => '${profitMargin.toStringAsFixed(1)}%';

  InventoryStatus get calculatedStatus {
    if (currentStock <= 0) return InventoryStatus.outOfStock;
    if (currentStock <= minStockLevel) return InventoryStatus.lowStock;
    return InventoryStatus.inStock;
  }

  @override
  List<Object?> get props => [
    id,
    productId,
    sellerId,
    sku,
    currentStock,
    minStockLevel,
    maxStockLevel,
    costPrice,
    sellingPrice,
    status,
    location,
    lastRestocked,
    expiryDate,
    metadata,
    createdAt,
    updatedAt,
  ];
}

@HiveType(typeId: 105)
@JsonSerializable()
class SellerOrder extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String sellerId;

  @HiveField(2)
  final String customerId;

  @HiveField(3)
  final String customerName;

  @HiveField(4)
  final List<OrderItem> items;

  @HiveField(5)
  final double subtotal;

  @HiveField(6)
  final double tax;

  @HiveField(7)
  final double shippingFee;

  @HiveField(8)
  final double total;

  @HiveField(9)
  final OrderStatus status;

  @HiveField(10)
  final String? shippingAddress;

  @HiveField(11)
  final String? trackingNumber;

  @HiveField(12)
  final DateTime orderDate;

  @HiveField(13)
  final DateTime? shippedDate;

  @HiveField(14)
  final DateTime? deliveredDate;

  @HiveField(15)
  final Map<String, dynamic> metadata;

  const SellerOrder({
    required this.id,
    required this.sellerId,
    required this.customerId,
    required this.customerName,
    required this.items,
    required this.subtotal,
    this.tax = 0.0,
    this.shippingFee = 0.0,
    required this.total,
    this.status = OrderStatus.pending,
    this.shippingAddress,
    this.trackingNumber,
    required this.orderDate,
    this.shippedDate,
    this.deliveredDate,
    this.metadata = const {},
  });

  factory SellerOrder.fromJson(Map<String, dynamic> json) =>
      _$SellerOrderFromJson(json);

  Map<String, dynamic> toJson() => _$SellerOrderToJson(this);

  String get statusDisplayText {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.returned:
        return 'Returned';
    }
  }

  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);

  @override
  List<Object?> get props => [
    id,
    sellerId,
    customerId,
    customerName,
    items,
    subtotal,
    tax,
    shippingFee,
    total,
    status,
    shippingAddress,
    trackingNumber,
    orderDate,
    shippedDate,
    deliveredDate,
    metadata,
  ];
}

@HiveType(typeId: 106)
@JsonSerializable()
class OrderItem extends Equatable {
  @HiveField(0)
  final String productId;

  @HiveField(1)
  final String productName;

  @HiveField(2)
  final String? productImage;

  @HiveField(3)
  final int quantity;

  @HiveField(4)
  final double unitPrice;

  @HiveField(5)
  final double totalPrice;

  @HiveField(6)
  final Map<String, dynamic> productVariant;

  const OrderItem({
    required this.productId,
    required this.productName,
    this.productImage,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.productVariant = const {},
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) =>
      _$OrderItemFromJson(json);

  Map<String, dynamic> toJson() => _$OrderItemToJson(this);

  @override
  List<Object?> get props => [
    productId,
    productName,
    productImage,
    quantity,
    unitPrice,
    totalPrice,
    productVariant,
  ];
}

@HiveType(typeId: 107)
@JsonSerializable()
class SellerAnalytics extends Equatable {
  @HiveField(0)
  final String sellerId;

  @HiveField(1)
  final DateTime date;

  @HiveField(2)
  final double totalSales;

  @HiveField(3)
  final int totalOrders;

  @HiveField(4)
  final int totalProducts;

  @HiveField(5)
  final double averageOrderValue;

  @HiveField(6)
  final int newCustomers;

  @HiveField(7)
  final int returningCustomers;

  @HiveField(8)
  final Map<String, double> categoryWiseSales;

  @HiveField(9)
  final Map<String, int> topSellingProducts;

  const SellerAnalytics({
    required this.sellerId,
    required this.date,
    this.totalSales = 0.0,
    this.totalOrders = 0,
    this.totalProducts = 0,
    this.averageOrderValue = 0.0,
    this.newCustomers = 0,
    this.returningCustomers = 0,
    this.categoryWiseSales = const {},
    this.topSellingProducts = const {},
  });

  factory SellerAnalytics.fromJson(Map<String, dynamic> json) =>
      _$SellerAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$SellerAnalyticsToJson(this);

  String get formattedTotalSales => '₹${totalSales.toStringAsFixed(2)}';
  String get formattedAverageOrderValue =>
      '₹${averageOrderValue.toStringAsFixed(2)}';

  @override
  List<Object?> get props => [
    sellerId,
    date,
    totalSales,
    totalOrders,
    totalProducts,
    averageOrderValue,
    newCustomers,
    returningCustomers,
    categoryWiseSales,
    topSellingProducts,
  ];
}
