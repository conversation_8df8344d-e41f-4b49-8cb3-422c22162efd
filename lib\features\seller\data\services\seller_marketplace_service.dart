import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';

import '../../domain/models/seller_models.dart';
import '../../../marketplace/domain/models/product.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';

class SellerMarketplaceService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static const Uuid _uuid = Uuid();
  
  static const String _sellersCollection = 'sellers';
  static const String _productsCollection = 'products';
  static const String _inventoryCollection = 'inventory';
  static const String _ordersCollection = 'seller_orders';
  static const String _analyticsCollection = 'seller_analytics';
  static const String _reviewsCollection = 'seller_reviews';

  // Seller Profile Management
  static Future<SellerProfile> createSellerProfile({
    required String businessName,
    required String ownerName,
    required String email,
    required String phone,
    String? businessAddress,
    String? businessDescription,
    List<String>? categories,
  }) async {
    try {
      final currentUserId = AuthService.currentUserId;
      if (currentUserId == null) throw Exception('User not authenticated');

      final profile = SellerProfile(
        id: currentUserId,
        businessName: businessName,
        ownerName: ownerName,
        email: email,
        phone: phone,
        businessAddress: businessAddress,
        businessDescription: businessDescription,
        categories: categories ?? [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore.collection(_sellersCollection).doc(currentUserId).set(profile.toJson());

      await AnalyticsService.logEvent('seller_profile_created', {
        'seller_id': currentUserId,
        'business_name': businessName,
        'categories': categories?.join(',') ?? '',
      });

      return profile;
    } catch (e) {
      debugPrint('❌ Error creating seller profile: $e');
      throw Exception('Failed to create seller profile: ${e.toString()}');
    }
  }

  static Future<SellerProfile?> getSellerProfile({String? sellerId}) async {
    try {
      final currentUserId = sellerId ?? AuthService.currentUserId;
      if (currentUserId == null) return null;

      final doc = await _firestore.collection(_sellersCollection).doc(currentUserId).get();
      
      if (doc.exists) {
        return SellerProfile.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting seller profile: $e');
      return null;
    }
  }

  static Stream<SellerProfile?> getSellerProfileStream({String? sellerId}) {
    final currentUserId = sellerId ?? AuthService.currentUserId;
    if (currentUserId == null) return Stream.value(null);

    return _firestore
        .collection(_sellersCollection)
        .doc(currentUserId)
        .snapshots()
        .map((doc) => doc.exists ? SellerProfile.fromFirestore(doc) : null);
  }

  // Product Management
  static Future<Product> addProduct({
    required String name,
    required String description,
    required double price,
    required String category,
    required String subcategory,
    required String brand,
    List<File>? images,
    Map<String, dynamic>? specifications,
    List<String>? tags,
    int stockQuantity = 0,
  }) async {
    try {
      final currentUserId = AuthService.currentUserId;
      if (currentUserId == null) throw Exception('User not authenticated');

      final seller = await getSellerProfile();
      if (seller == null) throw Exception('Seller profile not found');

      // Upload images
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _uploadProductImages(images);
      }

      final productId = _uuid.v4();
      final product = Product(
        id: productId,
        name: name,
        description: description,
        price: price,
        originalPrice: price,
        images: imageUrls,
        category: category,
        subcategory: subcategory,
        brand: brand,
        stockQuantity: stockQuantity,
        vendorId: currentUserId,
        vendorName: seller.businessName,
        specifications: specifications ?? {},
        tags: tags ?? [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore.collection(_productsCollection).doc(productId).set(product.toJson());

      // Create inventory item
      await _createInventoryItem(product, stockQuantity);

      await AnalyticsService.logProductAdded(
        productId: productId,
        category: category,
        price: price,
      );

      return product;
    } catch (e) {
      debugPrint('❌ Error adding product: $e');
      throw Exception('Failed to add product: ${e.toString()}');
    }
  }

  static Future<void> updateProduct({
    required String productId,
    String? name,
    String? description,
    double? price,
    String? category,
    String? subcategory,
    String? brand,
    List<File>? newImages,
    Map<String, dynamic>? specifications,
    List<String>? tags,
    int? stockQuantity,
  }) async {
    try {
      final currentUserId = AuthService.currentUserId;
      if (currentUserId == null) throw Exception('User not authenticated');

      final updates = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (name != null) updates['name'] = name;
      if (description != null) updates['description'] = description;
      if (price != null) updates['price'] = price;
      if (category != null) updates['category'] = category;
      if (subcategory != null) updates['subcategory'] = subcategory;
      if (brand != null) updates['brand'] = brand;
      if (specifications != null) updates['specifications'] = specifications;
      if (tags != null) updates['tags'] = tags;
      if (stockQuantity != null) updates['stockQuantity'] = stockQuantity;

      if (newImages != null && newImages.isNotEmpty) {
        final imageUrls = await _uploadProductImages(newImages);
        updates['images'] = imageUrls;
      }

      await _firestore.collection(_productsCollection).doc(productId).update(updates);

      // Update inventory if stock quantity changed
      if (stockQuantity != null) {
        await _updateInventoryStock(productId, stockQuantity);
      }

      await AnalyticsService.logEvent('product_updated', {
        'product_id': productId,
        'seller_id': currentUserId,
      });
    } catch (e) {
      debugPrint('❌ Error updating product: $e');
      throw Exception('Failed to update product: ${e.toString()}');
    }
  }

  static Future<void> deleteProduct(String productId) async {
    try {
      final currentUserId = AuthService.currentUserId;
      if (currentUserId == null) throw Exception('User not authenticated');

      // Delete product
      await _firestore.collection(_productsCollection).doc(productId).delete();

      // Delete inventory item
      await _deleteInventoryItem(productId);

      await AnalyticsService.logEvent('product_deleted', {
        'product_id': productId,
        'seller_id': currentUserId,
      });
    } catch (e) {
      debugPrint('❌ Error deleting product: $e');
      throw Exception('Failed to delete product: ${e.toString()}');
    }
  }

  // Get seller's products
  static Stream<List<Product>> getSellerProducts({
    String? sellerId,
    String? category,
    int limit = 50,
  }) {
    final currentUserId = sellerId ?? AuthService.currentUserId;
    if (currentUserId == null) return Stream.value([]);

    Query query = _firestore
        .collection(_productsCollection)
        .where('vendorId', isEqualTo: currentUserId)
        .orderBy('createdAt', descending: true)
        .limit(limit);

    if (category != null && category.isNotEmpty) {
      query = query.where('category', isEqualTo: category);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => Product.fromJson(doc.data() as Map<String, dynamic>)).toList();
    });
  }

  // Inventory Management
  static Future<InventoryItem> _createInventoryItem(Product product, int initialStock) async {
    final currentUserId = AuthService.currentUserId!;
    
    final inventoryItem = InventoryItem(
      id: _uuid.v4(),
      productId: product.id,
      sellerId: currentUserId,
      sku: 'SKU-${product.id.substring(0, 8).toUpperCase()}',
      currentStock: initialStock,
      costPrice: product.price * 0.7, // Assume 30% margin
      sellingPrice: product.price,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _firestore.collection(_inventoryCollection).doc(inventoryItem.id).set(inventoryItem.toJson());
    return inventoryItem;
  }

  static Future<void> _updateInventoryStock(String productId, int newStock) async {
    final currentUserId = AuthService.currentUserId!;
    
    final inventoryQuery = await _firestore
        .collection(_inventoryCollection)
        .where('productId', isEqualTo: productId)
        .where('sellerId', isEqualTo: currentUserId)
        .limit(1)
        .get();

    if (inventoryQuery.docs.isNotEmpty) {
      await inventoryQuery.docs.first.reference.update({
        'currentStock': newStock,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }
  }

  static Future<void> _deleteInventoryItem(String productId) async {
    final currentUserId = AuthService.currentUserId!;
    
    final inventoryQuery = await _firestore
        .collection(_inventoryCollection)
        .where('productId', isEqualTo: productId)
        .where('sellerId', isEqualTo: currentUserId)
        .get();

    for (final doc in inventoryQuery.docs) {
      await doc.reference.delete();
    }
  }

  static Stream<List<InventoryItem>> getInventoryItems({String? sellerId}) {
    final currentUserId = sellerId ?? AuthService.currentUserId;
    if (currentUserId == null) return Stream.value([]);

    return _firestore
        .collection(_inventoryCollection)
        .where('sellerId', isEqualTo: currentUserId)
        .orderBy('updatedAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => InventoryItem.fromJson(doc.data()))
          .toList();
    });
  }

  static Future<void> updateInventoryStock({
    required String inventoryId,
    required int newStock,
    String? location,
  }) async {
    try {
      final updates = <String, dynamic>{
        'currentStock': newStock,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (location != null) updates['location'] = location;

      await _firestore.collection(_inventoryCollection).doc(inventoryId).update(updates);

      await AnalyticsService.logEvent('inventory_updated', {
        'inventory_id': inventoryId,
        'new_stock': newStock,
      });
    } catch (e) {
      debugPrint('❌ Error updating inventory: $e');
      throw Exception('Failed to update inventory: ${e.toString()}');
    }
  }

  // Order Management
  static Stream<List<SellerOrder>> getSellerOrders({
    String? sellerId,
    OrderStatus? status,
    int limit = 50,
  }) {
    final currentUserId = sellerId ?? AuthService.currentUserId;
    if (currentUserId == null) return Stream.value([]);

    Query query = _firestore
        .collection(_ordersCollection)
        .where('sellerId', isEqualTo: currentUserId)
        .orderBy('orderDate', descending: true)
        .limit(limit);

    if (status != null) {
      query = query.where('status', isEqualTo: status.toString());
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => SellerOrder.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    });
  }

  static Future<void> updateOrderStatus({
    required String orderId,
    required OrderStatus newStatus,
    String? trackingNumber,
  }) async {
    try {
      final updates = <String, dynamic>{
        'status': newStatus.toString(),
      };

      if (trackingNumber != null) {
        updates['trackingNumber'] = trackingNumber;
      }

      if (newStatus == OrderStatus.shipped) {
        updates['shippedDate'] = FieldValue.serverTimestamp();
      } else if (newStatus == OrderStatus.delivered) {
        updates['deliveredDate'] = FieldValue.serverTimestamp();
      }

      await _firestore.collection(_ordersCollection).doc(orderId).update(updates);

      await AnalyticsService.logEvent('order_status_updated', {
        'order_id': orderId,
        'new_status': newStatus.toString(),
      });

      // Send notification to customer
      await _sendOrderStatusNotification(orderId, newStatus);
    } catch (e) {
      debugPrint('❌ Error updating order status: $e');
      throw Exception('Failed to update order status: ${e.toString()}');
    }
  }

  // Analytics
  static Future<SellerAnalytics> getSellerAnalytics({
    String? sellerId,
    DateTime? date,
  }) async {
    try {
      final currentUserId = sellerId ?? AuthService.currentUserId;
      if (currentUserId == null) throw Exception('User not authenticated');

      final targetDate = date ?? DateTime.now();
      final dateKey = '${targetDate.year}-${targetDate.month.toString().padLeft(2, '0')}-${targetDate.day.toString().padLeft(2, '0')}';

      final doc = await _firestore
          .collection(_analyticsCollection)
          .doc('${currentUserId}_$dateKey')
          .get();

      if (doc.exists) {
        return SellerAnalytics.fromJson(doc.data()!);
      }

      // Generate analytics if not exists
      return await _generateAnalytics(currentUserId, targetDate);
    } catch (e) {
      debugPrint('❌ Error getting analytics: $e');
      return SellerAnalytics(
        sellerId: sellerId ?? '',
        date: date ?? DateTime.now(),
      );
    }
  }

  // Helper methods
  static Future<List<String>> _uploadProductImages(List<File> images) async {
    final List<String> imageUrls = [];
    
    for (int i = 0; i < images.length; i++) {
      final file = images[i];
      final fileName = '${_uuid.v4()}_${i}.jpg';
      final ref = _storage.ref().child('products').child(fileName);
      
      await ref.putFile(file);
      final url = await ref.getDownloadURL();
      imageUrls.add(url);
    }
    
    return imageUrls;
  }

  static Future<SellerAnalytics> _generateAnalytics(String sellerId, DateTime date) async {
    // This would calculate analytics from orders, products, etc.
    // For now, return empty analytics
    final analytics = SellerAnalytics(
      sellerId: sellerId,
      date: date,
    );

    final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    await _firestore
        .collection(_analyticsCollection)
        .doc('${sellerId}_$dateKey')
        .set(analytics.toJson());

    return analytics;
  }

  static Future<void> _sendOrderStatusNotification(String orderId, OrderStatus status) async {
    // Implementation for sending notifications to customers
    await NotificationService.showLocalNotification(
      title: 'Order Update',
      body: 'Order $orderId status updated to ${status.toString()}',
      payload: orderId,
    );
  }

  // Search and filtering
  static Future<List<Product>> searchProducts({
    required String sellerId,
    String? query,
    String? category,
    double? minPrice,
    double? maxPrice,
    bool? inStock,
  }) async {
    try {
      Query queryRef = _firestore
          .collection(_productsCollection)
          .where('vendorId', isEqualTo: sellerId);

      if (category != null && category.isNotEmpty) {
        queryRef = queryRef.where('category', isEqualTo: category);
      }

      if (inStock == true) {
        queryRef = queryRef.where('inStock', isEqualTo: true);
      }

      final snapshot = await queryRef.get();
      List<Product> products = snapshot.docs
          .map((doc) => Product.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      // Apply additional filters
      if (query != null && query.isNotEmpty) {
        products = products.where((product) =>
            product.name.toLowerCase().contains(query.toLowerCase()) ||
            product.description.toLowerCase().contains(query.toLowerCase()) ||
            product.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()))
        ).toList();
      }

      if (minPrice != null) {
        products = products.where((product) => product.price >= minPrice).toList();
      }

      if (maxPrice != null) {
        products = products.where((product) => product.price <= maxPrice).toList();
      }

      return products;
    } catch (e) {
      debugPrint('❌ Error searching products: $e');
      return [];
    }
  }
}




