import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/rider/presentation/pages/splash_page.dart';
import '../../features/rider/presentation/pages/auth/login_page.dart';
import '../../features/rider/presentation/pages/auth/register_page.dart';
import '../../features/rider/presentation/pages/kyc/kyc_page.dart';
import '../../features/rider/presentation/pages/rider_dashboard_page.dart';
import '../../features/rider/presentation/pages/earnings_dashboard_page.dart';
import '../../features/rider/presentation/pages/withdrawal_page.dart';
import '../../features/rider/presentation/pages/bonus_incentives_page.dart';
import '../../features/rider/presentation/pages/orders/orders_page.dart';
import '../../features/rider/presentation/pages/profile/profile_page.dart';

// New Feature Imports
import '../../features/safety/presentation/pages/safety_dashboard_page.dart';
import '../../features/analytics/presentation/pages/analytics_dashboard_page.dart';
import '../../features/rating/presentation/pages/rating_dashboard_page.dart';

final riderRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    routes: [
      // Splash
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const RiderSplashPage(),
      ),

      // Authentication
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const RiderLoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RiderRegisterPage(),
      ),
      GoRoute(
        path: '/phone-verification',
        name: 'phone-verification',
        builder: (context, state) {
          // final phoneNumber = state.extra as String? ?? '';
          return const Placeholder(); // RiderPhoneVerificationPage will be implemented
        },
      ),

      // KYC Process
      GoRoute(
        path: '/kyc',
        name: 'kyc',
        builder: (context, state) => const RiderKYCPage(),
        routes: [
          GoRoute(
            path: 'documents',
            name: 'kyc-documents',
            builder: (context, state) =>
                const Placeholder(), // RiderDocumentUploadPage(),
          ),
          GoRoute(
            path: 'vehicle',
            name: 'kyc-vehicle',
            builder: (context, state) =>
                const Placeholder(), // RiderVehicleRegistrationPage(),
          ),
          GoRoute(
            path: 'bank',
            name: 'kyc-bank',
            builder: (context, state) =>
                const Placeholder(), // RiderBankDetailsPage(),
          ),
        ],
      ),

      // Main Dashboard
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (context, state) => const RiderDashboardPage(),
      ),

      // Earnings & Financial Management
      GoRoute(
        path: '/earnings',
        name: 'earnings',
        builder: (context, state) => const EarningsDashboardPage(),
      ),
      GoRoute(
        path: '/withdrawal',
        name: 'withdrawal',
        builder: (context, state) => const WithdrawalPage(),
      ),
      GoRoute(
        path: '/bonuses',
        name: 'bonuses',
        builder: (context, state) => const BonusIncentivesPage(),
      ),

      // Orders Management
      GoRoute(
        path: '/orders',
        name: 'orders',
        builder: (context, state) => const RiderOrdersPage(),
        routes: [
          GoRoute(
            path: 'detail/:orderId',
            name: 'order-detail',
            builder: (context, state) {
              // final orderId = state.pathParameters['orderId']!;
              return const Placeholder(); // RiderOrderDetailPage will be implemented
            },
          ),
          GoRoute(
            path: 'navigation/:orderId',
            name: 'order-navigation',
            builder: (context, state) {
              // final orderId = state.pathParameters['orderId']!;
              return const Placeholder(); // RiderNavigationPage will be implemented
            },
          ),
          GoRoute(
            path: 'history',
            name: 'order-history',
            builder: (context, state) =>
                const Placeholder(), // RiderOrderHistoryPage(),
          ),
        ],
      ),

      // Vehicle Management
      GoRoute(
        path: '/vehicle',
        name: 'vehicle',
        builder: (context, state) =>
            const Placeholder(), // RiderVehicleInfoPage(),
        routes: [
          GoRoute(
            path: 'documents',
            name: 'vehicle-documents',
            builder: (context, state) =>
                const Placeholder(), // RiderVehicleDocumentsPage(),
          ),
          GoRoute(
            path: 'maintenance',
            name: 'vehicle-maintenance',
            builder: (context, state) =>
                const Placeholder(), // RiderMaintenancePage(),
          ),
        ],
      ),

      // Insurance
      GoRoute(
        path: '/insurance',
        name: 'insurance',
        builder: (context, state) =>
            const Placeholder(), // RiderInsurancePage(),
        routes: [
          GoRoute(
            path: 'claims',
            name: 'insurance-claims',
            builder: (context, state) =>
                const Placeholder(), // RiderClaimsPage(),
          ),
        ],
      ),

      // Profile & Settings
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const RiderProfilePage(),
        routes: [
          GoRoute(
            path: 'settings',
            name: 'settings',
            builder: (context, state) =>
                const Placeholder(), // RiderSettingsPage(),
          ),
          GoRoute(
            path: 'help',
            name: 'help',
            builder: (context, state) =>
                const Placeholder(), // RiderHelpPage(),
          ),
          GoRoute(
            path: 'support',
            name: 'support',
            builder: (context, state) =>
                const Placeholder(), // RiderSupportPage(),
          ),
        ],
      ),

      // Emergency & Safety
      GoRoute(
        path: '/safety',
        name: 'safety',
        builder: (context, state) => const SafetyDashboardPage(),
      ),
      GoRoute(
        path: '/emergency',
        name: 'emergency',
        builder: (context, state) =>
            const Placeholder(), // RiderEmergencyPage(),
      ),

      // Analytics Dashboard
      GoRoute(
        path: '/analytics',
        name: 'analytics',
        builder: (context, state) =>
            const AnalyticsDashboardPage(sellerId: 'demo_rider_1'),
      ),

      // Ratings & Reviews
      GoRoute(
        path: '/ratings',
        name: 'ratings',
        builder: (context, state) => const RatingDashboardPage(),
      ),

      // Training & Resources
      GoRoute(
        path: '/training',
        name: 'training',
        builder: (context, state) =>
            const Placeholder(), // RiderTrainingPage(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Page not found: ${state.uri}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helper methods for Rider app
class RiderNavigation {
  static void goToSplash(BuildContext context) => context.go('/splash');
  static void goToLogin(BuildContext context) => context.go('/login');
  static void goToRegister(BuildContext context) => context.go('/register');
  static void goToPhoneVerification(BuildContext context, String phoneNumber) =>
      context.go('/phone-verification', extra: phoneNumber);
  static void goToKYC(BuildContext context) => context.go('/kyc');
  static void goToDashboard(BuildContext context) => context.go('/dashboard');
  static void goToOrders(BuildContext context) => context.go('/orders');
  static void goToOrderDetail(BuildContext context, String orderId) =>
      context.go('/orders/detail/$orderId');
  static void goToOrderNavigation(BuildContext context, String orderId) =>
      context.go('/orders/navigation/$orderId');
  static void goToVehicle(BuildContext context) => context.go('/vehicle');
  static void goToInsurance(BuildContext context) => context.go('/insurance');
  static void goToProfile(BuildContext context) => context.go('/profile');
  static void goToEmergency(BuildContext context) => context.go('/emergency');
  static void goToEarnings(BuildContext context) => context.go('/earnings');
  static void goToWithdrawal(BuildContext context) => context.go('/withdrawal');
  static void goToBonuses(BuildContext context) => context.go('/bonuses');
  static void goToSafety(BuildContext context) => context.go('/safety');
  static void goToAnalytics(BuildContext context) => context.go('/analytics');
  static void goToRatings(BuildContext context) => context.go('/ratings');
}
