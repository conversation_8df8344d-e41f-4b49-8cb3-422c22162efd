@echo off
echo Creating production keystore for Projek marketplace app...
echo.

REM Create keystore directory if it doesn't exist
if not exist "android\app" mkdir "android\app"

REM Generate production keystore
keytool -genkey -v -keystore android\app\projek-release-key.jks -keyalg RSA -keysize 2048 -validity 10000 -alias projek-key -dname "CN=Projek Marketplace, OU=Engineering, O=Projek Inc, L=Jakarta, ST=Jakarta, C=ID" -storepass projek123 -keypass projek123

echo.
echo Keystore created successfully!
echo Location: android\app\projek-release-key.jks
echo.
echo Now building signed release APK...
flutter build apk --release

echo.
echo Signed APK build completed!
echo Check build\app\outputs\flutter-apk\ for signed APKs
pause
