import 'package:flutter_riverpod/flutter_riverpod.dart';

// Provider to track active delivery status
final riderActiveDeliveryProvider = StateProvider<bool>((ref) => false);

// Provider to track current delivery details
final currentDeliveryProvider = StateProvider<String?>((ref) => null);

// Provider to track delivery status
final deliveryStatusProvider = StateProvider<DeliveryStatus>(
  (ref) => DeliveryStatus.idle,
);

enum DeliveryStatus {
  idle,
  assigned,
  pickedUp,
  inTransit,
  delivered,
  cancelled,
}
