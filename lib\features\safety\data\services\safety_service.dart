// import 'dart:math'; // Commented out for now
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../domain/models/safety_models.dart';

class SafetyService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const Uuid _uuid = Uuid();

  static const String _emergencyContactsCollection = 'emergency_contacts';
  static const String _sosAlertsCollection = 'sos_alerts';
  static const String _safetyCheckInsCollection = 'safety_checkins';
  static const String _incidentReportsCollection = 'incident_reports';
  static const String _safetySettingsCollection = 'safety_settings';

  // Emergency Contacts Management
  static Future<EmergencyContact> addEmergencyContact({
    required String name,
    required String phoneNumber,
    required String relationship,
    bool isPrimary = false,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final contactId = _uuid.v4();
      final now = DateTime.now();

      // If this is set as primary, unset other primary contacts
      if (isPrimary) {
        await _unsetPrimaryContacts(currentUser.uid);
      }

      final contact = EmergencyContact(
        id: contactId,
        userId: currentUser.uid,
        name: name,
        phoneNumber: phoneNumber,
        relationship: relationship,
        isPrimary: isPrimary,
        createdAt: now,
        updatedAt: now,
      );

      await _firestore
          .collection(_emergencyContactsCollection)
          .doc(contactId)
          .set(contact.toJson());

      return contact;
    } catch (e) {
      debugPrint('❌ Error adding emergency contact: $e');
      throw Exception('Failed to add emergency contact: ${e.toString()}');
    }
  }

  static Future<List<EmergencyContact>> getEmergencyContacts({
    String? userId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return [];

      final snapshot = await _firestore
          .collection(_emergencyContactsCollection)
          .where('userId', isEqualTo: targetUserId)
          .where('isActive', isEqualTo: true)
          .orderBy('isPrimary', descending: true)
          .orderBy('createdAt', descending: false)
          .get();

      return snapshot.docs.map((doc) {
        return EmergencyContact.fromJson(doc.data());
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting emergency contacts: $e');
      return [];
    }
  }

  static Future<void> updateEmergencyContact({
    required String contactId,
    String? name,
    String? phoneNumber,
    String? relationship,
    bool? isPrimary,
  }) async {
    try {
      final updates = <String, dynamic>{'updatedAt': DateTime.now()};

      if (name != null) updates['name'] = name;
      if (phoneNumber != null) updates['phoneNumber'] = phoneNumber;
      if (relationship != null) updates['relationship'] = relationship;
      if (isPrimary != null) {
        updates['isPrimary'] = isPrimary;
        if (isPrimary) {
          final currentUser = _auth.currentUser;
          if (currentUser != null) {
            await _unsetPrimaryContacts(currentUser.uid, excludeId: contactId);
          }
        }
      }

      await _firestore
          .collection(_emergencyContactsCollection)
          .doc(contactId)
          .update(updates);
    } catch (e) {
      debugPrint('❌ Error updating emergency contact: $e');
      throw Exception('Failed to update emergency contact: ${e.toString()}');
    }
  }

  static Future<void> deleteEmergencyContact(String contactId) async {
    try {
      await _firestore
          .collection(_emergencyContactsCollection)
          .doc(contactId)
          .update({'isActive': false, 'updatedAt': DateTime.now()});
    } catch (e) {
      debugPrint('❌ Error deleting emergency contact: $e');
      throw Exception('Failed to delete emergency contact: ${e.toString()}');
    }
  }

  // SOS Alert System
  static Future<SOSAlert> triggerSOSAlert({
    required EmergencyType type,
    required double latitude,
    required double longitude,
    required String address,
    String? message,
    List<String> imageUrls = const [],
    List<String> audioUrls = const [],
    String? orderId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final alertId = _uuid.v4();
      final now = DateTime.now();

      final alert = SOSAlert(
        id: alertId,
        userId: currentUser.uid,
        userName: currentUser.displayName ?? 'Unknown User',
        userPhone: currentUser.phoneNumber ?? '',
        type: type,
        latitude: latitude,
        longitude: longitude,
        address: address,
        message: message,
        imageUrls: imageUrls,
        audioUrls: audioUrls,
        triggeredAt: now,
        orderId: orderId,
      );

      await _firestore
          .collection(_sosAlertsCollection)
          .doc(alertId)
          .set(alert.toJson());

      // Notify emergency contacts
      await _notifyEmergencyContacts(alert);

      // Notify admin/support team
      await _notifyAdminTeam(alert);

      return alert;
    } catch (e) {
      debugPrint('❌ Error triggering SOS alert: $e');
      throw Exception('Failed to trigger SOS alert: ${e.toString()}');
    }
  }

  static Future<void> acknowledgeSOSAlert(String alertId) async {
    try {
      await _firestore.collection(_sosAlertsCollection).doc(alertId).update({
        'acknowledgedAt': DateTime.now(),
        'status': 'acknowledged',
      });
    } catch (e) {
      debugPrint('❌ Error acknowledging SOS alert: $e');
    }
  }

  static Future<void> resolveSOSAlert(
    String alertId, {
    String? resolution,
  }) async {
    try {
      final updates = {'resolvedAt': DateTime.now(), 'status': 'resolved'};

      if (resolution != null) {
        updates['metadata.resolution'] = resolution;
      }

      await _firestore
          .collection(_sosAlertsCollection)
          .doc(alertId)
          .update(updates);
    } catch (e) {
      debugPrint('❌ Error resolving SOS alert: $e');
    }
  }

  static Stream<List<SOSAlert>> getUserSOSAlerts({String? userId}) {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return Stream.value([]);

      return _firestore
          .collection(_sosAlertsCollection)
          .where('userId', isEqualTo: targetUserId)
          .orderBy('triggeredAt', descending: true)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs.map((doc) {
              return SOSAlert.fromJson(doc.data());
            }).toList();
          });
    } catch (e) {
      debugPrint('❌ Error getting user SOS alerts: $e');
      return Stream.value([]);
    }
  }

  // Safety Check-in System
  static Future<SafetyCheckIn> createSafetyCheckIn({
    required double latitude,
    required double longitude,
    required String address,
    required DateTime scheduledAt,
    String? orderId,
    bool isAutomatic = false,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final checkInId = _uuid.v4();
      final now = DateTime.now();

      final checkIn = SafetyCheckIn(
        id: checkInId,
        userId: currentUser.uid,
        orderId: orderId,
        latitude: latitude,
        longitude: longitude,
        address: address,
        scheduledAt: scheduledAt,
        isAutomatic: isAutomatic,
        createdAt: now,
      );

      await _firestore
          .collection(_safetyCheckInsCollection)
          .doc(checkInId)
          .set(checkIn.toJson());

      return checkIn;
    } catch (e) {
      debugPrint('❌ Error creating safety check-in: $e');
      throw Exception('Failed to create safety check-in: ${e.toString()}');
    }
  }

  static Future<void> completeSafetyCheckIn({
    required String checkInId,
    String? note,
  }) async {
    try {
      await _firestore
          .collection(_safetyCheckInsCollection)
          .doc(checkInId)
          .update({
            'completedAt': DateTime.now(),
            'status': SafetyCheckStatus.completed.toString(),
            'note': note,
          });
    } catch (e) {
      debugPrint('❌ Error completing safety check-in: $e');
      throw Exception('Failed to complete safety check-in: ${e.toString()}');
    }
  }

  static Future<void> markCheckInOverdue(String checkInId) async {
    try {
      await _firestore
          .collection(_safetyCheckInsCollection)
          .doc(checkInId)
          .update({
            'status': SafetyCheckStatus.overdue.toString(),
            'reminderCount': FieldValue.increment(1),
          });
    } catch (e) {
      debugPrint('❌ Error marking check-in overdue: $e');
    }
  }

  static Stream<List<SafetyCheckIn>> getUserSafetyCheckIns({
    String? userId,
    int limit = 50,
  }) {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return Stream.value([]);

      return _firestore
          .collection(_safetyCheckInsCollection)
          .where('userId', isEqualTo: targetUserId)
          .orderBy('scheduledAt', descending: true)
          .limit(limit)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs.map((doc) {
              return SafetyCheckIn.fromJson(doc.data());
            }).toList();
          });
    } catch (e) {
      debugPrint('❌ Error getting user safety check-ins: $e');
      return Stream.value([]);
    }
  }

  // Incident Reporting System
  static Future<IncidentReport> reportIncident({
    required EmergencyType incidentType,
    required String title,
    required String description,
    double? latitude,
    double? longitude,
    String? address,
    List<String> imageUrls = const [],
    List<String> videoUrls = const [],
    String? orderId,
    String? involvedUserId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final reportId = _uuid.v4();
      final now = DateTime.now();

      final report = IncidentReport(
        id: reportId,
        reporterId: currentUser.uid,
        reporterName: currentUser.displayName ?? 'Unknown User',
        reporterType: 'rider', // This would be determined based on user role
        incidentType: incidentType,
        title: title,
        description: description,
        latitude: latitude,
        longitude: longitude,
        address: address,
        imageUrls: imageUrls,
        videoUrls: videoUrls,
        orderId: orderId,
        involvedUserId: involvedUserId,
        reportedAt: now,
      );

      await _firestore
          .collection(_incidentReportsCollection)
          .doc(reportId)
          .set(report.toJson());

      // Notify admin team
      await _notifyAdminAboutIncident(report);

      return report;
    } catch (e) {
      debugPrint('❌ Error reporting incident: $e');
      throw Exception('Failed to report incident: ${e.toString()}');
    }
  }

  static Future<void> updateIncidentStatus({
    required String reportId,
    required IncidentStatus status,
    String? assignedTo,
    String? resolution,
  }) async {
    try {
      final updates = <String, dynamic>{'status': status.toString()};

      if (status == IncidentStatus.acknowledged) {
        updates['acknowledgedAt'] = DateTime.now();
      } else if (status == IncidentStatus.resolved ||
          status == IncidentStatus.closed) {
        updates['resolvedAt'] = DateTime.now();
      }

      if (assignedTo != null) updates['assignedTo'] = assignedTo;
      if (resolution != null) updates['resolution'] = resolution;

      await _firestore
          .collection(_incidentReportsCollection)
          .doc(reportId)
          .update(updates);
    } catch (e) {
      debugPrint('❌ Error updating incident status: $e');
    }
  }

  static Stream<List<IncidentReport>> getUserIncidentReports({String? userId}) {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return Stream.value([]);

      return _firestore
          .collection(_incidentReportsCollection)
          .where('reporterId', isEqualTo: targetUserId)
          .orderBy('reportedAt', descending: true)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs.map((doc) {
              return IncidentReport.fromJson(doc.data());
            }).toList();
          });
    } catch (e) {
      debugPrint('❌ Error getting user incident reports: $e');
      return Stream.value([]);
    }
  }

  // Safety Settings Management
  static Future<SafetySettings> getSafetySettings({String? userId}) async {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) throw Exception('User not authenticated');

      final doc = await _firestore
          .collection(_safetySettingsCollection)
          .doc(targetUserId)
          .get();

      if (doc.exists) {
        return SafetySettings.fromJson(doc.data()!);
      } else {
        // Create default settings
        return await _createDefaultSafetySettings(targetUserId);
      }
    } catch (e) {
      debugPrint('❌ Error getting safety settings: $e');
      throw Exception('Failed to get safety settings: ${e.toString()}');
    }
  }

  static Future<void> updateSafetySettings({
    bool? sosEnabled,
    bool? autoCheckInEnabled,
    int? checkInIntervalMinutes,
    bool? locationSharingEnabled,
    bool? emergencyContactsNotification,
    bool? adminNotification,
    List<String>? trustedContacts,
    Map<String, bool>? notificationPreferences,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final updates = <String, dynamic>{'updatedAt': DateTime.now()};

      if (sosEnabled != null) updates['sosEnabled'] = sosEnabled;
      if (autoCheckInEnabled != null)
        updates['autoCheckInEnabled'] = autoCheckInEnabled;
      if (checkInIntervalMinutes != null)
        updates['checkInIntervalMinutes'] = checkInIntervalMinutes;
      if (locationSharingEnabled != null)
        updates['locationSharingEnabled'] = locationSharingEnabled;
      if (emergencyContactsNotification != null)
        updates['emergencyContactsNotification'] =
            emergencyContactsNotification;
      if (adminNotification != null)
        updates['adminNotification'] = adminNotification;
      if (trustedContacts != null) updates['trustedContacts'] = trustedContacts;
      if (notificationPreferences != null)
        updates['notificationPreferences'] = notificationPreferences;

      await _firestore
          .collection(_safetySettingsCollection)
          .doc(currentUser.uid)
          .update(updates);
    } catch (e) {
      debugPrint('❌ Error updating safety settings: $e');
      throw Exception('Failed to update safety settings: ${e.toString()}');
    }
  }

  // Private helper methods
  static Future<void> _unsetPrimaryContacts(
    String userId, {
    String? excludeId,
  }) async {
    try {
      final snapshot = await _firestore
          .collection(_emergencyContactsCollection)
          .where('userId', isEqualTo: userId)
          .where('isPrimary', isEqualTo: true)
          .get();

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        if (excludeId == null || doc.id != excludeId) {
          batch.update(doc.reference, {
            'isPrimary': false,
            'updatedAt': DateTime.now(),
          });
        }
      }
      await batch.commit();
    } catch (e) {
      debugPrint('❌ Error unsetting primary contacts: $e');
    }
  }

  static Future<void> _notifyEmergencyContacts(SOSAlert alert) async {
    try {
      final contacts = await getEmergencyContacts(userId: alert.userId);

      // In a real app, this would send SMS/calls to emergency contacts
      debugPrint(
        '📱 Notifying ${contacts.length} emergency contacts about SOS alert',
      );

      // Update alert with notified contacts
      final notifiedContacts = contacts.map((c) => c.id).toList();
      await _firestore.collection(_sosAlertsCollection).doc(alert.id).update({
        'notifiedContacts': notifiedContacts,
      });
    } catch (e) {
      debugPrint('❌ Error notifying emergency contacts: $e');
    }
  }

  static Future<void> _notifyAdminTeam(SOSAlert alert) async {
    try {
      // In a real app, this would notify the admin/support team
      debugPrint('🚨 Notifying admin team about SOS alert: ${alert.id}');
    } catch (e) {
      debugPrint('❌ Error notifying admin team: $e');
    }
  }

  static Future<void> _notifyAdminAboutIncident(IncidentReport report) async {
    try {
      // In a real app, this would notify the admin team about the incident
      debugPrint('📋 Notifying admin team about incident report: ${report.id}');
    } catch (e) {
      debugPrint('❌ Error notifying admin about incident: $e');
    }
  }

  static Future<SafetySettings> _createDefaultSafetySettings(
    String userId,
  ) async {
    try {
      final settingsId = userId;
      final now = DateTime.now();

      final settings = SafetySettings(
        id: settingsId,
        userId: userId,
        updatedAt: now,
      );

      await _firestore
          .collection(_safetySettingsCollection)
          .doc(settingsId)
          .set(settings.toJson());

      return settings;
    } catch (e) {
      debugPrint('❌ Error creating default safety settings: $e');
      throw Exception(
        'Failed to create default safety settings: ${e.toString()}',
      );
    }
  }
}
