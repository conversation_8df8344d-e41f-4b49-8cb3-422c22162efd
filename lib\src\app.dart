import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../core/config/app_config.dart';
import '../core/theme/user_theme.dart';
import '../core/theme/rider_theme.dart';
import '../core/theme/seller_theme.dart';
import '../core/utils/app_router.dart';
import '../core/router/rider_router.dart';
import '../core/router/seller_router.dart';

class ProjekApp extends ConsumerWidget {
  const ProjekApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appType = AppConfig.currentAppType;

    return MaterialApp.router(
      title: _getAppTitle(appType),
      theme: _getTheme(appType),
      darkTheme: _getDarkTheme(appType),
      themeMode: ThemeMode.system,
      routerConfig: _getRouter(appType, ref),
      debugShowCheckedModeBanner: false,
    );
  }

  String _getAppTitle(AppType appType) {
    switch (appType) {
      case AppType.user:
        return 'Projek - Super App';
      case AppType.rider:
        return 'Projek Rider';
      case AppType.seller:
        return 'Projek Business';
    }
  }

  ThemeData _getTheme(AppType appType) {
    switch (appType) {
      case AppType.user:
        return UserTheme.lightTheme;
      case AppType.rider:
        return RiderTheme.lightTheme;
      case AppType.seller:
        return SellerTheme.lightTheme;
    }
  }

  ThemeData _getDarkTheme(AppType appType) {
    switch (appType) {
      case AppType.user:
        return UserTheme.darkTheme;
      case AppType.rider:
        return RiderTheme.darkTheme;
      case AppType.seller:
        return SellerTheme.darkTheme;
    }
  }

  GoRouter _getRouter(AppType appType, WidgetRef ref) {
    switch (appType) {
      case AppType.user:
        return ref.read(routerProvider);
      case AppType.rider:
        return ref.read(riderRouterProvider);
      case AppType.seller:
        return ref.read(sellerRouterProvider);
    }
  }
}

// Theme Definitions
class UserTheme {
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.light,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.white,
      foregroundColor: Colors.black87,
      elevation: 0,
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      type: BottomNavigationBarType.fixed,
    ),
  );

  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.dark,
    ),
  );
}

class RiderTheme {
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.deepPurple,
      brightness: Brightness.light,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.deepPurple,
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      selectedItemColor: Colors.deepPurple,
      unselectedItemColor: Colors.grey,
      type: BottomNavigationBarType.fixed,
    ),
  );

  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.deepPurple,
      brightness: Brightness.dark,
    ),
  );
}

class SellerTheme {
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.teal,
      brightness: Brightness.light,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.teal,
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      selectedItemColor: Colors.teal,
      unselectedItemColor: Colors.grey,
      type: BottomNavigationBarType.fixed,
    ),
  );

  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.teal,
      brightness: Brightness.dark,
    ),
  );
}
