import 'package:flutter/material.dart';
import '../../../tracking/domain/models/real_time_tracking_models.dart';
import '../../../tracking/domain/models/unified_order.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class OrderProgressWidget extends StatelessWidget {
  final RealTimeTracking tracking;

  const OrderProgressWidget({
    super.key,
    required this.tracking,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Progress',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          // Progress Steps
          _buildProgressSteps(),
        ],
      ),
    );
  }

  Widget _buildProgressSteps() {
    final steps = _getProgressSteps();
    
    return Column(
      children: List.generate(steps.length, (index) {
        final step = steps[index];
        final isLast = index == steps.length - 1;
        
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Step Indicator
            Column(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: step.isCompleted 
                        ? AppColors.success 
                        : step.isActive 
                            ? AppColors.primaryBlue 
                            : AppColors.textSecondary.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    step.isCompleted 
                        ? Icons.check 
                        : step.icon,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 40,
                    color: step.isCompleted 
                        ? AppColors.success 
                        : AppColors.textSecondary.withValues(alpha: 0.3),
                  ),
              ],
            ),
            
            const SizedBox(width: 12),
            
            // Step Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      step.title,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: step.isActive 
                            ? AppColors.primaryBlue 
                            : step.isCompleted 
                                ? AppColors.success 
                                : AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      step.description,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    if (step.timestamp != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        _formatTime(step.timestamp!),
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.info,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  List<ProgressStep> _getProgressSteps() {
    final status = tracking.status;
    
    return [
      ProgressStep(
        title: 'Order Confirmed',
        description: 'Your order has been confirmed and is being prepared',
        icon: Icons.check_circle,
        isCompleted: true,
        isActive: false,
        timestamp: tracking.startedAt,
      ),
      ProgressStep(
        title: 'Rider Assigned',
        description: 'A rider has been assigned to your order',
        icon: Icons.person,
        isCompleted: status != TrackingStatus.inactive,
        isActive: status == TrackingStatus.active,
        timestamp: status != TrackingStatus.inactive ? tracking.startedAt : null,
      ),
      ProgressStep(
        title: 'On the Way',
        description: 'Your rider is on the way to deliver your order',
        icon: Icons.motorcycle,
        isCompleted: status == TrackingStatus.completed,
        isActive: status == TrackingStatus.active,
        timestamp: status == TrackingStatus.active ? tracking.startedAt : null,
      ),
      ProgressStep(
        title: 'Delivered',
        description: 'Your order has been delivered successfully',
        icon: Icons.home,
        isCompleted: status == TrackingStatus.completed,
        isActive: false,
        timestamp: tracking.completedAt,
      ),
    ];
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }
}

class ProgressStep {
  final String title;
  final String description;
  final IconData icon;
  final bool isCompleted;
  final bool isActive;
  final DateTime? timestamp;

  const ProgressStep({
    required this.title,
    required this.description,
    required this.icon,
    required this.isCompleted,
    required this.isActive,
    this.timestamp,
  });
}
