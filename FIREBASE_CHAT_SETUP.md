# Firebase Chat App Setup Guide

## Overview
This is a complete Firebase-integrated Flutter chat application with Google Sign-In authentication, user profile management, and real-time messaging capabilities.

## Features Implemented

### ✅ Authentication & User Management
- **Google Sign-In**: Users authenticate using their Google account
- **Profile Completion**: New users complete their profile with name and date of birth
- **User Profile Storage**: Profile data stored in Firestore `users/{uid}` collection
- **Authentication State Management**: Automatic login state checking and persistence

### ✅ Firestore Collections Structure
```
users/{uid}
├── name: string (required)
├── dateOfBirth: timestamp (required)
├── email: string (from Google Auth)
└── createdAt: timestamp (server timestamp)

messages/{messageId}
├── text: string (message content)
├── userEmail: string (sender's email)
├── userId: string (sender's UID)
└── timestamp: timestamp (server timestamp)
```

### ✅ Real-time Chat Interface
- **Real-time Messages**: StreamBuilder listens to Firestore messages collection
- **Message Display**: Shows sender email, message text, and formatted timestamp
- **Message Ordering**: Messages displayed in descending order by timestamp
- **User Identification**: Different styling for current user vs other users
- **Send Messages**: Text input with send button for new messages
- **Auto-scroll**: Automatically scrolls to bottom when new messages arrive

### ✅ Technical Implementation
- **Single StatefulWidget**: All functionality contained in one widget with conditional rendering
- **State Management**: Proper loading states, error handling, and UI state management
- **Material Design**: Clean, responsive UI using Material Design 3
- **Error Handling**: Comprehensive error handling for auth and Firestore operations

## Firebase Setup Required

### 1. Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project named "projek-marketplace-demo" (or update `firebase_options.dart`)
3. Enable Google Analytics (optional)

### 2. Enable Authentication
1. In Firebase Console, go to Authentication > Sign-in method
2. Enable "Google" sign-in provider
3. Add your app's SHA-1 fingerprint for Android

### 3. Create Firestore Database
1. Go to Firestore Database
2. Create database in production mode
3. Deploy the security rules from `firestore.rules` file

### 4. Configure Android App
1. Add Android app to Firebase project
2. Download `google-services.json`
3. Place it in `android/app/` directory
4. Update `android/app/build.gradle` with Firebase dependencies

### 5. Update Firebase Configuration
Replace the demo values in `lib/firebase_options.dart` with your actual Firebase project configuration.

## How to Use the App

### 1. First Launch
- App shows welcome screen with "Sign in with Google" button
- User taps button to authenticate with Google account

### 2. Profile Completion (New Users)
- After successful Google sign-in, app checks if user profile exists
- If no profile found, shows profile completion form
- User enters full name and selects date of birth
- Taps "Save Profile" to store data in Firestore

### 3. Chat Interface (Existing Users)
- Users with complete profiles go directly to chat screen
- Real-time message list shows all conversations
- Text input at bottom for typing new messages
- Send button (or Enter key) sends message to Firestore
- Messages appear instantly for all connected users

### 4. Sign Out
- Users can sign out from profile completion screen or chat screen
- Returns to welcome screen for re-authentication

## Security Features

### Firestore Security Rules
- Users can only read/write their own profile data
- All authenticated users can read messages
- Users can only create messages with their own UID and email
- Messages cannot be updated or deleted (append-only chat)

### Authentication Security
- Google Sign-In provides secure OAuth authentication
- Firebase Auth handles token management and session persistence
- User identity verified through Firebase Auth tokens

## File Structure
```
lib/
├── main.dart                 # Complete chat app implementation
├── firebase_options.dart     # Firebase configuration
firestore.rules              # Firestore security rules
FIREBASE_CHAT_SETUP.md       # This setup guide
```

## Dependencies Added
- `firebase_core: ^3.6.0` - Firebase core functionality
- `firebase_auth: ^5.3.1` - Firebase authentication
- `cloud_firestore: ^5.4.4` - Firestore database
- `google_sign_in: ^6.2.1` - Google Sign-In integration
- `intl: ^0.19.0` - Date formatting utilities

## Testing the App
1. Run `flutter pub get` to install dependencies
2. Set up Firebase project and configuration
3. Run the app on a device or emulator
4. Test Google Sign-In flow
5. Complete profile for new users
6. Send messages and verify real-time updates
7. Test with multiple users to see real-time chat functionality

## Troubleshooting
- Ensure `google-services.json` is in correct location
- Verify SHA-1 fingerprint is added to Firebase project
- Check Firestore security rules are deployed
- Ensure Google Sign-In is enabled in Firebase Console
- Verify internet connection for real-time features
