import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'rider_earnings.g.dart';

@HiveType(typeId: 26)
@JsonSerializable()
class RiderEarnings {
  @HiveField(0)
  final String riderId;

  @HiveField(1)
  final double totalEarnings;

  @HiveField(2)
  final double todayEarnings;

  @HiveField(3)
  final double weeklyEarnings;

  @HiveField(4)
  final double monthlyEarnings;

  @HiveField(5)
  final double availableBalance;

  @HiveField(6)
  final double pendingAmount;

  @HiveField(7)
  final double totalWithdrawn;

  @HiveField(8)
  final List<EarningsTransaction> transactions;

  @HiveField(9)
  final List<CommissionBreakdown> commissionHistory;

  @HiveField(10)
  final TaxInfo taxInfo;

  @HiveField(11)
  final DateTime lastUpdated;

  @HiveField(12)
  final EarningsStats stats;

  const RiderEarnings({
    required this.riderId,
    required this.totalEarnings,
    required this.todayEarnings,
    required this.weeklyEarnings,
    required this.monthlyEarnings,
    required this.availableBalance,
    required this.pendingAmount,
    required this.totalWithdrawn,
    required this.transactions,
    required this.commissionHistory,
    required this.taxInfo,
    required this.lastUpdated,
    required this.stats,
  });

  factory RiderEarnings.fromJson(Map<String, dynamic> json) =>
      _$RiderEarningsFromJson(json);

  Map<String, dynamic> toJson() => _$RiderEarningsToJson(this);

  RiderEarnings copyWith({
    String? riderId,
    double? totalEarnings,
    double? todayEarnings,
    double? weeklyEarnings,
    double? monthlyEarnings,
    double? availableBalance,
    double? pendingAmount,
    double? totalWithdrawn,
    List<EarningsTransaction>? transactions,
    List<CommissionBreakdown>? commissionHistory,
    TaxInfo? taxInfo,
    DateTime? lastUpdated,
    EarningsStats? stats,
  }) {
    return RiderEarnings(
      riderId: riderId ?? this.riderId,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      todayEarnings: todayEarnings ?? this.todayEarnings,
      weeklyEarnings: weeklyEarnings ?? this.weeklyEarnings,
      monthlyEarnings: monthlyEarnings ?? this.monthlyEarnings,
      availableBalance: availableBalance ?? this.availableBalance,
      pendingAmount: pendingAmount ?? this.pendingAmount,
      totalWithdrawn: totalWithdrawn ?? this.totalWithdrawn,
      transactions: transactions ?? this.transactions,
      commissionHistory: commissionHistory ?? this.commissionHistory,
      taxInfo: taxInfo ?? this.taxInfo,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      stats: stats ?? this.stats,
    );
  }
}

@HiveType(typeId: 27)
@JsonSerializable()
class EarningsTransaction {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String rideId;

  @HiveField(2)
  final TransactionType type;

  @HiveField(3)
  final double amount;

  @HiveField(4)
  final double commission;

  @HiveField(5)
  final double netAmount;

  @HiveField(6)
  final DateTime timestamp;

  @HiveField(7)
  final String description;

  @HiveField(8)
  final TransactionStatus status;

  @HiveField(9)
  final Map<String, dynamic> metadata;

  const EarningsTransaction({
    required this.id,
    required this.rideId,
    required this.type,
    required this.amount,
    required this.commission,
    required this.netAmount,
    required this.timestamp,
    required this.description,
    required this.status,
    required this.metadata,
  });

  factory EarningsTransaction.fromJson(Map<String, dynamic> json) =>
      _$EarningsTransactionFromJson(json);

  Map<String, dynamic> toJson() => _$EarningsTransactionToJson(this);
}

@HiveType(typeId: 28)
enum TransactionType {
  @HiveField(0)
  rideEarning,
  @HiveField(1)
  bonus,
  @HiveField(2)
  incentive,
  @HiveField(3)
  penalty,
  @HiveField(4)
  withdrawal,
  @HiveField(5)
  refund,
  @HiveField(6)
  adjustment,
}

@HiveType(typeId: 29)
enum TransactionStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  completed,
  @HiveField(2)
  failed,
  @HiveField(3)
  cancelled,
}

@HiveType(typeId: 30)
@JsonSerializable()
class CommissionBreakdown {
  @HiveField(0)
  final String rideId;

  @HiveField(1)
  final double baseFare;

  @HiveField(2)
  final double distanceCharge;

  @HiveField(3)
  final double timeCharge;

  @HiveField(4)
  final double surgeMultiplier;

  @HiveField(5)
  final double totalFare;

  @HiveField(6)
  final double platformCommission;

  @HiveField(7)
  final double riderEarning;

  @HiveField(8)
  final double bonus;

  @HiveField(9)
  final double incentive;

  @HiveField(10)
  final double penalty;

  @HiveField(11)
  final double netEarning;

  @HiveField(12)
  final DateTime timestamp;

  @HiveField(13)
  final String rideType;

  const CommissionBreakdown({
    required this.rideId,
    required this.baseFare,
    required this.distanceCharge,
    required this.timeCharge,
    required this.surgeMultiplier,
    required this.totalFare,
    required this.platformCommission,
    required this.riderEarning,
    required this.bonus,
    required this.incentive,
    required this.penalty,
    required this.netEarning,
    required this.timestamp,
    required this.rideType,
  });

  factory CommissionBreakdown.fromJson(Map<String, dynamic> json) =>
      _$CommissionBreakdownFromJson(json);

  Map<String, dynamic> toJson() => _$CommissionBreakdownToJson(this);
}

@HiveType(typeId: 31)
@JsonSerializable()
class TaxInfo {
  @HiveField(0)
  final double totalTaxableIncome;

  @HiveField(1)
  final double tdsDeducted;

  @HiveField(2)
  final double gstCollected;

  @HiveField(3)
  final String panNumber;

  @HiveField(4)
  final bool isTaxExempt;

  @HiveField(5)
  final List<TaxDocument> taxDocuments;

  const TaxInfo({
    required this.totalTaxableIncome,
    required this.tdsDeducted,
    required this.gstCollected,
    required this.panNumber,
    required this.isTaxExempt,
    required this.taxDocuments,
  });

  factory TaxInfo.fromJson(Map<String, dynamic> json) =>
      _$TaxInfoFromJson(json);

  Map<String, dynamic> toJson() => _$TaxInfoToJson(this);
}

@HiveType(typeId: 32)
@JsonSerializable()
class TaxDocument {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String type;

  @HiveField(2)
  final String url;

  @HiveField(3)
  final DateTime generatedDate;

  @HiveField(4)
  final String financialYear;

  const TaxDocument({
    required this.id,
    required this.type,
    required this.url,
    required this.generatedDate,
    required this.financialYear,
  });

  factory TaxDocument.fromJson(Map<String, dynamic> json) =>
      _$TaxDocumentFromJson(json);

  Map<String, dynamic> toJson() => _$TaxDocumentToJson(this);
}

@HiveType(typeId: 33)
@JsonSerializable()
class EarningsStats {
  @HiveField(0)
  final double averageEarningPerRide;

  @HiveField(1)
  final double averageEarningPerHour;

  @HiveField(2)
  final double averageEarningPerDay;

  @HiveField(3)
  final int totalRidesCompleted;

  @HiveField(4)
  final double totalDistanceCovered;

  @HiveField(5)
  final double totalTimeOnline;

  @HiveField(6)
  final double peakHourEarnings;

  @HiveField(7)
  final double offPeakEarnings;

  @HiveField(8)
  final Map<String, double> earningsByRideType;

  @HiveField(9)
  final Map<String, double> earningsByDay;

  const EarningsStats({
    required this.averageEarningPerRide,
    required this.averageEarningPerHour,
    required this.averageEarningPerDay,
    required this.totalRidesCompleted,
    required this.totalDistanceCovered,
    required this.totalTimeOnline,
    required this.peakHourEarnings,
    required this.offPeakEarnings,
    required this.earningsByRideType,
    required this.earningsByDay,
  });

  factory EarningsStats.fromJson(Map<String, dynamic> json) =>
      _$EarningsStatsFromJson(json);

  Map<String, dynamic> toJson() => _$EarningsStatsToJson(this);
}
