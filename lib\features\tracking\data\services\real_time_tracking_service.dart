import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../../domain/models/real_time_tracking_models.dart';
import '../../domain/models/unified_order.dart';
import '../../../../core/services/location_service.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/services/multi_app_integration_service.dart';

class RealTimeTrackingService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _trackingCollection = 'real_time_tracking';
  static const String _routesCollection = 'optimized_routes';

  static Timer? _locationUpdateTimer;
  static StreamSubscription<Position>? _positionSubscription;
  static String? _activeOrderId;
  static String? _activeRiderId;

  // Google Maps API configuration
  static const String _googleMapsApiKey =
      'YOUR_GOOGLE_MAPS_API_KEY'; // Replace with your API key
  static const String _directionsApiUrl =
      'https://maps.googleapis.com/maps/api/directions/json';
  static const String _distanceMatrixApiUrl =
      'https://maps.googleapis.com/maps/api/distancematrix/json';

  // Start real-time tracking for a rider
  static Future<RealTimeTracking> startTracking({
    required String orderId,
    required String riderId,
  }) async {
    try {
      // Stop any existing tracking
      await stopTracking();

      final tracking = RealTimeTracking(
        orderId: orderId,
        riderId: riderId,
        status: TrackingStatus.active,
        startedAt: DateTime.now(),
      );

      // Save to Firestore
      await _firestore
          .collection(_trackingCollection)
          .doc(orderId)
          .set(tracking.toJson());

      // Set active tracking
      _activeOrderId = orderId;
      _activeRiderId = riderId;

      // Start location updates
      await _startLocationUpdates();

      // Calculate initial route
      await _calculateAndUpdateRoute(orderId);

      await AnalyticsService.logEvent('tracking_started', {
        'order_id': orderId,
        'rider_id': riderId,
      });

      return tracking;
    } catch (e) {
      debugPrint('❌ Error starting tracking: $e');
      throw Exception('Failed to start tracking: ${e.toString()}');
    }
  }

  // Stop real-time tracking
  static Future<void> stopTracking() async {
    try {
      if (_activeOrderId != null) {
        // Update tracking status
        await _firestore
            .collection(_trackingCollection)
            .doc(_activeOrderId)
            .update({
              'status': TrackingStatus.completed.toString(),
              'completedAt': FieldValue.serverTimestamp(),
            });

        await AnalyticsService.logEvent('tracking_stopped', {
          'order_id': _activeOrderId,
          'rider_id': _activeRiderId,
        });
      }

      // Stop location updates
      await _stopLocationUpdates();

      // Clear active tracking
      _activeOrderId = null;
      _activeRiderId = null;
    } catch (e) {
      debugPrint('❌ Error stopping tracking: $e');
    }
  }

  // Update rider location
  static Future<void> updateRiderLocation({
    required String orderId,
    required RealTimeLocation location,
  }) async {
    try {
      final trackingRef = _firestore
          .collection(_trackingCollection)
          .doc(orderId);

      // Update current location and add to history
      await trackingRef.update({
        'currentLocation': location.toJson(),
        'locationHistory': FieldValue.arrayUnion([location.toJson()]),
        'totalDistanceTraveled': FieldValue.increment(
          _calculateDistanceIncrement(orderId, location),
        ),
        'totalTimeElapsed': FieldValue.increment(30), // 30 seconds interval
      });

      // Recalculate ETA
      await _updateETA(orderId, location);

      // Send real-time update to user and seller
      await _broadcastLocationUpdate(orderId, location);

      // Check for delivery proximity
      await _checkDeliveryProximity(orderId, location);
    } catch (e) {
      debugPrint('❌ Error updating rider location: $e');
    }
  }

  // Calculate optimized route
  static Future<OptimizedRoute> calculateOptimizedRoute({
    required RealTimeLocation origin,
    required RealTimeLocation destination,
    RouteOptimizationType optimizationType = RouteOptimizationType.balanced,
  }) async {
    try {
      final url = Uri.parse(_directionsApiUrl).replace(
        queryParameters: {
          'origin': '${origin.latitude},${origin.longitude}',
          'destination': '${destination.latitude},${destination.longitude}',
          'key': _googleMapsApiKey,
          'mode': 'driving',
          'traffic_model': 'best_guess',
          'departure_time': 'now',
          'optimize': _getOptimizationParameter(optimizationType),
        },
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['status'] == 'OK' && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final leg = route['legs'][0];

          // Parse route points
          final points = _parseRoutePoints(
            route['overview_polyline']['points'],
          );

          final optimizedRoute = OptimizedRoute(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            points: points,
            totalDistance: leg['distance']['value'].toDouble(),
            totalDuration: leg['duration']['value'],
            optimizationType: optimizationType,
            calculatedAt: DateTime.now(),
            expiresAt: DateTime.now().add(const Duration(hours: 1)),
            trafficData: {
              'duration_in_traffic':
                  leg['duration_in_traffic']?['value'] ??
                  leg['duration']['value'],
              'traffic_conditions': _analyzeTrafficConditions(leg),
            },
            warnings: _extractWarnings(route),
          );

          // Cache the route
          await _cacheRoute(optimizedRoute);

          return optimizedRoute;
        } else {
          throw Exception('No route found: ${data['status']}');
        }
      } else {
        throw Exception('Failed to fetch route: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Error calculating route: $e');
      throw Exception('Failed to calculate route: ${e.toString()}');
    }
  }

  // Calculate ETA
  static Future<ETACalculation> calculateETA({
    required String orderId,
    required RealTimeLocation currentLocation,
    required RealTimeLocation destination,
  }) async {
    try {
      // Get current traffic conditions
      final url = Uri.parse(_distanceMatrixApiUrl).replace(
        queryParameters: {
          'origins': '${currentLocation.latitude},${currentLocation.longitude}',
          'destinations': '${destination.latitude},${destination.longitude}',
          'key': _googleMapsApiKey,
          'mode': 'driving',
          'traffic_model': 'best_guess',
          'departure_time': 'now',
        },
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['status'] == 'OK') {
          final element = data['rows'][0]['elements'][0];

          if (element['status'] == 'OK') {
            final distance = element['distance']['value'].toDouble();
            final duration =
                element['duration_in_traffic']?['value'] ??
                element['duration']['value'];

            final eta = ETACalculation(
              estimatedArrival: DateTime.now().add(Duration(seconds: duration)),
              confidenceLevel: _calculateConfidenceLevel(
                currentLocation,
                distance,
              ),
              remainingDistance: distance,
              remainingTime: duration,
              averageSpeed: distance / duration,
              calculatedAt: DateTime.now(),
              factors: _getETAFactors(element),
              trafficImpact: {
                'normal_duration': element['duration']['value'],
                'traffic_duration': duration,
                'delay_factor': duration / element['duration']['value'],
              },
            );

            // Update tracking with new ETA
            await _firestore
                .collection(_trackingCollection)
                .doc(orderId)
                .update({'currentETA': eta.toJson()});

            return eta;
          }
        }
      }

      // Fallback calculation
      return _calculateFallbackETA(currentLocation, destination);
    } catch (e) {
      debugPrint('❌ Error calculating ETA: $e');
      return _calculateFallbackETA(currentLocation, destination);
    }
  }

  // Get real-time tracking stream
  static Stream<RealTimeTracking> getTrackingStream(String orderId) {
    return _firestore
        .collection(_trackingCollection)
        .doc(orderId)
        .snapshots()
        .map((doc) {
          if (doc.exists) {
            return RealTimeTracking.fromFirestore(doc);
          } else {
            throw Exception('Tracking not found');
          }
        });
  }

  // Get tracking by order ID
  static Future<RealTimeTracking?> getTracking(String orderId) async {
    try {
      final doc = await _firestore
          .collection(_trackingCollection)
          .doc(orderId)
          .get();

      if (doc.exists) {
        return RealTimeTracking.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting tracking: $e');
      return null;
    }
  }

  // Private helper methods
  static Future<void> _startLocationUpdates() async {
    try {
      // Check permissions
      final hasPermission = await LocationService.checkPermission();
      if (hasPermission != LocationPermission.always &&
          hasPermission != LocationPermission.whileInUse) {
        throw Exception('Location permission not granted');
      }

      // Start position stream
      _positionSubscription =
          Geolocator.getPositionStream(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.high,
              distanceFilter: 10, // Update every 10 meters
            ),
          ).listen((position) {
            if (_activeOrderId != null) {
              final location = RealTimeLocation(
                latitude: position.latitude,
                longitude: position.longitude,
                accuracy: position.accuracy,
                altitude: position.altitude,
                speed: position.speed,
                heading: position.heading,
                timestamp: DateTime.now(),
              );

              updateRiderLocation(orderId: _activeOrderId!, location: location);
            }
          });

      // Start periodic updates timer
      _locationUpdateTimer = Timer.periodic(const Duration(seconds: 30), (
        timer,
      ) {
        if (_activeOrderId != null) {
          _performPeriodicUpdates(_activeOrderId!);
        }
      });
    } catch (e) {
      debugPrint('❌ Error starting location updates: $e');
    }
  }

  static Future<void> _stopLocationUpdates() async {
    _positionSubscription?.cancel();
    _positionSubscription = null;

    _locationUpdateTimer?.cancel();
    _locationUpdateTimer = null;
  }

  static Future<void> _calculateAndUpdateRoute(String orderId) async {
    try {
      // Get order details to find pickup and delivery locations
      final orderDoc = await _firestore
          .collection('unified_orders')
          .doc(orderId)
          .get();
      if (!orderDoc.exists) return;

      final order = UnifiedOrder.fromJson(orderDoc.data()!);

      // Get current rider location
      final currentPosition = await LocationService.getCurrentLocation();
      if (currentPosition == null) return;

      final currentLocation = RealTimeLocation(
        latitude: currentPosition.latitude,
        longitude: currentPosition.longitude,
        accuracy: currentPosition.accuracy,
        timestamp: DateTime.now(),
      );

      // Calculate route to pickup location first, then to delivery
      final pickupLocation = RealTimeLocation(
        latitude: order.pickupLocation.latitude,
        longitude: order.pickupLocation.longitude,
        accuracy: 0,
        timestamp: DateTime.now(),
      );

      final route = await calculateOptimizedRoute(
        origin: currentLocation,
        destination: pickupLocation,
        optimizationType: RouteOptimizationType.fastest,
      );

      // Update tracking with route
      await _firestore.collection(_trackingCollection).doc(orderId).update({
        'activeRoute': route.toJson(),
      });
    } catch (e) {
      debugPrint('❌ Error calculating route: $e');
    }
  }

  static Future<void> _updateETA(
    String orderId,
    RealTimeLocation currentLocation,
  ) async {
    try {
      // Get order destination
      final orderDoc = await _firestore
          .collection('unified_orders')
          .doc(orderId)
          .get();
      if (!orderDoc.exists) return;

      final order = UnifiedOrder.fromJson(orderDoc.data()!);

      final destination = RealTimeLocation(
        latitude: order.deliveryLocation.latitude,
        longitude: order.deliveryLocation.longitude,
        accuracy: 0,
        timestamp: DateTime.now(),
      );

      await calculateETA(
        orderId: orderId,
        currentLocation: currentLocation,
        destination: destination,
      );
    } catch (e) {
      debugPrint('❌ Error updating ETA: $e');
    }
  }

  static Future<void> _broadcastLocationUpdate(
    String orderId,
    RealTimeLocation location,
  ) async {
    try {
      // Send to user app
      await MultiAppIntegrationService.sendEvent(
        CrossAppEvent(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: CrossAppEventType.riderLocationUpdate,
          sourceApp: 'rider',
          targetApp: 'user',
          userId: '', // Will be filled from order data
          data: {
            'orderId': orderId,
            'location': location.toJson(),
            'timestamp': location.timestamp.toIso8601String(),
          },
          timestamp: DateTime.now(),
        ),
      );

      // Send to seller app
      await MultiAppIntegrationService.sendEvent(
        CrossAppEvent(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: CrossAppEventType.riderLocationUpdate,
          sourceApp: 'rider',
          targetApp: 'seller',
          userId: '', // Will be filled from order data
          data: {
            'orderId': orderId,
            'location': location.toJson(),
            'timestamp': location.timestamp.toIso8601String(),
          },
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      debugPrint('❌ Error broadcasting location update: $e');
    }
  }

  static Future<void> _checkDeliveryProximity(
    String orderId,
    RealTimeLocation location,
  ) async {
    try {
      // Get order destination
      final orderDoc = await _firestore
          .collection('unified_orders')
          .doc(orderId)
          .get();
      if (!orderDoc.exists) return;

      final order = UnifiedOrder.fromJson(orderDoc.data()!);

      final distance = Geolocator.distanceBetween(
        location.latitude,
        location.longitude,
        order.deliveryLocation.latitude,
        order.deliveryLocation.longitude,
      );

      // Check if within 100 meters of destination
      if (distance <= 100) {
        // Update order status to near destination
        await _firestore.collection('unified_orders').doc(orderId).update({
          'status': OrderStatus.nearDestination.toString(),
        });

        // Send proximity notification
        await NotificationService.showLocalNotification(
          title: 'Delivery Almost Here!',
          body: 'Your rider is within 100m of your location',
          payload: orderId,
        );
      }
    } catch (e) {
      debugPrint('❌ Error checking delivery proximity: $e');
    }
  }

  static double _calculateDistanceIncrement(
    String orderId,
    RealTimeLocation newLocation,
  ) {
    // This would calculate distance from last known location
    // For now, return a default increment
    return 10.0; // 10 meters
  }

  static List<RoutePoint> _parseRoutePoints(String encodedPolyline) {
    // Decode Google polyline and convert to RoutePoint list
    // This is a simplified implementation
    return [];
  }

  static String _getOptimizationParameter(RouteOptimizationType type) {
    switch (type) {
      case RouteOptimizationType.fastest:
        return 'time';
      case RouteOptimizationType.shortest:
        return 'distance';
      case RouteOptimizationType.balanced:
        return 'time';
      case RouteOptimizationType.fuelEfficient:
        return 'distance';
    }
  }

  static Map<String, dynamic> _analyzeTrafficConditions(
    Map<String, dynamic> leg,
  ) {
    final normalDuration = leg['duration']['value'];
    final trafficDuration =
        leg['duration_in_traffic']?['value'] ?? normalDuration;
    final delayFactor = trafficDuration / normalDuration;

    String condition;
    if (delayFactor < 1.1) {
      condition = 'light';
    } else if (delayFactor < 1.3) {
      condition = 'moderate';
    } else {
      condition = 'heavy';
    }

    return {
      'condition': condition,
      'delay_factor': delayFactor,
      'delay_minutes': ((trafficDuration - normalDuration) / 60).round(),
    };
  }

  static List<String> _extractWarnings(Map<String, dynamic> route) {
    final warnings = <String>[];
    if (route['warnings'] != null) {
      for (final warning in route['warnings']) {
        warnings.add(warning.toString());
      }
    }
    return warnings;
  }

  static int _calculateConfidenceLevel(
    RealTimeLocation location,
    double distance,
  ) {
    // Calculate confidence based on GPS accuracy and distance
    int confidence = 85; // Base confidence

    if (location.accuracy > 50) confidence -= 20;
    if (location.accuracy > 100) confidence -= 20;
    if (distance > 10000) confidence -= 10; // Long distance reduces confidence

    return math.max(confidence, 20);
  }

  static List<String> _getETAFactors(Map<String, dynamic> element) {
    final factors = <String>[];

    if (element['duration_in_traffic'] != null) {
      factors.add('traffic');
    }

    // Add more factors based on conditions
    factors.add('gps_accuracy');
    factors.add('route_optimization');

    return factors;
  }

  static ETACalculation _calculateFallbackETA(
    RealTimeLocation current,
    RealTimeLocation destination,
  ) {
    final distance = Geolocator.distanceBetween(
      current.latitude,
      current.longitude,
      destination.latitude,
      destination.longitude,
    );

    // Assume average speed of 30 km/h in city
    const averageSpeed = 30.0 / 3.6; // m/s
    final duration = (distance / averageSpeed).round();

    return ETACalculation(
      estimatedArrival: DateTime.now().add(Duration(seconds: duration)),
      confidenceLevel: 60, // Lower confidence for fallback
      remainingDistance: distance,
      remainingTime: duration,
      averageSpeed: averageSpeed,
      calculatedAt: DateTime.now(),
      factors: ['fallback_calculation'],
      trafficImpact: {},
    );
  }

  static Future<void> _cacheRoute(OptimizedRoute route) async {
    try {
      await _firestore
          .collection(_routesCollection)
          .doc(route.id)
          .set(route.toJson());
    } catch (e) {
      debugPrint('❌ Error caching route: $e');
    }
  }

  static Future<void> _performPeriodicUpdates(String orderId) async {
    try {
      // Recalculate route if needed
      final tracking = await getTracking(orderId);
      if (tracking?.activeRoute?.isExpired == true) {
        await _calculateAndUpdateRoute(orderId);
      }
    } catch (e) {
      debugPrint('❌ Error performing periodic updates: $e');
    }
  }

  // Get rider's active orders for tracking
  static Stream<List<RealTimeTracking>> getRiderActiveTracking(String riderId) {
    return _firestore
        .collection(_trackingCollection)
        .where('riderId', isEqualTo: riderId)
        .where('status', isEqualTo: TrackingStatus.active.toString())
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => RealTimeTracking.fromFirestore(doc))
              .toList();
        });
  }

  // Pause tracking (for breaks, etc.)
  static Future<void> pauseTracking(String orderId) async {
    try {
      await _firestore.collection(_trackingCollection).doc(orderId).update({
        'status': TrackingStatus.paused.toString(),
      });

      await _stopLocationUpdates();
    } catch (e) {
      debugPrint('❌ Error pausing tracking: $e');
    }
  }

  // Resume tracking
  static Future<void> resumeTracking(String orderId, String riderId) async {
    try {
      await _firestore.collection(_trackingCollection).doc(orderId).update({
        'status': TrackingStatus.active.toString(),
      });

      _activeOrderId = orderId;
      _activeRiderId = riderId;
      await _startLocationUpdates();
    } catch (e) {
      debugPrint('❌ Error resuming tracking: $e');
    }
  }

  // Mark order as delivered
  static Future<void> markDelivered(String orderId) async {
    try {
      await _firestore.collection(_trackingCollection).doc(orderId).update({
        'status': TrackingStatus.completed.toString(),
        'completedAt': FieldValue.serverTimestamp(),
      });

      // Update unified order status
      await _firestore.collection('unified_orders').doc(orderId).update({
        'status': OrderStatus.delivered.toString(),
      });

      // Stop tracking
      if (_activeOrderId == orderId) {
        await stopTracking();
      }

      // Send delivery confirmation
      await _sendDeliveryConfirmation(orderId);
    } catch (e) {
      debugPrint('❌ Error marking delivered: $e');
    }
  }

  static Future<void> _sendDeliveryConfirmation(String orderId) async {
    try {
      // Send delivery confirmation notification
      await NotificationService.showLocalNotification(
        title: 'Order Delivered!',
        body: 'Your order has been delivered successfully',
        payload: orderId,
      );
    } catch (e) {
      debugPrint('❌ Error sending delivery confirmation: $e');
    }
  }
}








