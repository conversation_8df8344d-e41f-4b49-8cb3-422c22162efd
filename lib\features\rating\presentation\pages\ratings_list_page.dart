import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../widgets/rating_widget.dart';
import '../../../rating/domain/models/rating_models.dart';
import '../../../rating/data/services/rating_service.dart';

class RatingsListPage extends ConsumerStatefulWidget {
  final String targetId;
  final String targetType;
  final String targetName;

  const RatingsListPage({
    super.key,
    required this.targetId,
    required this.targetType,
    required this.targetName,
  });

  @override
  ConsumerState<RatingsListPage> createState() => _RatingsListPageState();
}

class _RatingsListPageState extends ConsumerState<RatingsListPage> {
  RatingFilter _selectedFilter = RatingFilter.all;
  RatingSummary? _ratingSummary;

  @override
  void initState() {
    super.initState();
    _loadRatingSummary();
  }

  Future<void> _loadRatingSummary() async {
    final summary = await RatingService.getRatingSummary(
      widget.targetId,
      widget.targetType,
    );
    if (mounted) {
      setState(() {
        _ratingSummary = summary;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.targetName} Reviews'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton<RatingFilter>(
            icon: const Icon(Icons.filter_list),
            onSelected: (filter) {
              setState(() {
                _selectedFilter = filter;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: RatingFilter.all,
                child: Text('All Reviews'),
              ),
              const PopupMenuItem(
                value: RatingFilter.fiveStar,
                child: Text('5 Stars'),
              ),
              const PopupMenuItem(
                value: RatingFilter.fourStar,
                child: Text('4 Stars'),
              ),
              const PopupMenuItem(
                value: RatingFilter.threeStar,
                child: Text('3 Stars'),
              ),
              const PopupMenuItem(
                value: RatingFilter.twoStar,
                child: Text('2 Stars'),
              ),
              const PopupMenuItem(
                value: RatingFilter.oneStar,
                child: Text('1 Star'),
              ),
              const PopupMenuItem(
                value: RatingFilter.withReviews,
                child: Text('With Reviews'),
              ),
              const PopupMenuItem(
                value: RatingFilter.withImages,
                child: Text('With Photos'),
              ),
              const PopupMenuItem(
                value: RatingFilter.verified,
                child: Text('Verified Only'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Rating Summary
          if (_ratingSummary != null) ...[
            Container(
              color: AppColors.primaryBlue,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: RatingSummaryCard(
                    averageRating: _ratingSummary!.averageRating,
                    totalRatings: _ratingSummary!.totalRatings,
                    ratingDistribution: _ratingSummary!.ratingDistribution,
                    topTags: _ratingSummary!.topTags,
                  ),
                ),
              ),
            ),
          ],

          // Filter indicator
          if (_selectedFilter != RatingFilter.all)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: AppColors.primaryBlue.withOpacity(0.1),
              child: Row(
                children: [
                  Text(
                    'Filtered by: ${_getFilterText(_selectedFilter)}',
                    style: TextStyle(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _selectedFilter = RatingFilter.all;
                      });
                    },
                    child: const Text('Clear Filter'),
                  ),
                ],
              ),
            ),

          // Ratings List
          Expanded(
            child: StreamBuilder<List<Rating>>(
              stream: RatingService.getRatings(
                targetId: widget.targetId,
                filter: _selectedFilter,
              ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppColors.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading reviews',
                          style: AppTextStyles.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          snapshot.error.toString(),
                          style: TextStyle(color: AppColors.textSecondary),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {});
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                final ratings = snapshot.data ?? [];

                if (ratings.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.rate_review_outlined,
                          size: 64,
                          color: AppColors.grey400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _selectedFilter == RatingFilter.all
                              ? 'No reviews yet'
                              : 'No reviews match your filter',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _selectedFilter == RatingFilter.all
                              ? 'Be the first to leave a review!'
                              : 'Try changing your filter to see more reviews.',
                          style: TextStyle(color: AppColors.textSecondary),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: ratings.length,
                  itemBuilder: (context, index) {
                    final rating = ratings[index];
                    return RatingListItem(
                      userName: rating.userName,
                      userAvatar: rating.userAvatar,
                      rating: rating.rating,
                      review: rating.review,
                      tags: rating.tags,
                      images: rating.images,
                      createdAt: rating.createdAt,
                      helpfulCount: rating.helpfulCount,
                      isVerified: rating.isVerified,
                      onHelpfulTap: () => _markHelpful(rating.id),
                      onReportTap: () => _reportRating(rating.id),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getFilterText(RatingFilter filter) {
    switch (filter) {
      case RatingFilter.fiveStar:
        return '5 Stars';
      case RatingFilter.fourStar:
        return '4 Stars';
      case RatingFilter.threeStar:
        return '3 Stars';
      case RatingFilter.twoStar:
        return '2 Stars';
      case RatingFilter.oneStar:
        return '1 Star';
      case RatingFilter.withReviews:
        return 'With Reviews';
      case RatingFilter.withImages:
        return 'With Photos';
      case RatingFilter.verified:
        return 'Verified Only';
      case RatingFilter.all:
        return 'All Reviews';
    }
  }

  Future<void> _markHelpful(String ratingId) async {
    try {
      await RatingService.markRatingHelpful(ratingId);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Marked as helpful!'),
          duration: Duration(seconds: 1),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _reportRating(String ratingId) async {
    final reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Review'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Why are you reporting this review?'),
            const SizedBox(height: 16),
            ...['Inappropriate content', 'Spam', 'Fake review', 'Other'].map(
              (reason) => ListTile(
                title: Text(reason),
                onTap: () => Navigator.of(context).pop(reason),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (reason != null) {
      try {
        await RatingService.reportRating(ratingId, reason);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Review reported. Thank you for your feedback.'),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error reporting review: ${e.toString()}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }
}
