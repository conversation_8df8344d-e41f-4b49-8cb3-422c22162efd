import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Service to handle navigation between different parts of the super app
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  /// Navigate to rider app functionality
  static void navigateToRiderApp(BuildContext context) {
    try {
      // Navigate to the cab booking page
      context.push('/bookings/cab');

      // Show confirmation message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🚗 Opening Ride Booking...'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // Fallback: Show error message if navigation fails
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Unable to open ride booking: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () => navigateToRiderApp(context),
          ),
        ),
      );
    }
  }

  /// Navigate to seller app functionality
  static void navigateToSellerApp(BuildContext context, {String? productId}) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🛍️ Opening Seller App...'),
        backgroundColor: Colors.orange,
      ),
    );

    // TODO: Implement actual seller app navigation
    // This could navigate to seller dashboard or specific product
  }

  /// Navigate to food ordering
  static void navigateToFoodOrdering(BuildContext context) {
    // Navigate to categories filtered for food
    context.push('/categories/filtered?type=food&title=Food%20Delivery');
  }

  /// Navigate to games section
  static void navigateToGames(BuildContext context) {
    context.push('/games/spin-wheel');
  }

  /// Navigate to chat system
  static void navigateToChat(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('💬 Opening Chat...'),
        backgroundColor: Colors.blue,
      ),
    );

    // TODO: Navigate to chat list or create new chat
    // context.push('/chat');
  }

  /// Navigate to specific service booking
  static void navigateToService(BuildContext context, String serviceName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🔧 Opening $serviceName Service...'),
        backgroundColor: Colors.purple,
      ),
    );

    // TODO: Navigate to specific service booking page
    // context.push('/services/${serviceName.toLowerCase()}');
  }

  /// Navigate to marketplace category
  static void navigateToCategory(BuildContext context, String category) {
    context.push(
      '/categories/filtered?type=${category.toLowerCase()}&title=$category',
    );
  }

  /// Navigate to spin wheel game
  static void navigateToSpinGame(BuildContext context) {
    context.push('/games/spin-wheel');
  }

  /// Navigate to rewards system
  static void navigateToRewards(BuildContext context) {
    context.push('/loyalty');
  }

  /// Navigate to wallet
  static void navigateToWallet(BuildContext context) {
    context.push('/wallet');
  }

  /// Navigate to add money page
  static void navigateToAddMoney(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('💰 Opening Add Money...'),
        backgroundColor: Colors.green,
      ),
    );

    // TODO: Navigate to add money page
    // context.push('/wallet/add-money');
  }

  /// Navigate to send money page
  static void navigateToSendMoney(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('📤 Opening Send Money...'),
        backgroundColor: Colors.blue,
      ),
    );

    // TODO: Navigate to send money page
    // context.push('/wallet/send-money');
  }

  /// Show feature coming soon message
  static void showComingSoon(BuildContext context, String featureName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$featureName is coming soon!'),
        backgroundColor: Colors.grey[600],
        action: SnackBarAction(label: 'OK', onPressed: () {}),
      ),
    );
  }

  /// Navigate to marketplace home
  static void navigateToMarketplace(BuildContext context) {
    context.push('/categories');
  }

  /// Navigate to profile
  static void navigateToProfile(BuildContext context) {
    context.push('/profile');
  }

  /// Navigate to notifications
  static void navigateToNotifications(BuildContext context) {
    context.push('/notifications');
  }

  /// Navigate to advanced search
  static void navigateToAdvancedSearch(BuildContext context) {
    context.push('/search');
  }

  /// Navigate to safety dashboard
  static void navigateToSafety(BuildContext context) {
    context.push('/safety');
  }

  /// Navigate to analytics dashboard
  static void navigateToAnalytics(BuildContext context) {
    context.push('/analytics');
  }

  /// Navigate to ratings dashboard
  static void navigateToRatings(BuildContext context) {
    context.push('/ratings');
  }

  /// Navigate to advanced booking
  static void navigateToAdvancedBooking(BuildContext context) {
    context.push('/advanced-booking');
  }

  /// Navigate to help center
  static void navigateToHelpCenter(BuildContext context) {
    context.push('/enhanced-help-center');
  }
}
