import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/models/rating_models.dart';

class RatingDashboardPage extends ConsumerStatefulWidget {
  const RatingDashboardPage({super.key});

  @override
  ConsumerState<RatingDashboardPage> createState() =>
      _RatingDashboardPageState();
}

class _RatingDashboardPageState extends ConsumerState<RatingDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Rating> _myRatings = [];
  List<Rating> _receivedRatings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadRatingData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRatingData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // For demo purposes, create demo data inline
      _myRatings = _createDemoUserRatings();
      _receivedRatings = _createDemoReceivedRatings();
    } catch (e) {
      debugPrint('Error loading rating data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ratings & Reviews'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'My Ratings'),
            Tab(text: 'Received'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildMyRatingsTab(),
                _buildReceivedRatingsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _submitNewRating,
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.add, color: Colors.white),
        tooltip: 'Submit Rating',
      ),
    );
  }

  Widget _buildOverviewTab() {
    final averageGiven = _myRatings.isNotEmpty
        ? _myRatings.map((r) => r.rating).reduce((a, b) => a + b) /
              _myRatings.length
        : 0.0;

    final averageReceived = _receivedRatings.isNotEmpty
        ? _receivedRatings.map((r) => r.rating).reduce((a, b) => a + b) /
              _receivedRatings.length
        : 0.0;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Rating Summary Cards
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Ratings Given',
                  _myRatings.length.toString(),
                  averageGiven.toStringAsFixed(1),
                  Icons.star_rate,
                  AppColors.warning,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'Ratings Received',
                  _receivedRatings.length.toString(),
                  averageReceived.toStringAsFixed(1),
                  Icons.star,
                  AppColors.success,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Recent Activity
          Text('Recent Activity', style: AppTextStyles.headlineMedium),
          const SizedBox(height: 16),

          // Recent Ratings Given
          if (_myRatings.isNotEmpty) ...[
            Text('Recently Given', style: AppTextStyles.headlineSmall),
            const SizedBox(height: 8),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _myRatings.take(3).length,
              itemBuilder: (context, index) {
                final rating = _myRatings[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: _buildRatingCard(rating, showTarget: true),
                );
              },
            ),

            const SizedBox(height: 24),
          ],

          // Recent Ratings Received
          if (_receivedRatings.isNotEmpty) ...[
            Text('Recently Received', style: AppTextStyles.headlineSmall),
            const SizedBox(height: 8),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _receivedRatings.take(3).length,
              itemBuilder: (context, index) {
                final rating = _receivedRatings[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: _buildRatingCard(rating, showTarget: false),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMyRatingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'My Ratings (${_myRatings.length})',
                style: AppTextStyles.headlineMedium,
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _submitNewRating,
                icon: const Icon(Icons.add),
                label: const Text('Rate'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (_myRatings.isEmpty) ...[
            _buildEmptyState(
              'No Ratings Given',
              'You haven\'t rated any sellers or riders yet',
              Icons.star_outline,
            ),
          ] else ...[
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _myRatings.length,
              itemBuilder: (context, index) {
                final rating = _myRatings[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: _buildRatingCard(rating, showTarget: true),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildReceivedRatingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Received Ratings (${_receivedRatings.length})',
            style: AppTextStyles.headlineMedium,
          ),

          const SizedBox(height: 16),

          if (_receivedRatings.isEmpty) ...[
            _buildEmptyState(
              'No Ratings Received',
              'You haven\'t received any ratings yet',
              Icons.star_border,
            ),
          ] else ...[
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _receivedRatings.length,
              itemBuilder: (context, index) {
                final rating = _receivedRatings[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: _buildRatingCard(rating, showTarget: false),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String count,
    String average,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              count,
              style: AppTextStyles.headlineLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.star, color: AppColors.warning, size: 16),
                const SizedBox(width: 4),
                Text(
                  '$average avg',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(icon, size: 64, color: AppColors.grey400),
            const SizedBox(height: 16),
            Text(
              title,
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _submitNewRating() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rating submission form would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _viewRatingDetails(Rating rating) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing rating details for ${rating.targetType}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _editRating(Rating rating) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Editing rating for ${rating.targetType}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _replyToRating(Rating rating) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Replying to rating from ${rating.userName}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  List<Rating> _createDemoUserRatings() {
    final now = DateTime.now();
    return [
      Rating(
        id: 'rating_1',
        userId: 'demo_user_1',
        targetId: 'seller_1',
        targetType: 'seller',
        orderId: 'ORD12345',
        rating: 4.5,
        review: 'Great service! Fast delivery and good quality products.',
        tags: ['Fast Delivery', 'Quality Products'],
        images: [],
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 2)),
        isVerified: true,
        helpfulCount: 5,
        userName: 'Demo User',
      ),
      Rating(
        id: 'rating_2',
        userId: 'demo_user_1',
        targetId: 'rider_1',
        targetType: 'rider',
        orderId: 'ORD12344',
        rating: 5.0,
        review: 'Excellent rider! Very polite and delivered on time.',
        tags: ['Polite', 'On Time'],
        images: [],
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
        isVerified: true,
        helpfulCount: 3,
        userName: 'Demo User',
      ),
    ];
  }

  List<Rating> _createDemoReceivedRatings() {
    final now = DateTime.now();
    return [
      Rating(
        id: 'received_1',
        userId: 'customer_1',
        targetId: 'demo_user_1',
        targetType: 'seller',
        orderId: 'ORD12350',
        rating: 4.7,
        review: 'Excellent seller! Products were exactly as described.',
        tags: ['Quality Products', 'Honest Seller'],
        images: [],
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
        isVerified: true,
        helpfulCount: 8,
        userName: 'Sarah Johnson',
      ),
    ];
  }

  Widget _buildRatingCard(Rating rating, {required bool showTarget}) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primaryBlue,
          child: Text(
            rating.rating.toStringAsFixed(1),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          showTarget
              ? '${rating.targetType} (${rating.targetId})'
              : rating.userName,
          style: AppTextStyles.bodyLarge.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              rating.review ?? 'No review provided',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: List.generate(5, (index) {
                return Icon(
                  index < rating.rating ? Icons.star : Icons.star_border,
                  size: 16,
                  color: AppColors.warning,
                );
              }),
            ),
          ],
        ),
        onTap: () => _viewRatingDetails(rating),
      ),
    );
  }
}
