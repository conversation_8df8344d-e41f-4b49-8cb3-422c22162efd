import '../../domain/models/analytics_models.dart';

class DemoAnalyticsData {
  // Demo overview data for dashboard
  static Map<String, dynamic> getDemoOverviewData(String sellerId) {
    return {
      'totalRevenue': 45230.0,
      'totalOrders': 156,
      'averageRating': 4.6,
      'fulfillmentRate': 94.5,
    };
  }

  // Demo revenue trend data
  static List<ChartDataPoint> getDemoRevenueData() {
    return [
      ChartDataPoint(label: 'Mon', value: 1200.0, date: DateTime.now().subtract(const Duration(days: 6))),
      ChartDataPoint(label: 'Tue', value: 1450.0, date: DateTime.now().subtract(const Duration(days: 5))),
      ChartDataPoint(label: 'Wed', value: 1100.0, date: DateTime.now().subtract(const Duration(days: 4))),
      ChartDataPoint(label: 'Thu', value: 1800.0, date: DateTime.now().subtract(const Duration(days: 3))),
      ChartDataPoint(label: 'Fri', value: 2100.0, date: DateTime.now().subtract(const Duration(days: 2))),
      ChartDataPoint(label: 'Sat', value: 2400.0, date: DateTime.now().subtract(const Duration(days: 1))),
      ChartDataPoint(label: 'Sun', value: 1950.0, date: DateTime.now()),
    ];
  }

  // Demo category distribution data
  static List<ChartDataPoint> getDemoCategoryData() {
    return [
      const ChartDataPoint(label: 'Food & Dining', value: 35.0, category: 'food'),
      const ChartDataPoint(label: 'Groceries', value: 28.0, category: 'grocery'),
      const ChartDataPoint(label: 'Electronics', value: 20.0, category: 'electronics'),
      const ChartDataPoint(label: 'Fashion', value: 12.0, category: 'fashion'),
      const ChartDataPoint(label: 'Others', value: 5.0, category: 'others'),
    ];
  }

  // Demo sales data by category
  static List<ChartDataPoint> getDemoSalesData() {
    return [
      const ChartDataPoint(label: 'Food', value: 15800.0, category: 'food'),
      const ChartDataPoint(label: 'Grocery', value: 12650.0, category: 'grocery'),
      const ChartDataPoint(label: 'Electronics', value: 9200.0, category: 'electronics'),
      const ChartDataPoint(label: 'Fashion', value: 5430.0, category: 'fashion'),
      const ChartDataPoint(label: 'Home', value: 2150.0, category: 'home'),
    ];
  }

  // Demo hourly sales pattern
  static List<ChartDataPoint> getDemoHourlyData() {
    return [
      const ChartDataPoint(label: '9AM', value: 120.0),
      const ChartDataPoint(label: '10AM', value: 180.0),
      const ChartDataPoint(label: '11AM', value: 250.0),
      const ChartDataPoint(label: '12PM', value: 420.0),
      const ChartDataPoint(label: '1PM', value: 380.0),
      const ChartDataPoint(label: '2PM', value: 290.0),
      const ChartDataPoint(label: '3PM', value: 210.0),
      const ChartDataPoint(label: '4PM', value: 160.0),
      const ChartDataPoint(label: '5PM', value: 200.0),
      const ChartDataPoint(label: '6PM', value: 280.0),
      const ChartDataPoint(label: '7PM', value: 350.0),
      const ChartDataPoint(label: '8PM', value: 450.0),
      const ChartDataPoint(label: '9PM', value: 380.0),
      const ChartDataPoint(label: '10PM', value: 220.0),
    ];
  }

  // Demo rating distribution
  static List<ChartDataPoint> getDemoRatingData() {
    return [
      const ChartDataPoint(label: '5 Star', value: 68.0),
      const ChartDataPoint(label: '4 Star', value: 22.0),
      const ChartDataPoint(label: '3 Star', value: 7.0),
      const ChartDataPoint(label: '2 Star', value: 2.0),
      const ChartDataPoint(label: '1 Star', value: 1.0),
    ];
  }

  // Demo demographics data
  static List<ChartDataPoint> getDemoDemographicsData() {
    return [
      const ChartDataPoint(label: '18-25', value: 32.0),
      const ChartDataPoint(label: '26-35', value: 45.0),
      const ChartDataPoint(label: '36-45', value: 18.0),
      const ChartDataPoint(label: '46+', value: 5.0),
    ];
  }

  // Demo sales analytics
  static SalesAnalytics getDemoSalesAnalytics(String sellerId) {
    return SalesAnalytics(
      sellerId: sellerId,
      date: DateTime.now(),
      totalRevenue: 45230.0,
      totalOrders: 156,
      averageOrderValue: 290.0,
      newCustomers: 42,
      returningCustomers: 114,
      conversionRate: 3.2,
      categoryRevenue: {
        'food': 15800.0,
        'grocery': 12650.0,
        'electronics': 9200.0,
        'fashion': 5430.0,
        'home': 2150.0,
      },
      categoryOrders: {
        'food': 54,
        'grocery': 44,
        'electronics': 32,
        'fashion': 19,
        'home': 7,
      },
    );
  }

  // Demo performance metrics
  static PerformanceMetrics getDemoPerformanceMetrics(String sellerId) {
    return PerformanceMetrics(
      sellerId: sellerId,
      date: DateTime.now(),
      averageRating: 4.6,
      totalReviews: 234,
      responseTime: 12.0, // minutes
      fulfillmentRate: 94.5,
      onTimeDeliveryRate: 92.8,
      totalViews: 5420,
      totalClicks: 1240,
      clickThroughRate: 22.9,
      topProducts: {
        'product_1': 28,
        'product_2': 22,
        'product_3': 18,
        'product_4': 15,
        'product_5': 12,
      },
    );
  }

  // Demo customer insights
  static CustomerInsights getDemoCustomerInsights(String sellerId) {
    return CustomerInsights(
      sellerId: sellerId,
      date: DateTime.now(),
      ageGroups: {
        '18-25': 320,
        '26-35': 450,
        '36-45': 180,
        '46+': 50,
      },
      genderDistribution: {
        'male': 520,
        'female': 480,
      },
      locationDistribution: {
        'Mumbai': 340,
        'Delhi': 280,
        'Bangalore': 220,
        'Chennai': 160,
      },
      orderFrequency: {
        1: 200, // 200 customers order 1 time per month
        2: 150, // 150 customers order 2 times per month
        3: 100,
        4: 80,
        5: 50,
      },
      customerRetentionRate: 68.5,
      customerLifetimeValue: 2450.0,
      topCustomerSegments: [
        'Regular Buyers',
        'Premium Customers',
        'Bulk Buyers',
        'Occasional Shoppers',
      ],
    );
  }

  // Demo revenue breakdown
  static RevenueBreakdown getDemoRevenueBreakdown(String sellerId) {
    return RevenueBreakdown(
      sellerId: sellerId,
      date: DateTime.now(),
      grossRevenue: 45230.0,
      platformFees: 2261.5, // 5%
      deliveryCharges: 1580.0,
      taxes: 4070.7, // 9%
      discounts: 2260.0,
      netRevenue: 38450.0,
      hourlyRevenue: {
        '09:00': 1200.0,
        '10:00': 1800.0,
        '11:00': 2500.0,
        '12:00': 4200.0,
        '13:00': 3800.0,
        '14:00': 2900.0,
        '15:00': 2100.0,
        '16:00': 1600.0,
        '17:00': 2000.0,
        '18:00': 2800.0,
        '19:00': 3500.0,
        '20:00': 4500.0,
        '21:00': 3800.0,
        '22:00': 2200.0,
      },
      dailyRevenue: {
        'Monday': 6200.0,
        'Tuesday': 7100.0,
        'Wednesday': 5800.0,
        'Thursday': 8200.0,
        'Friday': 9100.0,
        'Saturday': 10500.0,
        'Sunday': 8330.0,
      },
    );
  }

  // Demo competitor analysis
  static CompetitorAnalysis getDemoCompetitorAnalysis(String sellerId) {
    return CompetitorAnalysis(
      sellerId: sellerId,
      date: DateTime.now(),
      marketShare: 12.5,
      marketRanking: 3,
      averageCompetitorRating: 4.2,
      averageCompetitorPrice: 285.0,
      categoryMarketShare: {
        'food': 15.2,
        'grocery': 8.9,
        'electronics': 18.5,
        'fashion': 6.8,
      },
      competitorStrengths: [
        'Faster delivery times',
        'Lower pricing in some categories',
        'Better customer service response',
      ],
      improvementOpportunities: [
        'Expand product variety',
        'Improve packaging quality',
        'Enhance mobile app experience',
        'Offer more payment options',
      ],
    );
  }

  // Generate trend data for charts
  static TrendData generateDemoTrendData(String metric) {
    final dataPoints = getDemoRevenueData();
    
    // Calculate simple trend
    final latest = dataPoints.last.value;
    final previous = dataPoints[dataPoints.length - 2].value;
    final trendPercentage = ((latest - previous) / previous) * 100;
    
    return TrendData(
      dataPoints: dataPoints,
      trendPercentage: trendPercentage.abs(),
      isPositiveTrend: trendPercentage >= 0,
      trendDescription: trendPercentage >= 0 
          ? '$metric increased by ${trendPercentage.toStringAsFixed(1)}%'
          : '$metric decreased by ${trendPercentage.abs().toStringAsFixed(1)}%',
    );
  }

  // Get demo analytics summary
  static Map<String, dynamic> getDemoAnalyticsSummary(String sellerId) {
    return {
      'totalRevenue': 45230.0,
      'totalOrders': 156,
      'averageRating': 4.6,
      'fulfillmentRate': 94.5,
      'newCustomers': 42,
      'returningCustomers': 114,
      'conversionRate': 3.2,
      'responseTime': 12.0,
      'onTimeDeliveryRate': 92.8,
      'customerRetentionRate': 68.5,
      'period': 'Last 30 days',
    };
  }
}
