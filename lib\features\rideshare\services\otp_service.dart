import 'dart:math';

class OTPService {
  static final Map<String, OTPData> _activeOTPs = {};
  static const int _otpLength = 6;
  static const int _expirationMinutes = 15;
  static const int _maxAttempts = 3;

  /// Generate a new OTP for a ride
  static OTPData generateOTP(String rideId) {
    final random = Random();
    final otp = List.generate(_otpLength, (index) => random.nextInt(10)).join();
    final expirationTime = DateTime.now().add(
      const Duration(minutes: _expirationMinutes),
    );

    final otpData = OTPData(
      otp: otp,
      rideId: rideId,
      generatedAt: DateTime.now(),
      expiresAt: expirationTime,
      attempts: 0,
      isUsed: false,
    );

    _activeOTPs[rideId] = otpData;
    return otpData;
  }

  /// Verify an OTP for a ride
  static OTPVerificationResult verifyOTP(String rideId, String inputOTP) {
    final otpData = _activeOTPs[rideId];

    if (otpData == null) {
      return OTPVerificationResult(
        isValid: false,
        error: 'No OTP found for this ride',
        errorType: OTPErrorType.notFound,
      );
    }

    if (otpData.isUsed) {
      return OTPVerificationResult(
        isValid: false,
        error: 'OTP has already been used',
        errorType: OTPErrorType.alreadyUsed,
      );
    }

    if (DateTime.now().isAfter(otpData.expiresAt)) {
      return OTPVerificationResult(
        isValid: false,
        error: 'OTP has expired',
        errorType: OTPErrorType.expired,
      );
    }

    if (otpData.attempts >= _maxAttempts) {
      return OTPVerificationResult(
        isValid: false,
        error: 'Maximum attempts exceeded',
        errorType: OTPErrorType.maxAttemptsExceeded,
      );
    }

    // Increment attempts
    otpData.attempts++;
    _activeOTPs[rideId] = otpData;

    if (otpData.otp == inputOTP) {
      // Mark as used
      otpData.isUsed = true;
      _activeOTPs[rideId] = otpData;

      return OTPVerificationResult(isValid: true, error: null, errorType: null);
    } else {
      final remainingAttempts = _maxAttempts - otpData.attempts;
      return OTPVerificationResult(
        isValid: false,
        error: 'Invalid OTP. $remainingAttempts attempts remaining',
        errorType: OTPErrorType.invalid,
        remainingAttempts: remainingAttempts,
      );
    }
  }

  /// Get OTP data for a ride
  static OTPData? getOTPData(String rideId) {
    return _activeOTPs[rideId];
  }

  /// Check if OTP is expired
  static bool isOTPExpired(String rideId) {
    final otpData = _activeOTPs[rideId];
    if (otpData == null) return true;
    return DateTime.now().isAfter(otpData.expiresAt);
  }

  /// Get remaining time for OTP expiration
  static Duration? getRemainingTime(String rideId) {
    final otpData = _activeOTPs[rideId];
    if (otpData == null) return null;

    final now = DateTime.now();
    if (now.isAfter(otpData.expiresAt)) return Duration.zero;

    return otpData.expiresAt.difference(now);
  }

  /// Regenerate OTP for a ride (in case of expiration or max attempts)
  static OTPData regenerateOTP(String rideId) {
    return generateOTP(rideId);
  }

  /// Clear OTP data for a ride (after completion)
  static void clearOTP(String rideId) {
    _activeOTPs.remove(rideId);
  }

  /// Get all active OTPs (for debugging/admin purposes)
  static Map<String, OTPData> getAllActiveOTPs() {
    return Map.from(_activeOTPs);
  }
}

class OTPData {
  final String otp;
  final String rideId;
  final DateTime generatedAt;
  final DateTime expiresAt;
  int attempts;
  bool isUsed;

  OTPData({
    required this.otp,
    required this.rideId,
    required this.generatedAt,
    required this.expiresAt,
    required this.attempts,
    required this.isUsed,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);

  Duration get remainingTime {
    final now = DateTime.now();
    if (now.isAfter(expiresAt)) return Duration.zero;
    return expiresAt.difference(now);
  }

  Map<String, dynamic> toJson() {
    return {
      'otp': otp,
      'rideId': rideId,
      'generatedAt': generatedAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'attempts': attempts,
      'isUsed': isUsed,
    };
  }
}

class OTPVerificationResult {
  final bool isValid;
  final String? error;
  final OTPErrorType? errorType;
  final int? remainingAttempts;

  OTPVerificationResult({
    required this.isValid,
    this.error,
    this.errorType,
    this.remainingAttempts,
  });
}

enum OTPErrorType {
  notFound,
  expired,
  invalid,
  alreadyUsed,
  maxAttemptsExceeded,
}
