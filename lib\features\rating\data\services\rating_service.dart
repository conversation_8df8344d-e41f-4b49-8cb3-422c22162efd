import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
// import '../../../core/services/analytics_service.dart'; // Commented out for now
// import '../../../core/services/notification_service.dart'; // Commented out for now
import '../../domain/models/rating_models.dart';

class RatingService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static const Uuid _uuid = Uuid();

  static const String _ratingsCollection = 'ratings';
  static const String _ratingSummariesCollection = 'rating_summaries';

  // Submit a rating
  static Future<Rating> submitRating({
    required String targetId,
    required String targetType,
    required String orderId,
    required double rating,
    String? review,
    List<String> tags = const [],
    List<File> images = const [],
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      // Upload images if any
      final imageUrls = <String>[];
      for (final image in images) {
        final url = await _uploadRatingImage(image);
        imageUrls.add(url);
      }

      final ratingId = _uuid.v4();
      final now = DateTime.now();

      // Get user info
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();
      final userData = userDoc.data() ?? {};

      final ratingData = Rating(
        id: ratingId,
        userId: currentUser.uid,
        targetId: targetId,
        targetType: targetType,
        orderId: orderId,
        rating: rating,
        review: review,
        tags: tags,
        images: imageUrls,
        createdAt: now,
        updatedAt: now,
        isVerified: true, // Auto-verify for now
        helpfulCount: 0,
        userName: userData['name'] ?? 'Anonymous User',
        userAvatar: userData['avatar'],
      );

      // Save rating
      await _firestore
          .collection(_ratingsCollection)
          .doc(ratingId)
          .set(ratingData.toJson());

      // Update rating summary
      await _updateRatingSummary(targetId, targetType);

      // Send notification to target user
      await _sendRatingNotification(targetId, targetType, rating, review);

      // Log analytics
      // await AnalyticsService.logEvent('rating_submitted', {
      //   'target_type': targetType,
      //   'rating': rating,
      //   'has_review': review != null,
      //   'has_images': imageUrls.isNotEmpty,
      //   'tags_count': tags.length,
      // });

      return ratingData;
    } catch (e) {
      debugPrint('❌ Error submitting rating: $e');
      throw Exception('Failed to submit rating: ${e.toString()}');
    }
  }

  // Get ratings for a target (rider, seller, service)
  static Stream<List<Rating>> getRatings({
    required String targetId,
    RatingFilter filter = RatingFilter.all,
    int limit = 20,
  }) {
    try {
      Query query = _firestore
          .collection(_ratingsCollection)
          .where('targetId', isEqualTo: targetId)
          .orderBy('createdAt', descending: true);

      // Apply filters
      switch (filter) {
        case RatingFilter.fiveStar:
          query = query.where('rating', isEqualTo: 5.0);
          break;
        case RatingFilter.fourStar:
          query = query.where('rating', isEqualTo: 4.0);
          break;
        case RatingFilter.threeStar:
          query = query.where('rating', isEqualTo: 3.0);
          break;
        case RatingFilter.twoStar:
          query = query.where('rating', isEqualTo: 2.0);
          break;
        case RatingFilter.oneStar:
          query = query.where('rating', isEqualTo: 1.0);
          break;
        case RatingFilter.withReviews:
          query = query.where('review', isNotEqualTo: null);
          break;
        case RatingFilter.withImages:
          query = query.where('images', isNotEqualTo: []);
          break;
        case RatingFilter.verified:
          query = query.where('isVerified', isEqualTo: true);
          break;
        case RatingFilter.all:
          break;
      }

      return query.limit(limit).snapshots().map((snapshot) {
        return snapshot.docs.map((doc) {
          return Rating.fromJson(doc.data() as Map<String, dynamic>);
        }).toList();
      });
    } catch (e) {
      debugPrint('❌ Error getting ratings: $e');
      return Stream.value([]);
    }
  }

  // Get rating summary for a target
  static Future<RatingSummary?> getRatingSummary(
    String targetId,
    String targetType,
  ) async {
    try {
      final doc = await _firestore
          .collection(_ratingSummariesCollection)
          .doc('${targetType}_$targetId')
          .get();

      if (doc.exists) {
        return RatingSummary.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting rating summary: $e');
      return null;
    }
  }

  // Mark rating as helpful
  static Future<void> markRatingHelpful(String ratingId) async {
    try {
      await _firestore.collection(_ratingsCollection).doc(ratingId).update({
        'helpfulCount': FieldValue.increment(1),
      });
    } catch (e) {
      debugPrint('❌ Error marking rating helpful: $e');
    }
  }

  // Report inappropriate rating
  static Future<void> reportRating(String ratingId, String reason) async {
    try {
      await _firestore.collection('rating_reports').add({
        'ratingId': ratingId,
        'reason': reason,
        'reportedBy': _auth.currentUser?.uid,
        'reportedAt': FieldValue.serverTimestamp(),
        'status': 'pending',
      });
    } catch (e) {
      debugPrint('❌ Error reporting rating: $e');
    }
  }

  // Private helper methods
  static Future<String> _uploadRatingImage(File image) async {
    try {
      final fileName = '${_uuid.v4()}.jpg';
      final ref = _storage.ref().child('rating_images/$fileName');
      await ref.putFile(image);
      return await ref.getDownloadURL();
    } catch (e) {
      debugPrint('❌ Error uploading rating image: $e');
      throw Exception('Failed to upload image');
    }
  }

  static Future<void> _updateRatingSummary(
    String targetId,
    String targetType,
  ) async {
    try {
      // Get all ratings for this target
      final ratingsSnapshot = await _firestore
          .collection(_ratingsCollection)
          .where('targetId', isEqualTo: targetId)
          .where('targetType', isEqualTo: targetType)
          .get();

      if (ratingsSnapshot.docs.isEmpty) return;

      final ratings = ratingsSnapshot.docs.map((doc) {
        return Rating.fromJson(doc.data());
      }).toList();

      // Calculate summary
      final totalRatings = ratings.length;
      final averageRating =
          ratings.map((r) => r.rating).reduce((a, b) => a + b) / totalRatings;

      final ratingDistribution = <int, int>{};
      for (int i = 1; i <= 5; i++) {
        ratingDistribution[i] = ratings
            .where((r) => r.rating.round() == i)
            .length;
      }

      // Get top tags
      final allTags = <String>[];
      for (final rating in ratings) {
        allTags.addAll(rating.tags);
      }
      final tagCounts = <String, int>{};
      for (final tag in allTags) {
        tagCounts[tag] = (tagCounts[tag] ?? 0) + 1;
      }
      final topTags = tagCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      final topTagsList = topTags.take(5).map((e) => e.key).toList();

      final summary = RatingSummary(
        targetId: targetId,
        targetType: targetType,
        averageRating: double.parse(averageRating.toStringAsFixed(1)),
        totalRatings: totalRatings,
        ratingDistribution: ratingDistribution,
        topTags: topTagsList,
        lastUpdated: DateTime.now(),
      );

      // Save summary
      await _firestore
          .collection(_ratingSummariesCollection)
          .doc('${targetType}_$targetId')
          .set(summary.toJson());
    } catch (e) {
      debugPrint('❌ Error updating rating summary: $e');
    }
  }

  static Future<void> _sendRatingNotification(
    String targetId,
    String targetType,
    double rating,
    String? review,
  ) async {
    try {
      // final title = 'New ${rating.toInt()}-Star Rating!';
      // final body = review != null && review.isNotEmpty
      //     ? 'Someone left you a review: "${review.length > 50 ? '${review.substring(0, 50)}...' : review}"'
      //     : 'You received a ${rating.toInt()}-star rating!';

      // await NotificationService.sendNotificationToUser(
      //   userId: targetId,
      //   title: title,
      //   body: body,
      //   data: {
      //     'type': 'rating',
      //     'targetType': targetType,
      //     'rating': rating.toString(),
      //   },
      // );
    } catch (e) {
      debugPrint('❌ Error sending rating notification: $e');
    }
  }
}
