# Vivo V23 5G APK Installation Guide

## 🔧 Pre-Installation Setup

### Step 1: Enable Developer Options
1. **Settings** → **About Phone**
2. Tap **"Build Number"** 7 times rapidly
3. Enter your lock screen password/PIN
4. You'll see "Developer options enabled"

### Step 2: Enable USB Debugging (for ADB installation)
1. **Settings** → **System** → **Developer Options**
2. Toggle **"USB Debugging"** ON
3. Toggle **"Install via USB"** ON

### Step 3: Enable Unknown Sources
**Method A (Recommended):**
1. **Settings** → **Security & Privacy**
2. **More Security Settings**
3. **Install Unknown Apps**
4. Select your **File Manager** app
5. Toggle **"Allow from this source"** ON

**Method B (Alternative):**
1. **Settings** → **Apps & Permissions**
2. **Permission Manager**
3. **Install Unknown Apps**
4. Enable for **File Manager** and **Chrome**

## 📱 Installation Methods

### Method 1: File Manager Installation
1. Transfer signed <PERSON>K to device via USB/Cloud
2. Open **File Manager**
3. Navigate to APK location
4. Tap the APK file
5. Tap **"Install"**
6. If blocked, tap **"Settings"** → Enable unknown sources
7. Return and tap **"Install"** again

### Method 2: ADB Installation (Most Reliable)
```bash
# Connect Vivo V23 5G via USB
# Enable USB Debugging first
adb devices
adb install -r APK_RELEASE/app-arm64-v8a-prod-release.apk
```

### Method 3: Chrome Download
1. Upload APK to Google Drive/Dropbox
2. Open link in Chrome on Vivo V23 5G
3. Download APK
4. Chrome will prompt to install
5. Allow Chrome to install unknown apps if needed

## 🚨 Troubleshooting Vivo-Specific Issues

### Issue: "Installation Blocked"
**Solution:**
1. **Settings** → **Security & Privacy**
2. **Device Security**
3. Temporarily disable **"Scan apps with Play Protect"**
4. Try installation again

### Issue: "App Not Installed - Invalid Package"
**Solutions:**
1. Ensure APK is properly signed (use our signing script)
2. Clear Package Installer cache:
   - **Settings** → **Apps** → **Package Installer**
   - **Storage** → **Clear Cache**
3. Restart device and try again

### Issue: "Insufficient Storage"
**Solution:**
1. Free up at least 500MB space
2. **Settings** → **Storage** → **Free up space**
3. Clear cache of unused apps

### Issue: "Parse Error"
**Solutions:**
1. Re-download/transfer APK file
2. Verify file integrity (check file size: ~50MB)
3. Try different transfer method

## ✅ Post-Installation Verification

### Test Checklist:
- [ ] App launches successfully
- [ ] No crash on startup
- [ ] Navigation works (go_router)
- [ ] State management active (Riverpod)
- [ ] Dependency injection working (get_it)
- [ ] Firebase connection established
- [ ] Marketplace features functional
- [ ] Maps integration working
- [ ] Image loading operational

## 🔄 Alternative Apps for Installation

If default file manager fails:
1. **APK Installer** (from Vivo App Store)
2. **Package Installer** (built-in)
3. **ES File Explorer**
4. **Total Commander**

## 📞 Emergency Fallback

If all methods fail:
1. Factory reset device (last resort)
2. Use different Android device for testing
3. Contact Vivo support for developer mode issues

## 🎯 Success Indicators

When installation succeeds:
- App icon appears in app drawer
- App launches without errors
- All marketplace features work
- No security warnings during use
