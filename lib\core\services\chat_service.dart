import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/chat_models.dart';
import 'analytics_service.dart';

class ChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collections
  static const String _chatsCollection = 'chats';
  static const String _messagesCollection = 'messages';
  static const String _usersCollection = 'users';

  /// Create a new chat between users
  static Future<String> createChat({
    required List<String> participantIds,
    required ChatType chatType,
    String? orderId,
    String? productId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final chatData = {
        'participants': participantIds,
        'chatType': chatType.toString(),
        'orderId': orderId,
        'productId': productId,
        'metadata': metadata ?? {},
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'lastMessage': '',
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastMessageSender': '',
        'isActive': true,
        'unreadCounts': {for (String id in participantIds) id: 0},
      };

      final docRef = await _firestore
          .collection(_chatsCollection)
          .add(chatData);

      await AnalyticsService.logEvent('chat_created', {
        'chat_type': chatType.toString(),
        'participant_count': participantIds.length,
        'has_order': orderId != null,
        'has_product': productId != null,
      });

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create chat: $e');
    }
  }

  /// Send a message in a chat
  static Future<void> sendMessage({
    required String chatId,
    required String message,
    MessageType messageType = MessageType.text,
    String? imageUrl,
    String? fileUrl,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final messageData = {
        'senderId': currentUser.uid,
        'senderName': currentUser.displayName ?? 'User',
        'message': message,
        'messageType': messageType.toString(),
        'imageUrl': imageUrl,
        'fileUrl': fileUrl,
        'metadata': metadata ?? {},
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'isEdited': false,
        'isDeleted': false,
      };

      // Add message to subcollection
      await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .collection(_messagesCollection)
          .add(messageData);

      // Update chat with last message info
      await _firestore.collection(_chatsCollection).doc(chatId).update({
        'lastMessage': messageType == MessageType.text
            ? message
            : _getMessageTypeDisplay(messageType),
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastMessageSender': currentUser.uid,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update unread counts for other participants
      final chatDoc = await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .get();
      if (chatDoc.exists) {
        final participants = List<String>.from(chatDoc.data()!['participants']);
        final unreadCounts = Map<String, dynamic>.from(
          chatDoc.data()!['unreadCounts'] ?? {},
        );

        for (String participantId in participants) {
          if (participantId != currentUser.uid) {
            unreadCounts[participantId] =
                (unreadCounts[participantId] ?? 0) + 1;
          }
        }

        await _firestore.collection(_chatsCollection).doc(chatId).update({
          'unreadCounts': unreadCounts,
        });
      }

      await AnalyticsService.logEvent('message_sent', {
        'chat_id': chatId,
        'message_type': messageType.toString(),
        'message_length': message.length,
      });
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  /// Get chat messages stream
  static Stream<List<ChatMessage>> getMessages(String chatId) {
    return _firestore
        .collection(_chatsCollection)
        .doc(chatId)
        .collection(_messagesCollection)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data();
            return ChatMessage.fromFirestore(doc.id, data);
          }).toList();
        });
  }

  /// Get user's chats stream
  static Stream<List<Chat>> getUserChats(String userId) {
    return _firestore
        .collection(_chatsCollection)
        .where('participants', arrayContains: userId)
        .where('isActive', isEqualTo: true)
        .orderBy('updatedAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data();
            return Chat.fromFirestore(doc.id, data);
          }).toList();
        });
  }

  /// Mark messages as read
  static Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      // Reset unread count for this user
      await _firestore.collection(_chatsCollection).doc(chatId).update({
        'unreadCounts.$userId': 0,
      });

      await AnalyticsService.logEvent('messages_marked_read', {
        'chat_id': chatId,
      });
    } catch (e) {
      throw Exception('Failed to mark messages as read: $e');
    }
  }

  /// Get or create chat between specific users
  static Future<String> getOrCreateChat({
    required String otherUserId,
    required ChatType chatType,
    String? orderId,
    String? productId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      // Check if chat already exists
      final existingChats = await _firestore
          .collection(_chatsCollection)
          .where('participants', arrayContains: currentUser.uid)
          .where('chatType', isEqualTo: chatType.toString())
          .where('isActive', isEqualTo: true)
          .get();

      for (var doc in existingChats.docs) {
        final participants = List<String>.from(doc.data()['participants']);
        if (participants.contains(otherUserId)) {
          // Check if order/product matches (if specified)
          if (orderId != null && doc.data()['orderId'] != orderId) continue;
          if (productId != null && doc.data()['productId'] != productId) {
            continue;
          }

          return doc.id; // Return existing chat
        }
      }

      // Create new chat if none exists
      return await createChat(
        participantIds: [currentUser.uid, otherUserId],
        chatType: chatType,
        orderId: orderId,
        productId: productId,
      );
    } catch (e) {
      throw Exception('Failed to get or create chat: $e');
    }
  }

  /// Delete/Archive a chat
  static Future<void> deleteChat(String chatId) async {
    try {
      await _firestore.collection(_chatsCollection).doc(chatId).update({
        'isActive': false,
        'deletedAt': FieldValue.serverTimestamp(),
      });

      await AnalyticsService.logEvent('chat_deleted', {'chat_id': chatId});
    } catch (e) {
      throw Exception('Failed to delete chat: $e');
    }
  }

  /// Get chat participants info
  static Future<List<ChatParticipant>> getChatParticipants(
    String chatId,
  ) async {
    try {
      final chatDoc = await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .get();
      if (!chatDoc.exists) throw Exception('Chat not found');

      final participants = List<String>.from(chatDoc.data()!['participants']);
      final participantInfos = <ChatParticipant>[];

      for (String participantId in participants) {
        final userDoc = await _firestore
            .collection(_usersCollection)
            .doc(participantId)
            .get();
        if (userDoc.exists) {
          final userData = userDoc.data()!;
          participantInfos.add(
            ChatParticipant(
              id: participantId,
              name: userData['name'] ?? 'User',
              avatar: userData['avatar'],
              userType: UserType.values.firstWhere(
                (type) => type.toString() == userData['userType'],
                orElse: () => UserType.user,
              ),
              isOnline: userData['isOnline'] ?? false,
              lastSeen: userData['lastSeen']?.toDate(),
            ),
          );
        }
      }

      return participantInfos;
    } catch (e) {
      throw Exception('Failed to get chat participants: $e');
    }
  }

  /// Update user online status
  static Future<void> updateOnlineStatus(bool isOnline) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      await _firestore.collection(_usersCollection).doc(currentUser.uid).update(
        {'isOnline': isOnline, 'lastSeen': FieldValue.serverTimestamp()},
      );
    } catch (e) {
      // Silently fail for online status updates
    }
  }

  /// Search messages in a chat
  static Future<List<ChatMessage>> searchMessages(
    String chatId,
    String query,
  ) async {
    try {
      final messages = await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .collection(_messagesCollection)
          .where('message', isGreaterThanOrEqualTo: query)
          .where('message', isLessThanOrEqualTo: '$query\uf8ff')
          .orderBy('message')
          .orderBy('timestamp', descending: true)
          .limit(50)
          .get();

      return messages.docs.map((doc) {
        final data = doc.data();
        return ChatMessage.fromFirestore(doc.id, data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search messages: $e');
    }
  }

  static String _getMessageTypeDisplay(MessageType type) {
    switch (type) {
      case MessageType.image:
        return '📷 Image';
      case MessageType.file:
        return '📎 File';
      case MessageType.location:
        return '📍 Location';
      case MessageType.voice:
        return '🎤 Voice message';
      default:
        return 'Message';
    }
  }
}
