@echo off
echo ========================================
echo PROJEK 3-IN-1 SUPER APP - BUILD ALL APKs
echo ========================================
echo.

echo Building all APK variants for Projek Super App...
echo This will create 6 APK files:
echo - User App (Debug + Release)
echo - Rider App (Debug + Release) 
echo - Seller App (Debug + Release)
echo.

echo ========================================
echo 1. BUILDING USER APP (DEBUG)
echo ========================================
echo Building User App Debug version...
flutter build apk --flavor userDev --debug
if %errorlevel% neq 0 (
    echo ERROR: User App Debug build failed!
    pause
    exit /b 1
)
echo ✅ User App Debug build completed!
echo.

echo ========================================
echo 2. BUILDING USER APP (RELEASE)
echo ========================================
echo Building User App Release version...
flutter build apk --flavor userProd --release
if %errorlevel% neq 0 (
    echo ERROR: User App Release build failed!
    pause
    exit /b 1
)
echo ✅ User App Release build completed!
echo.

echo ========================================
echo 3. BUILDING RIDER APP (DEBUG)
echo ========================================
echo Building Rider App Debug version...
flutter build apk --flavor riderDev --debug
if %errorlevel% neq 0 (
    echo ERROR: Rider App Debug build failed!
    pause
    exit /b 1
)
echo ✅ Rider App Debug build completed!
echo.

echo ========================================
echo 4. BUILDING RIDER APP (RELEASE)
echo ========================================
echo Building Rider App Release version...
flutter build apk --flavor riderProd --release
if %errorlevel% neq 0 (
    echo ERROR: Rider App Release build failed!
    pause
    exit /b 1
)
echo ✅ Rider App Release build completed!
echo.

echo ========================================
echo 5. BUILDING SELLER APP (DEBUG)
echo ========================================
echo Building Seller App Debug version...
flutter build apk --flavor sellerDev --debug
if %errorlevel% neq 0 (
    echo ERROR: Seller App Debug build failed!
    pause
    exit /b 1
)
echo ✅ Seller App Debug build completed!
echo.

echo ========================================
echo 6. BUILDING SELLER APP (RELEASE)
echo ========================================
echo Building Seller App Release version...
flutter build apk --flavor sellerProd --release
if %errorlevel% neq 0 (
    echo ERROR: Seller App Release build failed!
    pause
    exit /b 1
)
echo ✅ Seller App Release build completed!
echo.

echo ========================================
echo 🎉 ALL BUILDS COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo APK files have been generated in:
echo build\app\outputs\flutter-apk\
echo.
echo Generated APK files:
dir build\app\outputs\flutter-apk\*.apk /b
echo.
echo ========================================
echo APK DETAILS:
echo ========================================
echo 📱 USER APP (Main marketplace with gaming):
echo    - app-user-dev-debug.apk (Debug version)
echo    - app-user-prod-release.apk (Release version)
echo.
echo 🚴 RIDER APP (Delivery partners):
echo    - app-rider-dev-debug.apk (Debug version)
echo    - app-rider-prod-release.apk (Release version)
echo.
echo 🏪 SELLER APP (Vendors/merchants):
echo    - app-seller-dev-debug.apk (Debug version)
echo    - app-seller-prod-release.apk (Release version)
echo.
echo ========================================
echo INSTALLATION COMMANDS:
echo ========================================
echo To install on device/emulator:
echo adb install build\app\outputs\flutter-apk\app-user-dev-debug.apk
echo adb install build\app\outputs\flutter-apk\app-rider-dev-debug.apk
echo adb install build\app\outputs\flutter-apk\app-seller-dev-debug.apk
echo.
echo ========================================
echo BUILD COMPLETED! 🚀
echo ========================================
pause
