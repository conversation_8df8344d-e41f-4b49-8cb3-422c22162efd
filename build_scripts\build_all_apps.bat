@echo off
echo Building All Three Apps...
echo.

REM Clean previous builds
echo Cleaning previous builds...
flutter clean
flutter pub get

echo.
echo ========================================
echo Building User App
echo ========================================

REM Build User App
flutter build apk --debug --target=lib/main_user.dart --flavor=userDev --no-tree-shake-icons
if %ERRORLEVEL% NEQ 0 (
    echo ❌ User App Debug build failed!
    goto :error
)

flutter build apk --release --target=lib/main_user.dart --flavor=userProd --no-tree-shake-icons
if %ERRORLEVEL% NEQ 0 (
    echo ❌ User App Release build failed!
    goto :error
)

echo ✅ User App built successfully!

echo.
echo ========================================
echo Building Rider App
echo ========================================

REM Build Rider App
flutter build apk --debug --target=lib/main_rider.dart --flavor=riderDev --no-tree-shake-icons
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Rider App Debug build failed!
    goto :error
)

flutter build apk --release --target=lib/main_rider.dart --flavor=riderProd --no-tree-shake-icons
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Rider App Release build failed!
    goto :error
)

echo ✅ Rider App built successfully!

echo.
echo ========================================
echo Building Seller App
echo ========================================

REM Build Seller App
flutter build apk --debug --target=lib/main_seller.dart --flavor=sellerDev --no-tree-shake-icons
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Seller App Debug build failed!
    goto :error
)

flutter build apk --release --target=lib/main_seller.dart --flavor=sellerProd --no-tree-shake-icons
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Seller App Release build failed!
    goto :error
)

echo ✅ Seller App built successfully!

echo.
echo 🎉 All Apps Built Successfully!
echo.
echo ========================================
echo Build Results:
echo ========================================
echo User App Debug:   build\app\outputs\flutter-apk\app-user-dev-debug.apk
echo User App Release: build\app\outputs\flutter-apk\app-user-prod-release.apk
echo.
echo Rider App Debug:   build\app\outputs\flutter-apk\app-rider-dev-debug.apk
echo Rider App Release: build\app\outputs\flutter-apk\app-rider-prod-release.apk
echo.
echo Seller App Debug:   build\app\outputs\flutter-apk\app-seller-dev-debug.apk
echo Seller App Release: build\app\outputs\flutter-apk\app-seller-prod-release.apk
echo.
goto :end

:error
echo.
echo ❌ Build process failed!
echo Please check the error messages above.
exit /b 1

:end
pause
