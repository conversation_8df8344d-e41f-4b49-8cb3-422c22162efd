@echo off
echo ========================================
echo    QUICK APK BUILD FIX
echo ========================================
echo.

:: Create flutter-apk directory if it doesn't exist
if not exist "build\app\outputs\flutter-apk" (
    mkdir "build\app\outputs\flutter-apk"
    echo Created flutter-apk directory
)

:: Copy existing APK files to expected location
echo Copying APK files to expected location...

set "apk_found=false"

:: Check for User App APK
if exist "build\app\outputs\apk\userProd\debug\app-user-prod-debug.apk" (
    copy "build\app\outputs\apk\userProd\debug\app-user-prod-debug.apk" "build\app\outputs\flutter-apk\app-debug.apk" >nul 2>&1
    copy "build\app\outputs\apk\userProd\debug\app-user-prod-debug.apk" "build\app\outputs\flutter-apk\app-userprod-debug.apk" >nul 2>&1
    echo ✓ User App APK copied
    set "apk_found=true"
)

:: Check for Rider App APK  
if exist "build\app\outputs\apk\riderProd\debug\app-rider-prod-debug.apk" (
    copy "build\app\outputs\apk\riderProd\debug\app-rider-prod-debug.apk" "build\app\outputs\flutter-apk\app-riderprod-debug.apk" >nul 2>&1
    echo ✓ Rider App APK copied
    set "apk_found=true"
)

:: Check for Seller App APK
if exist "build\app\outputs\apk\sellerProd\debug\app-seller-prod-debug.apk" (
    copy "build\app\outputs\apk\sellerProd\debug\app-seller-prod-debug.apk" "build\app\outputs\flutter-apk\app-sellerprod-debug.apk" >nul 2>&1
    echo ✓ Seller App APK copied
    set "apk_found=true"
)

:: Also check the flutter-apk directory for existing files
if exist "build\app\outputs\flutter-apk\app-userprod-debug.apk" set "apk_found=true"
if exist "build\app\outputs\flutter-apk\app-riderprod-debug.apk" set "apk_found=true"
if exist "build\app\outputs\flutter-apk\app-sellerprod-debug.apk" set "apk_found=true"

echo.
if "%apk_found%"=="true" (
    echo ========================================
    echo ✓ SUCCESS! APK files are ready
    echo ========================================
    echo.
    echo Available APK files:
    if exist "build\app\outputs\flutter-apk\app-userprod-debug.apk" echo • User App: app-userprod-debug.apk
    if exist "build\app\outputs\flutter-apk\app-riderprod-debug.apk" echo • Rider App: app-riderprod-debug.apk  
    if exist "build\app\outputs\flutter-apk\app-sellerprod-debug.apk" echo • Seller App: app-sellerprod-debug.apk
    echo.
    echo You can now run:
    echo   flutter install
    echo.
    echo Or install the APK files directly on your device.
) else (
    echo ========================================
    echo ✗ No APK files found
    echo ========================================
    echo.
    echo To build APK files, run one of these commands:
    echo.
    echo For User App:
    echo   flutter build apk --debug --flavor userProd --target lib/main_user.dart
    echo.
    echo For Rider App:
    echo   flutter build apk --debug --flavor riderProd --target lib/main_rider.dart
    echo.
    echo For Seller App:
    echo   flutter build apk --debug --flavor sellerProd --target lib/main_seller.dart
    echo.
    echo Then run this script again to copy the APKs to the correct location.
)

echo ========================================
pause
