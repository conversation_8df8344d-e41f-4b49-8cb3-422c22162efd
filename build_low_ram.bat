@echo off
echo ========================================
echo OPTIMIZED BUILD FOR 8GB RAM + SLOW SSD
echo ========================================
echo.
echo System Optimizations:
echo - Reduced Gradle memory usage
echo - Parallel processing disabled
echo - Incremental builds enabled
echo - Cache optimization
echo.

REM Set Gradle options for low RAM systems
set GRADLE_OPTS=-Xmx2g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
set JAVA_OPTS=-Xmx2g

echo Setting up environment for low RAM system...
echo GRADLE_OPTS: %GRADLE_OPTS%
echo.

echo ========================================
echo STEP 1: MEMORY CLEANUP
echo ========================================
echo Stopping any running Gradle processes...
taskkill /f /im java.exe 2>nul
taskkill /f /im gradle.exe 2>nul

echo Cleaning temporary files...
if exist "%TEMP%\flutter_tools*" rmdir /s /q "%TEMP%\flutter_tools*" 2>nul
if exist "%USERPROFILE%\.gradle\caches\transforms-*" rmdir /s /q "%USERPROFILE%\.gradle\caches\transforms-*" 2>nul

echo ========================================
echo STEP 2: GRADLE OPTIMIZATION
echo ========================================
echo Configuring Gradle for low RAM...

REM Create gradle.properties for memory optimization
echo org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m > android\gradle.properties
echo org.gradle.parallel=false >> android\gradle.properties
echo org.gradle.daemon=true >> android\gradle.properties
echo org.gradle.configureondemand=true >> android\gradle.properties
echo android.enableJetifier=true >> android\gradle.properties
echo android.useAndroidX=true >> android\gradle.properties

echo ✅ Gradle optimized for 8GB RAM

echo ========================================
echo STEP 3: FLUTTER OPTIMIZATION
echo ========================================
echo Cleaning Flutter cache...
flutter clean

echo Getting dependencies (this may take 5-10 minutes on slow SSD)...
flutter pub get

echo ========================================
echo STEP 4: BUILDING APK (OPTIMIZED)
echo ========================================
echo Building User App with memory optimizations...
echo Expected time: 15-20 minutes on your system
echo.

REM Build with optimizations for slow systems
flutter build apk ^
  --flavor userDev ^
  --debug ^
  --no-tree-shake-icons ^
  --no-shrink ^
  --dart-define=flutter.inspector.structuredErrors=false

if %errorlevel% neq 0 (
    echo.
    echo ❌ BUILD FAILED!
    echo.
    echo Trying alternative build method...
    echo.
    
    REM Try simpler build
    flutter build apk --debug --flavor userDev --verbose
    
    if %errorlevel% neq 0 (
        echo.
        echo ❌ Alternative build also failed!
        echo.
        echo Possible solutions:
        echo 1. Close other applications to free RAM
        echo 2. Restart your PC
        echo 3. Try building without flavor: flutter build apk --debug
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo 🎉 BUILD COMPLETED!
echo ========================================
echo.
echo APK Location: build\app\outputs\flutter-apk\
dir build\app\outputs\flutter-apk\*.apk /b 2>nul

echo.
echo ========================================
echo SYSTEM PERFORMANCE TIPS:
echo ========================================
echo For faster future builds:
echo 1. Close unnecessary applications before building
echo 2. Use incremental builds: flutter run (instead of build)
echo 3. Consider upgrading to 16GB RAM
echo 4. Use flutter run --hot-reload for development
echo.

echo Installation command:
echo adb install build\app\outputs\flutter-apk\app-user-dev-debug.apk
echo.
pause
