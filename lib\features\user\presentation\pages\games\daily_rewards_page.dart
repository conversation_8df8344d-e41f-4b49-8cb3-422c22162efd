import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/analytics_service.dart';

final dailyRewardsProvider = StateNotifierProvider<DailyRewardsNotifier, DailyRewardsState>((ref) {
  return DailyRewardsNotifier();
});

class DailyRewardsState {
  final int currentDay;
  final List<bool> claimedDays;
  final int totalCoins;
  final DateTime? lastClaimDate;
  final bool canClaimToday;

  DailyRewardsState({
    this.currentDay = 1,
    this.claimedDays = const [false, false, false, false, false, false, false],
    this.totalCoins = 0,
    this.lastClaimDate,
    this.canClaimToday = true,
  });

  DailyRewardsState copyWith({
    int? currentDay,
    List<bool>? claimedDays,
    int? totalCoins,
    DateTime? lastClaimDate,
    bool? canClaimToday,
  }) {
    return DailyRewardsState(
      currentDay: currentDay ?? this.currentDay,
      claimedDays: claimedDays ?? this.claimedDays,
      totalCoins: totalCoins ?? this.totalCoins,
      lastClaimDate: lastClaimDate ?? this.lastClaimDate,
      canClaimToday: canClaimToday ?? this.canClaimToday,
    );
  }
}

class DailyRewardsNotifier extends StateNotifier<DailyRewardsState> {
  DailyRewardsNotifier() : super(DailyRewardsState()) {
    _checkDailyReset();
  }

  final List<DailyReward> rewards = [
    DailyReward(day: 1, coins: 10, icon: '💰'),
    DailyReward(day: 2, coins: 20, icon: '💎'),
    DailyReward(day: 3, coins: 30, icon: '🏆'),
    DailyReward(day: 4, coins: 40, icon: '🎁'),
    DailyReward(day: 5, coins: 50, icon: '⭐'),
    DailyReward(day: 6, coins: 75, icon: '🔥'),
    DailyReward(day: 7, coins: 100, icon: '👑'),
  ];

  void _checkDailyReset() {
    final now = DateTime.now();
    final lastClaim = state.lastClaimDate;
    
    if (lastClaim == null) {
      // First time user
      return;
    }

    final daysDifference = now.difference(lastClaim).inDays;
    
    if (daysDifference >= 1) {
      // Can claim today
      state = state.copyWith(canClaimToday: true);
      
      if (daysDifference > 1) {
        // Missed days, reset streak
        state = state.copyWith(
          currentDay: 1,
          claimedDays: [false, false, false, false, false, false, false],
        );
      }
    } else {
      // Already claimed today
      state = state.copyWith(canClaimToday: false);
    }
  }

  Future<void> claimReward() async {
    if (!state.canClaimToday || state.currentDay > 7) return;

    final reward = rewards[state.currentDay - 1];
    final newClaimedDays = List<bool>.from(state.claimedDays);
    newClaimedDays[state.currentDay - 1] = true;

    final nextDay = state.currentDay >= 7 ? 1 : state.currentDay + 1;
    final resetClaimed = state.currentDay >= 7 
        ? [false, false, false, false, false, false, false]
        : newClaimedDays;

    state = state.copyWith(
      currentDay: nextDay,
      claimedDays: resetClaimed,
      totalCoins: state.totalCoins + reward.coins,
      lastClaimDate: DateTime.now(),
      canClaimToday: false,
    );

    // Log analytics
    await AnalyticsService.logEvent('daily_reward_claimed', {
      'day': reward.day,
      'coins_earned': reward.coins,
      'total_coins': state.totalCoins,
      'streak_completed': state.currentDay == 1, // Reset means completed 7 days
    });
  }
}

class DailyReward {
  final int day;
  final int coins;
  final String icon;

  DailyReward({
    required this.day,
    required this.coins,
    required this.icon,
  });
}

class DailyRewardsPage extends ConsumerWidget {
  const DailyRewardsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final rewardsState = ref.watch(dailyRewardsProvider);
    final rewardsNotifier = ref.read(dailyRewardsProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('🎁 Daily Rewards'),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
        actions: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Icon(Icons.monetization_on, color: AppColors.ratingGold),
                const SizedBox(width: 4),
                Text(
                  '${rewardsState.totalCoins}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.userPrimary,
              AppColors.backgroundLight,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const Text(
                      '🔥 Daily Login Streak',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.userPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Day ${rewardsState.currentDay} of 7',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      rewardsState.canClaimToday
                          ? '🎉 Ready to claim today\'s reward!'
                          : '⏰ Come back tomorrow for your next reward',
                      style: TextStyle(
                        fontSize: 14,
                        color: rewardsState.canClaimToday 
                            ? AppColors.success 
                            : Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Rewards Grid
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 1.2,
                    ),
                    itemCount: rewardsNotifier.rewards.length,
                    itemBuilder: (context, index) {
                      final reward = rewardsNotifier.rewards[index];
                      final isClaimed = rewardsState.claimedDays[index];
                      final isToday = rewardsState.currentDay == reward.day;
                      final canClaim = isToday && rewardsState.canClaimToday;

                      return _buildRewardCard(
                        reward,
                        isClaimed,
                        isToday,
                        canClaim,
                        () => rewardsNotifier.claimReward(),
                      );
                    },
                  ),
                ),
              ),

              // Claim Button
              if (rewardsState.canClaimToday)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ElevatedButton(
                    onPressed: () => rewardsNotifier.claimReward(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 40,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      elevation: 8,
                    ),
                    child: Text(
                      '🎁 CLAIM TODAY\'S REWARD',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRewardCard(
    DailyReward reward,
    bool isClaimed,
    bool isToday,
    bool canClaim,
    VoidCallback onClaim,
  ) {
    return Card(
      elevation: isToday ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isToday
            ? const BorderSide(color: AppColors.userPrimary, width: 2)
            : BorderSide.none,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: isClaimed
              ? LinearGradient(
                  colors: [AppColors.success.withOpacity(0.8), AppColors.success],
                )
              : isToday
                  ? LinearGradient(
                      colors: [AppColors.userPrimary.withOpacity(0.8), AppColors.userPrimary],
                    )
                  : LinearGradient(
                      colors: [Colors.grey[300]!, Colors.grey[400]!],
                    ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              reward.icon,
              style: const TextStyle(fontSize: 32),
            ),
            const SizedBox(height: 8),
            Text(
              'Day ${reward.day}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isClaimed || isToday ? Colors.white : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${reward.coins} Coins',
              style: TextStyle(
                fontSize: 14,
                color: isClaimed || isToday ? Colors.white70 : Colors.grey[600],
              ),
            ),
            if (isClaimed)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
