import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../widgets/loyalty_widgets.dart';
import '../../domain/models/loyalty_models.dart';
import '../../data/services/loyalty_service.dart';
import '../../data/services/demo_loyalty_data.dart';

class LoyaltyDashboardPage extends ConsumerStatefulWidget {
  const LoyaltyDashboardPage({super.key});

  @override
  ConsumerState<LoyaltyDashboardPage> createState() => _LoyaltyDashboardPageState();
}

class _LoyaltyDashboardPageState extends ConsumerState<LoyaltyDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  LoyaltyAccount? _loyaltyAccount;
  List<RewardOffer> _availableOffers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadLoyaltyData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadLoyaltyData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // For demo purposes, use demo data
      _loyaltyAccount = DemoLoyaltyData.getDemoLoyaltyAccount();
      _availableOffers = DemoLoyaltyData.getDemoRewardOffers();
    } catch (e) {
      debugPrint('Error loading loyalty data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rewards & Loyalty'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Rewards'),
            Tab(text: 'History'),
            Tab(text: 'Referrals'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildRewardsTab(),
                _buildHistoryTab(),
                _buildReferralsTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    if (_loyaltyAccount == null) {
      return const Center(
        child: Text('No loyalty account found'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Loyalty Account Card
          LoyaltyAccountCard(
            account: _loyaltyAccount!,
            onTap: () {
              // Navigate to detailed view
            },
          ),
          
          const SizedBox(height: 24),
          
          // Tier Benefits
          TierBenefitsWidget(tier: _loyaltyAccount!.currentTier),
          
          const SizedBox(height: 24),
          
          // Quick Stats
          Text(
            'Your Stats',
            style: AppTextStyles.headlineMedium,
          ),
          const SizedBox(height: 16),
          
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.5,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              _buildStatCard(
                'Total Points Earned',
                _loyaltyAccount!.totalPoints.toString(),
                Icons.stars,
                AppColors.warning,
              ),
              _buildStatCard(
                'Total Cashback',
                '₹${_loyaltyAccount!.totalCashback.toStringAsFixed(0)}',
                Icons.account_balance_wallet,
                AppColors.success,
              ),
              _buildStatCard(
                'Lifetime Spending',
                '₹${_loyaltyAccount!.lifetimeSpending.toStringAsFixed(0)}',
                Icons.shopping_bag,
                AppColors.info,
              ),
              _buildStatCard(
                'Referrals Made',
                _loyaltyAccount!.totalReferrals.toString(),
                Icons.people,
                AppColors.primaryBlue,
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Featured Offers
          Row(
            children: [
              Text(
                'Featured Rewards',
                style: AppTextStyles.headlineMedium,
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  _tabController.animateTo(1);
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _availableOffers.take(3).length,
              itemBuilder: (context, index) {
                final offer = _availableOffers[index];
                return SizedBox(
                  width: 280,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16),
                    child: RewardOfferCard(
                      offer: offer,
                      canRedeem: _loyaltyAccount!.availablePoints >= offer.pointsCost,
                      onRedeem: () => _redeemOffer(offer),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRewardsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Points Summary
          if (_loyaltyAccount != null) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.primaryBlue.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.stars,
                    color: AppColors.warning,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Available Points',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        _loyaltyAccount!.availablePoints.toString(),
                        style: AppTextStyles.headlineLarge.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
          
          // Available Offers
          Text(
            'Available Rewards',
            style: AppTextStyles.headlineMedium,
          ),
          const SizedBox(height: 16),
          
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _availableOffers.length,
            itemBuilder: (context, index) {
              final offer = _availableOffers[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: RewardOfferCard(
                  offer: offer,
                  canRedeem: _loyaltyAccount != null && 
                            _loyaltyAccount!.availablePoints >= offer.pointsCost,
                  onRedeem: () => _redeemOffer(offer),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    final transactions = DemoLoyaltyData.getDemoRewardTransactions();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Transaction History',
            style: AppTextStyles.headlineMedium,
          ),
          const SizedBox(height: 16),
          
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: transactions.length,
            itemBuilder: (context, index) {
              return RewardTransactionItem(
                transaction: transactions[index],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildReferralsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Referral Code Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.card_giftcard,
                        color: AppColors.primaryBlue,
                        size: 28,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Your Referral Code',
                        style: AppTextStyles.headlineSmall,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.grey100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.grey300, style: BorderStyle.solid),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'PROJEK2024',
                          style: AppTextStyles.headlineMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            letterSpacing: 2,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: _copyReferralCode,
                          icon: const Icon(Icons.copy),
                          tooltip: 'Copy Code',
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    'Share your code and earn ₹50 cashback + 500 points for each friend who joins!',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _shareReferralCode,
                      icon: const Icon(Icons.share),
                      label: const Text('Share Code'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Referral Stats
          Text(
            'Referral Stats',
            style: AppTextStyles.headlineMedium,
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Friends Referred',
                  _loyaltyAccount?.totalReferrals.toString() ?? '0',
                  Icons.people,
                  AppColors.primaryBlue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Referral Earnings',
                  '₹${(_loyaltyAccount?.totalReferrals ?? 0) * 50}',
                  Icons.account_balance_wallet,
                  AppColors.success,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // How it works
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'How Referrals Work',
                    style: AppTextStyles.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  
                  _buildHowItWorksStep(
                    '1',
                    'Share Your Code',
                    'Send your referral code to friends and family',
                    Icons.share,
                  ),
                  
                  _buildHowItWorksStep(
                    '2',
                    'Friend Signs Up',
                    'They use your code when creating their account',
                    Icons.person_add,
                  ),
                  
                  _buildHowItWorksStep(
                    '3',
                    'Both Get Rewards',
                    'You both earn points and cashback when they place their first order',
                    Icons.card_giftcard,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTextStyles.headlineMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHowItWorksStep(String step, String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                step,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Icon(icon, color: AppColors.primaryBlue),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _redeemOffer(RewardOffer offer) async {
    if (_loyaltyAccount == null) return;

    try {
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Redeem Reward'),
          content: Text('Redeem "${offer.title}" for ${offer.pointsCost} points?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Redeem'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // In a real app, call the service
        // await LoyaltyService.redeemReward(userId: userId, offerId: offer.id);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully redeemed "${offer.title}"!'),
            backgroundColor: AppColors.success,
          ),
        );
        
        // Refresh data
        _loadLoyaltyData();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to redeem reward: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _copyReferralCode() {
    // In a real app, copy to clipboard
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Referral code copied to clipboard!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareReferralCode() {
    // In a real app, use share plugin
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
