import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'advanced_booking_models.g.dart';

@HiveType(typeId: 80)
enum BookingType {
  @HiveField(0)
  single,
  @HiveField(1)
  recurring,
  @HiveField(2)
  group,
  @HiveField(3)
  template,
}

@HiveType(typeId: 81)
enum RecurrencePattern {
  @HiveField(0)
  daily,
  @HiveField(1)
  weekly,
  @HiveField(2)
  biweekly,
  @HiveField(3)
  monthly,
  @HiveField(4)
  custom,
}

@HiveType(typeId: 82)
enum BookingStatus {
  @HiveField(0)
  scheduled,
  @HiveField(1)
  confirmed,
  @HiveField(2)
  inProgress,
  @HiveField(3)
  completed,
  @HiveField(4)
  cancelled,
  @HiveField(5)
  failed,
}

@HiveType(typeId: 83)
@JsonSerializable()
class AdvancedBooking extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final BookingType type;
  
  @HiveField(3)
  final String title;
  
  @HiveField(4)
  final String description;
  
  @HiveField(5)
  final String serviceType; // 'food', 'grocery', 'medicine', etc.
  
  @HiveField(6)
  final DateTime scheduledAt;
  
  @HiveField(7)
  final DateTime? endDate; // For recurring bookings
  
  @HiveField(8)
  final RecurrencePattern? recurrencePattern;
  
  @HiveField(9)
  final Map<String, dynamic> recurrenceConfig;
  
  @HiveField(10)
  final List<String> participantIds; // For group bookings
  
  @HiveField(11)
  final String? templateId;
  
  @HiveField(12)
  final BookingStatus status;
  
  @HiveField(13)
  final double estimatedCost;
  
  @HiveField(14)
  final String pickupAddress;
  
  @HiveField(15)
  final double pickupLatitude;
  
  @HiveField(16)
  final double pickupLongitude;
  
  @HiveField(17)
  final String deliveryAddress;
  
  @HiveField(18)
  final double deliveryLatitude;
  
  @HiveField(19)
  final double deliveryLongitude;
  
  @HiveField(20)
  final List<BookingItem> items;
  
  @HiveField(21)
  final Map<String, dynamic> preferences;
  
  @HiveField(22)
  final DateTime createdAt;
  
  @HiveField(23)
  final DateTime updatedAt;
  
  @HiveField(24)
  final String? notes;
  
  @HiveField(25)
  final List<String> generatedBookingIds; // For recurring bookings

  const AdvancedBooking({
    required this.id,
    required this.userId,
    this.type = BookingType.single,
    required this.title,
    this.description = '',
    required this.serviceType,
    required this.scheduledAt,
    this.endDate,
    this.recurrencePattern,
    this.recurrenceConfig = const {},
    this.participantIds = const [],
    this.templateId,
    this.status = BookingStatus.scheduled,
    this.estimatedCost = 0.0,
    required this.pickupAddress,
    required this.pickupLatitude,
    required this.pickupLongitude,
    required this.deliveryAddress,
    required this.deliveryLatitude,
    required this.deliveryLongitude,
    this.items = const [],
    this.preferences = const {},
    required this.createdAt,
    required this.updatedAt,
    this.notes,
    this.generatedBookingIds = const [],
  });

  factory AdvancedBooking.fromJson(Map<String, dynamic> json) => _$AdvancedBookingFromJson(json);
  Map<String, dynamic> toJson() => _$AdvancedBookingToJson(this);

  bool get isRecurring => type == BookingType.recurring;
  bool get isGroup => type == BookingType.group;
  bool get isTemplate => type == BookingType.template;
  bool get isActive => status != BookingStatus.cancelled && status != BookingStatus.completed;
  
  Duration get timeUntilScheduled => scheduledAt.difference(DateTime.now());
  bool get isUpcoming => timeUntilScheduled.inMinutes > 0;
  bool get isOverdue => timeUntilScheduled.inMinutes < -30 && status == BookingStatus.scheduled;

  @override
  List<Object?> get props => [
    id, userId, type, title, description, serviceType, scheduledAt, endDate,
    recurrencePattern, recurrenceConfig, participantIds, templateId, status,
    estimatedCost, pickupAddress, pickupLatitude, pickupLongitude,
    deliveryAddress, deliveryLatitude, deliveryLongitude, items, preferences,
    createdAt, updatedAt, notes, generatedBookingIds,
  ];
}

@HiveType(typeId: 84)
@JsonSerializable()
class BookingItem extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String category;
  
  @HiveField(3)
  final int quantity;
  
  @HiveField(4)
  final double unitPrice;
  
  @HiveField(5)
  final String? imageUrl;
  
  @HiveField(6)
  final String? description;
  
  @HiveField(7)
  final Map<String, dynamic> specifications;

  const BookingItem({
    required this.id,
    required this.name,
    required this.category,
    this.quantity = 1,
    this.unitPrice = 0.0,
    this.imageUrl,
    this.description,
    this.specifications = const {},
  });

  factory BookingItem.fromJson(Map<String, dynamic> json) => _$BookingItemFromJson(json);
  Map<String, dynamic> toJson() => _$BookingItemToJson(this);

  double get totalPrice => quantity * unitPrice;

  @override
  List<Object?> get props => [
    id, name, category, quantity, unitPrice, imageUrl, description, specifications,
  ];
}

@HiveType(typeId: 85)
@JsonSerializable()
class BookingTemplate extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String name;
  
  @HiveField(3)
  final String description;
  
  @HiveField(4)
  final String serviceType;
  
  @HiveField(5)
  final String pickupAddress;
  
  @HiveField(6)
  final double pickupLatitude;
  
  @HiveField(7)
  final double pickupLongitude;
  
  @HiveField(8)
  final String deliveryAddress;
  
  @HiveField(9)
  final double deliveryLatitude;
  
  @HiveField(10)
  final double deliveryLongitude;
  
  @HiveField(11)
  final List<BookingItem> items;
  
  @HiveField(12)
  final Map<String, dynamic> preferences;
  
  @HiveField(13)
  final DateTime createdAt;
  
  @HiveField(14)
  final DateTime updatedAt;
  
  @HiveField(15)
  final int usageCount;
  
  @HiveField(16)
  final bool isActive;

  const BookingTemplate({
    required this.id,
    required this.userId,
    required this.name,
    this.description = '',
    required this.serviceType,
    required this.pickupAddress,
    required this.pickupLatitude,
    required this.pickupLongitude,
    required this.deliveryAddress,
    required this.deliveryLatitude,
    required this.deliveryLongitude,
    this.items = const [],
    this.preferences = const {},
    required this.createdAt,
    required this.updatedAt,
    this.usageCount = 0,
    this.isActive = true,
  });

  factory BookingTemplate.fromJson(Map<String, dynamic> json) => _$BookingTemplateFromJson(json);
  Map<String, dynamic> toJson() => _$BookingTemplateToJson(this);

  double get estimatedCost => items.fold(0.0, (sum, item) => sum + item.totalPrice);

  @override
  List<Object?> get props => [
    id, userId, name, description, serviceType, pickupAddress, pickupLatitude,
    pickupLongitude, deliveryAddress, deliveryLatitude, deliveryLongitude,
    items, preferences, createdAt, updatedAt, usageCount, isActive,
  ];
}

@HiveType(typeId: 86)
@JsonSerializable()
class GroupBooking extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String organizerId;
  
  @HiveField(2)
  final String title;
  
  @HiveField(3)
  final String description;
  
  @HiveField(4)
  final String serviceType;
  
  @HiveField(5)
  final DateTime scheduledAt;
  
  @HiveField(6)
  final List<GroupParticipant> participants;
  
  @HiveField(7)
  final double totalCost;
  
  @HiveField(8)
  final String costSplitMethod; // 'equal', 'custom', 'by_items'
  
  @HiveField(9)
  final String pickupAddress;
  
  @HiveField(10)
  final double pickupLatitude;
  
  @HiveField(11)
  final double pickupLongitude;
  
  @HiveField(12)
  final String deliveryAddress;
  
  @HiveField(13)
  final double deliveryLatitude;
  
  @HiveField(14)
  final double deliveryLongitude;
  
  @HiveField(15)
  final BookingStatus status;
  
  @HiveField(16)
  final DateTime createdAt;
  
  @HiveField(17)
  final DateTime updatedAt;
  
  @HiveField(18)
  final String? notes;

  const GroupBooking({
    required this.id,
    required this.organizerId,
    required this.title,
    this.description = '',
    required this.serviceType,
    required this.scheduledAt,
    this.participants = const [],
    this.totalCost = 0.0,
    this.costSplitMethod = 'equal',
    required this.pickupAddress,
    required this.pickupLatitude,
    required this.pickupLongitude,
    required this.deliveryAddress,
    required this.deliveryLatitude,
    required this.deliveryLongitude,
    this.status = BookingStatus.scheduled,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
  });

  factory GroupBooking.fromJson(Map<String, dynamic> json) => _$GroupBookingFromJson(json);
  Map<String, dynamic> toJson() => _$GroupBookingToJson(this);

  int get participantCount => participants.length;
  double get costPerPerson => participantCount > 0 ? totalCost / participantCount : 0.0;
  bool get isConfirmed => participants.every((p) => p.hasConfirmed);

  @override
  List<Object?> get props => [
    id, organizerId, title, description, serviceType, scheduledAt, participants,
    totalCost, costSplitMethod, pickupAddress, pickupLatitude, pickupLongitude,
    deliveryAddress, deliveryLatitude, deliveryLongitude, status, createdAt,
    updatedAt, notes,
  ];
}

@HiveType(typeId: 87)
@JsonSerializable()
class GroupParticipant extends Equatable {
  @HiveField(0)
  final String userId;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String email;
  
  @HiveField(3)
  final String? phoneNumber;
  
  @HiveField(4)
  final bool hasConfirmed;
  
  @HiveField(5)
  final double shareAmount;
  
  @HiveField(6)
  final List<BookingItem> items;
  
  @HiveField(7)
  final DateTime joinedAt;
  
  @HiveField(8)
  final DateTime? confirmedAt;

  const GroupParticipant({
    required this.userId,
    required this.name,
    required this.email,
    this.phoneNumber,
    this.hasConfirmed = false,
    this.shareAmount = 0.0,
    this.items = const [],
    required this.joinedAt,
    this.confirmedAt,
  });

  factory GroupParticipant.fromJson(Map<String, dynamic> json) => _$GroupParticipantFromJson(json);
  Map<String, dynamic> toJson() => _$GroupParticipantToJson(this);

  @override
  List<Object?> get props => [
    userId, name, email, phoneNumber, hasConfirmed, shareAmount, items, joinedAt, confirmedAt,
  ];
}

// Booking configuration
class BookingConfig {
  static const Map<RecurrencePattern, Map<String, dynamic>> recurrenceConfigs = {
    RecurrencePattern.daily: {
      'name': 'Daily',
      'description': 'Repeat every day',
      'intervalDays': 1,
      'maxOccurrences': 365,
    },
    RecurrencePattern.weekly: {
      'name': 'Weekly',
      'description': 'Repeat every week',
      'intervalDays': 7,
      'maxOccurrences': 52,
    },
    RecurrencePattern.biweekly: {
      'name': 'Bi-weekly',
      'description': 'Repeat every 2 weeks',
      'intervalDays': 14,
      'maxOccurrences': 26,
    },
    RecurrencePattern.monthly: {
      'name': 'Monthly',
      'description': 'Repeat every month',
      'intervalDays': 30,
      'maxOccurrences': 12,
    },
    RecurrencePattern.custom: {
      'name': 'Custom',
      'description': 'Custom recurrence pattern',
      'intervalDays': 1,
      'maxOccurrences': 100,
    },
  };

  static const Map<String, List<String>> serviceTypeCategories = {
    'food': ['Restaurant', 'Fast Food', 'Cafe', 'Bakery', 'Desserts'],
    'grocery': ['Vegetables', 'Fruits', 'Dairy', 'Meat', 'Pantry'],
    'medicine': ['Prescription', 'OTC', 'Supplements', 'Medical Supplies'],
    'electronics': ['Mobile', 'Laptop', 'Accessories', 'Home Appliances'],
    'fashion': ['Clothing', 'Shoes', 'Accessories', 'Jewelry'],
    'home': ['Furniture', 'Decor', 'Kitchen', 'Cleaning'],
  };

  static const Map<String, String> costSplitMethods = {
    'equal': 'Split Equally',
    'custom': 'Custom Split',
    'by_items': 'Split by Items',
    'by_percentage': 'Split by Percentage',
  };

  static Map<String, dynamic>? getRecurrenceConfig(RecurrencePattern pattern) {
    return recurrenceConfigs[pattern];
  }

  static String getRecurrenceName(RecurrencePattern pattern) {
    return recurrenceConfigs[pattern]?['name'] ?? pattern.toString();
  }

  static List<String> getServiceCategories(String serviceType) {
    return serviceTypeCategories[serviceType] ?? [];
  }

  static String getCostSplitMethodName(String method) {
    return costSplitMethods[method] ?? method;
  }

  // Advance booking limits
  static const Map<String, int> advanceBookingLimits = {
    'food': 7, // 7 days
    'grocery': 30, // 30 days
    'medicine': 90, // 90 days
    'electronics': 180, // 6 months
    'fashion': 180, // 6 months
    'home': 180, // 6 months
  };

  static int getAdvanceBookingLimit(String serviceType) {
    return advanceBookingLimits[serviceType] ?? 30;
  }

  // Booking time slots
  static const List<String> timeSlots = [
    '09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '01:00 PM', '02:00 PM', '03:00 PM', '04:00 PM',
    '05:00 PM', '06:00 PM', '07:00 PM', '08:00 PM',
  ];

  // Group booking limits
  static const int maxGroupParticipants = 20;
  static const int minGroupParticipants = 2;
  static const double maxGroupBookingAmount = 50000.0;
}
