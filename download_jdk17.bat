@echo off
title Oracle JDK 17 Downloader
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                ORACLE JDK 17 DOWNLOADER                     ║
echo ║              Direct Download from Oracle                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Detect system architecture
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set ARCH=x64
    set JDK_URL=https://download.oracle.com/java/17/archive/jdk-17.0.9_windows-x64_bin.exe
    echo ✅ Detected: 64-bit Windows
) else (
    set ARCH=x86
    set JDK_URL=https://download.oracle.com/java/17/archive/jdk-17.0.9_windows-x86_bin.exe
    echo ✅ Detected: 32-bit Windows
)

set JDK_INSTALLER=jdk-17.0.9-windows-%ARCH%.exe

echo.
echo 🌐 Download URL: %JDK_URL%
echo 📁 Saving as: %JDK_INSTALLER%
echo.

echo 📥 Downloading Oracle JDK 17...
echo Please wait, this may take several minutes...
echo.

REM Method 1: PowerShell download with progress
powershell -Command "& {
    $ProgressPreference = 'Continue'
    [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
    try {
        Write-Host 'Starting download...' -ForegroundColor Green
        Invoke-WebRequest -Uri '%JDK_URL%' -OutFile '%JDK_INSTALLER%' -UseBasicParsing
        Write-Host 'Download completed successfully!' -ForegroundColor Green
    } catch {
        Write-Host 'Download failed:' $_.Exception.Message -ForegroundColor Red
        exit 1
    }
}"

if %errorlevel% neq 0 (
    echo.
    echo ❌ PowerShell download failed. Trying alternative method...
    echo.
    
    REM Method 2: Using curl (if available)
    curl --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo 🔄 Using curl to download...
        curl -L -o "%JDK_INSTALLER%" "%JDK_URL%"
    ) else (
        echo.
        echo ❌ Automatic download failed.
        echo.
        echo 🌐 Please download manually:
        echo 1. Open: %JDK_URL%
        echo 2. Save as: %JDK_INSTALLER%
        echo 3. Run this script again
        echo.
        start "" "%JDK_URL%"
        pause
        exit /b 1
    )
)

REM Verify download
if exist "%JDK_INSTALLER%" (
    echo.
    echo ✅ JDK 17 downloaded successfully!
    
    REM Get file size
    for %%A in ("%JDK_INSTALLER%") do set FILE_SIZE=%%~zA
    echo 📊 File size: %FILE_SIZE% bytes
    
    REM Verify it's a valid executable
    if %FILE_SIZE% LSS 100000000 (
        echo ⚠️  Warning: File size seems too small. Download may be incomplete.
        echo Expected size: ~150MB+
        pause
    )
    
    echo.
    echo 🚀 Starting installation...
    echo Please follow the installation wizard.
    echo.
    echo ⚠️  IMPORTANT: Install to default location:
    echo    C:\Program Files\Java\jdk-17
    echo.
    pause
    
    REM Run installer
    start /wait "%JDK_INSTALLER%"
    
    echo.
    echo ✅ Installation completed!
    echo.
    echo 🔧 Configuring environment variables...
    
    REM Set JAVA_HOME
    setx JAVA_HOME "C:\Program Files\Java\jdk-17" /M
    echo ✅ JAVA_HOME set to: C:\Program Files\Java\jdk-17
    
    REM Add to PATH
    for /f "tokens=2*" %%A in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set CURRENT_PATH=%%B
    echo %CURRENT_PATH% | find "C:\Program Files\Java\jdk-17\bin" >nul
    if %errorlevel% neq 0 (
        setx PATH "%CURRENT_PATH%;C:\Program Files\Java\jdk-17\bin" /M
        echo ✅ Added Java to system PATH
    ) else (
        echo ✅ Java already in PATH
    )
    
    echo.
    echo 🧹 Cleaning up installer...
    del "%JDK_INSTALLER%"
    
    echo.
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                    INSTALLATION COMPLETE                    ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo ✅ Oracle JDK 17 installed successfully!
    echo ✅ Environment variables configured
    echo.
    echo 📋 Next steps:
    echo 1. Close this window
    echo 2. Open new Command Prompt
    echo 3. Run: java -version
    echo 4. Run: flutter doctor
    echo 5. Run: flutter clean ^&^& flutter pub get
    echo.
    
) else (
    echo.
    echo ❌ Download failed or file not found.
    echo.
    echo 🔄 Manual download options:
    echo.
    echo Option 1 - Oracle Official:
    echo https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html
    echo.
    echo Option 2 - Direct Link:
    echo %JDK_URL%
    echo.
    echo Option 3 - OpenJDK Alternative:
    echo https://adoptium.net/temurin/releases/?version=17
    echo.
    start "" "https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html"
)

pause