// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SalesAnalyticsAdapter extends TypeAdapter<SalesAnalytics> {
  @override
  final int typeId = 30;

  @override
  SalesAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SalesAnalytics(
      sellerId: fields[0] as String,
      date: fields[1] as DateTime,
      totalRevenue: fields[2] as double,
      totalOrders: fields[3] as int,
      averageOrderValue: fields[4] as double,
      newCustomers: fields[5] as int,
      returningCustomers: fields[6] as int,
      conversionRate: fields[7] as double,
      categoryRevenue: (fields[8] as Map).cast<String, double>(),
      categoryOrders: (fields[9] as Map).cast<String, int>(),
    );
  }

  @override
  void write(BinaryWriter writer, SalesAnalytics obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.sellerId)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.totalRevenue)
      ..writeByte(3)
      ..write(obj.totalOrders)
      ..writeByte(4)
      ..write(obj.averageOrderValue)
      ..writeByte(5)
      ..write(obj.newCustomers)
      ..writeByte(6)
      ..write(obj.returningCustomers)
      ..writeByte(7)
      ..write(obj.conversionRate)
      ..writeByte(8)
      ..write(obj.categoryRevenue)
      ..writeByte(9)
      ..write(obj.categoryOrders);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SalesAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PerformanceMetricsAdapter extends TypeAdapter<PerformanceMetrics> {
  @override
  final int typeId = 31;

  @override
  PerformanceMetrics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PerformanceMetrics(
      sellerId: fields[0] as String,
      date: fields[1] as DateTime,
      averageRating: fields[2] as double,
      totalReviews: fields[3] as int,
      responseTime: fields[4] as double,
      fulfillmentRate: fields[5] as double,
      onTimeDeliveryRate: fields[6] as double,
      totalViews: fields[7] as int,
      totalClicks: fields[8] as int,
      clickThroughRate: fields[9] as double,
      topProducts: (fields[10] as Map).cast<String, int>(),
    );
  }

  @override
  void write(BinaryWriter writer, PerformanceMetrics obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.sellerId)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.averageRating)
      ..writeByte(3)
      ..write(obj.totalReviews)
      ..writeByte(4)
      ..write(obj.responseTime)
      ..writeByte(5)
      ..write(obj.fulfillmentRate)
      ..writeByte(6)
      ..write(obj.onTimeDeliveryRate)
      ..writeByte(7)
      ..write(obj.totalViews)
      ..writeByte(8)
      ..write(obj.totalClicks)
      ..writeByte(9)
      ..write(obj.clickThroughRate)
      ..writeByte(10)
      ..write(obj.topProducts);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PerformanceMetricsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomerInsightsAdapter extends TypeAdapter<CustomerInsights> {
  @override
  final int typeId = 32;

  @override
  CustomerInsights read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomerInsights(
      sellerId: fields[0] as String,
      date: fields[1] as DateTime,
      ageGroups: (fields[2] as Map).cast<String, int>(),
      genderDistribution: (fields[3] as Map).cast<String, int>(),
      locationDistribution: (fields[4] as Map).cast<String, int>(),
      orderFrequency: (fields[5] as Map).cast<int, int>(),
      customerRetentionRate: fields[6] as double,
      customerLifetimeValue: fields[7] as double,
      topCustomerSegments: (fields[8] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, CustomerInsights obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.sellerId)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.ageGroups)
      ..writeByte(3)
      ..write(obj.genderDistribution)
      ..writeByte(4)
      ..write(obj.locationDistribution)
      ..writeByte(5)
      ..write(obj.orderFrequency)
      ..writeByte(6)
      ..write(obj.customerRetentionRate)
      ..writeByte(7)
      ..write(obj.customerLifetimeValue)
      ..writeByte(8)
      ..write(obj.topCustomerSegments);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerInsightsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RevenueBreakdownAdapter extends TypeAdapter<RevenueBreakdown> {
  @override
  final int typeId = 33;

  @override
  RevenueBreakdown read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RevenueBreakdown(
      sellerId: fields[0] as String,
      date: fields[1] as DateTime,
      grossRevenue: fields[2] as double,
      platformFees: fields[3] as double,
      deliveryCharges: fields[4] as double,
      taxes: fields[5] as double,
      discounts: fields[6] as double,
      netRevenue: fields[7] as double,
      hourlyRevenue: (fields[8] as Map).cast<String, double>(),
      dailyRevenue: (fields[9] as Map).cast<String, double>(),
    );
  }

  @override
  void write(BinaryWriter writer, RevenueBreakdown obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.sellerId)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.grossRevenue)
      ..writeByte(3)
      ..write(obj.platformFees)
      ..writeByte(4)
      ..write(obj.deliveryCharges)
      ..writeByte(5)
      ..write(obj.taxes)
      ..writeByte(6)
      ..write(obj.discounts)
      ..writeByte(7)
      ..write(obj.netRevenue)
      ..writeByte(8)
      ..write(obj.hourlyRevenue)
      ..writeByte(9)
      ..write(obj.dailyRevenue);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RevenueBreakdownAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CompetitorAnalysisAdapter extends TypeAdapter<CompetitorAnalysis> {
  @override
  final int typeId = 34;

  @override
  CompetitorAnalysis read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CompetitorAnalysis(
      sellerId: fields[0] as String,
      date: fields[1] as DateTime,
      marketShare: fields[2] as double,
      marketRanking: fields[3] as int,
      averageCompetitorRating: fields[4] as double,
      averageCompetitorPrice: fields[5] as double,
      categoryMarketShare: (fields[6] as Map).cast<String, double>(),
      competitorStrengths: (fields[7] as List).cast<String>(),
      improvementOpportunities: (fields[8] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, CompetitorAnalysis obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.sellerId)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.marketShare)
      ..writeByte(3)
      ..write(obj.marketRanking)
      ..writeByte(4)
      ..write(obj.averageCompetitorRating)
      ..writeByte(5)
      ..write(obj.averageCompetitorPrice)
      ..writeByte(6)
      ..write(obj.categoryMarketShare)
      ..writeByte(7)
      ..write(obj.competitorStrengths)
      ..writeByte(8)
      ..write(obj.improvementOpportunities);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CompetitorAnalysisAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AnalyticsFilterAdapter extends TypeAdapter<AnalyticsFilter> {
  @override
  final int typeId = 36;

  @override
  AnalyticsFilter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AnalyticsFilter(
      timeRange: fields[0] as AnalyticsTimeRange,
      startDate: fields[1] as DateTime?,
      endDate: fields[2] as DateTime?,
      categories: (fields[3] as List).cast<String>(),
      products: (fields[4] as List).cast<String>(),
      location: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, AnalyticsFilter obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.timeRange)
      ..writeByte(1)
      ..write(obj.startDate)
      ..writeByte(2)
      ..write(obj.endDate)
      ..writeByte(3)
      ..write(obj.categories)
      ..writeByte(4)
      ..write(obj.products)
      ..writeByte(5)
      ..write(obj.location);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalyticsFilterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AnalyticsTimeRangeAdapter extends TypeAdapter<AnalyticsTimeRange> {
  @override
  final int typeId = 35;

  @override
  AnalyticsTimeRange read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AnalyticsTimeRange.today;
      case 1:
        return AnalyticsTimeRange.yesterday;
      case 2:
        return AnalyticsTimeRange.last7Days;
      case 3:
        return AnalyticsTimeRange.last30Days;
      case 4:
        return AnalyticsTimeRange.last90Days;
      case 5:
        return AnalyticsTimeRange.thisMonth;
      case 6:
        return AnalyticsTimeRange.lastMonth;
      case 7:
        return AnalyticsTimeRange.thisYear;
      case 8:
        return AnalyticsTimeRange.custom;
      default:
        return AnalyticsTimeRange.today;
    }
  }

  @override
  void write(BinaryWriter writer, AnalyticsTimeRange obj) {
    switch (obj) {
      case AnalyticsTimeRange.today:
        writer.writeByte(0);
        break;
      case AnalyticsTimeRange.yesterday:
        writer.writeByte(1);
        break;
      case AnalyticsTimeRange.last7Days:
        writer.writeByte(2);
        break;
      case AnalyticsTimeRange.last30Days:
        writer.writeByte(3);
        break;
      case AnalyticsTimeRange.last90Days:
        writer.writeByte(4);
        break;
      case AnalyticsTimeRange.thisMonth:
        writer.writeByte(5);
        break;
      case AnalyticsTimeRange.lastMonth:
        writer.writeByte(6);
        break;
      case AnalyticsTimeRange.thisYear:
        writer.writeByte(7);
        break;
      case AnalyticsTimeRange.custom:
        writer.writeByte(8);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalyticsTimeRangeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SalesAnalytics _$SalesAnalyticsFromJson(Map<String, dynamic> json) =>
    SalesAnalytics(
      sellerId: json['sellerId'] as String,
      date: DateTime.parse(json['date'] as String),
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      totalOrders: (json['totalOrders'] as num).toInt(),
      averageOrderValue: (json['averageOrderValue'] as num).toDouble(),
      newCustomers: (json['newCustomers'] as num).toInt(),
      returningCustomers: (json['returningCustomers'] as num).toInt(),
      conversionRate: (json['conversionRate'] as num).toDouble(),
      categoryRevenue: (json['categoryRevenue'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      categoryOrders: Map<String, int>.from(json['categoryOrders'] as Map),
    );

Map<String, dynamic> _$SalesAnalyticsToJson(SalesAnalytics instance) =>
    <String, dynamic>{
      'sellerId': instance.sellerId,
      'date': instance.date.toIso8601String(),
      'totalRevenue': instance.totalRevenue,
      'totalOrders': instance.totalOrders,
      'averageOrderValue': instance.averageOrderValue,
      'newCustomers': instance.newCustomers,
      'returningCustomers': instance.returningCustomers,
      'conversionRate': instance.conversionRate,
      'categoryRevenue': instance.categoryRevenue,
      'categoryOrders': instance.categoryOrders,
    };

PerformanceMetrics _$PerformanceMetricsFromJson(Map<String, dynamic> json) =>
    PerformanceMetrics(
      sellerId: json['sellerId'] as String,
      date: DateTime.parse(json['date'] as String),
      averageRating: (json['averageRating'] as num).toDouble(),
      totalReviews: (json['totalReviews'] as num).toInt(),
      responseTime: (json['responseTime'] as num).toDouble(),
      fulfillmentRate: (json['fulfillmentRate'] as num).toDouble(),
      onTimeDeliveryRate: (json['onTimeDeliveryRate'] as num).toDouble(),
      totalViews: (json['totalViews'] as num).toInt(),
      totalClicks: (json['totalClicks'] as num).toInt(),
      clickThroughRate: (json['clickThroughRate'] as num).toDouble(),
      topProducts: Map<String, int>.from(json['topProducts'] as Map),
    );

Map<String, dynamic> _$PerformanceMetricsToJson(PerformanceMetrics instance) =>
    <String, dynamic>{
      'sellerId': instance.sellerId,
      'date': instance.date.toIso8601String(),
      'averageRating': instance.averageRating,
      'totalReviews': instance.totalReviews,
      'responseTime': instance.responseTime,
      'fulfillmentRate': instance.fulfillmentRate,
      'onTimeDeliveryRate': instance.onTimeDeliveryRate,
      'totalViews': instance.totalViews,
      'totalClicks': instance.totalClicks,
      'clickThroughRate': instance.clickThroughRate,
      'topProducts': instance.topProducts,
    };

CustomerInsights _$CustomerInsightsFromJson(Map<String, dynamic> json) =>
    CustomerInsights(
      sellerId: json['sellerId'] as String,
      date: DateTime.parse(json['date'] as String),
      ageGroups: Map<String, int>.from(json['ageGroups'] as Map),
      genderDistribution:
          Map<String, int>.from(json['genderDistribution'] as Map),
      locationDistribution:
          Map<String, int>.from(json['locationDistribution'] as Map),
      orderFrequency: (json['orderFrequency'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(int.parse(k), (e as num).toInt()),
      ),
      customerRetentionRate: (json['customerRetentionRate'] as num).toDouble(),
      customerLifetimeValue: (json['customerLifetimeValue'] as num).toDouble(),
      topCustomerSegments: (json['topCustomerSegments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CustomerInsightsToJson(CustomerInsights instance) =>
    <String, dynamic>{
      'sellerId': instance.sellerId,
      'date': instance.date.toIso8601String(),
      'ageGroups': instance.ageGroups,
      'genderDistribution': instance.genderDistribution,
      'locationDistribution': instance.locationDistribution,
      'orderFrequency':
          instance.orderFrequency.map((k, e) => MapEntry(k.toString(), e)),
      'customerRetentionRate': instance.customerRetentionRate,
      'customerLifetimeValue': instance.customerLifetimeValue,
      'topCustomerSegments': instance.topCustomerSegments,
    };

RevenueBreakdown _$RevenueBreakdownFromJson(Map<String, dynamic> json) =>
    RevenueBreakdown(
      sellerId: json['sellerId'] as String,
      date: DateTime.parse(json['date'] as String),
      grossRevenue: (json['grossRevenue'] as num).toDouble(),
      platformFees: (json['platformFees'] as num).toDouble(),
      deliveryCharges: (json['deliveryCharges'] as num).toDouble(),
      taxes: (json['taxes'] as num).toDouble(),
      discounts: (json['discounts'] as num).toDouble(),
      netRevenue: (json['netRevenue'] as num).toDouble(),
      hourlyRevenue: (json['hourlyRevenue'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      dailyRevenue: (json['dailyRevenue'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$RevenueBreakdownToJson(RevenueBreakdown instance) =>
    <String, dynamic>{
      'sellerId': instance.sellerId,
      'date': instance.date.toIso8601String(),
      'grossRevenue': instance.grossRevenue,
      'platformFees': instance.platformFees,
      'deliveryCharges': instance.deliveryCharges,
      'taxes': instance.taxes,
      'discounts': instance.discounts,
      'netRevenue': instance.netRevenue,
      'hourlyRevenue': instance.hourlyRevenue,
      'dailyRevenue': instance.dailyRevenue,
    };

CompetitorAnalysis _$CompetitorAnalysisFromJson(Map<String, dynamic> json) =>
    CompetitorAnalysis(
      sellerId: json['sellerId'] as String,
      date: DateTime.parse(json['date'] as String),
      marketShare: (json['marketShare'] as num).toDouble(),
      marketRanking: (json['marketRanking'] as num).toInt(),
      averageCompetitorRating:
          (json['averageCompetitorRating'] as num).toDouble(),
      averageCompetitorPrice:
          (json['averageCompetitorPrice'] as num).toDouble(),
      categoryMarketShare:
          (json['categoryMarketShare'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      competitorStrengths: (json['competitorStrengths'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      improvementOpportunities:
          (json['improvementOpportunities'] as List<dynamic>)
              .map((e) => e as String)
              .toList(),
    );

Map<String, dynamic> _$CompetitorAnalysisToJson(CompetitorAnalysis instance) =>
    <String, dynamic>{
      'sellerId': instance.sellerId,
      'date': instance.date.toIso8601String(),
      'marketShare': instance.marketShare,
      'marketRanking': instance.marketRanking,
      'averageCompetitorRating': instance.averageCompetitorRating,
      'averageCompetitorPrice': instance.averageCompetitorPrice,
      'categoryMarketShare': instance.categoryMarketShare,
      'competitorStrengths': instance.competitorStrengths,
      'improvementOpportunities': instance.improvementOpportunities,
    };

AnalyticsFilter _$AnalyticsFilterFromJson(Map<String, dynamic> json) =>
    AnalyticsFilter(
      timeRange: $enumDecode(_$AnalyticsTimeRangeEnumMap, json['timeRange']),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      products: (json['products'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      location: json['location'] as String?,
    );

Map<String, dynamic> _$AnalyticsFilterToJson(AnalyticsFilter instance) =>
    <String, dynamic>{
      'timeRange': _$AnalyticsTimeRangeEnumMap[instance.timeRange]!,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'categories': instance.categories,
      'products': instance.products,
      'location': instance.location,
    };

const _$AnalyticsTimeRangeEnumMap = {
  AnalyticsTimeRange.today: 'today',
  AnalyticsTimeRange.yesterday: 'yesterday',
  AnalyticsTimeRange.last7Days: 'last7Days',
  AnalyticsTimeRange.last30Days: 'last30Days',
  AnalyticsTimeRange.last90Days: 'last90Days',
  AnalyticsTimeRange.thisMonth: 'thisMonth',
  AnalyticsTimeRange.lastMonth: 'lastMonth',
  AnalyticsTimeRange.thisYear: 'thisYear',
  AnalyticsTimeRange.custom: 'custom',
};

ChartDataPoint _$ChartDataPointFromJson(Map<String, dynamic> json) =>
    ChartDataPoint(
      label: json['label'] as String,
      value: (json['value'] as num).toDouble(),
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      category: json['category'] as String?,
    );

Map<String, dynamic> _$ChartDataPointToJson(ChartDataPoint instance) =>
    <String, dynamic>{
      'label': instance.label,
      'value': instance.value,
      'date': instance.date?.toIso8601String(),
      'category': instance.category,
    };

TrendData _$TrendDataFromJson(Map<String, dynamic> json) => TrendData(
      dataPoints: (json['dataPoints'] as List<dynamic>)
          .map((e) => ChartDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      trendPercentage: (json['trendPercentage'] as num).toDouble(),
      isPositiveTrend: json['isPositiveTrend'] as bool,
      trendDescription: json['trendDescription'] as String,
    );

Map<String, dynamic> _$TrendDataToJson(TrendData instance) => <String, dynamic>{
      'dataPoints': instance.dataPoints,
      'trendPercentage': instance.trendPercentage,
      'isPositiveTrend': instance.isPositiveTrend,
      'trendDescription': instance.trendDescription,
    };
