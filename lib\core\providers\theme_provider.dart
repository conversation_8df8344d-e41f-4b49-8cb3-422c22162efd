import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../database/hive_service.dart';
import '../theme/user_theme.dart';

final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  void _loadThemeMode() {
    final savedTheme = HiveService.getThemeMode();
    switch (savedTheme) {
      case 'light':
        state = ThemeMode.light;
        break;
      case 'dark':
        state = ThemeMode.dark;
        break;
      default:
        state = ThemeMode.system;
    }
  }

  void setThemeMode(ThemeMode mode) {
    state = mode;
    HiveService.saveThemeMode(mode.name);
  }

  void toggleTheme() {
    final newMode = state == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    setThemeMode(newMode);
  }
}

// User Theme Provider
final userThemeProvider = Provider<UserTheme>((ref) {
  return UserTheme();
});

// Location Service Provider (for Rider app)
final locationServiceProvider = Provider((ref) {
  // This would return a proper LocationService instance
  // For now, return a mock object to prevent errors
  return _MockLocationService();
});

class _MockLocationService {
  Future<void> startLocationTracking() async {
    // Mock implementation
  }

  Future<void> stopLocationTracking() async {
    // Mock implementation
  }
}

// Active Delivery Provider (for Rider app)
final activeDeliveryProvider = StateProvider<bool>((ref) {
  return false; // No active delivery by default
});

// Pending Orders Provider (for Seller app)
final pendingOrdersProvider = StateProvider<int>((ref) {
  return 0; // No pending orders by default
});

// Low Stock Items Provider (for Seller app)
final lowStockItemsProvider = StateProvider<int>((ref) {
  return 0; // No low stock items by default
});
