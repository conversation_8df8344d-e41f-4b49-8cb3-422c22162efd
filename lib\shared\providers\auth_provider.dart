import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_role.dart';
import '../../core/utils/app_logger.dart';

/// Authentication state model
class AuthState {
  final User? user;
  final UserRole? role;
  final bool isLoading;
  final String? error;

  const AuthState({this.user, this.role, this.isLoading = false, this.error});

  AuthState copyWith({
    User? user,
    UserRole? role,
    bool? isLoading,
    String? error,
  }) {
    return AuthState(
      user: user ?? this.user,
      role: role ?? this.role,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  bool get isAuthenticated => user != null;
  bool get hasRole => role != null;
}

/// Authentication service provider
class AuthService extends StateNotifier<AuthState> {
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;

  AuthService(this._auth, this._firestore) : super(const AuthState()) {
    // Listen to auth state changes
    _auth.authStateChanges().listen(_onAuthStateChanged);
  }

  /// Handle authentication state changes
  Future<void> _onAuthStateChanged(User? user) async {
    if (user == null) {
      state = const AuthState();
      return;
    }

    state = state.copyWith(user: user, isLoading: true);

    try {
      // Get user role from Firestore
      final userDoc = await _firestore.collection('users').doc(user.uid).get();

      UserRole? role;
      if (userDoc.exists) {
        final roleString = userDoc.data()?['role'] as String?;
        if (roleString != null) {
          role = UserRole.fromString(roleString);
        }
      }

      state = state.copyWith(
        user: user,
        role: role,
        isLoading: false,
        error: null,
      );

      AppLogger.auth(
        'User authenticated',
        data: {'uid': user.uid, 'email': user.email, 'role': role?.value},
      );
    } catch (e, stackTrace) {
      AppLogger.error('Error getting user role', e, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load user data',
      );
    }
  }

  /// Sign in with email and password
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        AppLogger.auth(
          'User signed in successfully',
          data: {'uid': credential.user!.uid, 'email': email},
        );
        return true;
      }
      return false;
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Sign in failed', e);
      state = state.copyWith(isLoading: false, error: _getAuthErrorMessage(e));
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected sign in error', e, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
      return false;
    }
  }

  /// Sign up with email and password
  Future<bool> signUpWithEmailAndPassword(
    String email,
    String password,
    UserRole role,
    Map<String, dynamic> additionalData,
  ) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Create user document in Firestore
        await _firestore.collection('users').doc(credential.user!.uid).set({
          'email': email,
          'role': role.value,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
          ...additionalData,
        });

        AppLogger.auth(
          'User signed up successfully',
          data: {
            'uid': credential.user!.uid,
            'email': email,
            'role': role.value,
          },
        );
        return true;
      }
      return false;
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Sign up failed', e);
      state = state.copyWith(isLoading: false, error: _getAuthErrorMessage(e));
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected sign up error', e, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      AppLogger.auth('User signed out');
    } catch (e, stackTrace) {
      AppLogger.error('Sign out failed', e, stackTrace);
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      AppLogger.auth('Password reset email sent', data: {'email': email});
      return true;
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Password reset failed', e);
      state = state.copyWith(error: _getAuthErrorMessage(e));
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected password reset error', e, stackTrace);
      state = state.copyWith(error: 'An unexpected error occurred');
      return false;
    }
  }

  /// Update user role
  Future<bool> updateUserRole(UserRole newRole) async {
    if (state.user == null) return false;

    try {
      await _firestore.collection('users').doc(state.user!.uid).update({
        'role': newRole.value,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      state = state.copyWith(role: newRole);
      AppLogger.auth(
        'User role updated',
        data: {'uid': state.user!.uid, 'newRole': newRole.value},
      );
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update user role', e, stackTrace);
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get user-friendly error message
  String _getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak.';
      case 'invalid-email':
        return 'Invalid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }
}

/// Firebase Auth provider
final firebaseAuthProvider = Provider<FirebaseAuth>((ref) {
  return FirebaseAuth.instance;
});

/// Firestore provider
final firestoreProvider = Provider<FirebaseFirestore>((ref) {
  return FirebaseFirestore.instance;
});

/// Auth service provider
final authServiceProvider = StateNotifierProvider<AuthService, AuthState>((
  ref,
) {
  final auth = ref.watch(firebaseAuthProvider);
  final firestore = ref.watch(firestoreProvider);
  return AuthService(auth, firestore);
});

/// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authServiceProvider).user;
});

/// Current user role provider
final currentUserRoleProvider = Provider<UserRole?>((ref) {
  return ref.watch(authServiceProvider).role;
});

/// Is authenticated provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authServiceProvider).isAuthenticated;
});
