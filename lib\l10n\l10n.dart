import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

/// Localization support for the Projek app
class AppLocalizations {
  AppLocalizations(this.locale);

  final Locale locale;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English (US)
    Locale('hi', 'IN'), // Hindi (India)
    Locale('ta', 'IN'), // Tamil (India)
    Locale('te', 'IN'), // Telugu (India)
    Locale('kn', 'IN'), // Kannada (India)
    Locale('ml', 'IN'), // Malayalam (India)
    Locale('bn', 'IN'), // Bengali (India)
    Locale('gu', 'IN'), // Gujarati (India)
    Locale('mr', 'IN'), // Marathi (India)
    Locale('pa', 'IN'), // Punjabi (India)
  ];

  // Common strings
  String get appName => _localizedValues[locale.languageCode]?['app_name'] ?? 'Projek';
  String get welcome => _localizedValues[locale.languageCode]?['welcome'] ?? 'Welcome';
  String get login => _localizedValues[locale.languageCode]?['login'] ?? 'Login';
  String get register => _localizedValues[locale.languageCode]?['register'] ?? 'Register';
  String get logout => _localizedValues[locale.languageCode]?['logout'] ?? 'Logout';
  String get profile => _localizedValues[locale.languageCode]?['profile'] ?? 'Profile';
  String get settings => _localizedValues[locale.languageCode]?['settings'] ?? 'Settings';
  String get help => _localizedValues[locale.languageCode]?['help'] ?? 'Help';
  String get about => _localizedValues[locale.languageCode]?['about'] ?? 'About';
  String get cancel => _localizedValues[locale.languageCode]?['cancel'] ?? 'Cancel';
  String get confirm => _localizedValues[locale.languageCode]?['confirm'] ?? 'Confirm';
  String get save => _localizedValues[locale.languageCode]?['save'] ?? 'Save';
  String get delete => _localizedValues[locale.languageCode]?['delete'] ?? 'Delete';
  String get edit => _localizedValues[locale.languageCode]?['edit'] ?? 'Edit';
  String get search => _localizedValues[locale.languageCode]?['search'] ?? 'Search';
  String get loading => _localizedValues[locale.languageCode]?['loading'] ?? 'Loading...';
  String get error => _localizedValues[locale.languageCode]?['error'] ?? 'Error';
  String get success => _localizedValues[locale.languageCode]?['success'] ?? 'Success';
  String get retry => _localizedValues[locale.languageCode]?['retry'] ?? 'Retry';

  // Navigation
  String get home => _localizedValues[locale.languageCode]?['home'] ?? 'Home';
  String get wallet => _localizedValues[locale.languageCode]?['wallet'] ?? 'Wallet';
  String get marketplace => _localizedValues[locale.languageCode]?['marketplace'] ?? 'Marketplace';
  String get services => _localizedValues[locale.languageCode]?['services'] ?? 'Services';

  // Wallet
  String get balance => _localizedValues[locale.languageCode]?['balance'] ?? 'Balance';
  String get addMoney => _localizedValues[locale.languageCode]?['add_money'] ?? 'Add Money';
  String get sendMoney => _localizedValues[locale.languageCode]?['send_money'] ?? 'Send Money';
  String get transactionHistory => _localizedValues[locale.languageCode]?['transaction_history'] ?? 'Transaction History';

  // Marketplace
  String get categories => _localizedValues[locale.languageCode]?['categories'] ?? 'Categories';
  String get products => _localizedValues[locale.languageCode]?['products'] ?? 'Products';
  String get cart => _localizedValues[locale.languageCode]?['cart'] ?? 'Cart';
  String get checkout => _localizedValues[locale.languageCode]?['checkout'] ?? 'Checkout';
  String get orders => _localizedValues[locale.languageCode]?['orders'] ?? 'Orders';

  // Services
  String get bookService => _localizedValues[locale.languageCode]?['book_service'] ?? 'Book Service';
  String get serviceProvider => _localizedValues[locale.languageCode]?['service_provider'] ?? 'Service Provider';
  String get booking => _localizedValues[locale.languageCode]?['booking'] ?? 'Booking';

  // Localized values map
  static const Map<String, Map<String, String>> _localizedValues = {
    'en': {
      'app_name': 'Projek',
      'welcome': 'Welcome',
      'login': 'Login',
      'register': 'Register',
      'logout': 'Logout',
      'profile': 'Profile',
      'settings': 'Settings',
      'help': 'Help',
      'about': 'About',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'search': 'Search',
      'loading': 'Loading...',
      'error': 'Error',
      'success': 'Success',
      'retry': 'Retry',
      'home': 'Home',
      'wallet': 'Wallet',
      'marketplace': 'Marketplace',
      'services': 'Services',
      'balance': 'Balance',
      'add_money': 'Add Money',
      'send_money': 'Send Money',
      'transaction_history': 'Transaction History',
      'categories': 'Categories',
      'products': 'Products',
      'cart': 'Cart',
      'checkout': 'Checkout',
      'orders': 'Orders',
      'book_service': 'Book Service',
      'service_provider': 'Service Provider',
      'booking': 'Booking',
    },
    'hi': {
      'app_name': 'प्रोजेक',
      'welcome': 'स्वागत है',
      'login': 'लॉगिन',
      'register': 'रजिस्टर',
      'logout': 'लॉगआउट',
      'profile': 'प्रोफाइल',
      'settings': 'सेटिंग्स',
      'help': 'सहायता',
      'about': 'के बारे में',
      'cancel': 'रद्द करें',
      'confirm': 'पुष्टि करें',
      'save': 'सेव करें',
      'delete': 'डिलीट करें',
      'edit': 'संपादित करें',
      'search': 'खोजें',
      'loading': 'लोड हो रहा है...',
      'error': 'त्रुटि',
      'success': 'सफलता',
      'retry': 'पुनः प्रयास',
      'home': 'होम',
      'wallet': 'वॉलेट',
      'marketplace': 'मार्केटप्लेस',
      'services': 'सेवाएं',
      'balance': 'बैलेंस',
      'add_money': 'पैसे जोड़ें',
      'send_money': 'पैसे भेजें',
      'transaction_history': 'लेनदेन इतिहास',
      'categories': 'श्रेणियां',
      'products': 'उत्पाद',
      'cart': 'कार्ट',
      'checkout': 'चेकआउट',
      'orders': 'ऑर्डर',
      'book_service': 'सेवा बुक करें',
      'service_provider': 'सेवा प्रदाता',
      'booking': 'बुकिंग',
    },
  };
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLocalizations.supportedLocales
        .any((supportedLocale) => supportedLocale.languageCode == locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
