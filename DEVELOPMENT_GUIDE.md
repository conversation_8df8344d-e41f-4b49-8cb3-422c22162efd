# 📱 PROJEK USER APP - COMPLETE DEVELOPMENT GUIDE

## 🎯 OVERVIEW
This guide will help you set up complete development environment for Projek User app on your physical Android device with hot reload for real-time code editing.

## 📋 PREREQUISITES CHECKLIST
- [ ] Flutter SDK 3.8.0+ installed
- [ ] Android Studio or VS Code with Flutter extensions
- [ ] Physical Android device (V2130 detected)
- [ ] USB cable (data transfer capable)
- [ ] Windows 10/11 development machine

## 🔧 PART 1: ANDROID DEVICE SETUP

### Step 1: Enable Developer Options
1. Open **Settings** on your Android device
2. Scroll to **About phone** or **About device**
3. Find **Build number** (may be under Software information)
4. **Tap Build number 7 times rapidly**
5. You'll see: "You are now a developer!"

### Step 2: Enable USB Debugging
1. Go back to **Settings**
2. Find **Developer options** (usually under System)
3. Toggle **Developer options ON**
4. Enable these options:
   - ✅ **USB debugging**
   - ✅ **Stay awake** (keeps screen on while charging)
   - ✅ **OEM unlocking** (if available)
   - ✅ **Disable adb authorization timeout**
   - ✅ **USB debugging (Security settings)**

### Step 3: Additional Developer Settings
Enable these for better development experience:
- ✅ **Don't keep activities** (for testing app lifecycle)
- ✅ **Show layout bounds** (for UI debugging)
- ✅ **Force GPU rendering**
- ✅ **Enable view attribute inspection**

## 🔌 PART 2: PHYSICAL CONNECTION SETUP

### Step 1: Connect Device
1. Use a **high-quality USB cable** (not just charging cable)
2. Connect your Android device to computer
3. On device, select **File Transfer (MTP)** mode
4. Allow **USB debugging** when popup appears
5. Check **Always allow from this computer**

### Step 2: Verify Connection
Your device should appear as: **V2130 (1397182984001HG)**

## 🛠️ PART 3: DEVELOPMENT ENVIRONMENT SETUP

### Step 1: Project Structure
```
Projek/
├── lib/
│   ├── main_user.dart     ← User app entry point
│   ├── main_rider.dart    ← Rider app entry point
│   ├── main_seller.dart   ← Seller app entry point
│   └── features/          ← Feature-based architecture
├── android/               ← Android configuration
├── firebase_options_user.dart ← Firebase config
└── pubspec.yaml          ← Dependencies
```

### Step 2: Firebase Configuration
- ✅ User App: projek-user (com.projek.user)
- ✅ Package: com.projek.user
- ✅ App ID: 1:625844282144:android:8b57ffd6c4eb27e1682d45

## ⚡ PART 4: HOT RELOAD DEVELOPMENT WORKFLOW

### Development Commands
| Command | Action | When to Use |
|---------|--------|-------------|
| `r` | Hot Reload | UI changes, widget updates |
| `R` | Hot Restart | New imports, main() changes |
| `h` | Help | Show all commands |
| `d` | Detach | Keep app running, detach debugger |
| `c` | Clear | Clear console output |
| `q` | Quit | Stop app and exit |

### What Triggers Hot Reload ✅
- Widget modifications
- UI layout changes
- Text and color updates
- Style modifications
- Method implementations
- Build method changes

### What Requires Hot Restart 🔄
- Adding new imports
- Changing main() function
- Global variable changes
- Static field modifications
- Enum definitions
- App initialization changes

## 🎯 PART 5: PROJEK USER APP FEATURES TO TEST

### 1. Authentication System
- Google Sign-in integration
- Firebase Authentication
- User profile management
- Session persistence

### 2. Marketplace Features
- Multi-vendor product browsing
- Category navigation
- Search and filtering
- Product details and reviews

### 3. ProjekCoin Wallet
- Digital wallet balance
- Transaction history
- Payment processing
- Reward system

### 4. Education Modules
- Course catalog
- Video streaming
- Progress tracking
- Certificates

### 5. Service Categories
- Food delivery
- Grocery shopping
- Electronics
- Clothing
- Beauty services
- Repairs and maintenance

### 6. Super App Integration
- Unified navigation
- Cross-service features
- Notification system
- Analytics tracking

## 🔧 PART 6: TROUBLESHOOTING GUIDE

### Common Issues and Solutions

#### Device Not Detected
```bash
# Check device connection
adb devices
flutter devices

# Reset ADB if needed
adb kill-server
adb start-server
```

#### Build Failures
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter pub deps
```

#### Hot Reload Not Working
- Check if you're modifying the right files
- Ensure no syntax errors
- Try hot restart (R) instead
- Check console for error messages

#### Performance Issues
- Use Profile mode for performance testing
- Enable GPU rendering in developer options
- Monitor memory usage
- Check for memory leaks

## 📱 PART 7: DEVELOPMENT BEST PRACTICES

### Code Organization
- Follow feature-based architecture
- Use proper state management (Riverpod)
- Implement clean architecture principles
- Write testable code

### Testing Strategy
- Unit tests for business logic
- Widget tests for UI components
- Integration tests for user flows
- Performance testing on physical device

### Debugging Techniques
- Use Flutter Inspector
- Add debug prints strategically
- Use breakpoints effectively
- Monitor network requests

### Performance Optimization
- Optimize image loading
- Implement lazy loading
- Use efficient state management
- Monitor build times

## 🚀 PART 8: ADVANCED DEVELOPMENT FEATURES

### Flutter DevTools
- Performance profiling
- Memory analysis
- Network monitoring
- Widget inspection

### Firebase Integration
- Real-time database
- Cloud messaging
- Analytics tracking
- Crash reporting

### State Management
- Riverpod providers
- State persistence
- Error handling
- Loading states

## 📊 PART 9: MONITORING AND ANALYTICS

### Development Metrics
- Build times
- Hot reload performance
- App startup time
- Memory usage

### User Experience Testing
- Navigation flows
- Form submissions
- Payment processes
- Error scenarios

## 🎯 NEXT STEPS

1. **Set up development environment**
2. **Run initial app build**
3. **Test hot reload functionality**
4. **Explore app features**
5. **Start development workflow**
6. **Implement new features**
7. **Test on physical device**
8. **Optimize performance**

---

**Ready to start development? Use the provided scripts and follow this guide step by step!**
