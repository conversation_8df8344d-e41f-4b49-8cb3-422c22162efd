import '../../domain/models/advanced_booking_models.dart';

class DemoBookingData {
  // Demo advanced bookings
  static List<AdvancedBooking> getDemoAdvancedBookings() {
    final now = DateTime.now();
    
    return [
      // Upcoming single booking
      AdvancedBooking(
        id: 'booking_1',
        userId: 'demo_user_1',
        type: BookingType.single,
        title: 'Weekly Grocery Shopping',
        description: 'Regular grocery items for the week',
        serviceType: 'grocery',
        scheduledAt: now.add(const Duration(hours: 4)),
        pickupAddress: 'BigBasket Store, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Home - A-123, Sector 15, Noida',
        deliveryLatitude: 28.5245,
        deliveryLongitude: 77.3825,
        items: [
          BookingItem(
            id: 'item_1',
            name: 'Basmati Rice',
            category: 'Grains',
            quantity: 2,
            unitPrice: 150.0,
          ),
          BookingItem(
            id: 'item_2',
            name: 'Fresh Vegetables',
            category: 'Vegetables',
            quantity: 1,
            unitPrice: 200.0,
          ),
          BookingItem(
            id: 'item_3',
            name: 'Dairy Products',
            category: 'Dairy',
            quantity: 1,
            unitPrice: 180.0,
          ),
        ],
        estimatedCost: 530.0,
        status: BookingStatus.confirmed,
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        notes: 'Please call before delivery',
      ),
      
      // Recurring booking
      AdvancedBooking(
        id: 'booking_2',
        userId: 'demo_user_1',
        type: BookingType.recurring,
        title: 'Daily Fresh Milk',
        description: 'Fresh milk delivery every morning',
        serviceType: 'grocery',
        scheduledAt: now.add(const Duration(days: 1)),
        endDate: now.add(const Duration(days: 30)),
        recurrencePattern: RecurrencePattern.daily,
        recurrenceConfig: {
          'time': '07:00',
          'skipWeekends': false,
        },
        pickupAddress: 'Mother Dairy, Sector 12, Noida',
        pickupLatitude: 28.5189,
        pickupLongitude: 77.3758,
        deliveryAddress: 'Home - A-123, Sector 15, Noida',
        deliveryLatitude: 28.5245,
        deliveryLongitude: 77.3825,
        items: [
          BookingItem(
            id: 'item_4',
            name: 'Fresh Milk',
            category: 'Dairy',
            quantity: 2,
            unitPrice: 30.0,
          ),
        ],
        estimatedCost: 60.0,
        status: BookingStatus.scheduled,
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 1)),
        generatedBookingIds: ['booking_2_1', 'booking_2_2', 'booking_2_3'],
      ),
      
      // Completed booking
      AdvancedBooking(
        id: 'booking_3',
        userId: 'demo_user_1',
        type: BookingType.single,
        title: 'Medicine Delivery',
        description: 'Prescription medicines from Apollo Pharmacy',
        serviceType: 'medicine',
        scheduledAt: now.subtract(const Duration(days: 2)),
        pickupAddress: 'Apollo Pharmacy, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Home - A-123, Sector 15, Noida',
        deliveryLatitude: 28.5245,
        deliveryLongitude: 77.3825,
        items: [
          BookingItem(
            id: 'item_5',
            name: 'Paracetamol',
            category: 'Medicine',
            quantity: 2,
            unitPrice: 25.0,
          ),
          BookingItem(
            id: 'item_6',
            name: 'Vitamin D3',
            category: 'Supplements',
            quantity: 1,
            unitPrice: 180.0,
          ),
        ],
        estimatedCost: 230.0,
        status: BookingStatus.completed,
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 2)),
        notes: 'Urgent delivery required',
      ),
      
      // Food booking
      AdvancedBooking(
        id: 'booking_4',
        userId: 'demo_user_1',
        type: BookingType.single,
        title: 'Lunch from Dominos',
        description: 'Pizza and sides for office lunch',
        serviceType: 'food',
        scheduledAt: now.add(const Duration(hours: 2)),
        pickupAddress: 'Dominos Pizza, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Office - Tower A, Sector 16, Noida',
        deliveryLatitude: 28.5289,
        deliveryLongitude: 77.3867,
        items: [
          BookingItem(
            id: 'item_7',
            name: 'Margherita Pizza (Large)',
            category: 'Pizza',
            quantity: 2,
            unitPrice: 450.0,
          ),
          BookingItem(
            id: 'item_8',
            name: 'Garlic Bread',
            category: 'Sides',
            quantity: 1,
            unitPrice: 120.0,
          ),
        ],
        estimatedCost: 1020.0,
        status: BookingStatus.scheduled,
        createdAt: now.subtract(const Duration(hours: 1)),
        updatedAt: now.subtract(const Duration(minutes: 30)),
      ),
      
      // Weekly recurring booking
      AdvancedBooking(
        id: 'booking_5',
        userId: 'demo_user_1',
        type: BookingType.recurring,
        title: 'Weekly House Cleaning Supplies',
        description: 'Cleaning supplies delivered every Sunday',
        serviceType: 'home',
        scheduledAt: now.add(const Duration(days: 3)),
        endDate: now.add(const Duration(days: 90)),
        recurrencePattern: RecurrencePattern.weekly,
        recurrenceConfig: {
          'dayOfWeek': 'Sunday',
          'time': '10:00',
        },
        pickupAddress: 'Big Bazaar, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Home - A-123, Sector 15, Noida',
        deliveryLatitude: 28.5245,
        deliveryLongitude: 77.3825,
        items: [
          BookingItem(
            id: 'item_9',
            name: 'Floor Cleaner',
            category: 'Cleaning',
            quantity: 2,
            unitPrice: 85.0,
          ),
          BookingItem(
            id: 'item_10',
            name: 'Toilet Paper',
            category: 'Hygiene',
            quantity: 4,
            unitPrice: 45.0,
          ),
        ],
        estimatedCost: 350.0,
        status: BookingStatus.scheduled,
        createdAt: now.subtract(const Duration(days: 7)),
        updatedAt: now.subtract(const Duration(days: 3)),
        generatedBookingIds: ['booking_5_1', 'booking_5_2', 'booking_5_3'],
      ),
    ];
  }

  // Demo booking templates
  static List<BookingTemplate> getDemoBookingTemplates() {
    final now = DateTime.now();
    
    return [
      BookingTemplate(
        id: 'template_1',
        userId: 'demo_user_1',
        name: 'Weekly Grocery Run',
        description: 'Standard weekly grocery shopping list',
        serviceType: 'grocery',
        pickupAddress: 'BigBasket Store, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Home - A-123, Sector 15, Noida',
        deliveryLatitude: 28.5245,
        deliveryLongitude: 77.3825,
        items: [
          BookingItem(
            id: 'template_item_1',
            name: 'Basmati Rice (5kg)',
            category: 'Grains',
            quantity: 1,
            unitPrice: 350.0,
          ),
          BookingItem(
            id: 'template_item_2',
            name: 'Mixed Vegetables',
            category: 'Vegetables',
            quantity: 1,
            unitPrice: 250.0,
          ),
          BookingItem(
            id: 'template_item_3',
            name: 'Dairy Bundle',
            category: 'Dairy',
            quantity: 1,
            unitPrice: 200.0,
          ),
          BookingItem(
            id: 'template_item_4',
            name: 'Cooking Oil (1L)',
            category: 'Pantry',
            quantity: 1,
            unitPrice: 120.0,
          ),
        ],
        preferences: {
          'deliveryTime': 'morning',
          'contactBeforeDelivery': true,
          'leaveAtDoor': false,
        },
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 10)),
        usageCount: 8,
      ),
      
      BookingTemplate(
        id: 'template_2',
        userId: 'demo_user_1',
        name: 'Office Lunch Order',
        description: 'Regular lunch order for office team',
        serviceType: 'food',
        pickupAddress: 'Subway, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Office - Tower A, Sector 16, Noida',
        deliveryLatitude: 28.5289,
        deliveryLongitude: 77.3867,
        items: [
          BookingItem(
            id: 'template_item_5',
            name: 'Veggie Delite Sub (6 inch)',
            category: 'Sandwich',
            quantity: 4,
            unitPrice: 180.0,
          ),
          BookingItem(
            id: 'template_item_6',
            name: 'Chicken Teriyaki Sub (6 inch)',
            category: 'Sandwich',
            quantity: 3,
            unitPrice: 220.0,
          ),
          BookingItem(
            id: 'template_item_7',
            name: 'Cookies',
            category: 'Dessert',
            quantity: 7,
            unitPrice: 40.0,
          ),
        ],
        preferences: {
          'deliveryTime': 'lunch',
          'contactBeforeDelivery': false,
          'specialInstructions': 'Deliver to reception',
        },
        createdAt: now.subtract(const Duration(days: 45)),
        updatedAt: now.subtract(const Duration(days: 5)),
        usageCount: 15,
      ),
      
      BookingTemplate(
        id: 'template_3',
        userId: 'demo_user_1',
        name: 'Monthly Medicine Refill',
        description: 'Regular prescription medicine refill',
        serviceType: 'medicine',
        pickupAddress: 'Apollo Pharmacy, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Home - A-123, Sector 15, Noida',
        deliveryLatitude: 28.5245,
        deliveryLongitude: 77.3825,
        items: [
          BookingItem(
            id: 'template_item_8',
            name: 'Blood Pressure Medicine',
            category: 'Prescription',
            quantity: 2,
            unitPrice: 150.0,
          ),
          BookingItem(
            id: 'template_item_9',
            name: 'Diabetes Medicine',
            category: 'Prescription',
            quantity: 1,
            unitPrice: 280.0,
          ),
          BookingItem(
            id: 'template_item_10',
            name: 'Multivitamins',
            category: 'Supplements',
            quantity: 1,
            unitPrice: 320.0,
          ),
        ],
        preferences: {
          'deliveryTime': 'evening',
          'contactBeforeDelivery': true,
          'prescriptionRequired': true,
        },
        createdAt: now.subtract(const Duration(days: 60)),
        updatedAt: now.subtract(const Duration(days: 15)),
        usageCount: 4,
      ),
    ];
  }

  // Demo group bookings
  static List<GroupBooking> getDemoGroupBookings() {
    final now = DateTime.now();
    
    return [
      GroupBooking(
        id: 'group_1',
        organizerId: 'demo_user_1',
        title: 'Office Party Food Order',
        description: 'Food for the monthly office celebration',
        serviceType: 'food',
        scheduledAt: now.add(const Duration(days: 2)),
        participants: [
          GroupParticipant(
            userId: 'participant_1',
            name: 'John Smith',
            email: '<EMAIL>',
            hasConfirmed: true,
            shareAmount: 400.0,
            joinedAt: now.subtract(const Duration(days: 3)),
            confirmedAt: now.subtract(const Duration(days: 2)),
          ),
          GroupParticipant(
            userId: 'participant_2',
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            hasConfirmed: true,
            shareAmount: 400.0,
            joinedAt: now.subtract(const Duration(days: 3)),
            confirmedAt: now.subtract(const Duration(days: 1)),
          ),
          GroupParticipant(
            userId: 'participant_3',
            name: 'Mike Wilson',
            email: '<EMAIL>',
            hasConfirmed: false,
            shareAmount: 400.0,
            joinedAt: now.subtract(const Duration(days: 2)),
          ),
          GroupParticipant(
            userId: 'participant_4',
            name: 'Emily Davis',
            email: '<EMAIL>',
            hasConfirmed: true,
            shareAmount: 400.0,
            joinedAt: now.subtract(const Duration(days: 2)),
            confirmedAt: now.subtract(const Duration(hours: 12)),
          ),
          GroupParticipant(
            userId: 'participant_5',
            name: 'David Brown',
            email: '<EMAIL>',
            hasConfirmed: false,
            shareAmount: 400.0,
            joinedAt: now.subtract(const Duration(days: 1)),
          ),
        ],
        totalCost: 2000.0,
        costSplitMethod: 'equal',
        pickupAddress: 'Pizza Hut, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Office - Tower A, Sector 16, Noida',
        deliveryLatitude: 28.5289,
        deliveryLongitude: 77.3867,
        status: BookingStatus.scheduled,
        createdAt: now.subtract(const Duration(days: 4)),
        updatedAt: now.subtract(const Duration(hours: 6)),
        notes: 'Please coordinate with reception for delivery',
      ),
      
      GroupBooking(
        id: 'group_2',
        organizerId: 'demo_user_1',
        title: 'Weekend Grocery Shopping',
        description: 'Bulk grocery shopping for the apartment complex',
        serviceType: 'grocery',
        scheduledAt: now.add(const Duration(days: 5)),
        participants: [
          GroupParticipant(
            userId: 'neighbor_1',
            name: 'Apartment 101',
            email: '<EMAIL>',
            hasConfirmed: true,
            shareAmount: 800.0,
            joinedAt: now.subtract(const Duration(days: 7)),
            confirmedAt: now.subtract(const Duration(days: 6)),
          ),
          GroupParticipant(
            userId: 'neighbor_2',
            name: 'Apartment 102',
            email: '<EMAIL>',
            hasConfirmed: true,
            shareAmount: 600.0,
            joinedAt: now.subtract(const Duration(days: 6)),
            confirmedAt: now.subtract(const Duration(days: 5)),
          ),
          GroupParticipant(
            userId: 'neighbor_3',
            name: 'Apartment 201',
            email: '<EMAIL>',
            hasConfirmed: false,
            shareAmount: 700.0,
            joinedAt: now.subtract(const Duration(days: 5)),
          ),
        ],
        totalCost: 2100.0,
        costSplitMethod: 'custom',
        pickupAddress: 'Metro Cash & Carry, Sector 18, Noida',
        pickupLatitude: 28.5355,
        pickupLongitude: 77.3910,
        deliveryAddress: 'Apartment Complex, Sector 15, Noida',
        deliveryLatitude: 28.5245,
        deliveryLongitude: 77.3825,
        status: BookingStatus.scheduled,
        createdAt: now.subtract(const Duration(days: 8)),
        updatedAt: now.subtract(const Duration(days: 2)),
        notes: 'Bulk order for multiple families',
      ),
    ];
  }

  // Get booking statistics
  static Map<String, dynamic> getBookingStatistics() {
    return {
      'totalBookings': 1247,
      'activeBookings': 89,
      'completedBookings': 1098,
      'cancelledBookings': 60,
      'recurringBookings': 45,
      'groupBookings': 23,
      'templatesCreated': 156,
      'templatesUsed': 892,
      'averageBookingValue': 485.50,
      'totalBookingValue': 605000.0,
      'mostPopularService': 'Food Delivery',
      'peakBookingTime': '12:00 PM - 2:00 PM',
      'averageDeliveryTime': 28, // minutes
      'customerSatisfactionRate': 94.5, // percentage
    };
  }

  // Get service type distribution
  static Map<String, Map<String, dynamic>> getServiceTypeDistribution() {
    return {
      'food': {
        'count': 456,
        'percentage': 36.6,
        'averageValue': 320.0,
        'averageDeliveryTime': 25,
      },
      'grocery': {
        'count': 389,
        'percentage': 31.2,
        'averageValue': 650.0,
        'averageDeliveryTime': 45,
      },
      'medicine': {
        'count': 198,
        'percentage': 15.9,
        'averageValue': 280.0,
        'averageDeliveryTime': 20,
      },
      'electronics': {
        'count': 89,
        'percentage': 7.1,
        'averageValue': 1250.0,
        'averageDeliveryTime': 60,
      },
      'fashion': {
        'count': 67,
        'percentage': 5.4,
        'averageValue': 890.0,
        'averageDeliveryTime': 40,
      },
      'home': {
        'count': 48,
        'percentage': 3.8,
        'averageValue': 420.0,
        'averageDeliveryTime': 35,
      },
    };
  }

  // Get booking trends over time
  static List<Map<String, dynamic>> getBookingTrends() {
    final now = DateTime.now();
    
    return List.generate(30, (index) {
      final date = now.subtract(Duration(days: 29 - index));
      final random = (index * 7 + 3) % 15; // Pseudo-random for demo
      
      return {
        'date': date,
        'totalBookings': 35 + random,
        'completedBookings': 32 + (random % 8),
        'cancelledBookings': 1 + (random % 3),
        'averageValue': 450.0 + (random * 25),
      };
    });
  }
}
