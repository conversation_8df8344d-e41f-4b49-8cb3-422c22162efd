import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/services/payment_service.dart';
import '../../domain/models/payment_method.dart';

// Payment service provider
final paymentServiceProvider = Provider<PaymentService>((ref) {
  return PaymentService();
});

// Available payment methods provider
final availablePaymentMethodsProvider = Provider<List<PaymentMethod>>((ref) {
  return IndianPaymentMethods.all;
});

// UPI apps provider
final availableUpiAppsProvider = FutureProvider<List<MockUpiApp>>((ref) async {
  final paymentService = ref.watch(paymentServiceProvider);
  return await paymentService.getAvailableUpiApps();
});

// Payment state provider
final paymentStateProvider =
    StateNotifierProvider<PaymentNotifier, PaymentState>((ref) {
      final paymentService = ref.watch(paymentServiceProvider);
      return PaymentNotifier(paymentService);
    });

// Payment state
class PaymentState {
  final bool isProcessing;
  final PaymentMethod? selectedMethod;
  final String? errorMessage;
  final PaymentTransaction? lastTransaction;

  const PaymentState({
    this.isProcessing = false,
    this.selectedMethod,
    this.errorMessage,
    this.lastTransaction,
  });

  PaymentState copyWith({
    bool? isProcessing,
    PaymentMethod? selectedMethod,
    String? errorMessage,
    PaymentTransaction? lastTransaction,
  }) {
    return PaymentState(
      isProcessing: isProcessing ?? this.isProcessing,
      selectedMethod: selectedMethod ?? this.selectedMethod,
      errorMessage: errorMessage ?? this.errorMessage,
      lastTransaction: lastTransaction ?? this.lastTransaction,
    );
  }
}

// Payment notifier
class PaymentNotifier extends StateNotifier<PaymentState> {
  final PaymentService _paymentService;

  PaymentNotifier(this._paymentService) : super(const PaymentState());

  void selectPaymentMethod(PaymentMethod method) {
    state = state.copyWith(selectedMethod: method, errorMessage: null);
  }

  Future<void> processPayment({
    required double amount,
    required String orderId,
    required String description,
    String customerName = 'Customer',
    String customerEmail = '<EMAIL>',
    String customerPhone = '**********',
  }) async {
    if (state.selectedMethod == null) {
      state = state.copyWith(errorMessage: 'Please select a payment method');
      return;
    }

    state = state.copyWith(isProcessing: true, errorMessage: null);

    try {
      switch (state.selectedMethod!.type) {
        case PaymentType.upi:
          await _processUpiPayment(amount, orderId, description);
          break;
        case PaymentType.card:
        case PaymentType.netBanking:
        case PaymentType.wallet:
          await _processRazorpayPayment(
            amount,
            orderId,
            description,
            customerName,
            customerEmail,
            customerPhone,
          );
          break;
        case PaymentType.cod:
          await _processCodPayment(orderId, amount);
          break;
        case PaymentType.emi:
          await _processEmiPayment(
            amount,
            orderId,
            description,
            customerName,
            customerEmail,
            customerPhone,
          );
          break;
      }
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: 'Payment failed: ${e.toString()}',
      );
    }
  }

  Future<void> _processUpiPayment(
    double amount,
    String orderId,
    String description,
  ) async {
    try {
      final response = await _paymentService.initiateUpiPayment(
        app: state.selectedMethod!.name,
        amount: amount,
        receiverUpiId: 'merchant@upi', // Replace with actual merchant UPI ID
        receiverName: 'Projek',
        transactionNote: description,
        transactionRefId: orderId,
      );

      if (response != null && response.status == MockUpiPaymentStatus.success) {
        final transaction = PaymentTransaction(
          id:
              response.transactionId ??
              'upi_${DateTime.now().millisecondsSinceEpoch}',
          orderId: orderId,
          paymentMethodId: state.selectedMethod!.id,
          amount: amount,
          status: PaymentStatus.success,
          createdAt: DateTime.now(),
          completedAt: DateTime.now(),
          transactionId: response.transactionId,
        );

        state = state.copyWith(
          isProcessing: false,
          lastTransaction: transaction,
        );
      } else {
        state = state.copyWith(
          isProcessing: false,
          errorMessage: 'UPI payment failed or cancelled',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: 'UPI payment error: ${e.toString()}',
      );
    }
  }

  Future<void> _processRazorpayPayment(
    double amount,
    String orderId,
    String description,
    String customerName,
    String customerEmail,
    String customerPhone,
  ) async {
    try {
      await _paymentService.initiateRazorpayPayment(
        amount: amount,
        orderId: orderId,
        description: description,
        customerName: customerName,
        customerEmail: customerEmail,
        customerPhone: customerPhone,
      );

      // Note: Actual success/failure will be handled by Razorpay callbacks
      // This is just to update the processing state
      state = state.copyWith(isProcessing: false);
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: 'Razorpay payment error: ${e.toString()}',
      );
    }
  }

  Future<void> _processCodPayment(String orderId, double amount) async {
    try {
      final transaction = await _paymentService.processCodPayment(
        orderId: orderId,
        amount: amount,
      );

      state = state.copyWith(isProcessing: false, lastTransaction: transaction);
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: 'COD processing error: ${e.toString()}',
      );
    }
  }

  Future<void> _processEmiPayment(
    double amount,
    String orderId,
    String description,
    String customerName,
    String customerEmail,
    String customerPhone,
  ) async {
    // EMI payments typically go through Razorpay with EMI options
    await _processRazorpayPayment(
      amount,
      orderId,
      description,
      customerName,
      customerEmail,
      customerPhone,
    );
  }

  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  void reset() {
    state = const PaymentState();
  }
}
