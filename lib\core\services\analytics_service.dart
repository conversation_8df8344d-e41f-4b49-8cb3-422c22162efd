import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import '../config/app_config.dart';
import '../config/environment.dart';
import '../utils/logger.dart';

class AnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  static final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  static Future<void> initialize() async {
    // Enable/disable based on environment
    await _analytics.setAnalyticsCollectionEnabled(
      EnvironmentConfig.enableAnalytics,
    );
    await _crashlytics.setCrashlyticsCollectionEnabled(
      EnvironmentConfig.enableCrashlytics,
    );

    // Set user properties
    await _analytics.setUserProperty(
      name: 'app_type',
      value: AppConfig.currentAppType.name,
    );

    await _analytics.setUserProperty(
      name: 'environment',
      value: EnvironmentConfig.currentEnvironment.name,
    );
  }

  // Screen tracking
  static Future<void> logScreenView(
    String screenName, {
    String? screenClass,
  }) async {
    if (!EnvironmentConfig.enableAnalytics) return;

    await _analytics.logScreenView(
      screenName: screenName,
      screenClass: screenClass ?? screenName,
    );
  }

  // Event tracking
  static Future<void> logEvent(
    String eventName,
    Map<String, dynamic>? parameters,
  ) async {
    if (!EnvironmentConfig.enableAnalytics) return;

    // Add app context to all events
    final enrichedParameters = <String, Object>{
      'app_type': AppConfig.currentAppType.name,
      'environment': EnvironmentConfig.currentEnvironment.name,
      ...?_convertToObjectMap(parameters),
    };

    await _analytics.logEvent(name: eventName, parameters: enrichedParameters);
  }

  // Helper method to convert Map<String, dynamic> to Map<String, Object>
  static Map<String, Object>? _convertToObjectMap(Map<String, dynamic>? input) {
    if (input == null) return null;
    return input.map((key, value) => MapEntry(key, value as Object));
  }

  // User interaction tracking
  static Future<void> logButtonTap(String buttonName, String screenName) async {
    await logEvent('button_tap', {
      'button_name': buttonName,
      'screen_name': screenName,
    });
  }

  static Future<void> logSearch(String searchTerm, {String? category}) async {
    await _analytics.logSearch(
      searchTerm: searchTerm,
      parameters: <String, Object>{if (category != null) 'category': category},
    );
  }

  // E-commerce tracking
  static Future<void> logPurchase({
    required String transactionId,
    required double value,
    required String currency,
    List<Map<String, dynamic>>? items,
  }) async {
    await _analytics.logPurchase(
      transactionId: transactionId,
      value: value,
      currency: currency,
      parameters: <String, Object>{if (items != null) 'items': items as Object},
    );
  }

  static Future<void> logAddToCart({
    required String itemId,
    required String itemName,
    required String category,
    required double value,
    required String currency,
  }) async {
    await logEvent('add_to_cart', {
      'item_id': itemId,
      'item_name': itemName,
      'item_category': category,
      'value': value,
      'currency': currency,
    });
  }

  static Future<void> logRemoveFromCart({
    required String itemId,
    required String itemName,
    required String category,
    required double value,
    required String currency,
  }) async {
    await logEvent('remove_from_cart', {
      'item_id': itemId,
      'item_name': itemName,
      'item_category': category,
      'value': value,
      'currency': currency,
    });
  }

  // User lifecycle tracking
  static Future<void> logSignUp(String method) async {
    await _analytics.logSignUp(signUpMethod: method);
  }

  static Future<void> logLogin(String method) async {
    await _analytics.logLogin(loginMethod: method);
  }

  // App-specific events
  static Future<void> logOrderPlaced({
    required String orderId,
    required double value,
    required String vendorId,
    required int itemCount,
  }) async {
    await logEvent('order_placed', {
      'order_id': orderId,
      'value': value,
      'vendor_id': vendorId,
      'item_count': itemCount,
    });
  }

  static Future<void> logOrderStatusChanged({
    required String orderId,
    required String oldStatus,
    required String newStatus,
  }) async {
    await logEvent('order_status_changed', {
      'order_id': orderId,
      'old_status': oldStatus,
      'new_status': newStatus,
    });
  }

  static Future<void> logWalletTopup({
    required double amount,
    required String paymentMethod,
  }) async {
    await logEvent('wallet_topup', {
      'amount': amount,
      'payment_method': paymentMethod,
    });
  }

  static Future<void> logServiceBooking({
    required String serviceType,
    required String providerId,
    required double amount,
  }) async {
    await logEvent('service_booking', {
      'service_type': serviceType,
      'provider_id': providerId,
      'amount': amount,
    });
  }

  // Rider-specific events
  static Future<void> logRiderOrderAccepted(String orderId) async {
    await logEvent('rider_order_accepted', {'order_id': orderId});
  }

  static Future<void> logRiderOrderCompleted({
    required String orderId,
    required double earnings,
    required int deliveryTime,
  }) async {
    await logEvent('rider_order_completed', {
      'order_id': orderId,
      'earnings': earnings,
      'delivery_time_minutes': deliveryTime,
    });
  }

  // Seller-specific events
  static Future<void> logProductAdded({
    required String productId,
    required String category,
    required double price,
  }) async {
    await logEvent('product_added', {
      'product_id': productId,
      'category': category,
      'price': price,
    });
  }

  static Future<void> logSellerOrderProcessed({
    required String orderId,
    required String action, // accepted, rejected, prepared, etc.
  }) async {
    await logEvent('seller_order_processed', {
      'order_id': orderId,
      'action': action,
    });
  }

  // User identification
  static Future<void> setUserId(String userId) async {
    await _analytics.setUserId(id: userId);
    await _crashlytics.setUserIdentifier(userId);
  }

  static Future<void> setUserProperty(String name, String value) async {
    await _analytics.setUserProperty(name: name, value: value);
  }

  // Error and crash tracking
  static Future<void> logError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? additionalData,
  }) async {
    if (!EnvironmentConfig.enableCrashlytics) return;

    // Log to Crashlytics
    await _crashlytics.recordError(
      error,
      stackTrace,
      information:
          additionalData?.entries.map((e) => '${e.key}: ${e.value}').toList() ??
          [],
    );

    // Also log as analytics event for non-fatal errors
    await logEvent('error_occurred', {
      'error_type': error.runtimeType.toString(),
      'error_message': error.toString(),
      ...?additionalData,
    });
  }

  static Future<void> logCustomCrash(
    String reason, {
    Map<String, dynamic>? additionalData,
  }) async {
    await _crashlytics.log(reason);

    if (additionalData != null) {
      for (final entry in additionalData.entries) {
        await _crashlytics.setCustomKey(entry.key, entry.value);
      }
    }
  }

  // Performance tracking
  static Future<void> logPerformance(
    String operation,
    Duration duration,
  ) async {
    await logEvent('performance_metric', {
      'operation': operation,
      'duration_ms': duration.inMilliseconds,
    });
  }

  // Debug methods
  static Future<void> logDebugEvent(
    String eventName,
    Map<String, dynamic> data,
  ) async {
    if (EnvironmentConfig.enableDetailedLogging) {
      Logger.analytics(eventName, data);
    }

    if (EnvironmentConfig.enableAnalytics) {
      await logEvent('debug_$eventName', data);
    }
  }
}
