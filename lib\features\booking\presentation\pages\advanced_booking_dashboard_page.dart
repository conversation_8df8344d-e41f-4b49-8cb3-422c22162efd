import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../widgets/advanced_booking_widgets.dart';
import '../../domain/models/advanced_booking_models.dart';
import '../../data/services/demo_booking_data.dart';

class AdvancedBookingDashboardPage extends ConsumerStatefulWidget {
  const AdvancedBookingDashboardPage({super.key});

  @override
  ConsumerState<AdvancedBookingDashboardPage> createState() =>
      _AdvancedBookingDashboardPageState();
}

class _AdvancedBookingDashboardPageState
    extends ConsumerState<AdvancedBookingDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<AdvancedBooking> _bookings = [];
  List<BookingTemplate> _templates = [];
  List<GroupBooking> _groupBookings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadBookingData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBookingData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // For demo purposes, use demo data
      _bookings = DemoBookingData.getDemoAdvancedBookings();
      _templates = DemoBookingData.getDemoBookingTemplates();
      _groupBookings = DemoBookingData.getDemoGroupBookings();
    } catch (e) {
      debugPrint('Error loading booking data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Advanced Bookings'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showCreateBookingOptions,
            icon: const Icon(Icons.add),
            tooltip: 'Create Booking',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Recurring'),
            Tab(text: 'Templates'),
            Tab(text: 'Groups'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildAllBookingsTab(),
                _buildRecurringBookingsTab(),
                _buildTemplatesTab(),
                _buildGroupBookingsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateBookingOptions,
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildAllBookingsTab() {
    final upcomingBookings = _bookings.where((b) => b.isUpcoming).toList();
    final pastBookings = _bookings.where((b) => !b.isUpcoming).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick Stats
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Total Bookings',
                      _bookings.length.toString(),
                      Icons.calendar_today,
                      AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatItem(
                      'Upcoming',
                      upcomingBookings.length.toString(),
                      Icons.schedule,
                      AppColors.warning,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatItem(
                      'Completed',
                      _bookings
                          .where((b) => b.status == BookingStatus.completed)
                          .length
                          .toString(),
                      Icons.check_circle,
                      AppColors.success,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Upcoming Bookings
          if (upcomingBookings.isNotEmpty) ...[
            Text('Upcoming Bookings', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 16),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: upcomingBookings.length,
              itemBuilder: (context, index) {
                final booking = upcomingBookings[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: AdvancedBookingCard(
                    booking: booking,
                    onTap: () => _viewBookingDetails(booking),
                    onCancel: () => _cancelBooking(booking),
                  ),
                );
              },
            ),

            const SizedBox(height: 24),
          ],

          // Past Bookings
          if (pastBookings.isNotEmpty) ...[
            Text('Past Bookings', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 16),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: pastBookings.length,
              itemBuilder: (context, index) {
                final booking = pastBookings[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: AdvancedBookingCard(
                    booking: booking,
                    onTap: () => _viewBookingDetails(booking),
                  ),
                );
              },
            ),
          ],

          if (_bookings.isEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 64,
                      color: AppColors.grey400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Bookings Yet',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Create your first booking to get started',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _showCreateBookingOptions,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Create Booking'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecurringBookingsTab() {
    final recurringBookings = _bookings.where((b) => b.isRecurring).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('Recurring Bookings', style: AppTextStyles.headlineMedium),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _createRecurringBooking,
                icon: const Icon(Icons.repeat),
                label: const Text('Create Recurring'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (recurringBookings.isEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(Icons.repeat, size: 64, color: AppColors.grey400),
                    const SizedBox(height: 16),
                    Text(
                      'No Recurring Bookings',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Set up recurring bookings for regular deliveries',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: recurringBookings.length,
              itemBuilder: (context, index) {
                final booking = recurringBookings[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: AdvancedBookingCard(
                    booking: booking,
                    onTap: () => _viewBookingDetails(booking),
                    onCancel: () => _cancelBooking(booking),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTemplatesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('Booking Templates', style: AppTextStyles.headlineMedium),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _createTemplate,
                icon: const Icon(Icons.bookmark_add),
                label: const Text('Create Template'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warning,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (_templates.isEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.bookmark_outline,
                      size: 64,
                      color: AppColors.grey400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Templates Yet',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Create templates for frequently used booking configurations',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _templates.length,
              itemBuilder: (context, index) {
                final template = _templates[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: BookingTemplateCard(
                    template: template,
                    onUse: () => _useTemplate(template),
                    onEdit: () => _editTemplate(template),
                    onDelete: () => _deleteTemplate(template),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGroupBookingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('Group Bookings', style: AppTextStyles.headlineMedium),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _createGroupBooking,
                icon: const Icon(Icons.group_add),
                label: const Text('Create Group'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (_groupBookings.isEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(Icons.group, size: 64, color: AppColors.grey400),
                    const SizedBox(height: 16),
                    Text(
                      'No Group Bookings',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Create group bookings to share costs with friends',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _groupBookings.length,
              itemBuilder: (context, index) {
                final groupBooking = _groupBookings[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: GroupBookingCard(
                    groupBooking: groupBooking,
                    onTap: () => _viewGroupBookingDetails(groupBooking),
                    onManage: () => _manageGroupBooking(groupBooking),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTextStyles.headlineMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _showCreateBookingOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Create New Booking', style: AppTextStyles.headlineSmall),
            const SizedBox(height: 16),

            ListTile(
              leading: const Icon(
                Icons.calendar_today,
                color: AppColors.primaryBlue,
              ),
              title: const Text('Single Booking'),
              subtitle: const Text('One-time delivery booking'),
              onTap: () {
                Navigator.pop(context);
                _createSingleBooking();
              },
            ),

            ListTile(
              leading: const Icon(Icons.repeat, color: AppColors.primaryBlue),
              title: const Text('Recurring Booking'),
              subtitle: const Text('Regular scheduled deliveries'),
              onTap: () {
                Navigator.pop(context);
                _createRecurringBooking();
              },
            ),

            ListTile(
              leading: const Icon(Icons.group, color: Colors.purple),
              title: const Text('Group Booking'),
              subtitle: const Text('Share booking with friends'),
              onTap: () {
                Navigator.pop(context);
                _createGroupBooking();
              },
            ),

            ListTile(
              leading: const Icon(Icons.bookmark, color: AppColors.warning),
              title: const Text('From Template'),
              subtitle: const Text('Use saved booking template'),
              onTap: () {
                Navigator.pop(context);
                _showTemplateSelection();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _createSingleBooking() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Single booking creation form would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _createRecurringBooking() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Recurring booking creation form would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _createGroupBooking() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Group booking creation form would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _createTemplate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Template creation form would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showTemplateSelection() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Template selection dialog would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _viewBookingDetails(AdvancedBooking booking) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing details for: ${booking.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _cancelBooking(AdvancedBooking booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: Text('Are you sure you want to cancel "${booking.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Cancelled: ${booking.title}'),
                  backgroundColor: AppColors.error,
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  void _useTemplate(BookingTemplate template) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Using template: ${template.name}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _editTemplate(BookingTemplate template) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Editing template: ${template.name}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _deleteTemplate(BookingTemplate template) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Template'),
        content: Text('Are you sure you want to delete "${template.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Deleted template: ${template.name}'),
                  backgroundColor: AppColors.error,
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _viewGroupBookingDetails(GroupBooking groupBooking) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing group booking: ${groupBooking.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _manageGroupBooking(GroupBooking groupBooking) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Managing group booking: ${groupBooking.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
