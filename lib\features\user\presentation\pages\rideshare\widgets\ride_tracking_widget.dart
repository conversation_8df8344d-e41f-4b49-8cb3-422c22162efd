import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../core/theme/app_colors.dart';

class RideTrackingWidget extends StatelessWidget {
  final String rideStatus;
  final Map<String, dynamic> driver;
  final String pickupLocation;
  final String dropLocation;
  final double? estimatedTime;
  final double? distance;

  const RideTrackingWidget({
    super.key,
    required this.rideStatus,
    required this.driver,
    required this.pickupLocation,
    required this.dropLocation,
    this.estimatedTime,
    this.distance,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status header
          _buildStatusHeader(),
          const SizedBox(height: 16),

          // Driver info
          _buildDriverInfo(),
          const SizedBox(height: 16),

          // Trip details
          _buildTripDetails(),
          const SizedBox(height: 16),

          // Progress indicator
          _buildProgressIndicator(),
        ],
      ),
    );
  }

  Widget _buildStatusHeader() {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (rideStatus.toLowerCase()) {
      case 'searching':
        statusColor = Colors.orange;
        statusIcon = Icons.search;
        statusText = 'Searching for driver...';
        break;
      case 'driver_assigned':
        statusColor = AppColors.userPrimary;
        statusIcon = Icons.person_pin;
        statusText = 'Driver assigned';
        break;
      case 'driver_arriving':
        statusColor = Colors.blue;
        statusIcon = Icons.directions_car;
        statusText = 'Driver is arriving';
        break;
      case 'in_progress':
        statusColor = AppColors.accentGreen;
        statusIcon = Icons.navigation;
        statusText = 'Trip in progress';
        break;
      case 'completed':
        statusColor = AppColors.success;
        statusIcon = Icons.check_circle;
        statusText = 'Trip completed';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.info;
        statusText = 'Unknown status';
    }

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(statusIcon, color: statusColor, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                statusText,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              if (estimatedTime != null)
                Text(
                  'ETA: ${estimatedTime!.toInt()} minutes',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDriverInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.grey[300],
            child: const Icon(Icons.person, color: Colors.grey, size: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  driver['name'] as String,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  '${driver['vehicleModel']} • ${driver['vehicleNumber']}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Icon(Icons.star, size: 14, color: Colors.amber[600]),
              const SizedBox(width: 4),
              Text(
                '${driver['rating']}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTripDetails() {
    return Column(
      children: [
        // Pickup location
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: AppColors.accentGreen,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Pickup',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    pickupLocation,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        // Connecting line
        Container(
          margin: const EdgeInsets.only(left: 6, top: 8, bottom: 8),
          child: Column(
            children: List.generate(
              3,
              (index) => Container(
                margin: const EdgeInsets.only(bottom: 4),
                width: 2,
                height: 6,
                color: Colors.grey[400],
              ),
            ),
          ),
        ),

        // Drop location
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: AppColors.error,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Drop',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    dropLocation,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    double progress;
    String progressText;

    switch (rideStatus.toLowerCase()) {
      case 'searching':
        progress = 0.1;
        progressText = 'Searching for driver';
        break;
      case 'driver_assigned':
        progress = 0.3;
        progressText = 'Driver assigned';
        break;
      case 'driver_arriving':
        progress = 0.5;
        progressText = 'Driver arriving';
        break;
      case 'in_progress':
        progress = 0.8;
        progressText = 'Trip in progress';
        break;
      case 'completed':
        progress = 1.0;
        progressText = 'Trip completed';
        break;
      default:
        progress = 0.0;
        progressText = 'Unknown';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              progressText,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.userPrimary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[200],
          valueColor: const AlwaysStoppedAnimation<Color>(
            AppColors.userPrimary,
          ),
          minHeight: 6,
        ),
        if (distance != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'Distance: ${distance!.toStringAsFixed(1)} km',
              style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
            ),
          ),
      ],
    );
  }
}
