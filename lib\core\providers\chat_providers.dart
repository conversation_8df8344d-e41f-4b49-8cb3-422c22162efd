import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/chat_service.dart';
import '../models/chat_models.dart';

// Provider for current user's chats
final userChatsProvider = StreamProvider<List<Chat>>((ref) {
  final currentUser = FirebaseAuth.instance.currentUser;
  if (currentUser == null) {
    return Stream.value([]);
  }
  return ChatService.getUserChats(currentUser.uid);
});

// Provider for messages in a specific chat
final chatMessagesProvider = StreamProvider.family<List<ChatMessage>, String>((ref, chatId) {
  return ChatService.getMessages(chatId);
});

// Provider for chat participants
final chatParticipantsProvider = FutureProvider.family<List<ChatParticipant>, String>((ref, chatId) {
  return ChatService.getChatParticipants(chatId);
});

// Provider for unread message count
final unreadMessageCountProvider = Provider<int>((ref) {
  final chatsAsync = ref.watch(userChatsProvider);
  final currentUser = FirebaseAuth.instance.currentUser;
  
  return chatsAsync.when(
    data: (chats) {
      if (currentUser == null) return 0;
      
      int totalUnread = 0;
      for (final chat in chats) {
        totalUnread += chat.getUnreadCount(currentUser.uid);
      }
      return totalUnread;
    },
    loading: () => 0,
    error: (_, __) => 0,
  );
});

// Provider for online status
final onlineStatusProvider = StateProvider<bool>((ref) => false);

// Provider for typing status in a chat
final typingStatusProvider = StateProvider.family<bool, String>((ref, chatId) => false);

// Provider for chat search query
final chatSearchProvider = StateProvider<String>((ref) => '');

// Provider for filtered chats based on search
final filteredChatsProvider = Provider<List<Chat>>((ref) {
  final chatsAsync = ref.watch(userChatsProvider);
  final searchQuery = ref.watch(chatSearchProvider);
  
  return chatsAsync.when(
    data: (chats) {
      if (searchQuery.isEmpty) return chats;
      
      return chats.where((chat) {
        return chat.lastMessage.toLowerCase().contains(searchQuery.toLowerCase()) ||
               chat.getChatTitle('', []).toLowerCase().contains(searchQuery.toLowerCase());
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Provider for chat statistics
final chatStatsProvider = Provider<ChatStats>((ref) {
  final chatsAsync = ref.watch(userChatsProvider);
  final currentUser = FirebaseAuth.instance.currentUser;
  
  return chatsAsync.when(
    data: (chats) {
      if (currentUser == null) {
        return ChatStats(
          totalChats: 0,
          unreadChats: 0,
          supportChats: 0,
          sellerChats: 0,
          riderChats: 0,
        );
      }
      
      int totalChats = chats.length;
      int unreadChats = 0;
      int supportChats = 0;
      int sellerChats = 0;
      int riderChats = 0;
      
      for (final chat in chats) {
        if (chat.getUnreadCount(currentUser.uid) > 0) {
          unreadChats++;
        }
        
        switch (chat.chatType) {
          case ChatType.support:
            supportChats++;
            break;
          case ChatType.userSeller:
            sellerChats++;
            break;
          case ChatType.userRider:
            riderChats++;
            break;
          default:
            break;
        }
      }
      
      return ChatStats(
        totalChats: totalChats,
        unreadChats: unreadChats,
        supportChats: supportChats,
        sellerChats: sellerChats,
        riderChats: riderChats,
      );
    },
    loading: () => ChatStats(
      totalChats: 0,
      unreadChats: 0,
      supportChats: 0,
      sellerChats: 0,
      riderChats: 0,
    ),
    error: (_, __) => ChatStats(
      totalChats: 0,
      unreadChats: 0,
      supportChats: 0,
      sellerChats: 0,
      riderChats: 0,
    ),
  );
});

class ChatStats {
  final int totalChats;
  final int unreadChats;
  final int supportChats;
  final int sellerChats;
  final int riderChats;

  ChatStats({
    required this.totalChats,
    required this.unreadChats,
    required this.supportChats,
    required this.sellerChats,
    required this.riderChats,
  });
}
