// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SearchQueryAdapter extends TypeAdapter<SearchQuery> {
  @override
  final int typeId = 52;

  @override
  SearchQuery read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SearchQuery(
      id: fields[0] as String,
      query: fields[1] as String,
      type: fields[2] as SearchType,
      categories: (fields[3] as List).cast<String>(),
      minPrice: fields[4] as double?,
      maxPrice: fields[5] as double?,
      minRating: fields[6] as double?,
      maxDistance: fields[7] as double?,
      location: fields[8] as String?,
      sortBy: fields[9] as SortOption,
      tags: (fields[10] as List).cast<String>(),
      isVoiceSearch: fields[11] as bool,
      createdAt: fields[12] as DateTime,
      filters: (fields[13] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, SearchQuery obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.query)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.categories)
      ..writeByte(4)
      ..write(obj.minPrice)
      ..writeByte(5)
      ..write(obj.maxPrice)
      ..writeByte(6)
      ..write(obj.minRating)
      ..writeByte(7)
      ..write(obj.maxDistance)
      ..writeByte(8)
      ..write(obj.location)
      ..writeByte(9)
      ..write(obj.sortBy)
      ..writeByte(10)
      ..write(obj.tags)
      ..writeByte(11)
      ..write(obj.isVoiceSearch)
      ..writeByte(12)
      ..write(obj.createdAt)
      ..writeByte(13)
      ..write(obj.filters);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchQueryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SearchResultAdapter extends TypeAdapter<SearchResult> {
  @override
  final int typeId = 53;

  @override
  SearchResult read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SearchResult(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String,
      type: fields[3] as SearchType,
      imageUrl: fields[4] as String?,
      price: fields[5] as double?,
      rating: fields[6] as double?,
      reviewCount: fields[7] as int?,
      category: fields[8] as String?,
      sellerId: fields[9] as String?,
      sellerName: fields[10] as String?,
      distance: fields[11] as double?,
      tags: (fields[12] as List).cast<String>(),
      relevanceScore: fields[13] as double,
      metadata: (fields[14] as Map).cast<String, dynamic>(),
      lastUpdated: fields[15] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, SearchResult obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.imageUrl)
      ..writeByte(5)
      ..write(obj.price)
      ..writeByte(6)
      ..write(obj.rating)
      ..writeByte(7)
      ..write(obj.reviewCount)
      ..writeByte(8)
      ..write(obj.category)
      ..writeByte(9)
      ..write(obj.sellerId)
      ..writeByte(10)
      ..write(obj.sellerName)
      ..writeByte(11)
      ..write(obj.distance)
      ..writeByte(12)
      ..write(obj.tags)
      ..writeByte(13)
      ..write(obj.relevanceScore)
      ..writeByte(14)
      ..write(obj.metadata)
      ..writeByte(15)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchResultAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SearchSuggestionAdapter extends TypeAdapter<SearchSuggestion> {
  @override
  final int typeId = 54;

  @override
  SearchSuggestion read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SearchSuggestion(
      id: fields[0] as String,
      text: fields[1] as String,
      type: fields[2] as SearchType,
      frequency: fields[3] as int,
      relevanceScore: fields[4] as double,
      category: fields[5] as String?,
      keywords: (fields[6] as List).cast<String>(),
      lastUsed: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SearchSuggestion obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.text)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.frequency)
      ..writeByte(4)
      ..write(obj.relevanceScore)
      ..writeByte(5)
      ..write(obj.category)
      ..writeByte(6)
      ..write(obj.keywords)
      ..writeByte(7)
      ..write(obj.lastUsed);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchSuggestionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SearchHistoryAdapter extends TypeAdapter<SearchHistory> {
  @override
  final int typeId = 55;

  @override
  SearchHistory read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SearchHistory(
      id: fields[0] as String,
      userId: fields[1] as String,
      query: fields[2] as SearchQuery,
      resultCount: fields[3] as int,
      searchedAt: fields[4] as DateTime,
      clickedResults: (fields[5] as List).cast<String>(),
      searchDuration: fields[6] as Duration,
    );
  }

  @override
  void write(BinaryWriter writer, SearchHistory obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.query)
      ..writeByte(3)
      ..write(obj.resultCount)
      ..writeByte(4)
      ..write(obj.searchedAt)
      ..writeByte(5)
      ..write(obj.clickedResults)
      ..writeByte(6)
      ..write(obj.searchDuration);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchHistoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SearchFilterAdapter extends TypeAdapter<SearchFilter> {
  @override
  final int typeId = 56;

  @override
  SearchFilter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SearchFilter(
      id: fields[0] as String,
      name: fields[1] as String,
      displayName: fields[2] as String,
      type: fields[3] as FilterType,
      options: (fields[4] as List).cast<FilterOption>(),
      isMultiSelect: fields[5] as bool,
      sortOrder: fields[6] as int,
    );
  }

  @override
  void write(BinaryWriter writer, SearchFilter obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.displayName)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.options)
      ..writeByte(5)
      ..write(obj.isMultiSelect)
      ..writeByte(6)
      ..write(obj.sortOrder);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchFilterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FilterOptionAdapter extends TypeAdapter<FilterOption> {
  @override
  final int typeId = 58;

  @override
  FilterOption read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FilterOption(
      id: fields[0] as String,
      value: fields[1] as String,
      displayName: fields[2] as String,
      count: fields[3] as int,
      isSelected: fields[4] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, FilterOption obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.value)
      ..writeByte(2)
      ..write(obj.displayName)
      ..writeByte(3)
      ..write(obj.count)
      ..writeByte(4)
      ..write(obj.isSelected);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FilterOptionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SearchAnalyticsAdapter extends TypeAdapter<SearchAnalytics> {
  @override
  final int typeId = 59;

  @override
  SearchAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SearchAnalytics(
      id: fields[0] as String,
      userId: fields[1] as String,
      topQueries: (fields[2] as Map).cast<String, int>(),
      topCategories: (fields[3] as Map).cast<String, int>(),
      topFilters: (fields[4] as Map).cast<String, int>(),
      averageSearchTime: fields[5] as double,
      totalSearches: fields[6] as int,
      clickThroughRate: fields[7] as double,
      lastUpdated: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SearchAnalytics obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.topQueries)
      ..writeByte(3)
      ..write(obj.topCategories)
      ..writeByte(4)
      ..write(obj.topFilters)
      ..writeByte(5)
      ..write(obj.averageSearchTime)
      ..writeByte(6)
      ..write(obj.totalSearches)
      ..writeByte(7)
      ..write(obj.clickThroughRate)
      ..writeByte(8)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SearchTypeAdapter extends TypeAdapter<SearchType> {
  @override
  final int typeId = 50;

  @override
  SearchType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return SearchType.products;
      case 1:
        return SearchType.services;
      case 2:
        return SearchType.sellers;
      case 3:
        return SearchType.categories;
      case 4:
        return SearchType.all;
      default:
        return SearchType.products;
    }
  }

  @override
  void write(BinaryWriter writer, SearchType obj) {
    switch (obj) {
      case SearchType.products:
        writer.writeByte(0);
        break;
      case SearchType.services:
        writer.writeByte(1);
        break;
      case SearchType.sellers:
        writer.writeByte(2);
        break;
      case SearchType.categories:
        writer.writeByte(3);
        break;
      case SearchType.all:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SortOptionAdapter extends TypeAdapter<SortOption> {
  @override
  final int typeId = 51;

  @override
  SortOption read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return SortOption.relevance;
      case 1:
        return SortOption.priceLowToHigh;
      case 2:
        return SortOption.priceHighToLow;
      case 3:
        return SortOption.rating;
      case 4:
        return SortOption.newest;
      case 5:
        return SortOption.popularity;
      case 6:
        return SortOption.distance;
      default:
        return SortOption.relevance;
    }
  }

  @override
  void write(BinaryWriter writer, SortOption obj) {
    switch (obj) {
      case SortOption.relevance:
        writer.writeByte(0);
        break;
      case SortOption.priceLowToHigh:
        writer.writeByte(1);
        break;
      case SortOption.priceHighToLow:
        writer.writeByte(2);
        break;
      case SortOption.rating:
        writer.writeByte(3);
        break;
      case SortOption.newest:
        writer.writeByte(4);
        break;
      case SortOption.popularity:
        writer.writeByte(5);
        break;
      case SortOption.distance:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SortOptionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FilterTypeAdapter extends TypeAdapter<FilterType> {
  @override
  final int typeId = 57;

  @override
  FilterType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return FilterType.checkbox;
      case 1:
        return FilterType.radio;
      case 2:
        return FilterType.range;
      case 3:
        return FilterType.dropdown;
      case 4:
        return FilterType.slider;
      default:
        return FilterType.checkbox;
    }
  }

  @override
  void write(BinaryWriter writer, FilterType obj) {
    switch (obj) {
      case FilterType.checkbox:
        writer.writeByte(0);
        break;
      case FilterType.radio:
        writer.writeByte(1);
        break;
      case FilterType.range:
        writer.writeByte(2);
        break;
      case FilterType.dropdown:
        writer.writeByte(3);
        break;
      case FilterType.slider:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FilterTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SearchQuery _$SearchQueryFromJson(Map<String, dynamic> json) => SearchQuery(
      id: json['id'] as String,
      query: json['query'] as String,
      type: $enumDecodeNullable(_$SearchTypeEnumMap, json['type']) ??
          SearchType.all,
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      minRating: (json['minRating'] as num?)?.toDouble(),
      maxDistance: (json['maxDistance'] as num?)?.toDouble(),
      location: json['location'] as String?,
      sortBy: $enumDecodeNullable(_$SortOptionEnumMap, json['sortBy']) ??
          SortOption.relevance,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      isVoiceSearch: json['isVoiceSearch'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      filters: json['filters'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$SearchQueryToJson(SearchQuery instance) =>
    <String, dynamic>{
      'id': instance.id,
      'query': instance.query,
      'type': _$SearchTypeEnumMap[instance.type]!,
      'categories': instance.categories,
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
      'minRating': instance.minRating,
      'maxDistance': instance.maxDistance,
      'location': instance.location,
      'sortBy': _$SortOptionEnumMap[instance.sortBy]!,
      'tags': instance.tags,
      'isVoiceSearch': instance.isVoiceSearch,
      'createdAt': instance.createdAt.toIso8601String(),
      'filters': instance.filters,
    };

const _$SearchTypeEnumMap = {
  SearchType.products: 'products',
  SearchType.services: 'services',
  SearchType.sellers: 'sellers',
  SearchType.categories: 'categories',
  SearchType.all: 'all',
};

const _$SortOptionEnumMap = {
  SortOption.relevance: 'relevance',
  SortOption.priceLowToHigh: 'priceLowToHigh',
  SortOption.priceHighToLow: 'priceHighToLow',
  SortOption.rating: 'rating',
  SortOption.newest: 'newest',
  SortOption.popularity: 'popularity',
  SortOption.distance: 'distance',
};

SearchResult _$SearchResultFromJson(Map<String, dynamic> json) => SearchResult(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$SearchTypeEnumMap, json['type']),
      imageUrl: json['imageUrl'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      rating: (json['rating'] as num?)?.toDouble(),
      reviewCount: (json['reviewCount'] as num?)?.toInt(),
      category: json['category'] as String?,
      sellerId: json['sellerId'] as String?,
      sellerName: json['sellerName'] as String?,
      distance: (json['distance'] as num?)?.toDouble(),
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      relevanceScore: (json['relevanceScore'] as num).toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$SearchResultToJson(SearchResult instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$SearchTypeEnumMap[instance.type]!,
      'imageUrl': instance.imageUrl,
      'price': instance.price,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'category': instance.category,
      'sellerId': instance.sellerId,
      'sellerName': instance.sellerName,
      'distance': instance.distance,
      'tags': instance.tags,
      'relevanceScore': instance.relevanceScore,
      'metadata': instance.metadata,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };

SearchSuggestion _$SearchSuggestionFromJson(Map<String, dynamic> json) =>
    SearchSuggestion(
      id: json['id'] as String,
      text: json['text'] as String,
      type: $enumDecode(_$SearchTypeEnumMap, json['type']),
      frequency: (json['frequency'] as num?)?.toInt() ?? 1,
      relevanceScore: (json['relevanceScore'] as num).toDouble(),
      category: json['category'] as String?,
      keywords: (json['keywords'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      lastUsed: DateTime.parse(json['lastUsed'] as String),
    );

Map<String, dynamic> _$SearchSuggestionToJson(SearchSuggestion instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'type': _$SearchTypeEnumMap[instance.type]!,
      'frequency': instance.frequency,
      'relevanceScore': instance.relevanceScore,
      'category': instance.category,
      'keywords': instance.keywords,
      'lastUsed': instance.lastUsed.toIso8601String(),
    };

SearchHistory _$SearchHistoryFromJson(Map<String, dynamic> json) =>
    SearchHistory(
      id: json['id'] as String,
      userId: json['userId'] as String,
      query: SearchQuery.fromJson(json['query'] as Map<String, dynamic>),
      resultCount: (json['resultCount'] as num).toInt(),
      searchedAt: DateTime.parse(json['searchedAt'] as String),
      clickedResults: (json['clickedResults'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      searchDuration:
          Duration(microseconds: (json['searchDuration'] as num).toInt()),
    );

Map<String, dynamic> _$SearchHistoryToJson(SearchHistory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'query': instance.query,
      'resultCount': instance.resultCount,
      'searchedAt': instance.searchedAt.toIso8601String(),
      'clickedResults': instance.clickedResults,
      'searchDuration': instance.searchDuration.inMicroseconds,
    };

SearchFilter _$SearchFilterFromJson(Map<String, dynamic> json) => SearchFilter(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      type: $enumDecode(_$FilterTypeEnumMap, json['type']),
      options: (json['options'] as List<dynamic>)
          .map((e) => FilterOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      isMultiSelect: json['isMultiSelect'] as bool? ?? false,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$SearchFilterToJson(SearchFilter instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'type': _$FilterTypeEnumMap[instance.type]!,
      'options': instance.options,
      'isMultiSelect': instance.isMultiSelect,
      'sortOrder': instance.sortOrder,
    };

const _$FilterTypeEnumMap = {
  FilterType.checkbox: 'checkbox',
  FilterType.radio: 'radio',
  FilterType.range: 'range',
  FilterType.dropdown: 'dropdown',
  FilterType.slider: 'slider',
};

FilterOption _$FilterOptionFromJson(Map<String, dynamic> json) => FilterOption(
      id: json['id'] as String,
      value: json['value'] as String,
      displayName: json['displayName'] as String,
      count: (json['count'] as num?)?.toInt() ?? 0,
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$FilterOptionToJson(FilterOption instance) =>
    <String, dynamic>{
      'id': instance.id,
      'value': instance.value,
      'displayName': instance.displayName,
      'count': instance.count,
      'isSelected': instance.isSelected,
    };

SearchAnalytics _$SearchAnalyticsFromJson(Map<String, dynamic> json) =>
    SearchAnalytics(
      id: json['id'] as String,
      userId: json['userId'] as String,
      topQueries: Map<String, int>.from(json['topQueries'] as Map),
      topCategories: Map<String, int>.from(json['topCategories'] as Map),
      topFilters: Map<String, int>.from(json['topFilters'] as Map),
      averageSearchTime: (json['averageSearchTime'] as num).toDouble(),
      totalSearches: (json['totalSearches'] as num).toInt(),
      clickThroughRate: (json['clickThroughRate'] as num).toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$SearchAnalyticsToJson(SearchAnalytics instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'topQueries': instance.topQueries,
      'topCategories': instance.topCategories,
      'topFilters': instance.topFilters,
      'averageSearchTime': instance.averageSearchTime,
      'totalSearches': instance.totalSearches,
      'clickThroughRate': instance.clickThroughRate,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
