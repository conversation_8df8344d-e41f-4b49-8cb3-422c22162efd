# Three-App Integration Guide: User → Seller → Rider Workflow

This document explains the complete order flow integration between the three apps in the Projek ecosystem.

## 🏗️ Architecture Overview

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  USER APP   │    │ SELLER APP  │    │  RIDER APP  │
│             │    │             │    │             │
│ • Browse    │    │ • Dashboard │    │ • Orders   │
│ • Order     │───▶│ • Orders    │───▶│ • Tracking │
│ • Track     │    │ • Products  │    │ • Earnings │
│ • Pay       │    │ • Earnings  │    │ • Profile  │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 📱 App Details

| App | Package Name | Main File | Theme Color | Purpose |
|-----|-------------|-----------|-------------|---------|
| **User** | `com.projek.user` | `lib/main.dart` | Green (#4CAF50) | Food ordering & booking |
| **Seller** | `com.projek.seller` | `lib/main_seller.dart` | Blue (#2196F3) | Restaurant management |
| **Rider** | `com.projek.rider` | `lib/main_rider.dart` | Green (#4CAF50) | Delivery management |

## 🔄 Complete Order Flow

### 1. **User Places Order** (User App)
```
User App Actions:
├── Browse restaurants and menu items
├── Add items to cart
├── Enter delivery address
├── Choose payment method
├── Place order
└── Receive order confirmation
```

**Order Status:** `pending`

### 2. **Seller Receives Order** (Seller App)
```
Seller App Actions:
├── Receive order notification
├── Review order details
├── Check item availability
├── Accept or reject order
└── Start preparation (if accepted)
```

**Order Status:** `pending` → `accepted` → `preparing`

### 3. **Seller Prepares Order** (Seller App)
```
Seller App Actions:
├── Update preparation status
├── Manage cooking time
├── Mark order as ready
└── Notify system for rider assignment
```

**Order Status:** `preparing` → `ready`

### 4. **Rider Assignment** (Seller App → Rider App)
```
Assignment Process:
├── Seller marks order as ready
├── System finds available riders
├── Order assigned to nearest rider
└── Rider receives pickup notification
```

**Order Status:** `ready` → `assigned`

### 5. **Rider Pickup & Delivery** (Rider App)
```
Rider App Actions:
├── Accept delivery assignment
├── Navigate to restaurant
├── Pick up order
├── Navigate to customer
├── Deliver order
└── Mark as delivered
```

**Order Status:** `assigned` → `pickedUp` → `inTransit` → `delivered`

### 6. **User Tracking** (User App)
```
User App Features:
├── Real-time order status updates
├── Estimated delivery time
├── Rider location tracking
├── Order history
└── Rating & feedback
```

## 🚀 Running Each App

### Commands:
```bash
# User App
flutter run --target lib/main.dart --flavor userDev

# Seller App  
flutter run --target lib/main_seller.dart --flavor sellerDev

# Rider App
flutter run --target lib/main_rider.dart --flavor riderDev
```

### Using Scripts:
```bash
# Windows
scripts\run_user.bat
scripts\run_seller.bat  
scripts\run_rider.bat

# Linux/Mac
./scripts/run_user.sh
./scripts/run_seller.sh
./scripts/run_rider.sh
```

## 📊 Key Features by App

### 🛒 **User App Features:**
- **Browse & Search:** Restaurant listings, menu items, categories
- **Ordering:** Cart management, checkout, payment integration
- **Tracking:** Real-time order status, rider location
- **History:** Past orders, favorites, reorder functionality
- **Profile:** Address management, payment methods, preferences

### 🏪 **Seller App Features:**
- **Dashboard:** Today's summary, quick stats, notifications
- **Order Management:** Accept/reject orders, preparation tracking
- **Product Management:** Add/edit menu items, inventory control
- **Earnings:** Revenue tracking, analytics, transaction history
- **Profile:** Restaurant details, business hours, settings

### 🏍️ **Rider App Features:**
- **Home:** Online/offline toggle, today's summary
- **Orders:** Active deliveries, pickup notifications
- **Earnings:** Daily earnings, trip history, payment tracking
- **Navigation:** GPS integration, route optimization
- **Profile:** Vehicle details, documents, ratings

## 🔗 Integration Points

### 1. **Order Status Synchronization**
- Real-time status updates across all apps
- Push notifications for status changes
- Consistent order tracking

### 2. **Location Services**
- Restaurant locations for user browsing
- Delivery address validation
- Rider GPS tracking for real-time updates

### 3. **Payment Integration**
- User payment processing
- Seller earnings calculation
- Rider payment distribution

### 4. **Communication System**
- In-app messaging between user-rider
- Order updates and notifications
- Customer support integration

## 🛠️ Technical Implementation

### Shared Models:
- `Order` - Complete order information
- `OrderItem` - Individual menu items
- `Customer`, `Seller`, `Rider` - User profiles
- `Address` - Location information
- `OrderStatus` - Status enumeration

### Data Flow:
```
User App ──┐
           ├──▶ Shared Database/API ◀──┐
Seller App ─┘                         ├── Rider App
                                      ┘
```

### Real-time Updates:
- WebSocket connections for live updates
- Push notifications for critical events
- Local caching for offline functionality

## 🎯 Demo Scenario

1. **User** opens User app, browses "Spice Garden Restaurant"
2. **User** adds "Chicken Biryani" and "Paneer Butter Masala" to cart
3. **User** places order for ₹500, pays online
4. **Seller** receives order notification in Seller app
5. **Seller** accepts order, starts preparation (30 min estimated)
6. **Seller** marks order as ready after cooking
7. **System** assigns order to available rider
8. **Rider** receives pickup notification in Rider app
9. **Rider** picks up order from restaurant
10. **Rider** delivers to customer address
11. **User** receives order, rates the experience

## 🔧 Development Tips

### Hot Reload:
- Each app supports independent hot reload
- Changes in shared models affect all apps
- Test integration by running multiple apps simultaneously

### Testing:
- Use demo data for development
- Test order flow across all three apps
- Verify real-time updates and notifications

### Deployment:
- Each app builds as separate APK
- Different package names prevent conflicts
- Can be published separately on app stores

## 📈 Future Enhancements

- **Analytics Dashboard:** Business intelligence for sellers
- **Route Optimization:** AI-powered delivery routing
- **Multi-language Support:** Localization for different regions
- **Advanced Notifications:** Rich push notifications with actions
- **Loyalty Programs:** Rewards and points system
- **Live Chat:** Real-time communication between all parties
