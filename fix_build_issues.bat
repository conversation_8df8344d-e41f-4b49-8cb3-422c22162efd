@echo off
title Projek Build Issues Fixer
color 0C

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  PROJEK BUILD ISSUES FIXER                  ║
echo ║              Comprehensive Problem Resolution                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Step 1: Diagnosing current issues...
echo Checking Flutter doctor status...
flutter doctor
echo.

echo 📱 Step 2: Checking device connection...
echo Verifying Android device connection...
adb devices
flutter devices
echo.

echo 🧹 Step 3: Deep cleaning build artifacts...
echo Removing all build artifacts and caches...
if exist "build" rmdir /s /q build
if exist ".dart_tool" rmdir /s /q .dart_tool
if exist "android\.gradle" rmdir /s /q android\.gradle
if exist "android\app\build" rmdir /s /q android\app\build
echo ✅ Build artifacts cleaned

echo.
echo 📦 Step 4: Clearing and resetting pub cache...
echo Clearing global pub cache...
flutter pub cache clean
echo ✅ Pub cache cleared

echo.
echo 🔧 Step 5: Setting up custom environment...
set PUB_CACHE=E:\Appdata\flutter_pub_cache
set FLUTTER_WEB_USE_SKIA=true
set FLUTTER_WEB_AUTO_DETECT=true
echo ✅ Custom environment configured

echo.
echo 📥 Step 6: Getting fresh dependencies...
echo Installing all dependencies from scratch...
flutter pub get
echo ✅ Dependencies installed

echo.
echo 🔨 Step 7: Running dependency analysis...
echo Analyzing dependency tree...
flutter pub deps
echo ✅ Dependencies analyzed

echo.
echo 🏗️ Step 8: Pre-building for Android platform...
echo Preparing Android build tools and cache...
flutter precache --android
echo ✅ Android precache completed

echo.
echo 🔄 Step 9: Resetting ADB connection...
echo Restarting ADB server...
adb kill-server
timeout /t 2 /nobreak >nul
adb start-server
echo ✅ ADB connection reset

echo.
echo 🎯 Step 10: Verifying fix...
echo Checking if issues are resolved...
flutter doctor --verbose
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    BUILD ENVIRONMENT FIXED!                 ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  ✅ Build artifacts cleaned                                 ║
echo ║  ✅ Pub cache reset                                         ║
echo ║  ✅ Dependencies reinstalled                                ║
echo ║  ✅ Android tools prepared                                  ║
echo ║  ✅ ADB connection reset                                    ║
echo ║                                                              ║
echo ║  🚀 NOW TRY RUNNING YOUR APP:                              ║
echo ║     .\dev_user_app.bat                                      ║
echo ║                                                              ║
echo ║  🔧 OR MANUAL COMMAND:                                      ║
echo ║     flutter run --target lib/main_user.dart \              ║
echo ║                 -d 1397182984001HG --hot                   ║
echo ║                                                              ║
echo ║  🆘 IF STILL HAVING ISSUES:                                ║
echo ║     1. Check USB cable connection                           ║
echo ║     2. Restart Android device                               ║
echo ║     3. Re-enable USB debugging                              ║
echo ║     4. Try different USB port                               ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo Press any key to continue...
pause >nul
