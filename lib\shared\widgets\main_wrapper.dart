import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../core/utils/app_router.dart';
// Removed cart, wishlist, and wallet provider imports

class MainWrapper extends ConsumerStatefulWidget {
  final Widget child;

  const MainWrapper({super.key, required this.child});

  @override
  ConsumerState<MainWrapper> createState() => _MainWrapperState();
}

class _MainWrapperState extends ConsumerState<MainWrapper> {
  int _selectedIndex = 0;

  // Core navigation items for User app (booking functionality focus)
  static const List<NavigationItem> _navigationItems = [
    NavigationItem(
      route: AppRoutes.home,
      icon: Icons.home_outlined,
      selectedIcon: Icons.home,
      label: 'Home',
    ),
    NavigationItem(
      route: AppRoutes.categories,
      icon: Icons.grid_view_outlined,
      selectedIcon: Icons.grid_view,
      label: 'Services',
    ),
    NavigationItem(
      route: AppRoutes.orders,
      icon: Icons.receipt_long_outlined,
      selectedIcon: Icons.receipt_long,
      label: 'Bookings',
    ),
    NavigationItem(
      route: AppRoutes.profile,
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
      label: 'Profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    // Removed cart, wishlist, and wallet providers

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: NavigationBar(
        selectedIndex: _selectedIndex,
        onDestinationSelected: _onDestinationSelected,
        destinations: _navigationItems.asMap().entries.map((entry) {
          final item = entry.value;

          Widget icon = Icon(item.icon);
          Widget selectedIcon = Icon(item.selectedIcon);

          // Removed badge logic for cart, wishlist, and wallet

          return NavigationDestination(
            icon: icon,
            selectedIcon: selectedIcon,
            label: item.label,
          );
        }).toList(),
      ),
    );
  }

  void _onDestinationSelected(int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      context.go(_navigationItems[index].route);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateSelectedIndex();
  }

  void _updateSelectedIndex() {
    final String location = GoRouterState.of(context).uri.path;
    for (int i = 0; i < _navigationItems.length; i++) {
      if (location.startsWith(_navigationItems[i].route)) {
        if (_selectedIndex != i) {
          setState(() {
            _selectedIndex = i;
          });
        }
        break;
      }
    }
  }
}

class NavigationItem {
  final String route;
  final IconData icon;
  final IconData selectedIcon;
  final String label;

  const NavigationItem({
    required this.route,
    required this.icon,
    required this.selectedIcon,
    required this.label,
  });
}
