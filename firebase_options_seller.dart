// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_seller.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptionsSeller.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptionsSeller {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'No Web API Key for this project',
    appId: '1:93751239001:web:your_web_app_id_here',
    messagingSenderId: '93751239001',
    projectId: 'projek-seller',
    authDomain: 'projek-seller.firebaseapp.com',
    storageBucket: 'projek-seller.appspot.com',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:93751239001:android:9f9270770c23fe0d60ac48',
    messagingSenderId: '93751239001',
    projectId: 'projek-seller',
    storageBucket: 'projek-seller.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:93751239001:ios:your_ios_app_id_here',
    messagingSenderId: '93751239001',
    projectId: 'projek-seller',
    storageBucket: 'projek-seller.appspot.com',
    iosBundleId: 'com.projek.seller',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:93751239001:macos:your_macos_app_id_here',
    messagingSenderId: '93751239001',
    projectId: 'projek-seller',
    storageBucket: 'projek-seller.appspot.com',
    iosBundleId: 'com.projek.seller',
  );
}
