#!/bin/bash

echo "🚀 Testing All Projek Apps"
echo "=========================="

# Test User App
echo "📱 Testing User App..."
flutter test --target=lib/main_user.dart
if [ $? -eq 0 ]; then
    echo "✅ User App tests passed"
else
    echo "❌ User App tests failed"
fi

# Test Rider App
echo "🚴 Testing Rider App..."
flutter test --target=lib/main_rider.dart
if [ $? -eq 0 ]; then
    echo "✅ Rider App tests passed"
else
    echo "❌ Rider App tests failed"
fi

# Test Seller App
echo "🏪 Testing Seller App..."
flutter test --target=lib/main_seller.dart
if [ $? -eq 0 ]; then
    echo "✅ Seller App tests passed"
else
    echo "❌ Seller App tests failed"
fi

echo "=========================="
echo "🎉 All app tests completed!"
