import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
// import '../../../core/services/analytics_service.dart'; // Commented out for now
import '../../domain/models/search_models.dart';

class AdvancedSearchService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const Uuid _uuid = Uuid();

  static const String _searchHistoryCollection = 'search_history';
  static const String _searchSuggestionsCollection = 'search_suggestions';
  static const String _searchAnalyticsCollection = 'search_analytics';
  static const String _productsCollection = 'products';
  static const String _servicesCollection = 'services';
  static const String _sellersCollection = 'sellers';

  // Perform advanced search
  static Future<List<SearchResult>> search({
    required SearchQuery query,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final startTime = DateTime.now();
      final results = <SearchResult>[];

      // Search based on type
      switch (query.type) {
        case SearchType.products:
          results.addAll(await _searchProducts(query, limit, offset));
          break;
        case SearchType.services:
          results.addAll(await _searchServices(query, limit, offset));
          break;
        case SearchType.sellers:
          results.addAll(await _searchSellers(query, limit, offset));
          break;
        case SearchType.categories:
          results.addAll(await _searchCategories(query, limit, offset));
          break;
        case SearchType.all:
          // Search all types and merge results
          final productResults = await _searchProducts(
            query,
            limit ~/ 3,
            offset,
          );
          final serviceResults = await _searchServices(
            query,
            limit ~/ 3,
            offset,
          );
          final sellerResults = await _searchSellers(query, limit ~/ 3, offset);

          results.addAll(productResults);
          results.addAll(serviceResults);
          results.addAll(sellerResults);
          break;
      }

      // Apply sorting
      _sortResults(results, query.sortBy);

      // Apply additional filters
      final filteredResults = _applyFilters(results, query);

      // Limit results
      final finalResults = filteredResults.take(limit).toList();

      // Save search history
      final searchDuration = DateTime.now().difference(startTime);
      await _saveSearchHistory(query, finalResults.length, searchDuration);

      // Update search analytics
      await _updateSearchAnalytics(query, finalResults.length);

      // Log search event
      // await AnalyticsService.logEvent('search_performed', {
      //   'query': query.query,
      //   'type': query.type.toString(),
      //   'result_count': finalResults.length,
      //   'is_voice_search': query.isVoiceSearch,
      //   'search_duration_ms': searchDuration.inMilliseconds,
      // });

      return finalResults;
    } catch (e) {
      debugPrint('❌ Error performing search: $e');
      return [];
    }
  }

  // Get search suggestions
  static Future<List<SearchSuggestion>> getSuggestions({
    required String query,
    SearchType? type,
    int limit = 10,
  }) async {
    try {
      if (query.isEmpty) return [];

      Query suggestionsQuery = _firestore
          .collection(_searchSuggestionsCollection)
          .where('text', isGreaterThanOrEqualTo: query.toLowerCase())
          .where('text', isLessThan: '${query.toLowerCase()}z')
          .orderBy('relevanceScore', descending: true)
          .orderBy('frequency', descending: true);

      if (type != null) {
        suggestionsQuery = suggestionsQuery.where(
          'type',
          isEqualTo: type.toString(),
        );
      }

      final snapshot = await suggestionsQuery.limit(limit).get();

      final suggestions = snapshot.docs.map((doc) {
        return SearchSuggestion.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();

      // Add AI-powered suggestions
      final aiSuggestions = await _generateAISuggestions(query, type);
      suggestions.addAll(aiSuggestions);

      // Remove duplicates and sort by relevance
      final uniqueSuggestions = <String, SearchSuggestion>{};
      for (final suggestion in suggestions) {
        if (!uniqueSuggestions.containsKey(suggestion.text) ||
            uniqueSuggestions[suggestion.text]!.relevanceScore <
                suggestion.relevanceScore) {
          uniqueSuggestions[suggestion.text] = suggestion;
        }
      }

      final finalSuggestions = uniqueSuggestions.values.toList()
        ..sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

      return finalSuggestions.take(limit).toList();
    } catch (e) {
      debugPrint('❌ Error getting suggestions: $e');
      return [];
    }
  }

  // Get search history
  static Future<List<SearchHistory>> getSearchHistory({
    String? userId,
    int limit = 20,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return [];

      final snapshot = await _firestore
          .collection(_searchHistoryCollection)
          .where('userId', isEqualTo: targetUserId)
          .orderBy('searchedAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        return SearchHistory.fromJson(doc.data());
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting search history: $e');
      return [];
    }
  }

  // Clear search history
  static Future<void> clearSearchHistory({String? userId}) async {
    try {
      final currentUser = _auth.currentUser;
      final targetUserId = userId ?? currentUser?.uid;

      if (targetUserId == null) return;

      final batch = _firestore.batch();
      final snapshot = await _firestore
          .collection(_searchHistoryCollection)
          .where('userId', isEqualTo: targetUserId)
          .get();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      debugPrint('❌ Error clearing search history: $e');
    }
  }

  // Get available filters for search type
  static List<SearchFilter> getAvailableFilters(SearchType type) {
    switch (type) {
      case SearchType.products:
        return _getProductFilters();
      case SearchType.services:
        return _getServiceFilters();
      case SearchType.sellers:
        return _getSellerFilters();
      case SearchType.all:
        return _getAllFilters();
      default:
        return [];
    }
  }

  // Voice search processing
  static Future<SearchQuery> processVoiceSearch(String voiceText) async {
    try {
      // Clean and process voice text
      final cleanedText = _cleanVoiceText(voiceText);

      // Extract search intent and parameters
      final searchIntent = await _extractSearchIntent(cleanedText);

      final query = SearchQuery(
        id: _uuid.v4(),
        query: searchIntent['query'] ?? cleanedText,
        type: _parseSearchType(searchIntent['type']),
        categories: searchIntent['categories'] ?? [],
        minPrice: searchIntent['minPrice'],
        maxPrice: searchIntent['maxPrice'],
        minRating: searchIntent['minRating'],
        location: searchIntent['location'],
        isVoiceSearch: true,
        createdAt: DateTime.now(),
        filters: searchIntent['filters'] ?? {},
      );

      return query;
    } catch (e) {
      debugPrint('❌ Error processing voice search: $e');
      return SearchQuery(
        id: _uuid.v4(),
        query: voiceText,
        isVoiceSearch: true,
        createdAt: DateTime.now(),
      );
    }
  }

  // Private helper methods
  static Future<List<SearchResult>> _searchProducts(
    SearchQuery query,
    int limit,
    int offset,
  ) async {
    try {
      Query productsQuery = _firestore.collection(_productsCollection);

      // Apply text search
      if (query.query.isNotEmpty) {
        productsQuery = productsQuery.where(
          'searchKeywords',
          arrayContainsAny: _generateKeywords(query.query),
        );
      }

      // Apply category filter
      if (query.categories.isNotEmpty) {
        productsQuery = productsQuery.where(
          'category',
          whereIn: query.categories,
        );
      }

      // Apply price filter
      if (query.minPrice != null) {
        productsQuery = productsQuery.where(
          'price',
          isGreaterThanOrEqualTo: query.minPrice,
        );
      }
      if (query.maxPrice != null) {
        productsQuery = productsQuery.where(
          'price',
          isLessThanOrEqualTo: query.maxPrice,
        );
      }

      final snapshot = await productsQuery.limit(limit).get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return SearchResult(
          id: doc.id,
          title: data['name'] ?? '',
          description: data['description'] ?? '',
          type: SearchType.products,
          imageUrl: data['imageUrl'],
          price: data['price']?.toDouble(),
          rating: data['rating']?.toDouble(),
          reviewCount: data['reviewCount'],
          category: data['category'],
          sellerId: data['sellerId'],
          sellerName: data['sellerName'],
          tags: List<String>.from(data['tags'] ?? []),
          relevanceScore: _calculateRelevanceScore(query.query, data),
          metadata: data,
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error searching products: $e');
      return [];
    }
  }

  static Future<List<SearchResult>> _searchServices(
    SearchQuery query,
    int limit,
    int offset,
  ) async {
    try {
      Query servicesQuery = _firestore.collection(_servicesCollection);

      if (query.query.isNotEmpty) {
        servicesQuery = servicesQuery.where(
          'searchKeywords',
          arrayContainsAny: _generateKeywords(query.query),
        );
      }

      if (query.categories.isNotEmpty) {
        servicesQuery = servicesQuery.where(
          'category',
          whereIn: query.categories,
        );
      }

      final snapshot = await servicesQuery.limit(limit).get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return SearchResult(
          id: doc.id,
          title: data['name'] ?? '',
          description: data['description'] ?? '',
          type: SearchType.services,
          imageUrl: data['imageUrl'],
          price: data['price']?.toDouble(),
          rating: data['rating']?.toDouble(),
          reviewCount: data['reviewCount'],
          category: data['category'],
          sellerId: data['providerId'],
          sellerName: data['providerName'],
          tags: List<String>.from(data['tags'] ?? []),
          relevanceScore: _calculateRelevanceScore(query.query, data),
          metadata: data,
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error searching services: $e');
      return [];
    }
  }

  static Future<List<SearchResult>> _searchSellers(
    SearchQuery query,
    int limit,
    int offset,
  ) async {
    try {
      Query sellersQuery = _firestore.collection(_sellersCollection);

      if (query.query.isNotEmpty) {
        sellersQuery = sellersQuery.where(
          'searchKeywords',
          arrayContainsAny: _generateKeywords(query.query),
        );
      }

      final snapshot = await sellersQuery.limit(limit).get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return SearchResult(
          id: doc.id,
          title: data['name'] ?? '',
          description: data['description'] ?? '',
          type: SearchType.sellers,
          imageUrl: data['profileImage'],
          rating: data['rating']?.toDouble(),
          reviewCount: data['reviewCount'],
          category: data['category'],
          sellerId: doc.id,
          sellerName: data['name'],
          tags: List<String>.from(data['specialties'] ?? []),
          relevanceScore: _calculateRelevanceScore(query.query, data),
          metadata: data,
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error searching sellers: $e');
      return [];
    }
  }

  static Future<List<SearchResult>> _searchCategories(
    SearchQuery query,
    int limit,
    int offset,
  ) async {
    // Implementation for category search
    return [];
  }

  static void _sortResults(List<SearchResult> results, SortOption sortBy) {
    switch (sortBy) {
      case SortOption.relevance:
        results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
        break;
      case SortOption.priceLowToHigh:
        results.sort((a, b) => (a.price ?? 0).compareTo(b.price ?? 0));
        break;
      case SortOption.priceHighToLow:
        results.sort((a, b) => (b.price ?? 0).compareTo(a.price ?? 0));
        break;
      case SortOption.rating:
        results.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));
        break;
      case SortOption.popularity:
        results.sort(
          (a, b) => (b.reviewCount ?? 0).compareTo(a.reviewCount ?? 0),
        );
        break;
      case SortOption.distance:
        results.sort((a, b) => (a.distance ?? 0).compareTo(b.distance ?? 0));
        break;
      case SortOption.newest:
        results.sort(
          (a, b) => (b.lastUpdated ?? DateTime.now()).compareTo(
            a.lastUpdated ?? DateTime.now(),
          ),
        );
        break;
    }
  }

  static List<SearchResult> _applyFilters(
    List<SearchResult> results,
    SearchQuery query,
  ) {
    var filteredResults = results;

    // Apply rating filter
    if (query.minRating != null) {
      filteredResults = filteredResults.where((result) {
        return (result.rating ?? 0) >= query.minRating!;
      }).toList();
    }

    // Apply distance filter
    if (query.maxDistance != null) {
      filteredResults = filteredResults.where((result) {
        return (result.distance ?? 0) <= query.maxDistance!;
      }).toList();
    }

    // Apply tag filters
    if (query.tags.isNotEmpty) {
      filteredResults = filteredResults.where((result) {
        return query.tags.any((tag) => result.tags.contains(tag));
      }).toList();
    }

    return filteredResults;
  }

  static List<String> _generateKeywords(String text) {
    final words = text.toLowerCase().split(' ');
    final keywords = <String>[];

    for (final word in words) {
      if (word.length > 2) {
        keywords.add(word);
        // Add partial matches
        for (int i = 3; i <= word.length; i++) {
          keywords.add(word.substring(0, i));
        }
      }
    }

    return keywords.toSet().toList();
  }

  static double _calculateRelevanceScore(
    String query,
    Map<String, dynamic> data,
  ) {
    double score = 0.0;
    final queryWords = query.toLowerCase().split(' ');

    // Title match
    final title = (data['name'] ?? '').toString().toLowerCase();
    for (final word in queryWords) {
      if (title.contains(word)) {
        score += 10.0;
      }
    }

    // Description match
    final description = (data['description'] ?? '').toString().toLowerCase();
    for (final word in queryWords) {
      if (description.contains(word)) {
        score += 5.0;
      }
    }

    // Tags match
    final tags = List<String>.from(data['tags'] ?? []);
    for (final tag in tags) {
      for (final word in queryWords) {
        if (tag.toLowerCase().contains(word)) {
          score += 3.0;
        }
      }
    }

    // Boost by rating and popularity
    score += (data['rating'] ?? 0.0) * 2.0;
    score += (data['reviewCount'] ?? 0) * 0.1;

    return score;
  }

  static Future<void> _saveSearchHistory(
    SearchQuery query,
    int resultCount,
    Duration searchDuration,
  ) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final history = SearchHistory(
        id: _uuid.v4(),
        userId: currentUser.uid,
        query: query,
        resultCount: resultCount,
        searchedAt: DateTime.now(),
        searchDuration: searchDuration,
      );

      await _firestore
          .collection(_searchHistoryCollection)
          .doc(history.id)
          .set(history.toJson());
    } catch (e) {
      debugPrint('❌ Error saving search history: $e');
    }
  }

  static Future<void> _updateSearchAnalytics(
    SearchQuery query,
    int resultCount,
  ) async {
    // Implementation for updating search analytics
  }

  static Future<List<SearchSuggestion>> _generateAISuggestions(
    String query,
    SearchType? type,
  ) async {
    // Simulate AI-powered suggestions
    final suggestions = <SearchSuggestion>[];

    // Add some demo AI suggestions
    if (query.length >= 2) {
      final aiSuggestions = [
        '${query} near me',
        '${query} delivery',
        '${query} best rated',
        '${query} cheap',
        '${query} premium',
      ];

      for (int i = 0; i < aiSuggestions.length; i++) {
        suggestions.add(
          SearchSuggestion(
            id: _uuid.v4(),
            text: aiSuggestions[i],
            type: type ?? SearchType.all,
            relevanceScore: 5.0 - i,
            lastUsed: DateTime.now(),
          ),
        );
      }
    }

    return suggestions;
  }

  static String _cleanVoiceText(String voiceText) {
    return voiceText.toLowerCase().replaceAll(RegExp(r'[^\w\s]'), '').trim();
  }

  static Future<Map<String, dynamic>> _extractSearchIntent(String text) async {
    // Simple intent extraction - in a real app, this would use NLP
    final intent = <String, dynamic>{};

    // Extract price mentions
    final priceRegex = RegExp(r'under (\d+)|below (\d+)|less than (\d+)');
    final priceMatch = priceRegex.firstMatch(text);
    if (priceMatch != null) {
      final price = double.tryParse(
        priceMatch.group(1) ??
            priceMatch.group(2) ??
            priceMatch.group(3) ??
            '0',
      );
      intent['maxPrice'] = price;
    }

    // Extract location mentions
    if (text.contains('near me') || text.contains('nearby')) {
      intent['location'] = 'current';
    }

    // Extract rating mentions
    if (text.contains('best rated') || text.contains('top rated')) {
      intent['minRating'] = 4.0;
    }

    // Clean query
    String cleanQuery = text
        .replaceAll(RegExp(r'under \d+|below \d+|less than \d+'), '')
        .replaceAll('near me', '')
        .replaceAll('nearby', '')
        .replaceAll('best rated', '')
        .replaceAll('top rated', '')
        .trim();

    intent['query'] = cleanQuery;

    return intent;
  }

  static SearchType _parseSearchType(String? type) {
    if (type == null) return SearchType.all;

    switch (type.toLowerCase()) {
      case 'product':
      case 'products':
        return SearchType.products;
      case 'service':
      case 'services':
        return SearchType.services;
      case 'seller':
      case 'sellers':
      case 'shop':
      case 'shops':
        return SearchType.sellers;
      default:
        return SearchType.all;
    }
  }

  static List<SearchFilter> _getProductFilters() {
    return [
      SearchFilter(
        id: 'category',
        name: 'category',
        displayName: 'Category',
        type: FilterType.checkbox,
        isMultiSelect: true,
        options: [
          FilterOption(
            id: 'food',
            value: 'food',
            displayName: 'Food & Dining',
            count: 150,
          ),
          FilterOption(
            id: 'grocery',
            value: 'grocery',
            displayName: 'Groceries',
            count: 120,
          ),
          FilterOption(
            id: 'electronics',
            value: 'electronics',
            displayName: 'Electronics',
            count: 80,
          ),
          FilterOption(
            id: 'fashion',
            value: 'fashion',
            displayName: 'Fashion',
            count: 95,
          ),
        ],
      ),
      SearchFilter(
        id: 'price',
        name: 'price',
        displayName: 'Price Range',
        type: FilterType.range,
        options: [],
      ),
      SearchFilter(
        id: 'rating',
        name: 'rating',
        displayName: 'Rating',
        type: FilterType.radio,
        options: [
          FilterOption(
            id: '4+',
            value: '4',
            displayName: '4+ Stars',
            count: 200,
          ),
          FilterOption(
            id: '3+',
            value: '3',
            displayName: '3+ Stars',
            count: 350,
          ),
          FilterOption(
            id: '2+',
            value: '2',
            displayName: '2+ Stars',
            count: 450,
          ),
        ],
      ),
    ];
  }

  static List<SearchFilter> _getServiceFilters() {
    return [
      SearchFilter(
        id: 'category',
        name: 'category',
        displayName: 'Service Type',
        type: FilterType.checkbox,
        isMultiSelect: true,
        options: [
          FilterOption(
            id: 'home',
            value: 'home',
            displayName: 'Home Services',
            count: 80,
          ),
          FilterOption(
            id: 'beauty',
            value: 'beauty',
            displayName: 'Beauty & Wellness',
            count: 60,
          ),
          FilterOption(
            id: 'repair',
            value: 'repair',
            displayName: 'Repair & Maintenance',
            count: 90,
          ),
          FilterOption(
            id: 'cleaning',
            value: 'cleaning',
            displayName: 'Cleaning',
            count: 45,
          ),
        ],
      ),
    ];
  }

  static List<SearchFilter> _getSellerFilters() {
    return [
      SearchFilter(
        id: 'rating',
        name: 'rating',
        displayName: 'Seller Rating',
        type: FilterType.radio,
        options: [
          FilterOption(
            id: '4.5+',
            value: '4.5',
            displayName: '4.5+ Stars',
            count: 50,
          ),
          FilterOption(
            id: '4+',
            value: '4',
            displayName: '4+ Stars',
            count: 120,
          ),
          FilterOption(
            id: '3+',
            value: '3',
            displayName: '3+ Stars',
            count: 200,
          ),
        ],
      ),
    ];
  }

  static List<SearchFilter> _getAllFilters() {
    final filters = <SearchFilter>[];
    filters.addAll(_getProductFilters());
    filters.addAll(_getServiceFilters());
    filters.addAll(_getSellerFilters());
    return filters;
  }
}
