/// Enum representing different user roles in the Projek app ecosystem
enum UserRole {
  /// Regular user who uses services and makes purchases
  user('user', 'User', 'Customer who uses the platform services'),

  /// Delivery rider who delivers orders
  rider('rider', 'Rider', 'Delivery partner who fulfills orders'),

  /// Seller who provides products and services
  seller('seller', 'Seller', 'Business owner who sells products/services'),

  /// Admin with full system access
  admin('admin', 'Admin', 'System administrator with full access');

  const UserRole(this.value, this.displayName, this.description);

  /// The string value used in database/API
  final String value;

  /// Human-readable display name
  final String displayName;

  /// Description of the role
  final String description;

  /// Get UserRole from string value
  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.user,
    );
  }

  /// Get UserRole from name (for backwards compatibility)
  static UserRole fromName(String name) {
    return UserRole.values.firstWhere(
      (role) => role.name == name,
      orElse: () => UserRole.user,
    );
  }

  /// Check if this role has admin privileges
  bool get isAdmin => this == UserRole.admin;

  /// Check if this role can sell products/services
  bool get canSell => this == UserRole.seller || this == UserRole.admin;

  /// Check if this role can deliver orders
  bool get canDeliver => this == UserRole.rider || this == UserRole.admin;

  /// Check if this role is a customer
  bool get isCustomer => this == UserRole.user;

  /// Get the main app route for this role
  String get mainRoute {
    switch (this) {
      case UserRole.user:
        return '/user/home';
      case UserRole.rider:
        return '/rider/dashboard';
      case UserRole.seller:
        return '/seller/dashboard';
      case UserRole.admin:
        return '/admin/dashboard';
    }
  }

  /// Get the login route for this role
  String get loginRoute {
    switch (this) {
      case UserRole.user:
        return '/user/login';
      case UserRole.rider:
        return '/rider/login';
      case UserRole.seller:
        return '/seller/login';
      case UserRole.admin:
        return '/admin/login';
    }
  }

  /// Get the app name for this role
  String get appName {
    switch (this) {
      case UserRole.user:
        return 'Projek - Super App';
      case UserRole.rider:
        return 'Projek Rider - Delivery Partner';
      case UserRole.seller:
        return 'Projek Seller - Business Dashboard';
      case UserRole.admin:
        return 'Projek Admin - System Management';
    }
  }

  /// Get the primary color for this role's theme
  String get primaryColorHex {
    switch (this) {
      case UserRole.user:
        return '#2196F3'; // Blue
      case UserRole.rider:
        return '#4CAF50'; // Green
      case UserRole.seller:
        return '#FF9800'; // Orange
      case UserRole.admin:
        return '#9C27B0'; // Purple
    }
  }

  /// Get permissions for this role
  List<String> get permissions {
    switch (this) {
      case UserRole.user:
        return [
          'book_services',
          'browse_marketplace',
          'make_payments',
          'track_bookings',
          'chat_support',
          'view_booking_history',
        ];
      case UserRole.rider:
        return [
          'accept_deliveries',
          'track_location',
          'update_delivery_status',
          'navigate_routes',
          'chat_customers',
          'chat_sellers',
          'view_earnings',
        ];
      case UserRole.seller:
        return [
          'manage_products',
          'manage_services',
          'process_bookings',
          'assign_riders',
          'view_analytics',
          'manage_inventory',
          'chat_customers',
          'track_sales',
        ];
      case UserRole.admin:
        return [
          'manage_users',
          'view_all_data',
          'system_settings',
          'manage_roles',
          'view_reports',
          'moderate_content',
        ];
    }
  }

  /// Check if this role has a specific permission
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  @override
  String toString() => value;
}
