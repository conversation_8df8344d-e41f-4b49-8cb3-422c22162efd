import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart' as path;
import '../utils/app_logger.dart';

class StorageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Upload a file to Firebase Storage
  Future<String?> uploadFile({
    required File file,
    required String folder,
    String? fileName,
  }) async {
    try {
      final String fileExtension = path.extension(file.path);
      final String uploadFileName = fileName ?? 
          '${DateTime.now().millisecondsSinceEpoch}$fileExtension';
      
      final Reference ref = _storage.ref().child('$folder/$uploadFileName');
      
      final UploadTask uploadTask = ref.putFile(file);
      final TaskSnapshot snapshot = await uploadTask;
      
      final String downloadUrl = await snapshot.ref.getDownloadURL();
      
      AppLogger.info('File uploaded successfully: $downloadUrl');
      return downloadUrl;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to upload file', e, stackTrace);
      return null;
    }
  }

  /// Upload bytes to Firebase Storage
  Future<String?> uploadBytes({
    required Uint8List bytes,
    required String folder,
    required String fileName,
    String? contentType,
  }) async {
    try {
      final Reference ref = _storage.ref().child('$folder/$fileName');
      
      final SettableMetadata metadata = SettableMetadata(
        contentType: contentType,
      );
      
      final UploadTask uploadTask = ref.putData(bytes, metadata);
      final TaskSnapshot snapshot = await uploadTask;
      
      final String downloadUrl = await snapshot.ref.getDownloadURL();
      
      AppLogger.info('Bytes uploaded successfully: $downloadUrl');
      return downloadUrl;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to upload bytes', e, stackTrace);
      return null;
    }
  }

  /// Delete a file from Firebase Storage
  Future<bool> deleteFile(String downloadUrl) async {
    try {
      final Reference ref = _storage.refFromURL(downloadUrl);
      await ref.delete();
      
      AppLogger.info('File deleted successfully: $downloadUrl');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to delete file', e, stackTrace);
      return false;
    }
  }

  /// Get download URL for a file
  Future<String?> getDownloadUrl(String filePath) async {
    try {
      final Reference ref = _storage.ref().child(filePath);
      final String downloadUrl = await ref.getDownloadURL();
      return downloadUrl;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get download URL', e, stackTrace);
      return null;
    }
  }

  /// Upload rider profile image
  Future<String?> uploadRiderProfileImage({
    required String riderId,
    required File imageFile,
  }) async {
    return await uploadFile(
      file: imageFile,
      folder: 'rider_profiles',
      fileName: '${riderId}_profile${path.extension(imageFile.path)}',
    );
  }

  /// Upload rider document
  Future<String?> uploadRiderDocument({
    required String riderId,
    required File documentFile,
    required String documentType,
  }) async {
    return await uploadFile(
      file: documentFile,
      folder: 'rider_documents/$riderId',
      fileName: '${documentType}_${DateTime.now().millisecondsSinceEpoch}${path.extension(documentFile.path)}',
    );
  }

  /// Upload vehicle image
  Future<String?> uploadVehicleImage({
    required String riderId,
    required File imageFile,
    required String imageType,
  }) async {
    return await uploadFile(
      file: imageFile,
      folder: 'vehicle_images/$riderId',
      fileName: '${imageType}_${DateTime.now().millisecondsSinceEpoch}${path.extension(imageFile.path)}',
    );
  }

  /// Get file metadata
  Future<FullMetadata?> getFileMetadata(String downloadUrl) async {
    try {
      final Reference ref = _storage.refFromURL(downloadUrl);
      final FullMetadata metadata = await ref.getMetadata();
      return metadata;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get file metadata', e, stackTrace);
      return null;
    }
  }

  /// List files in a folder
  Future<List<Reference>> listFiles(String folderPath) async {
    try {
      final Reference ref = _storage.ref().child(folderPath);
      final ListResult result = await ref.listAll();
      return result.items;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to list files', e, stackTrace);
      return [];
    }
  }

  /// Get file size
  Future<int?> getFileSize(String downloadUrl) async {
    try {
      final FullMetadata? metadata = await getFileMetadata(downloadUrl);
      return metadata?.size;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get file size', e, stackTrace);
      return null;
    }
  }

  /// Check if file exists
  Future<bool> fileExists(String filePath) async {
    try {
      final Reference ref = _storage.ref().child(filePath);
      await ref.getMetadata();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Upload multiple files
  Future<List<String>> uploadMultipleFiles({
    required List<File> files,
    required String folder,
  }) async {
    final List<String> downloadUrls = [];
    
    for (final File file in files) {
      final String? url = await uploadFile(
        file: file,
        folder: folder,
      );
      if (url != null) {
        downloadUrls.add(url);
      }
    }
    
    return downloadUrls;
  }

  /// Delete multiple files
  Future<bool> deleteMultipleFiles(List<String> downloadUrls) async {
    bool allDeleted = true;
    
    for (final String url in downloadUrls) {
      final bool deleted = await deleteFile(url);
      if (!deleted) {
        allDeleted = false;
      }
    }
    
    return allDeleted;
  }

  /// Get storage usage for a folder
  Future<int> getFolderSize(String folderPath) async {
    try {
      final List<Reference> files = await listFiles(folderPath);
      int totalSize = 0;
      
      for (final Reference file in files) {
        final FullMetadata metadata = await file.getMetadata();
        totalSize += metadata.size ?? 0;
      }
      
      return totalSize;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get folder size', e, stackTrace);
      return 0;
    }
  }

  /// Format file size
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get file extension from URL
  static String getFileExtension(String url) {
    return path.extension(Uri.parse(url).path);
  }

  /// Generate unique filename
  static String generateUniqueFileName(String originalName) {
    final String extension = path.extension(originalName);
    final String nameWithoutExtension = path.basenameWithoutExtension(originalName);
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    return '${nameWithoutExtension}_$timestamp$extension';
  }
}

// Riverpod provider for StorageService
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

// Provider for upload progress tracking
final uploadProgressProvider = StateProvider<double>((ref) => 0.0);

// Provider for storage usage tracking
final storageUsageProvider = FutureProvider.family<int, String>((ref, folderPath) async {
  final storageService = ref.read(storageServiceProvider);
  return await storageService.getFolderSize(folderPath);
});
