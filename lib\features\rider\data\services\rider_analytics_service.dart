import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/rider_analytics.dart';
import '../../../../demo/rider_demo_data.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/utils/app_logger.dart';

class RiderAnalyticsService {
  final StorageService _storageService;
  final StreamController<RiderAnalytics?> _analyticsController = StreamController<RiderAnalytics?>.broadcast();

  RiderAnalyticsService(this._storageService);

  Stream<RiderAnalytics?> get analyticsStream => _analyticsController.stream;

  Future<RiderAnalytics?> getRiderAnalytics(String riderId) async {
    try {
      final analytics = RiderDemoData.getRiderAnalytics(riderId);
      if (analytics != null) {
        _analyticsController.add(analytics);
      }
      return analytics;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get rider analytics', e, stackTrace);
      return null;
    }
  }

  Future<PerformanceMetrics?> getPerformanceMetrics(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      return analytics?.performance;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get performance metrics', e, stackTrace);
      return null;
    }
  }

  Future<RideStatistics?> getRideStatistics(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      return analytics?.rideStats;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get ride statistics', e, stackTrace);
      return null;
    }
  }

  Future<EarningsAnalytics?> getEarningsAnalytics(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      return analytics?.earnings;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get earnings analytics', e, stackTrace);
      return null;
    }
  }

  Future<CustomerFeedback?> getCustomerFeedback(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      return analytics?.feedback;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get customer feedback', e, stackTrace);
      return null;
    }
  }

  Future<LocationAnalytics?> getLocationAnalytics(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      return analytics?.location;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get location analytics', e, stackTrace);
      return null;
    }
  }

  Future<TimeAnalytics?> getTimeAnalytics(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      return analytics?.timeAnalytics;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get time analytics', e, stackTrace);
      return null;
    }
  }

  Future<List<PerformanceTrend>> getPerformanceTrends(
    String riderId, {
    DateTime? startDate,
    DateTime? endDate,
    int days = 30,
  }) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      final analytics = await getRiderAnalytics(riderId);
      if (analytics == null) return [];

      // In a real app, this would filter based on date range
      return analytics.performance.trends;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get performance trends', e, stackTrace);
      return [];
    }
  }

  Future<List<EarningsTrend>> getEarningsTrends(
    String riderId, {
    DateTime? startDate,
    DateTime? endDate,
    int days = 30,
  }) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      final analytics = await getRiderAnalytics(riderId);
      if (analytics == null) return [];

      // In a real app, this would filter based on date range
      return analytics.earnings.trends;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get earnings trends', e, stackTrace);
      return [];
    }
  }

  Future<List<HotspotArea>> getHotspotAreas(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      return analytics?.location.hotspots ?? [];
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get hotspot areas', e, stackTrace);
      return [];
    }
  }

  Future<List<PopularRoute>> getPopularRoutes(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      return analytics?.rideStats.popularRoutes ?? [];
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get popular routes', e, stackTrace);
      return [];
    }
  }

  Future<List<CustomerReview>> getRecentReviews(
    String riderId, {
    int limit = 10,
  }) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final analytics = await getRiderAnalytics(riderId);
      if (analytics == null) return [];

      final reviews = analytics.feedback.recentReviews;
      return reviews.take(limit).toList();
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get recent reviews', e, stackTrace);
      return [];
    }
  }

  Future<Map<String, dynamic>> getDashboardSummary(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      final analytics = await getRiderAnalytics(riderId);
      if (analytics == null) return {};

      return {
        'totalRides': analytics.rideStats.totalRides,
        'todayRides': analytics.rideStats.todayRides,
        'weeklyRides': analytics.rideStats.weeklyRides,
        'monthlyRides': analytics.rideStats.monthlyRides,
        'overallRating': analytics.performance.overallRating,
        'acceptanceRate': analytics.performance.acceptanceRate,
        'completionRate': analytics.performance.completionRate,
        'totalEarnings': analytics.earnings.totalEarnings,
        'averageEarningPerRide': analytics.earnings.averageEarningPerRide,
        'averageEarningPerHour': analytics.earnings.averageEarningPerHour,
        'totalDistance': analytics.rideStats.totalDistance,
        'totalOnlineHours': analytics.timeAnalytics.totalOnlineHours,
        'utilizationRate': analytics.timeAnalytics.utilizationRate,
        'customerSatisfactionScore': analytics.performance.customerSatisfactionScore,
      };
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get dashboard summary', e, stackTrace);
      return {};
    }
  }

  Future<Map<String, dynamic>> getComparisonData(
    String riderId,
    String comparisonType, // 'city', 'vehicle_type', 'experience'
  ) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      // In a real app, this would compare with other riders
      return {
        'yourRating': 4.8,
        'averageRating': 4.5,
        'yourEarnings': 45678.50,
        'averageEarnings': 38456.20,
        'yourRides': 1247,
        'averageRides': 987,
        'yourAcceptanceRate': 92.5,
        'averageAcceptanceRate': 85.3,
        'percentile': 85, // You're better than 85% of riders
        'rank': 156, // Out of 1000 riders in your category
      };
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get comparison data', e, stackTrace);
      return {};
    }
  }

  Future<List<Map<String, dynamic>>> getGoalsAndAchievements(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      return [
        {
          'id': 'goal_1',
          'title': 'Rating Master',
          'description': 'Maintain 4.8+ rating for 30 days',
          'type': 'rating',
          'target': 4.8,
          'current': 4.8,
          'progress': 100.0,
          'achieved': true,
          'reward': '₹500 bonus',
          'icon': 'star',
        },
        {
          'id': 'goal_2',
          'title': 'Century Rider',
          'description': 'Complete 100 rides this month',
          'type': 'rides',
          'target': 100,
          'current': 78,
          'progress': 78.0,
          'achieved': false,
          'reward': '₹1000 bonus',
          'icon': 'motorcycle',
        },
        {
          'id': 'goal_3',
          'title': 'Distance Champion',
          'description': 'Cover 1000 km this month',
          'type': 'distance',
          'target': 1000.0,
          'current': 856.5,
          'progress': 85.65,
          'achieved': false,
          'reward': '₹750 bonus',
          'icon': 'route',
        },
      ];
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get goals and achievements', e, stackTrace);
      return [];
    }
  }

  Future<Map<String, dynamic>> getRecommendations(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 400));
      
      return {
        'bestTimes': [
          {'time': '08:00-10:00', 'reason': 'High demand, good earnings'},
          {'time': '18:00-20:00', 'reason': 'Peak hours, surge pricing'},
        ],
        'bestAreas': [
          {'area': 'Fancy Bazaar', 'reason': 'Consistent ride requests'},
          {'area': 'Airport Road', 'reason': 'Long distance rides'},
        ],
        'improvements': [
          {'area': 'Response Time', 'suggestion': 'Try to accept rides within 30 seconds'},
          {'area': 'Customer Service', 'suggestion': 'Greet customers politely'},
        ],
        'opportunities': [
          {'type': 'Peak Hour Bonus', 'description': 'Work during 6-10 PM for extra earnings'},
          {'type': 'Weekend Surge', 'description': 'Saturdays have 20% higher fares'},
        ],
      };
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get recommendations', e, stackTrace);
      return {};
    }
  }

  Future<bool> exportAnalyticsReport(
    String riderId,
    String reportType, // 'pdf', 'excel', 'csv'
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      await Future.delayed(Duration(seconds: 2));
      
      // In a real app, this would generate and return download URL
      AppLogger.info('Analytics report exported for rider $riderId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to export analytics report', e, stackTrace);
      return false;
    }
  }

  void dispose() {
    _analyticsController.close();
  }
}

// Provider for RiderAnalyticsService
final riderAnalyticsServiceProvider = Provider<RiderAnalyticsService>((ref) {
  final storageService = ref.read(storageServiceProvider);
  return RiderAnalyticsService(storageService);
});

// Provider for rider analytics
final riderAnalyticsProvider = FutureProvider.family<RiderAnalytics?, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getRiderAnalytics(riderId);
});

// Provider for performance metrics
final performanceMetricsProvider = FutureProvider.family<PerformanceMetrics?, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getPerformanceMetrics(riderId);
});

// Provider for ride statistics
final rideStatisticsProvider = FutureProvider.family<RideStatistics?, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getRideStatistics(riderId);
});

// Provider for customer feedback
final customerFeedbackProvider = FutureProvider.family<CustomerFeedback?, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getCustomerFeedback(riderId);
});

// Provider for location analytics
final locationAnalyticsProvider = FutureProvider.family<LocationAnalytics?, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getLocationAnalytics(riderId);
});

// Provider for dashboard summary
final dashboardSummaryProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getDashboardSummary(riderId);
});

// Provider for hotspot areas
final hotspotAreasProvider = FutureProvider.family<List<HotspotArea>, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getHotspotAreas(riderId);
});

// Provider for popular routes
final popularRoutesProvider = FutureProvider.family<List<PopularRoute>, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getPopularRoutes(riderId);
});

// Provider for recent reviews
final recentReviewsProvider = FutureProvider.family<List<CustomerReview>, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getRecentReviews(riderId);
});

// Provider for goals and achievements
final goalsAchievementsProvider = FutureProvider.family<List<Map<String, dynamic>>, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getGoalsAndAchievements(riderId);
});

// Provider for recommendations
final recommendationsProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, riderId) {
  final service = ref.read(riderAnalyticsServiceProvider);
  return service.getRecommendations(riderId);
});
