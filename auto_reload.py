#!/usr/bin/env python3
"""
Automatic Hot Reload Script for Flutter
Watches for file changes and triggers hot reload automatically
"""

import os
import time
import subprocess
import sys
from pathlib import Path

class FlutterAutoReload:
    def __init__(self, project_path, watch_extensions=None):
        self.project_path = Path(project_path)
        self.watch_extensions = watch_extensions or ['.dart']
        self.last_modified = {}
        self.flutter_process = None
        
    def get_dart_files(self):
        """Get all Dart files in the project"""
        dart_files = []
        for ext in self.watch_extensions:
            dart_files.extend(self.project_path.rglob(f'*{ext}'))
        return dart_files
    
    def check_for_changes(self):
        """Check if any files have been modified"""
        current_files = self.get_dart_files()
        changed_files = []
        
        for file_path in current_files:
            try:
                current_mtime = file_path.stat().st_mtime
                if str(file_path) not in self.last_modified:
                    self.last_modified[str(file_path)] = current_mtime
                elif current_mtime > self.last_modified[str(file_path)]:
                    changed_files.append(file_path)
                    self.last_modified[str(file_path)] = current_mtime
            except OSError:
                continue
                
        return changed_files
    
    def trigger_hot_reload(self):
        """Send 'r' command to trigger hot reload"""
        try:
            # This would work if we had access to the Flutter process stdin
            print("🔥 File changed! Triggering hot reload...")
            print("💡 Press 'r' in your Flutter terminal to reload")
            print("=" * 50)
        except Exception as e:
            print(f"Error triggering reload: {e}")
    
    def watch(self):
        """Main watch loop"""
        print("🚀 Starting Flutter Auto Reload Watcher...")
        print(f"📁 Watching: {self.project_path}")
        print(f"📄 Extensions: {', '.join(self.watch_extensions)}")
        print("💡 Save any .dart file to see this message!")
        print("=" * 50)
        
        # Initialize file timestamps
        self.get_dart_files()
        
        try:
            while True:
                changed_files = self.check_for_changes()
                if changed_files:
                    for file_path in changed_files:
                        print(f"📝 Changed: {file_path.name}")
                    self.trigger_hot_reload()
                
                time.sleep(0.5)  # Check every 500ms
                
        except KeyboardInterrupt:
            print("\n👋 Auto reload watcher stopped")

if __name__ == "__main__":
    project_path = "."  # Current directory
    watcher = FlutterAutoReload(project_path)
    watcher.watch()
