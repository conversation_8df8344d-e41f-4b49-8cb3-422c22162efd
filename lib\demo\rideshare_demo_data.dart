import 'package:flutter/material.dart';

class RideShareDemoData {
  // Ride types with pricing and features
  static const Map<String, Map<String, dynamic>> rideTypes = {
    'Economy': {
      'id': 'economy',
      'name': 'Economy',
      'description': 'Affordable rides for everyday travel',
      'icon': Icons.directions_car,
      'color': Color(0xFF4CAF50),
      'basePrice': 8.0,
      'pricePerKm': 12.0,
      'pricePerMin': 2.0,
      'capacity': 4,
      'features': ['AC', 'Music', 'GPS Tracking'],
      'estimatedArrival': '3-5 mins',
      'vehicleTypes': ['Sedan', 'Hatchback'],
    },
    'Premium': {
      'id': 'premium',
      'name': 'Premium',
      'description': 'Comfortable rides with premium vehicles',
      'icon': Icons.car_rental,
      'color': Color(0xFF2196F3),
      'basePrice': 15.0,
      'pricePerKm': 18.0,
      'pricePerMin': 3.0,
      'capacity': 4,
      'features': [
        'AC',
        'Premium Music',
        'GPS Tracking',
        'Water Bottle',
        'Phone Charger',
      ],
      'estimatedArrival': '2-4 mins',
      'vehicleTypes': ['SUV', 'Premium Sedan'],
    },
    'Shared': {
      'id': 'shared',
      'name': 'Shared',
      'description': 'Share your ride and save money',
      'icon': Icons.people,
      'color': Color(0xFFFF9800),
      'basePrice': 5.0,
      'pricePerKm': 8.0,
      'pricePerMin': 1.5,
      'capacity': 3,
      'features': ['AC', 'GPS Tracking', 'Shared Route'],
      'estimatedArrival': '5-8 mins',
      'vehicleTypes': ['Sedan', 'Hatchback'],
    },
  };

  // Demo drivers with details
  static List<Map<String, dynamic>> getAllDrivers() {
    return [
      {
        'id': 'driver_1',
        'name': 'Rajesh Kumar',
        'phone': '+91 98765 43210',
        'rating': 4.8,
        'reviewCount': 1234,
        'profileImage': 'assets/images/demo/driver_1.jpg',
        'vehicleType': 'Sedan',
        'vehicleModel': 'Honda City',
        'vehicleNumber': 'AS 01 AB 1234',
        'vehicleColor': 'White',
        'experience': '5 years',
        'totalRides': 2500,
        'languages': ['Hindi', 'English', 'Assamese'],
        'currentLocation': {'lat': 26.1445, 'lng': 91.7362},
        'isAvailable': true,
        'estimatedArrival': 3,
        'rideTypes': ['Economy', 'Premium'],
      },
      {
        'id': 'driver_2',
        'name': 'Amit Sharma',
        'phone': '+91 87654 32109',
        'rating': 4.9,
        'reviewCount': 987,
        'profileImage': 'assets/images/demo/driver_2.jpg',
        'vehicleType': 'SUV',
        'vehicleModel': 'Mahindra XUV300',
        'vehicleNumber': 'AS 02 CD 5678',
        'vehicleColor': 'Black',
        'experience': '7 years',
        'totalRides': 3200,
        'languages': ['Hindi', 'English', 'Bengali'],
        'currentLocation': {'lat': 26.1545, 'lng': 91.7462},
        'isAvailable': true,
        'estimatedArrival': 5,
        'rideTypes': ['Premium', 'Shared'],
      },
      {
        'id': 'driver_3',
        'name': 'Priya Devi',
        'phone': '+91 76543 21098',
        'rating': 4.7,
        'reviewCount': 756,
        'profileImage': 'assets/images/demo/driver_3.jpg',
        'vehicleType': 'Hatchback',
        'vehicleModel': 'Maruti Swift',
        'vehicleNumber': 'AS 03 EF 9012',
        'vehicleColor': 'Red',
        'experience': '3 years',
        'totalRides': 1800,
        'languages': ['Hindi', 'Assamese'],
        'currentLocation': {'lat': 26.1345, 'lng': 91.7262},
        'isAvailable': true,
        'estimatedArrival': 4,
        'rideTypes': ['Economy', 'Shared'],
      },
    ];
  }

  // Popular locations in Guwahati
  static List<Map<String, dynamic>> getPopularLocations() {
    return [
      {
        'id': 'loc_1',
        'name': 'Kamakhya Temple',
        'address': 'Kamakhya, Guwahati, Assam',
        'coordinates': {'lat': 26.1665, 'lng': 91.7035},
        'type': 'Religious Place',
        'icon': Icons.temple_hindu,
      },
      {
        'id': 'loc_2',
        'name': 'Guwahati Railway Station',
        'address': 'Railway Station Rd, Guwahati, Assam',
        'coordinates': {'lat': 26.1833, 'lng': 91.7333},
        'type': 'Transport Hub',
        'icon': Icons.train,
      },
      {
        'id': 'loc_3',
        'name': 'Paltan Bazaar',
        'address': 'Paltan Bazaar, Guwahati, Assam',
        'coordinates': {'lat': 26.1833, 'lng': 91.7500},
        'type': 'Shopping Area',
        'icon': Icons.shopping_bag,
      },
      {
        'id': 'loc_4',
        'name': 'LGBI Airport',
        'address': 'Borjhar, Guwahati, Assam',
        'coordinates': {'lat': 26.1061, 'lng': 91.5856},
        'type': 'Airport',
        'icon': Icons.flight,
      },
      {
        'id': 'loc_5',
        'name': 'Fancy Bazaar',
        'address': 'Fancy Bazaar, Guwahati, Assam',
        'coordinates': {'lat': 26.1833, 'lng': 91.7667},
        'type': 'Market',
        'icon': Icons.store,
      },
    ];
  }

  // Sample ride history
  static List<Map<String, dynamic>> getRideHistory() {
    return [
      {
        'id': 'ride_1',
        'rideNumber': 'RD001234',
        'driverId': 'driver_1',
        'driverName': 'Rajesh Kumar',
        'rideType': 'Economy',
        'pickupLocation': 'Paltan Bazaar, Guwahati',
        'dropLocation': 'Kamakhya Temple, Guwahati',
        'distance': 8.5,
        'duration': 25,
        'fare': 145.0,
        'status': 'Completed',
        'bookingTime': DateTime.now().subtract(const Duration(days: 2)),
        'completionTime': DateTime.now().subtract(
          const Duration(days: 2, minutes: -30),
        ),
        'rating': 5.0,
        'paymentMethod': 'UPI',
      },
      {
        'id': 'ride_2',
        'rideNumber': 'RD001235',
        'driverId': 'driver_2',
        'driverName': 'Amit Sharma',
        'rideType': 'Premium',
        'pickupLocation': 'LGBI Airport, Guwahati',
        'dropLocation': 'Fancy Bazaar, Guwahati',
        'distance': 22.3,
        'duration': 45,
        'fare': 485.0,
        'status': 'Completed',
        'bookingTime': DateTime.now().subtract(const Duration(days: 5)),
        'completionTime': DateTime.now().subtract(
          const Duration(days: 5, minutes: -50),
        ),
        'rating': 4.0,
        'paymentMethod': 'Cash',
      },
    ];
  }

  // Calculate fare based on ride type and distance
  static Map<String, dynamic> calculateFare(
    String rideType,
    double distance,
    int duration,
  ) {
    final rideData = rideTypes[rideType]!;
    final basePrice = rideData['basePrice'] as double;
    final pricePerKm = rideData['pricePerKm'] as double;
    final pricePerMin = rideData['pricePerMin'] as double;

    final baseFare = basePrice;
    final distanceFare = distance * pricePerKm;
    final timeFare = duration * pricePerMin;
    final subtotal = baseFare + distanceFare + timeFare;

    final gst = subtotal * 0.05; // 5% GST
    final platformFee = 10.0;
    final total = subtotal + gst + platformFee;

    return {
      'baseFare': baseFare,
      'distanceFare': distanceFare,
      'timeFare': timeFare,
      'subtotal': subtotal,
      'gst': gst,
      'platformFee': platformFee,
      'total': total,
      'currency': '₹',
    };
  }

  // Get available drivers for a ride type
  static List<Map<String, dynamic>> getAvailableDrivers(String rideType) {
    return getAllDrivers()
        .where(
          (driver) =>
              driver['isAvailable'] &&
              (driver['rideTypes'] as List).contains(rideType),
        )
        .toList();
  }

  // Get driver by ID
  static Map<String, dynamic>? getDriverById(String driverId) {
    try {
      return getAllDrivers().firstWhere((driver) => driver['id'] == driverId);
    } catch (e) {
      return null;
    }
  }

  // Search locations
  static List<Map<String, dynamic>> searchLocations(String query) {
    final lowercaseQuery = query.toLowerCase();
    return getPopularLocations().where((location) {
      return location['name'].toString().toLowerCase().contains(
            lowercaseQuery,
          ) ||
          location['address'].toString().toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Commission rates by ride type
  static const Map<String, double> commissionRates = {
    'economy': 0.20, // 20% commission
    'premium': 0.18, // 18% commission
    'shared': 0.22, // 22% commission
  };

  // Sample rider earnings data
  static Map<String, dynamic> getRiderEarnings(String riderId) {
    final sampleEarnings = {
      'rider_1': {
        'totalEarnings': 2450.0,
        'totalRides': 45,
        'completedRides': 42,
        'cancelledRides': 3,
        'averageRating': 4.8,
        'totalBonuses': 180.0,
        'totalDeductions': 75.0,
        'averageEarningsPerRide': 58.33,
        'completionRate': 93.33,
        'todayEarnings': 320.0,
        'todayRides': 6,
        'weeklyEarnings': 1250.0,
        'monthlyEarnings': 4800.0,
      },
      'rider_2': {
        'totalEarnings': 1890.0,
        'totalRides': 38,
        'completedRides': 35,
        'cancelledRides': 3,
        'averageRating': 4.6,
        'totalBonuses': 140.0,
        'totalDeductions': 90.0,
        'averageEarningsPerRide': 54.0,
        'completionRate': 92.11,
        'todayEarnings': 280.0,
        'todayRides': 5,
        'weeklyEarnings': 980.0,
        'monthlyEarnings': 3600.0,
      },
      'rider_3': {
        'totalEarnings': 3200.0,
        'totalRides': 62,
        'completedRides': 58,
        'cancelledRides': 4,
        'averageRating': 4.9,
        'totalBonuses': 250.0,
        'totalDeductions': 100.0,
        'averageEarningsPerRide': 55.17,
        'completionRate': 93.55,
        'todayEarnings': 420.0,
        'todayRides': 8,
        'weeklyEarnings': 1680.0,
        'monthlyEarnings': 6400.0,
      },
    };

    return sampleEarnings[riderId] ?? sampleEarnings['rider_1']!;
  }

  // Sample completed rides with commission breakdown
  static List<Map<String, dynamic>> getCompletedRides(String riderId) {
    return [
      {
        'rideId': 'ride_001',
        'date': DateTime.now().subtract(const Duration(hours: 2)),
        'rideType': 'economy',
        'totalFare': 180.0,
        'baseCommission': 36.0,
        'peakHourBonus': 9.0,
        'completionBonus': 10.0,
        'ratingBonus': 5.0,
        'platformFee': 5.0,
        'netEarnings': 55.0,
        'userRating': 5.0,
        'pickup': 'Kamakhya Temple',
        'drop': 'Guwahati Railway Station',
        'distance': 8.5,
        'duration': 25,
      },
      {
        'rideId': 'ride_002',
        'date': DateTime.now().subtract(const Duration(hours: 4)),
        'rideType': 'premium',
        'totalFare': 280.0,
        'baseCommission': 50.4,
        'peakHourBonus': 0.0,
        'completionBonus': 10.0,
        'ratingBonus': 0.0,
        'platformFee': 5.0,
        'netEarnings': 55.4,
        'userRating': 4.0,
        'pickup': 'Guwahati Airport',
        'drop': 'Fancy Bazaar',
        'distance': 12.3,
        'duration': 35,
      },
      {
        'rideId': 'ride_003',
        'date': DateTime.now().subtract(const Duration(hours: 6)),
        'rideType': 'shared',
        'totalFare': 120.0,
        'baseCommission': 26.4,
        'peakHourBonus': 6.0,
        'completionBonus': 10.0,
        'ratingBonus': 5.0,
        'platformFee': 5.0,
        'netEarnings': 42.4,
        'userRating': 5.0,
        'pickup': 'Paltan Bazaar',
        'drop': 'Dispur',
        'distance': 6.8,
        'duration': 20,
      },
    ];
  }

  // OTP-related demo data
  static Map<String, dynamic> getOTPData(String rideId) {
    return {
      'otp': '123456',
      'generatedAt': DateTime.now().subtract(const Duration(minutes: 5)),
      'expiresAt': DateTime.now().add(const Duration(minutes: 10)),
      'attempts': 0,
      'maxAttempts': 3,
      'isUsed': false,
      'isExpired': false,
    };
  }

  // Peak hours configuration
  static bool isPeakHour([DateTime? time]) {
    final checkTime = time ?? DateTime.now();
    final hour = checkTime.hour;

    // Peak hours: 7-10 AM and 5-9 PM
    return (hour >= 7 && hour <= 10) || (hour >= 17 && hour <= 21);
  }

  // Bonus and penalty rates
  static const Map<String, double> bonusRates = {
    'peakHourBonus': 0.05, // 5% of total fare
    'completionBonus': 10.0, // ₹10 per ride
    'ratingBonus': 5.0, // ₹5 for 5-star rides
  };

  static const Map<String, double> penaltyRates = {
    'cancellationPenalty': 25.0, // ₹25 per cancellation
    'platformFee': 5.0, // ₹5 per ride
  };
}
