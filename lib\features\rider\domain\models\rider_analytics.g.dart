// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rider_analytics.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RiderAnalyticsAdapter extends TypeAdapter<RiderAnalytics> {
  @override
  final int typeId = 47;

  @override
  RiderAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RiderAnalytics(
      riderId: fields[0] as String,
      performance: fields[1] as PerformanceMetrics,
      rideStats: fields[2] as RideStatistics,
      earnings: fields[3] as EarningsAnalytics,
      feedback: fields[4] as CustomerFeedback,
      location: fields[5] as LocationAnalytics,
      timeAnalytics: fields[6] as TimeAnalytics,
      lastUpdated: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, RiderAnalytics obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.riderId)
      ..writeByte(1)
      ..write(obj.performance)
      ..writeByte(2)
      ..write(obj.rideStats)
      ..writeByte(3)
      ..write(obj.earnings)
      ..writeByte(4)
      ..write(obj.feedback)
      ..writeByte(5)
      ..write(obj.location)
      ..writeByte(6)
      ..write(obj.timeAnalytics)
      ..writeByte(7)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RiderAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PerformanceMetricsAdapter extends TypeAdapter<PerformanceMetrics> {
  @override
  final int typeId = 48;

  @override
  PerformanceMetrics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PerformanceMetrics(
      overallRating: fields[0] as double,
      acceptanceRate: fields[1] as double,
      completionRate: fields[2] as double,
      cancellationRate: fields[3] as double,
      onTimePercentage: fields[4] as double,
      averageResponseTime: fields[5] as double,
      totalRidesCompleted: fields[6] as int,
      totalRidesCancelled: fields[7] as int,
      customerSatisfactionScore: fields[8] as double,
      trends: (fields[9] as List).cast<PerformanceTrend>(),
    );
  }

  @override
  void write(BinaryWriter writer, PerformanceMetrics obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.overallRating)
      ..writeByte(1)
      ..write(obj.acceptanceRate)
      ..writeByte(2)
      ..write(obj.completionRate)
      ..writeByte(3)
      ..write(obj.cancellationRate)
      ..writeByte(4)
      ..write(obj.onTimePercentage)
      ..writeByte(5)
      ..write(obj.averageResponseTime)
      ..writeByte(6)
      ..write(obj.totalRidesCompleted)
      ..writeByte(7)
      ..write(obj.totalRidesCancelled)
      ..writeByte(8)
      ..write(obj.customerSatisfactionScore)
      ..writeByte(9)
      ..write(obj.trends);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PerformanceMetricsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PerformanceTrendAdapter extends TypeAdapter<PerformanceTrend> {
  @override
  final int typeId = 49;

  @override
  PerformanceTrend read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PerformanceTrend(
      date: fields[0] as DateTime,
      rating: fields[1] as double,
      ridesCompleted: fields[2] as int,
      earnings: fields[3] as double,
      acceptanceRate: fields[4] as double,
    );
  }

  @override
  void write(BinaryWriter writer, PerformanceTrend obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.rating)
      ..writeByte(2)
      ..write(obj.ridesCompleted)
      ..writeByte(3)
      ..write(obj.earnings)
      ..writeByte(4)
      ..write(obj.acceptanceRate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PerformanceTrendAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RideStatisticsAdapter extends TypeAdapter<RideStatistics> {
  @override
  final int typeId = 50;

  @override
  RideStatistics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RideStatistics(
      totalRides: fields[0] as int,
      todayRides: fields[1] as int,
      weeklyRides: fields[2] as int,
      monthlyRides: fields[3] as int,
      totalDistance: fields[4] as double,
      averageRideDistance: fields[5] as double,
      totalDuration: fields[6] as double,
      averageRideDuration: fields[7] as double,
      ridesByType: (fields[8] as Map).cast<String, int>(),
      ridesByHour: (fields[9] as Map).cast<String, int>(),
      ridesByDay: (fields[10] as Map).cast<String, int>(),
      popularRoutes: (fields[11] as List).cast<PopularRoute>(),
    );
  }

  @override
  void write(BinaryWriter writer, RideStatistics obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.totalRides)
      ..writeByte(1)
      ..write(obj.todayRides)
      ..writeByte(2)
      ..write(obj.weeklyRides)
      ..writeByte(3)
      ..write(obj.monthlyRides)
      ..writeByte(4)
      ..write(obj.totalDistance)
      ..writeByte(5)
      ..write(obj.averageRideDistance)
      ..writeByte(6)
      ..write(obj.totalDuration)
      ..writeByte(7)
      ..write(obj.averageRideDuration)
      ..writeByte(8)
      ..write(obj.ridesByType)
      ..writeByte(9)
      ..write(obj.ridesByHour)
      ..writeByte(10)
      ..write(obj.ridesByDay)
      ..writeByte(11)
      ..write(obj.popularRoutes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RideStatisticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PopularRouteAdapter extends TypeAdapter<PopularRoute> {
  @override
  final int typeId = 51;

  @override
  PopularRoute read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PopularRoute(
      fromLocation: fields[0] as String,
      toLocation: fields[1] as String,
      rideCount: fields[2] as int,
      averageEarning: fields[3] as double,
      averageDuration: fields[4] as double,
    );
  }

  @override
  void write(BinaryWriter writer, PopularRoute obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.fromLocation)
      ..writeByte(1)
      ..write(obj.toLocation)
      ..writeByte(2)
      ..write(obj.rideCount)
      ..writeByte(3)
      ..write(obj.averageEarning)
      ..writeByte(4)
      ..write(obj.averageDuration);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PopularRouteAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EarningsAnalyticsAdapter extends TypeAdapter<EarningsAnalytics> {
  @override
  final int typeId = 52;

  @override
  EarningsAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return EarningsAnalytics(
      totalEarnings: fields[0] as double,
      averageEarningPerRide: fields[1] as double,
      averageEarningPerHour: fields[2] as double,
      peakHourEarnings: fields[3] as double,
      offPeakEarnings: fields[4] as double,
      earningsByHour: (fields[5] as Map).cast<String, double>(),
      earningsByDay: (fields[6] as Map).cast<String, double>(),
      earningsByRideType: (fields[7] as Map).cast<String, double>(),
      trends: (fields[8] as List).cast<EarningsTrend>(),
    );
  }

  @override
  void write(BinaryWriter writer, EarningsAnalytics obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.totalEarnings)
      ..writeByte(1)
      ..write(obj.averageEarningPerRide)
      ..writeByte(2)
      ..write(obj.averageEarningPerHour)
      ..writeByte(3)
      ..write(obj.peakHourEarnings)
      ..writeByte(4)
      ..write(obj.offPeakEarnings)
      ..writeByte(5)
      ..write(obj.earningsByHour)
      ..writeByte(6)
      ..write(obj.earningsByDay)
      ..writeByte(7)
      ..write(obj.earningsByRideType)
      ..writeByte(8)
      ..write(obj.trends);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EarningsAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EarningsTrendAdapter extends TypeAdapter<EarningsTrend> {
  @override
  final int typeId = 53;

  @override
  EarningsTrend read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return EarningsTrend(
      date: fields[0] as DateTime,
      earnings: fields[1] as double,
      rides: fields[2] as int,
      hoursOnline: fields[3] as double,
    );
  }

  @override
  void write(BinaryWriter writer, EarningsTrend obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.earnings)
      ..writeByte(2)
      ..write(obj.rides)
      ..writeByte(3)
      ..write(obj.hoursOnline);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EarningsTrendAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomerFeedbackAdapter extends TypeAdapter<CustomerFeedback> {
  @override
  final int typeId = 54;

  @override
  CustomerFeedback read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomerFeedback(
      averageRating: fields[0] as double,
      totalReviews: fields[1] as int,
      ratingDistribution: (fields[2] as Map).cast<int, int>(),
      positiveComments: (fields[3] as List).cast<String>(),
      negativeComments: (fields[4] as List).cast<String>(),
      feedbackCategories: (fields[5] as Map).cast<String, int>(),
      recentReviews: (fields[6] as List).cast<CustomerReview>(),
    );
  }

  @override
  void write(BinaryWriter writer, CustomerFeedback obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.averageRating)
      ..writeByte(1)
      ..write(obj.totalReviews)
      ..writeByte(2)
      ..write(obj.ratingDistribution)
      ..writeByte(3)
      ..write(obj.positiveComments)
      ..writeByte(4)
      ..write(obj.negativeComments)
      ..writeByte(5)
      ..write(obj.feedbackCategories)
      ..writeByte(6)
      ..write(obj.recentReviews);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerFeedbackAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomerReviewAdapter extends TypeAdapter<CustomerReview> {
  @override
  final int typeId = 55;

  @override
  CustomerReview read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomerReview(
      id: fields[0] as String,
      customerId: fields[1] as String,
      customerName: fields[2] as String,
      rating: fields[3] as int,
      comment: fields[4] as String,
      timestamp: fields[5] as DateTime,
      rideId: fields[6] as String,
    );
  }

  @override
  void write(BinaryWriter writer, CustomerReview obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.customerId)
      ..writeByte(2)
      ..write(obj.customerName)
      ..writeByte(3)
      ..write(obj.rating)
      ..writeByte(4)
      ..write(obj.comment)
      ..writeByte(5)
      ..write(obj.timestamp)
      ..writeByte(6)
      ..write(obj.rideId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerReviewAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LocationAnalyticsAdapter extends TypeAdapter<LocationAnalytics> {
  @override
  final int typeId = 56;

  @override
  LocationAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocationAnalytics(
      hotspots: (fields[0] as List).cast<HotspotArea>(),
      ridesByArea: (fields[1] as Map).cast<String, int>(),
      earningsByArea: (fields[2] as Map).cast<String, double>(),
      recommendedAreas: (fields[3] as List).cast<String>(),
      totalDistanceCovered: fields[4] as double,
    );
  }

  @override
  void write(BinaryWriter writer, LocationAnalytics obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.hotspots)
      ..writeByte(1)
      ..write(obj.ridesByArea)
      ..writeByte(2)
      ..write(obj.earningsByArea)
      ..writeByte(3)
      ..write(obj.recommendedAreas)
      ..writeByte(4)
      ..write(obj.totalDistanceCovered);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HotspotAreaAdapter extends TypeAdapter<HotspotArea> {
  @override
  final int typeId = 57;

  @override
  HotspotArea read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HotspotArea(
      name: fields[0] as String,
      latitude: fields[1] as double,
      longitude: fields[2] as double,
      radius: fields[3] as double,
      rideCount: fields[4] as int,
      averageEarning: fields[5] as double,
      peakHours: (fields[6] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, HotspotArea obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.latitude)
      ..writeByte(2)
      ..write(obj.longitude)
      ..writeByte(3)
      ..write(obj.radius)
      ..writeByte(4)
      ..write(obj.rideCount)
      ..writeByte(5)
      ..write(obj.averageEarning)
      ..writeByte(6)
      ..write(obj.peakHours);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HotspotAreaAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TimeAnalyticsAdapter extends TypeAdapter<TimeAnalytics> {
  @override
  final int typeId = 58;

  @override
  TimeAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TimeAnalytics(
      totalOnlineHours: fields[0] as double,
      averageOnlineHoursPerDay: fields[1] as double,
      onlineHoursByDay: (fields[2] as Map).cast<String, double>(),
      onlineHoursByHour: (fields[3] as Map).cast<String, double>(),
      mostActiveHours: (fields[4] as List).cast<String>(),
      leastActiveHours: (fields[5] as List).cast<String>(),
      utilizationRate: fields[6] as double,
    );
  }

  @override
  void write(BinaryWriter writer, TimeAnalytics obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.totalOnlineHours)
      ..writeByte(1)
      ..write(obj.averageOnlineHoursPerDay)
      ..writeByte(2)
      ..write(obj.onlineHoursByDay)
      ..writeByte(3)
      ..write(obj.onlineHoursByHour)
      ..writeByte(4)
      ..write(obj.mostActiveHours)
      ..writeByte(5)
      ..write(obj.leastActiveHours)
      ..writeByte(6)
      ..write(obj.utilizationRate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimeAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RiderAnalytics _$RiderAnalyticsFromJson(Map<String, dynamic> json) =>
    RiderAnalytics(
      riderId: json['riderId'] as String,
      performance: PerformanceMetrics.fromJson(
          json['performance'] as Map<String, dynamic>),
      rideStats:
          RideStatistics.fromJson(json['rideStats'] as Map<String, dynamic>),
      earnings:
          EarningsAnalytics.fromJson(json['earnings'] as Map<String, dynamic>),
      feedback:
          CustomerFeedback.fromJson(json['feedback'] as Map<String, dynamic>),
      location:
          LocationAnalytics.fromJson(json['location'] as Map<String, dynamic>),
      timeAnalytics:
          TimeAnalytics.fromJson(json['timeAnalytics'] as Map<String, dynamic>),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$RiderAnalyticsToJson(RiderAnalytics instance) =>
    <String, dynamic>{
      'riderId': instance.riderId,
      'performance': instance.performance,
      'rideStats': instance.rideStats,
      'earnings': instance.earnings,
      'feedback': instance.feedback,
      'location': instance.location,
      'timeAnalytics': instance.timeAnalytics,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

PerformanceMetrics _$PerformanceMetricsFromJson(Map<String, dynamic> json) =>
    PerformanceMetrics(
      overallRating: (json['overallRating'] as num).toDouble(),
      acceptanceRate: (json['acceptanceRate'] as num).toDouble(),
      completionRate: (json['completionRate'] as num).toDouble(),
      cancellationRate: (json['cancellationRate'] as num).toDouble(),
      onTimePercentage: (json['onTimePercentage'] as num).toDouble(),
      averageResponseTime: (json['averageResponseTime'] as num).toDouble(),
      totalRidesCompleted: (json['totalRidesCompleted'] as num).toInt(),
      totalRidesCancelled: (json['totalRidesCancelled'] as num).toInt(),
      customerSatisfactionScore:
          (json['customerSatisfactionScore'] as num).toDouble(),
      trends: (json['trends'] as List<dynamic>)
          .map((e) => PerformanceTrend.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PerformanceMetricsToJson(PerformanceMetrics instance) =>
    <String, dynamic>{
      'overallRating': instance.overallRating,
      'acceptanceRate': instance.acceptanceRate,
      'completionRate': instance.completionRate,
      'cancellationRate': instance.cancellationRate,
      'onTimePercentage': instance.onTimePercentage,
      'averageResponseTime': instance.averageResponseTime,
      'totalRidesCompleted': instance.totalRidesCompleted,
      'totalRidesCancelled': instance.totalRidesCancelled,
      'customerSatisfactionScore': instance.customerSatisfactionScore,
      'trends': instance.trends,
    };

PerformanceTrend _$PerformanceTrendFromJson(Map<String, dynamic> json) =>
    PerformanceTrend(
      date: DateTime.parse(json['date'] as String),
      rating: (json['rating'] as num).toDouble(),
      ridesCompleted: (json['ridesCompleted'] as num).toInt(),
      earnings: (json['earnings'] as num).toDouble(),
      acceptanceRate: (json['acceptanceRate'] as num).toDouble(),
    );

Map<String, dynamic> _$PerformanceTrendToJson(PerformanceTrend instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'rating': instance.rating,
      'ridesCompleted': instance.ridesCompleted,
      'earnings': instance.earnings,
      'acceptanceRate': instance.acceptanceRate,
    };

RideStatistics _$RideStatisticsFromJson(Map<String, dynamic> json) =>
    RideStatistics(
      totalRides: (json['totalRides'] as num).toInt(),
      todayRides: (json['todayRides'] as num).toInt(),
      weeklyRides: (json['weeklyRides'] as num).toInt(),
      monthlyRides: (json['monthlyRides'] as num).toInt(),
      totalDistance: (json['totalDistance'] as num).toDouble(),
      averageRideDistance: (json['averageRideDistance'] as num).toDouble(),
      totalDuration: (json['totalDuration'] as num).toDouble(),
      averageRideDuration: (json['averageRideDuration'] as num).toDouble(),
      ridesByType: Map<String, int>.from(json['ridesByType'] as Map),
      ridesByHour: Map<String, int>.from(json['ridesByHour'] as Map),
      ridesByDay: Map<String, int>.from(json['ridesByDay'] as Map),
      popularRoutes: (json['popularRoutes'] as List<dynamic>)
          .map((e) => PopularRoute.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RideStatisticsToJson(RideStatistics instance) =>
    <String, dynamic>{
      'totalRides': instance.totalRides,
      'todayRides': instance.todayRides,
      'weeklyRides': instance.weeklyRides,
      'monthlyRides': instance.monthlyRides,
      'totalDistance': instance.totalDistance,
      'averageRideDistance': instance.averageRideDistance,
      'totalDuration': instance.totalDuration,
      'averageRideDuration': instance.averageRideDuration,
      'ridesByType': instance.ridesByType,
      'ridesByHour': instance.ridesByHour,
      'ridesByDay': instance.ridesByDay,
      'popularRoutes': instance.popularRoutes,
    };

PopularRoute _$PopularRouteFromJson(Map<String, dynamic> json) => PopularRoute(
      fromLocation: json['fromLocation'] as String,
      toLocation: json['toLocation'] as String,
      rideCount: (json['rideCount'] as num).toInt(),
      averageEarning: (json['averageEarning'] as num).toDouble(),
      averageDuration: (json['averageDuration'] as num).toDouble(),
    );

Map<String, dynamic> _$PopularRouteToJson(PopularRoute instance) =>
    <String, dynamic>{
      'fromLocation': instance.fromLocation,
      'toLocation': instance.toLocation,
      'rideCount': instance.rideCount,
      'averageEarning': instance.averageEarning,
      'averageDuration': instance.averageDuration,
    };

EarningsAnalytics _$EarningsAnalyticsFromJson(Map<String, dynamic> json) =>
    EarningsAnalytics(
      totalEarnings: (json['totalEarnings'] as num).toDouble(),
      averageEarningPerRide: (json['averageEarningPerRide'] as num).toDouble(),
      averageEarningPerHour: (json['averageEarningPerHour'] as num).toDouble(),
      peakHourEarnings: (json['peakHourEarnings'] as num).toDouble(),
      offPeakEarnings: (json['offPeakEarnings'] as num).toDouble(),
      earningsByHour: (json['earningsByHour'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      earningsByDay: (json['earningsByDay'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      earningsByRideType:
          (json['earningsByRideType'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      trends: (json['trends'] as List<dynamic>)
          .map((e) => EarningsTrend.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EarningsAnalyticsToJson(EarningsAnalytics instance) =>
    <String, dynamic>{
      'totalEarnings': instance.totalEarnings,
      'averageEarningPerRide': instance.averageEarningPerRide,
      'averageEarningPerHour': instance.averageEarningPerHour,
      'peakHourEarnings': instance.peakHourEarnings,
      'offPeakEarnings': instance.offPeakEarnings,
      'earningsByHour': instance.earningsByHour,
      'earningsByDay': instance.earningsByDay,
      'earningsByRideType': instance.earningsByRideType,
      'trends': instance.trends,
    };

EarningsTrend _$EarningsTrendFromJson(Map<String, dynamic> json) =>
    EarningsTrend(
      date: DateTime.parse(json['date'] as String),
      earnings: (json['earnings'] as num).toDouble(),
      rides: (json['rides'] as num).toInt(),
      hoursOnline: (json['hoursOnline'] as num).toDouble(),
    );

Map<String, dynamic> _$EarningsTrendToJson(EarningsTrend instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'earnings': instance.earnings,
      'rides': instance.rides,
      'hoursOnline': instance.hoursOnline,
    };

CustomerFeedback _$CustomerFeedbackFromJson(Map<String, dynamic> json) =>
    CustomerFeedback(
      averageRating: (json['averageRating'] as num).toDouble(),
      totalReviews: (json['totalReviews'] as num).toInt(),
      ratingDistribution:
          (json['ratingDistribution'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(int.parse(k), (e as num).toInt()),
      ),
      positiveComments: (json['positiveComments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      negativeComments: (json['negativeComments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      feedbackCategories:
          Map<String, int>.from(json['feedbackCategories'] as Map),
      recentReviews: (json['recentReviews'] as List<dynamic>)
          .map((e) => CustomerReview.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CustomerFeedbackToJson(CustomerFeedback instance) =>
    <String, dynamic>{
      'averageRating': instance.averageRating,
      'totalReviews': instance.totalReviews,
      'ratingDistribution':
          instance.ratingDistribution.map((k, e) => MapEntry(k.toString(), e)),
      'positiveComments': instance.positiveComments,
      'negativeComments': instance.negativeComments,
      'feedbackCategories': instance.feedbackCategories,
      'recentReviews': instance.recentReviews,
    };

CustomerReview _$CustomerReviewFromJson(Map<String, dynamic> json) =>
    CustomerReview(
      id: json['id'] as String,
      customerId: json['customerId'] as String,
      customerName: json['customerName'] as String,
      rating: (json['rating'] as num).toInt(),
      comment: json['comment'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      rideId: json['rideId'] as String,
    );

Map<String, dynamic> _$CustomerReviewToJson(CustomerReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'rating': instance.rating,
      'comment': instance.comment,
      'timestamp': instance.timestamp.toIso8601String(),
      'rideId': instance.rideId,
    };

LocationAnalytics _$LocationAnalyticsFromJson(Map<String, dynamic> json) =>
    LocationAnalytics(
      hotspots: (json['hotspots'] as List<dynamic>)
          .map((e) => HotspotArea.fromJson(e as Map<String, dynamic>))
          .toList(),
      ridesByArea: Map<String, int>.from(json['ridesByArea'] as Map),
      earningsByArea: (json['earningsByArea'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      recommendedAreas: (json['recommendedAreas'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      totalDistanceCovered: (json['totalDistanceCovered'] as num).toDouble(),
    );

Map<String, dynamic> _$LocationAnalyticsToJson(LocationAnalytics instance) =>
    <String, dynamic>{
      'hotspots': instance.hotspots,
      'ridesByArea': instance.ridesByArea,
      'earningsByArea': instance.earningsByArea,
      'recommendedAreas': instance.recommendedAreas,
      'totalDistanceCovered': instance.totalDistanceCovered,
    };

HotspotArea _$HotspotAreaFromJson(Map<String, dynamic> json) => HotspotArea(
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      radius: (json['radius'] as num).toDouble(),
      rideCount: (json['rideCount'] as num).toInt(),
      averageEarning: (json['averageEarning'] as num).toDouble(),
      peakHours:
          (json['peakHours'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$HotspotAreaToJson(HotspotArea instance) =>
    <String, dynamic>{
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'radius': instance.radius,
      'rideCount': instance.rideCount,
      'averageEarning': instance.averageEarning,
      'peakHours': instance.peakHours,
    };

TimeAnalytics _$TimeAnalyticsFromJson(Map<String, dynamic> json) =>
    TimeAnalytics(
      totalOnlineHours: (json['totalOnlineHours'] as num).toDouble(),
      averageOnlineHoursPerDay:
          (json['averageOnlineHoursPerDay'] as num).toDouble(),
      onlineHoursByDay: (json['onlineHoursByDay'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      onlineHoursByHour:
          (json['onlineHoursByHour'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      mostActiveHours: (json['mostActiveHours'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      leastActiveHours: (json['leastActiveHours'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      utilizationRate: (json['utilizationRate'] as num).toDouble(),
    );

Map<String, dynamic> _$TimeAnalyticsToJson(TimeAnalytics instance) =>
    <String, dynamic>{
      'totalOnlineHours': instance.totalOnlineHours,
      'averageOnlineHoursPerDay': instance.averageOnlineHoursPerDay,
      'onlineHoursByDay': instance.onlineHoursByDay,
      'onlineHoursByHour': instance.onlineHoursByHour,
      'mostActiveHours': instance.mostActiveHours,
      'leastActiveHours': instance.leastActiveHours,
      'utilizationRate': instance.utilizationRate,
    };
