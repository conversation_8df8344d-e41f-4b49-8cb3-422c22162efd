import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_logger.dart';
import 'location_service.dart';
import 'notification_service.dart';
import 'analytics_service.dart';

class BackgroundService extends ChangeNotifier {
  bool _isRunning = false;
  bool _isInitialized = false;
  DateTime? _lastUpdate;

  bool get isRunning => _isRunning;
  bool get isInitialized => _isInitialized;
  DateTime? get lastUpdate => _lastUpdate;

  /// Initialize the background service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('Initializing Background Service');

      // Initialize background tasks
      await _setupBackgroundTasks();

      _isInitialized = true;
      _lastUpdate = DateTime.now();

      await AnalyticsService.logEvent('background_service_initialized', {
        'timestamp': DateTime.now().toIso8601String(),
      });

      AppLogger.info('Background Service initialized successfully');
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('Failed to initialize Background Service', e, stackTrace);
      rethrow;
    }
  }

  /// Start background service
  Future<void> start() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_isRunning) {
      AppLogger.warning('Background Service already running');
      return;
    }

    try {
      AppLogger.info('Starting Background Service');

      _isRunning = true;
      _lastUpdate = DateTime.now();

      // Start background tasks
      await _startLocationTracking();
      await _startOrderMonitoring();
      await _startNotificationHandling();

      await AnalyticsService.logEvent('background_service_started', {
        'timestamp': DateTime.now().toIso8601String(),
      });

      AppLogger.info('Background Service started successfully');
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('Failed to start Background Service', e, stackTrace);
      _isRunning = false;
      rethrow;
    }
  }

  /// Stop background service
  Future<void> stop() async {
    if (!_isRunning) {
      AppLogger.warning('Background Service not running');
      return;
    }

    try {
      AppLogger.info('Stopping Background Service');

      _isRunning = false;

      // Stop background tasks
      await _stopLocationTracking();
      await _stopOrderMonitoring();
      await _stopNotificationHandling();

      await AnalyticsService.logEvent('background_service_stopped', {
        'timestamp': DateTime.now().toIso8601String(),
        'duration_seconds': _getRunDuration(),
      });

      AppLogger.info('Background Service stopped successfully');
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('Failed to stop Background Service', e, stackTrace);
      rethrow;
    }
  }

  /// Setup background tasks
  Future<void> _setupBackgroundTasks() async {
    try {
      // Setup periodic tasks for rider app
      AppLogger.info('Setting up background tasks');

      // Add any background task setup here
      // Example: periodic location updates, order status checks, etc.
    } catch (e, stackTrace) {
      AppLogger.error('Error setting up background tasks', e, stackTrace);
      rethrow;
    }
  }

  /// Start location tracking in background
  Future<void> _startLocationTracking() async {
    try {
      AppLogger.info('Starting background location tracking');

      // Start location tracking for delivery
      // This would integrate with LocationService
    } catch (e, stackTrace) {
      AppLogger.error('Error starting location tracking', e, stackTrace);
    }
  }

  /// Stop location tracking
  Future<void> _stopLocationTracking() async {
    try {
      AppLogger.info('Stopping background location tracking');

      // Stop location tracking
    } catch (e, stackTrace) {
      AppLogger.error('Error stopping location tracking', e, stackTrace);
    }
  }

  /// Start order monitoring
  Future<void> _startOrderMonitoring() async {
    try {
      AppLogger.info('Starting background order monitoring');

      // Monitor for new orders, status changes, etc.
    } catch (e, stackTrace) {
      AppLogger.error('Error starting order monitoring', e, stackTrace);
    }
  }

  /// Stop order monitoring
  Future<void> _stopOrderMonitoring() async {
    try {
      AppLogger.info('Stopping background order monitoring');

      // Stop order monitoring
    } catch (e, stackTrace) {
      AppLogger.error('Error stopping order monitoring', e, stackTrace);
    }
  }

  /// Start notification handling
  Future<void> _startNotificationHandling() async {
    try {
      AppLogger.info('Starting background notification handling');

      // Handle background notifications
    } catch (e, stackTrace) {
      AppLogger.error('Error starting notification handling', e, stackTrace);
    }
  }

  /// Stop notification handling
  Future<void> _stopNotificationHandling() async {
    try {
      AppLogger.info('Stopping background notification handling');

      // Stop notification handling
    } catch (e, stackTrace) {
      AppLogger.error('Error stopping notification handling', e, stackTrace);
    }
  }

  /// Get service run duration in seconds
  int _getRunDuration() {
    if (_lastUpdate == null) return 0;
    return DateTime.now().difference(_lastUpdate!).inSeconds;
  }

  /// Update background service status
  void updateStatus() {
    _lastUpdate = DateTime.now();
    notifyListeners();
  }

  /// Get service statistics
  Map<String, dynamic> getStats() {
    return {
      'is_running': _isRunning,
      'is_initialized': _isInitialized,
      'last_update': _lastUpdate?.toIso8601String(),
      'run_duration_seconds': _getRunDuration(),
    };
  }

  /// Start background tasks
  Future<void> startBackgroundTasks() async {
    await start();
  }

  /// Stop background tasks
  Future<void> stopBackgroundTasks() async {
    await stop();
  }

  /// Enable background mode
  Future<void> enableBackgroundMode() async {
    await onAppBackground();
  }

  /// Disable background mode
  Future<void> disableBackgroundMode() async {
    await onAppForeground();
  }

  /// Handle app going to background
  Future<void> onAppBackground() async {
    if (_isRunning) {
      AppLogger.info('App went to background - maintaining background service');
      // Keep essential services running
    }
  }

  /// Handle app coming to foreground
  Future<void> onAppForeground() async {
    if (_isRunning) {
      AppLogger.info('App came to foreground - updating background service');
      updateStatus();
    }
  }

  @override
  void dispose() {
    if (_isRunning) {
      stop();
    }
    AppLogger.info('Background Service disposed');
    super.dispose();
  }
}

// Riverpod provider for Background Service
final backgroundServiceProvider = ChangeNotifierProvider<BackgroundService>((
  ref,
) {
  return BackgroundService();
});
