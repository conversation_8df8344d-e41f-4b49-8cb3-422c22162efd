// ignore_for_file: constant_pattern_never_matches_value_type

import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
// Removed wallet import - using payment domain models instead
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';

import '../../domain/models/payment_models.dart';
import '../../domain/models/payment_method.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/config/app_config.dart';

class PaymentGatewayService {
  static late Razorpay _razorpay;
  static final Map<String, Completer<PaymentResult>> _pendingPayments = {};

  // Payment gateway configurations
  static const String _razorpayKeyId =
      'rzp_test_1DP5mmOlF5G5ag'; // Replace with your key
  static const String _razorpayKeySecret =
      'YOUR_RAZORPAY_KEY_SECRET'; // Replace with your secret
  static const String _merchantName = 'Projek';

  // Initialize payment gateways
  static Future<void> initialize() async {
    try {
      _razorpay = Razorpay();
      _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handleRazorpaySuccess);
      _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handleRazorpayError);
      _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);

      debugPrint('✅ Payment Gateway Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Payment Gateway: $e');
    }
  }

  // Process payment based on method
  static Future<PaymentResult> processPayment({
    required PaymentRequest request,
  }) async {
    try {
      if (request.method.type case PaymentType.upi) {
        return await _processUpiPayment(request);
      } else if (request.method.type case PaymentType.card) {
        return await _processCardPayment(request);
      } else if (request.method.type case PaymentType.netBanking) {
        return await _processNetBankingPayment(request);
      } else if (request.method.type case PaymentType.wallet) {
        return await _processWalletPayment(request);
      } else if (request.method.type case PaymentType.emi) {
        return await _processEmiPayment(request);
      } else if (request.method.type case PaymentType.cod) {
        return await _processCodPayment(request);
      } else {
        throw Exception('Unsupported payment method');
      }
    } catch (e) {
      debugPrint('❌ Error processing payment: $e');
      return PaymentResult(
        success: false,
        transactionId: null,
        orderId: request.orderId,
        amount: request.amount,
        method: request.method,
        timestamp: DateTime.now(),
        error: e.toString(),
      );
    }
  }

  // UPI Payment
  static Future<PaymentResult> _processUpiPayment(
    PaymentRequest request,
  ) async {
    try {
      // For UPI, we'll use Razorpay's UPI integration
      final options = {
        'key': _razorpayKeyId,
        'amount': (request.amount * 100).toInt(), // Amount in paise
        'name': _merchantName,
        'description': request.description,
        'order_id': await _createRazorpayOrder(request),
        'method': {'upi': true},
        'prefill': {
          'contact': request.customerPhone,
          'email': request.customerEmail,
          'name': request.customerName,
        },
        'theme': {'color': '#2563EB'},
        'modal': {
          'ondismiss': () {
            _completePayment(
              request.orderId,
              PaymentResult(
                success: false,
                transactionId: null,
                orderId: request.orderId,
                amount: request.amount,
                method: request.method,
                timestamp: DateTime.now(),
                error: 'Payment cancelled by user',
              ),
            );
          },
        },
      };

      final completer = Completer<PaymentResult>();
      _pendingPayments[request.orderId] = completer;

      _razorpay.open(options);
      return await completer.future;
    } catch (e) {
      throw Exception('UPI payment failed: ${e.toString()}');
    }
  }

  // Card Payment
  static Future<PaymentResult> _processCardPayment(
    PaymentRequest request,
  ) async {
    try {
      final options = {
        'key': _razorpayKeyId,
        'amount': (request.amount * 100).toInt(),
        'name': _merchantName,
        'description': request.description,
        'order_id': await _createRazorpayOrder(request),
        'method': {'card': true},
        'prefill': {
          'contact': request.customerPhone,
          'email': request.customerEmail,
          'name': request.customerName,
        },
        'theme': {'color': '#2563EB'},
      };

      final completer = Completer<PaymentResult>();
      _pendingPayments[request.orderId] = completer;

      _razorpay.open(options);
      return await completer.future;
    } catch (e) {
      throw Exception('Card payment failed: ${e.toString()}');
    }
  }

  // Net Banking Payment
  static Future<PaymentResult> _processNetBankingPayment(
    PaymentRequest request,
  ) async {
    try {
      final options = {
        'key': _razorpayKeyId,
        'amount': (request.amount * 100).toInt(),
        'name': _merchantName,
        'description': request.description,
        'order_id': await _createRazorpayOrder(request),
        'method': {'netbanking': true},
        'prefill': {
          'contact': request.customerPhone,
          'email': request.customerEmail,
          'name': request.customerName,
        },
        'theme': {'color': '#2563EB'},
      };

      final completer = Completer<PaymentResult>();
      _pendingPayments[request.orderId] = completer;

      _razorpay.open(options);
      return await completer.future;
    } catch (e) {
      throw Exception('Net banking payment failed: ${e.toString()}');
    }
  }

  // Wallet Payment (Third-party wallets like Paytm, PhonePe)
  static Future<PaymentResult> _processWalletPayment(
    PaymentRequest request,
  ) async {
    try {
      final options = {
        'key': _razorpayKeyId,
        'amount': (request.amount * 100).toInt(),
        'name': _merchantName,
        'description': request.description,
        'order_id': await _createRazorpayOrder(request),
        'method': {'wallet': true},
        'prefill': {
          'contact': request.customerPhone,
          'email': request.customerEmail,
          'name': request.customerName,
        },
        'theme': {'color': '#2563EB'},
      };

      final completer = Completer<PaymentResult>();
      _pendingPayments[request.orderId] = completer;

      _razorpay.open(options);
      return await completer.future;
    } catch (e) {
      throw Exception('Wallet payment failed: ${e.toString()}');
    }
  }

  // EMI Payment
  static Future<PaymentResult> _processEmiPayment(
    PaymentRequest request,
  ) async {
    try {
      final options = {
        'key': _razorpayKeyId,
        'amount': (request.amount * 100).toInt(),
        'name': _merchantName,
        'description': request.description,
        'order_id': await _createRazorpayOrder(request),
        'method': {'emi': true},
        'prefill': {
          'contact': request.customerPhone,
          'email': request.customerEmail,
          'name': request.customerName,
        },
        'theme': {'color': '#2563EB'},
      };

      final completer = Completer<PaymentResult>();
      _pendingPayments[request.orderId] = completer;

      _razorpay.open(options);
      return await completer.future;
    } catch (e) {
      throw Exception('EMI payment failed: ${e.toString()}');
    }
  }

  // Cash on Delivery
  static Future<PaymentResult> _processCodPayment(
    PaymentRequest request,
  ) async {
    // COD doesn't require immediate payment processing
    return PaymentResult(
      success: true,
      transactionId: 'cod_${DateTime.now().millisecondsSinceEpoch}',
      orderId: request.orderId,
      amount: request.amount,
      method: request.method,
      timestamp: DateTime.now(),
      metadata: {'paymentMode': 'cod'},
    );
  }

  // Create Razorpay order
  static Future<String> _createRazorpayOrder(PaymentRequest request) async {
    try {
      final auth = base64Encode(
        utf8.encode('$_razorpayKeyId:$_razorpayKeySecret'),
      );

      final response = await http.post(
        Uri.parse('https://api.razorpay.com/v1/orders'),
        headers: {
          'Authorization': 'Basic $auth',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'amount': (request.amount * 100).toInt(),
          'currency': 'INR',
          'receipt': request.orderId,
          'notes': {
            'customer_id': request.customerId,
            'customer_name': request.customerName,
            'customer_email': request.customerEmail,
            'customer_phone': request.customerPhone,
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['id'];
      } else {
        throw Exception('Failed to create Razorpay order: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error creating Razorpay order: ${e.toString()}');
    }
  }

  // Verify payment signature
  static bool verifyPaymentSignature({
    required String orderId,
    required String paymentId,
    required String signature,
  }) {
    try {
      final message = '$orderId|$paymentId';
      final key = utf8.encode(_razorpayKeySecret);
      final bytes = utf8.encode(message);
      final hmac = Hmac(sha256, key);
      final digest = hmac.convert(bytes);
      final generatedSignature = digest.toString();

      return generatedSignature == signature;
    } catch (e) {
      debugPrint('❌ Error verifying signature: $e');
      return false;
    }
  }

  // Razorpay event handlers
  static void _handleRazorpaySuccess(PaymentSuccessResponse response) {
    final orderId = response.orderId;
    if (orderId != null && _pendingPayments.containsKey(orderId)) {
      final result = PaymentResult(
        success: true,
        transactionId: response.paymentId,
        orderId: orderId,
        amount: 0.0, // Will be filled from request
        method: PaymentMethod(
          id: 'razorpay',
          name: 'Razorpay',
          displayName: 'Razorpay',
          type: PaymentType.card,
          iconPath: 'assets/icons/payment/razorpay.png',
        ),
        timestamp: DateTime.now(),
        metadata: {'signature': response.signature, 'gateway': 'razorpay'},
      );

      _completePayment(orderId, result);
    }
  }

  static void _handleRazorpayError(PaymentFailureResponse response) {
    // Extract orderId from error response
    String? orderId;
    try {
      // Try to extract orderId from error data if available
      if (response.error != null && response.error is Map) {
        orderId = (response.error as Map)['order_id']?.toString();
      }
    } catch (_) {}

    if (orderId != null && _pendingPayments.containsKey(orderId)) {
      final result = PaymentResult(
        success: false,
        transactionId: null,
        orderId: orderId,
        amount: 0.0,
        method: PaymentMethod(
          id: 'razorpay',
          name: 'Razorpay',
          displayName: 'Razorpay',
          type: PaymentType.card,
          iconPath: 'assets/icons/payment/razorpay.png',
        ),
        timestamp: DateTime.now(),
        error: '${response.message} (Code: ${response.code})',
      );

      _completePayment(orderId, result);
    }
  }

  static void _handleExternalWallet(ExternalWalletResponse response) {
    // Extract orderId from wallet response
    String? orderId;
    try {
      // Try to extract orderId from wallet data if available
      if (response.walletName != null) {
        // Find the pending payment that matches this wallet
        orderId = _pendingPayments.keys.firstWhere(
          (key) => key.isNotEmpty,
          orElse: () => '',
        );
      }
    } catch (_) {}

    if (orderId != null &&
        orderId.isNotEmpty &&
        _pendingPayments.containsKey(orderId)) {
      final result = PaymentResult(
        success: true,
        transactionId: 'wallet_${DateTime.now().millisecondsSinceEpoch}',
        orderId: orderId,
        amount: 0.0,
        method: PaymentMethod(
          id: response.walletName ?? 'external_wallet',
          name: response.walletName ?? 'External Wallet',
          displayName: response.walletName ?? 'External Wallet',
          type: PaymentType.wallet,
          iconPath: 'assets/icons/payment/wallet.png',
        ),
        timestamp: DateTime.now(),
        metadata: {'wallet_name': response.walletName, 'gateway': 'razorpay'},
      );

      _completePayment(orderId, result);
    }
  }

  static void _completePayment(String orderId, PaymentResult result) {
    final completer = _pendingPayments.remove(orderId);
    if (completer != null && !completer.isCompleted) {
      completer.complete(result);
    }
  }

  // Get available payment methods
  static List<PaymentMethod> getAvailablePaymentMethods() {
    return [
      PaymentMethod(
        id: 'upi',
        name: 'UPI',
        displayName: 'UPI (Google Pay, PhonePe, Paytm)',
        type: PaymentType.upi,
        iconPath: 'assets/icons/payment/upi.png',
        isEnabled: true,
      ),
      PaymentMethod(
        id: 'card',
        name: 'Card',
        displayName: 'Credit/Debit Card',
        type: PaymentType.card,
        iconPath: 'assets/icons/payment/card.png',
        isEnabled: true,
      ),
      PaymentMethod(
        id: 'netbanking',
        name: 'Net Banking',
        displayName: 'Net Banking',
        type: PaymentType.netBanking,
        iconPath: 'assets/icons/payment/netbanking.png',
        isEnabled: true,
      ),
      PaymentMethod(
        id: 'wallet',
        name: 'Wallet',
        displayName: 'Paytm, PhonePe, Amazon Pay',
        type: PaymentType.wallet,
        iconPath: 'assets/icons/payment/wallet.png',
        isEnabled: true,
      ),
      PaymentMethod(
        id: 'emi',
        name: 'EMI',
        displayName: 'No Cost EMI',
        type: PaymentType.emi,
        iconPath: 'assets/icons/payment/emi.png',
        isEnabled: true,
      ),
      PaymentMethod(
        id: 'cod',
        name: 'COD',
        displayName: 'Cash on Delivery',
        type: PaymentType.cod,
        iconPath: 'assets/icons/payment/cod.png',
        isEnabled: true,
      ),
    ];
  }

  // Refund payment
  static Future<RefundResult> refundPayment({
    required String paymentId,
    required double amount,
    String? reason,
  }) async {
    try {
      final auth = base64Encode(
        utf8.encode('$_razorpayKeyId:$_razorpayKeySecret'),
      );

      final response = await http.post(
        Uri.parse('https://api.razorpay.com/v1/payments/$paymentId/refund'),
        headers: {
          'Authorization': 'Basic $auth',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'amount': (amount * 100).toInt(),
          'notes': {
            'reason': reason ?? 'Customer requested refund',
            'refund_timestamp': DateTime.now().toIso8601String(),
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return RefundResult(
          success: true,
          refundId: data['id'],
          amount: amount,
          status: data['status'],
          timestamp: DateTime.now(),
        );
      } else {
        throw Exception('Refund failed: ${response.body}');
      }
    } catch (e) {
      return RefundResult(
        success: false,
        refundId: null,
        amount: amount,
        status: 'failed',
        timestamp: DateTime.now(),
        error: e.toString(),
      );
    }
  }

  // Dispose
  static void dispose() {
    _razorpay.clear();
    _pendingPayments.clear();
  }
}
