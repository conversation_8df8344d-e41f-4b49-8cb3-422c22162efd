import 'dart:convert';
import 'package:crypto/crypto.dart';

/// UID Generator for Projek App
/// Combines Name + Date of Birth + 567 + Random elements
class UIDGenerator {
  static const String _magicNumber = '567';

  /// Generate UID from user details
  /// Format: [NameCode][DOB][567][RandomHash]
  static String generateUID({
    required String fullName,
    required DateTime dateOfBirth,
    String? phoneNumber,
  }) {
    // 1. Process Name (First 3 chars of first name + First 3 chars of last name)
    final nameCode = _generateNameCode(fullName);

    // 2. Process Date of Birth (DDMMYY format)
    final dobCode = _generateDOBCode(dateOfBirth);

    // 3. Add magic number 567
    final magicCode = _magicNumber;

    // 4. Generate random hash (4 characters)
    final randomHash = _generateRandomHash(fullName, dateOfBirth, phoneNumber);

    // 5. Combine all parts
    final uid = '$nameCode$dobCode$magicCode$randomHash';

    return uid.toUpperCase();
  }

  /// Generate name code from full name
  static String _generateNameCode(String fullName) {
    final names = fullName.trim().split(' ');

    if (names.isEmpty) return 'USR';

    String firstName = names.first.replaceAll(RegExp(r'[^a-zA-Z]'), '');
    String lastName = names.length > 1
        ? names.last.replaceAll(RegExp(r'[^a-zA-Z]'), '')
        : firstName;

    // Take first 3 characters of each name
    String firstCode = firstName.length >= 3
        ? firstName.substring(0, 3)
        : firstName.padRight(3, 'X');

    String lastCode = lastName.length >= 3
        ? lastName.substring(0, 3)
        : lastName.padRight(3, 'X');

    return '$firstCode$lastCode';
  }

  /// Generate DOB code (DDMMYY format)
  static String _generateDOBCode(DateTime dateOfBirth) {
    final day = dateOfBirth.day.toString().padLeft(2, '0');
    final month = dateOfBirth.month.toString().padLeft(2, '0');
    final year = (dateOfBirth.year % 100).toString().padLeft(2, '0');

    return '$day$month$year';
  }

  /// Generate random hash for uniqueness
  static String _generateRandomHash(String name, DateTime dob, String? phone) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final phoneCode = phone?.substring(phone.length - 4) ?? '0000';

    final input =
        '$name${dob.toIso8601String()}$phoneCode$timestamp$_magicNumber';
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);

    // Take first 4 characters of hash
    return digest.toString().substring(0, 4);
  }

  /// Validate UID format
  static bool isValidUID(String uid) {
    // Expected format: 6 chars (name) + 6 chars (DOB) + 3 chars (567) + 4 chars (hash) = 19 chars
    if (uid.length != 19) return false;

    // Check if contains 567
    if (!uid.contains(_magicNumber)) return false;

    // Check DOB part (positions 6-11) contains only digits
    final dobPart = uid.substring(6, 12);
    if (!RegExp(r'^\d{6}$').hasMatch(dobPart)) return false;

    return true;
  }

  /// Extract information from UID
  static Map<String, dynamic> parseUID(String uid) {
    if (!isValidUID(uid)) {
      throw ArgumentError('Invalid UID format');
    }

    final nameCode = uid.substring(0, 6);
    final dobCode = uid.substring(6, 12);
    final magicCode = uid.substring(12, 15);
    final hashCode = uid.substring(15, 19);

    // Parse DOB
    final day = int.parse(dobCode.substring(0, 2));
    final month = int.parse(dobCode.substring(2, 4));
    final year = 2000 + int.parse(dobCode.substring(4, 6));

    return {
      'nameCode': nameCode,
      'dateOfBirth': DateTime(year, month, day),
      'magicNumber': magicCode,
      'hashCode': hashCode,
      'isValid': magicCode == _magicNumber,
    };
  }

  /// Generate multiple UID suggestions
  static List<String> generateUIDSuggestions({
    required String fullName,
    required DateTime dateOfBirth,
    String? phoneNumber,
    int count = 3,
  }) {
    final suggestions = <String>[];

    for (int i = 0; i < count; i++) {
      // Add small delay to ensure different timestamps
      Future.delayed(Duration(milliseconds: i * 10));

      final uid = generateUID(
        fullName: fullName,
        dateOfBirth: dateOfBirth,
        phoneNumber: phoneNumber,
      );

      suggestions.add(uid);
    }

    return suggestions;
  }

  /// Generate UID with custom format
  static String generateCustomUID({
    required String fullName,
    required DateTime dateOfBirth,
    String? customPrefix,
    String? customSuffix,
  }) {
    final baseUID = generateUID(fullName: fullName, dateOfBirth: dateOfBirth);

    String customUID = baseUID;

    if (customPrefix != null) {
      customUID = '$customPrefix$customUID';
    }

    if (customSuffix != null) {
      customUID = '$customUID$customSuffix';
    }

    return customUID;
  }
}

/// UID Model for storing user identification
class UserUID {
  final String uid;
  final String fullName;
  final DateTime dateOfBirth;
  final DateTime createdAt;
  final String? phoneNumber;
  final Map<String, dynamic> metadata;

  UserUID({
    required this.uid,
    required this.fullName,
    required this.dateOfBirth,
    required this.createdAt,
    this.phoneNumber,
    this.metadata = const {},
  });

  /// Create UserUID from user details
  factory UserUID.create({
    required String fullName,
    required DateTime dateOfBirth,
    String? phoneNumber,
    Map<String, dynamic>? metadata,
  }) {
    final uid = UIDGenerator.generateUID(
      fullName: fullName,
      dateOfBirth: dateOfBirth,
      phoneNumber: phoneNumber,
    );

    return UserUID(
      uid: uid,
      fullName: fullName,
      dateOfBirth: dateOfBirth,
      createdAt: DateTime.now(),
      phoneNumber: phoneNumber,
      metadata: metadata ?? {},
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'fullName': fullName,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'phoneNumber': phoneNumber,
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory UserUID.fromJson(Map<String, dynamic> json) {
    return UserUID(
      uid: json['uid'],
      fullName: json['fullName'],
      dateOfBirth: DateTime.parse(json['dateOfBirth']),
      createdAt: DateTime.parse(json['createdAt']),
      phoneNumber: json['phoneNumber'],
      metadata: json['metadata'] ?? {},
    );
  }

  /// Get UID info
  Map<String, dynamic> get uidInfo => UIDGenerator.parseUID(uid);

  /// Check if UID is valid
  bool get isValid => UIDGenerator.isValidUID(uid);

  @override
  String toString() => uid;
}

/// Example Usage:
///
/// // Generate UID for user
/// final uid = UIDGenerator.generateUID(
///   fullName: 'Rahul Kumar',
///   dateOfBirth: DateTime(1995, 8, 15),
///   phoneNumber: '+************',
/// );
/// // Result: RAHKUM150895567A1B2
///
/// // Create UserUID object
/// final userUID = UserUID.create(
///   fullName: 'Priya Sharma',
///   dateOfBirth: DateTime(1992, 12, 25),
///   phoneNumber: '+************',
/// );
///
/// // Validate UID
/// final isValid = UIDGenerator.isValidUID('RAHKUM150895567A1B2');
///
/// // Parse UID information
/// final info = UIDGenerator.parseUID('RAHKUM150895567A1B2');
/// print(info['dateOfBirth']); // 1995-08-15
