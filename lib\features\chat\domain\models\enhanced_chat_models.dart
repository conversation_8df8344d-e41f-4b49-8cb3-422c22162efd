// All imports removed as they're unused - the file defines its own enums

// Remove code generation dependency and implement manual serialization
// part 'enhanced_chat_models.g.dart';

import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

@HiveType(typeId: 60)
enum EnhancedMessageType {
  @HiveField(0)
  text,
  @HiveField(1)
  image,
  @HiveField(2)
  video,
  @HiveField(3)
  audio,
  @HiveField(4)
  file,
  @HiveField(5)
  location,
  @HiveField(6)
  contact,
  @HiveField(7)
  system,
  @HiveField(8)
  sticker,
  @HiveField(9)
  orderUpdate,
  @HiveField(10)
  serviceUpdate
}

@HiveType(typeId: 61)
enum EnhancedChatType {
  @HiveField(0)
  directMessage,
  @HiveField(1)
  group,
  @HiveField(2)
  support,
  @HiveField(3)
  order,
  @HiveField(4)
  service,
  @HiveField(5)
  broadcast
}

@HiveType(typeId: 62)
enum MessageStatus {
  @HiveField(0)
  sending,
  @HiveField(1)
  sent,
  @HiveField(2)
  delivered,
  @HiveField(3)
  read,
  @HiveField(4)
  failed,
}

@HiveType(typeId: 63)
enum ParticipantRole {
  @HiveField(0)
  member,
  @HiveField(1)
  admin,
  @HiveField(2)
  moderator,
  @HiveField(3)
  owner,
  @HiveField(4)
  readonly,
}

@HiveType(typeId: 64)
class MediaAttachment extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String url;

  @HiveField(2)
  final String fileName;

  @HiveField(3)
  final String mimeType;

  @HiveField(4)
  final int fileSize;

  @HiveField(5)
  final int? width;

  @HiveField(6)
  final int? height;

  @HiveField(7)
  final int? duration; // For audio/video in seconds

  @HiveField(8)
  final String? thumbnailUrl;

  @HiveField(9)
  final Map<String, dynamic> metadata;

  const MediaAttachment({
    required this.id,
    required this.url,
    required this.fileName,
    required this.mimeType,
    required this.fileSize,
    this.width,
    this.height,
    this.duration,
    this.thumbnailUrl,
    this.metadata = const {},
  });

  factory MediaAttachment.fromJson(Map<String, dynamic> json) =>
      MediaAttachment(
        id: json['id'] as String,
        url: json['url'] as String,
        fileName: json['fileName'] as String,
        mimeType: json['mimeType'] as String,
        fileSize: json['fileSize'] as int,
        width: json['width'] as int?,
        height: json['height'] as int?,
        duration: json['duration'] as int?,
        thumbnailUrl: json['thumbnailUrl'] as String?,
        metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      );

  Map<String, dynamic> toJson() => {
    'id': id,
    'url': url,
    'fileName': fileName,
    'mimeType': mimeType,
    'fileSize': fileSize,
    'width': width,
    'height': height,
    'duration': duration,
    'thumbnailUrl': thumbnailUrl,
    'metadata': metadata,
  };

  @override
  List<Object?> get props => [
    id,
    url,
    fileName,
    mimeType,
    fileSize,
    width,
    height,
    duration,
    thumbnailUrl,
    metadata,
  ];
}

@HiveType(typeId: 65)
@JsonSerializable()
class ChatParticipant extends Equatable {
  @HiveField(0)
  final String userId;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String? email;

  @HiveField(3)
  final String? avatarUrl;

  @HiveField(4)
  final ParticipantRole role;

  @HiveField(5)
  final DateTime joinedAt;

  @HiveField(6)
  final DateTime? lastSeenAt;

  @HiveField(7)
  final bool isOnline;

  @HiveField(8)
  final bool isMuted;

  @HiveField(9)
  final Map<String, dynamic> permissions;

  const ChatParticipant({
    required this.userId,
    required this.name,
    this.email,
    this.avatarUrl,
    this.role = ParticipantRole.member,
    required this.joinedAt,
    this.lastSeenAt,
    this.isOnline = false,
    this.isMuted = false,
    this.permissions = const {},
  });

  factory ChatParticipant.fromJson(Map<String, dynamic> json) =>
      ChatParticipant(
        userId: json['userId'] as String,
        name: json['name'] as String,
        email: json['email'] as String?,
        avatarUrl: json['avatarUrl'] as String?,
        role: json['role'] != null 
            ? ParticipantRole.values.firstWhere(
                (e) => e.toString() == 'ParticipantRole.${json['role']}',
                orElse: () => ParticipantRole.member)
            : ParticipantRole.member,
        joinedAt: json['joinedAt'] != null 
            ? DateTime.parse(json['joinedAt'] as String) 
            : DateTime.now(),
        lastSeenAt: json['lastSeenAt'] != null 
            ? DateTime.parse(json['lastSeenAt'] as String) 
            : null,
        isOnline: json['isOnline'] as bool? ?? false,
        isMuted: json['isMuted'] as bool? ?? false,
        permissions: json['permissions'] as Map<String, dynamic>? ?? const {},
      );

  Map<String, dynamic> toJson() => {
    'userId': userId,
    'name': name,
    'email': email,
    'avatarUrl': avatarUrl,
    'role': role.toString().split('.').last,
    'joinedAt': joinedAt.toIso8601String(),
    'lastSeenAt': lastSeenAt?.toIso8601String(),
    'isOnline': isOnline,
    'isMuted': isMuted,
    'permissions': permissions,
  };

  @override
  List<Object?> get props => [
    userId,
    name,
    email,
    avatarUrl,
    role,
    joinedAt,
    lastSeenAt,
    isOnline,
    isMuted,
    permissions,
  ];
}

@HiveType(typeId: 66)
@JsonSerializable()
class MessageReaction extends Equatable {
  @HiveField(0)
  final String emoji;

  @HiveField(1)
  final List<String> userIds;

  @HiveField(2)
  final DateTime createdAt;

  const MessageReaction({
    required this.emoji,
    required this.userIds,
    required this.createdAt,
  });

  factory MessageReaction.fromJson(Map<String, dynamic> json) =>
      MessageReaction(
        emoji: json['emoji'] as String,
        userIds: List<String>.from(json['userIds'] as List),
        createdAt: DateTime.parse(json['createdAt'] as String),
      );

  Map<String, dynamic> toJson() => {
    'emoji': emoji,
    'userIds': userIds,
    'createdAt': createdAt.toIso8601String(),
  };

  @override
  List<Object?> get props => [emoji, userIds, createdAt];
}

@HiveType(typeId: 67)
@JsonSerializable()
class EnhancedChatMessage extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String chatId;

  @HiveField(2)
  final String senderId;

  @HiveField(3)
  final String senderName;

  @HiveField(4)
  final String? senderAvatarUrl;

  @HiveField(5)
  final EnhancedMessageType type;

  @HiveField(6)
  final String content;

  @HiveField(7)
  final List<MediaAttachment> attachments;

  @HiveField(8)
  final List<MessageReaction> reactions;

  @HiveField(9)
  final String? replyToMessageId;

  @HiveField(10)
  final String? forwardedFromChatId;

  @HiveField(11)
  final DateTime timestamp;

  @HiveField(12)
  final DateTime? editedAt;

  @HiveField(13)
  final MessageStatus status;

  @HiveField(14)
  final Map<String, DateTime> readBy;

  @HiveField(15)
  final bool isDeleted;

  @HiveField(16)
  final bool isPinned;

  @HiveField(17)
  final Map<String, dynamic> metadata;

  @HiveField(18)
  final DateTime? expiresAt; // For disappearing messages

  const EnhancedChatMessage({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    this.senderAvatarUrl,
    required this.type,
    required this.content,
    this.attachments = const [],
    this.reactions = const [],
    this.replyToMessageId,
    this.forwardedFromChatId,
    required this.timestamp,
    this.editedAt,
    this.status = MessageStatus.sending,
    this.readBy = const {},
    this.isDeleted = false,
    this.isPinned = false,
    this.metadata = const {},
    this.expiresAt,
  });

  factory EnhancedChatMessage.fromJson(Map<String, dynamic> json) =>
      EnhancedChatMessage(
        id: json['id'] as String,
        chatId: json['chatId'] as String,
        senderId: json['senderId'] as String,
        senderName: json['senderName'] as String,
        senderAvatarUrl: json['senderAvatarUrl'] as String?,
        type: json['type'] != null
            ? EnhancedMessageType.values.firstWhere(
                (e) => e.toString() == 'EnhancedMessageType.${json['type']}',
                orElse: () => EnhancedMessageType.text)
            : EnhancedMessageType.text,
        content: json['content'] as String,
        attachments: (json['attachments'] as List<dynamic>?)
                ?.map((e) => MediaAttachment.fromJson(e as Map<String, dynamic>))
                .toList() ??
            [],
        reactions: (json['reactions'] as List<dynamic>?)
                ?.map((e) => MessageReaction.fromJson(e as Map<String, dynamic>))
                .toList() ??
            [],
        replyToMessageId: json['replyToMessageId'] as String?,
        forwardedFromChatId: json['forwardedFromChatId'] as String?,
        timestamp: DateTime.parse(json['timestamp'] as String),
        editedAt: json['editedAt'] != null
            ? DateTime.parse(json['editedAt'] as String)
            : null,
        status: json['status'] != null
            ? MessageStatus.values.firstWhere(
                (e) => e.toString() == 'MessageStatus.${json['status']}',
                orElse: () => MessageStatus.sending)
            : MessageStatus.sending,
        readBy: (json['readBy'] as Map<String, dynamic>?)?.map(
              (k, e) => MapEntry(k, DateTime.parse(e as String)),
            ) ??
            {},
        isDeleted: json['isDeleted'] as bool? ?? false,
        isPinned: json['isPinned'] as bool? ?? false,
        metadata: json['metadata'] as Map<String, dynamic>? ?? {},
        expiresAt: json['expiresAt'] != null
            ? DateTime.parse(json['expiresAt'] as String)
            : null,
      );

  Map<String, dynamic> toJson() => {
    'id': id,
    'chatId': chatId,
    'senderId': senderId,
    'senderName': senderName,
    'senderAvatarUrl': senderAvatarUrl,
    'type': type.toString().split('.').last,
    'content': content,
    'attachments': attachments.map((a) => a.toJson()).toList(),
    'reactions': reactions.map((r) => r.toJson()).toList(),
    'replyToMessageId': replyToMessageId,
    'forwardedFromChatId': forwardedFromChatId,
    'timestamp': timestamp.toIso8601String(),
    'editedAt': editedAt?.toIso8601String(),
    'status': status.toString().split('.').last,
    'readBy': readBy.map((k, v) => MapEntry(k, v.toIso8601String())),
    'isDeleted': isDeleted,
    'isPinned': isPinned,
    'metadata': metadata,
    'expiresAt': expiresAt?.toIso8601String(),
  };

  factory EnhancedChatMessage.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return EnhancedChatMessage.fromJson({
      'id': doc.id,
      ...data,
      'timestamp': (data['timestamp'] as Timestamp?)
          ?.toDate()
          .toIso8601String(),
      'editedAt': (data['editedAt'] as Timestamp?)?.toDate().toIso8601String(),
      'expiresAt': (data['expiresAt'] as Timestamp?)
          ?.toDate()
          .toIso8601String(),
      'readBy':
          (data['readBy'] as Map<String, dynamic>?)?.map(
            (key, value) =>
                MapEntry(key, (value as Timestamp).toDate().toIso8601String()),
          ) ??
          {},
    });
  }

  EnhancedChatMessage copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? senderName,
    String? senderAvatarUrl,
    EnhancedMessageType? type,
    String? content,
    List<MediaAttachment>? attachments,
    List<MessageReaction>? reactions,
    String? replyToMessageId,
    String? forwardedFromChatId,
    DateTime? timestamp,
    DateTime? editedAt,
    MessageStatus? status,
    Map<String, DateTime>? readBy,
    bool? isDeleted,
    bool? isPinned,
    Map<String, dynamic>? metadata,
    DateTime? expiresAt,
  }) {
    return EnhancedChatMessage(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatarUrl: senderAvatarUrl ?? this.senderAvatarUrl,
      type: type ?? this.type,
      content: content ?? this.content,
      attachments: attachments ?? this.attachments,
      reactions: reactions ?? this.reactions,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      forwardedFromChatId: forwardedFromChatId ?? this.forwardedFromChatId,
      timestamp: timestamp ?? this.timestamp,
      editedAt: editedAt ?? this.editedAt,
      status: status ?? this.status,
      readBy: readBy ?? this.readBy,
      isDeleted: isDeleted ?? this.isDeleted,
      isPinned: isPinned ?? this.isPinned,
      metadata: metadata ?? this.metadata,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  @override
  List<Object?> get props => [
    id,
    chatId,
    senderId,
    senderName,
    senderAvatarUrl,
    type,
    content,
    attachments,
    reactions,
    replyToMessageId,
    forwardedFromChatId,
    timestamp,
    editedAt,
    status,
    readBy,
    isDeleted,
    isPinned,
    metadata,
    expiresAt,
  ];
}

@HiveType(typeId: 68)
@JsonSerializable()
class EnhancedChat extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String? description;

  @HiveField(3)
  final String? avatarUrl;

  @HiveField(4)
  final EnhancedChatType type;

  @HiveField(5)
  final List<ChatParticipant> participants;

  @HiveField(6)
  final String? orderId;

  @HiveField(7)
  final String? serviceId;

  @HiveField(8)
  final DateTime createdAt;

  @HiveField(9)
  final DateTime updatedAt;

  @HiveField(10)
  final String? lastMessageId;

  @HiveField(11)
  final String lastMessageContent;

  @HiveField(12)
  final DateTime? lastMessageTime;

  @HiveField(13)
  final String? lastMessageSenderId;

  @HiveField(14)
  final bool isActive;

  @HiveField(15)
  final bool isArchived;

  @HiveField(16)
  final Map<String, int> unreadCounts;

  @HiveField(17)
  final List<String> pinnedMessageIds;

  @HiveField(18)
  final Map<String, dynamic> settings;

  @HiveField(19)
  final Map<String, dynamic> metadata;

  const EnhancedChat({
    required this.id,
    required this.name,
    this.description,
    this.avatarUrl,
    required this.type,
    required this.participants,
    this.orderId,
    this.serviceId,
    required this.createdAt,
    required this.updatedAt,
    this.lastMessageId,
    this.lastMessageContent = '',
    this.lastMessageTime,
    this.lastMessageSenderId,
    this.isActive = true,
    this.isArchived = false,
    this.unreadCounts = const {},
    this.pinnedMessageIds = const [],
    this.settings = const {},
    this.metadata = const {},
  });

  factory EnhancedChat.fromJson(Map<String, dynamic> json) =>
      EnhancedChat(
        id: json['id'] as String,
        name: json['name'] as String,
        description: json['description'] as String?,
        avatarUrl: json['avatarUrl'] as String?,
        type: json['type'] != null
            ? EnhancedChatType.values.firstWhere(
                (e) => e.toString() == 'EnhancedChatType.${json['type']}',
                orElse: () => EnhancedChatType.directMessage)
            : EnhancedChatType.directMessage,
        participants: (json['participants'] as List<dynamic>?)
                ?.map((e) => ChatParticipant.fromJson(e as Map<String, dynamic>))
                .toList() ??
            [],
        orderId: json['orderId'] as String?,
        serviceId: json['serviceId'] as String?,
        createdAt: DateTime.parse(json['createdAt'] as String),
        updatedAt: DateTime.parse(json['updatedAt'] as String),
        lastMessageId: json['lastMessageId'] as String?,
        lastMessageContent: json['lastMessageContent'] as String? ?? '',
        lastMessageTime: json['lastMessageTime'] != null
            ? DateTime.parse(json['lastMessageTime'] as String)
            : null,
        lastMessageSenderId: json['lastMessageSenderId'] as String?,
        isActive: json['isActive'] as bool? ?? true,
        isArchived: json['isArchived'] as bool? ?? false,
        unreadCounts: (json['unreadCounts'] as Map<String, dynamic>?)?.map(
              (k, e) => MapEntry(k, e as int),
            ) ??
            {},
        pinnedMessageIds: (json['pinnedMessageIds'] as List<dynamic>?)
                ?.map((e) => e as String)
                .toList() ??
            [],
        settings: json['settings'] as Map<String, dynamic>? ?? {},
        metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      );

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'avatarUrl': avatarUrl,
    'type': type.toString().split('.').last,
    'participants': participants.map((p) => p.toJson()).toList(),
    'orderId': orderId,
    'serviceId': serviceId,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'lastMessageId': lastMessageId,
    'lastMessageContent': lastMessageContent,
    'lastMessageTime': lastMessageTime?.toIso8601String(),
    'lastMessageSenderId': lastMessageSenderId,
    'isActive': isActive,
    'isArchived': isArchived,
    'unreadCounts': unreadCounts,
    'pinnedMessageIds': pinnedMessageIds,
    'settings': settings,
    'metadata': metadata,
  };

  factory EnhancedChat.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return EnhancedChat.fromJson({
      'id': doc.id,
      ...data,
      'createdAt': (data['createdAt'] as Timestamp?)
          ?.toDate()
          .toIso8601String(),
      'updatedAt': (data['updatedAt'] as Timestamp?)
          ?.toDate()
          .toIso8601String(),
      'lastMessageTime': (data['lastMessageTime'] as Timestamp?)
          ?.toDate()
          .toIso8601String(),
    });
  }

  EnhancedChat copyWith({
    String? id,
    String? name,
    String? description,
    String? avatarUrl,
    EnhancedChatType? type,
    List<ChatParticipant>? participants,
    String? orderId,
    String? serviceId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? lastMessageId,
    String? lastMessageContent,
    DateTime? lastMessageTime,
    String? lastMessageSenderId,
    bool? isActive,
    bool? isArchived,
    Map<String, int>? unreadCounts,
    List<String>? pinnedMessageIds,
    Map<String, dynamic>? settings,
    Map<String, dynamic>? metadata,
  }) {
    return EnhancedChat(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      type: type ?? this.type,
      participants: participants ?? this.participants,
      orderId: orderId ?? this.orderId,
      serviceId: serviceId ?? this.serviceId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastMessageId: lastMessageId ?? this.lastMessageId,
      lastMessageContent: lastMessageContent ?? this.lastMessageContent,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      isActive: isActive ?? this.isActive,
      isArchived: isArchived ?? this.isArchived,
      unreadCounts: unreadCounts ?? this.unreadCounts,
      pinnedMessageIds: pinnedMessageIds ?? this.pinnedMessageIds,
      settings: settings ?? this.settings,
      metadata: metadata ?? this.metadata,
    );
  }

  int getUnreadCount(String userId) => unreadCounts[userId] ?? 0;

  bool isParticipant(String userId) =>
      participants.any((p) => p.userId == userId);

  ChatParticipant? getParticipant(String userId) =>
      participants.where((p) => p.userId == userId).firstOrNull;

  bool canUserSendMessages(String userId) {
    final participant = getParticipant(userId);
    if (participant == null) return false;
    if (participant.role == ParticipantRole.readonly) return false;
    return isActive;
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    avatarUrl,
    type,
    participants,
    orderId,
    serviceId,
    createdAt,
    updatedAt,
    lastMessageId,
    lastMessageContent,
    lastMessageTime,
    lastMessageSenderId,
    isActive,
    isArchived,
    unreadCounts,
    pinnedMessageIds,
    settings,
    metadata,
  ];
}
















