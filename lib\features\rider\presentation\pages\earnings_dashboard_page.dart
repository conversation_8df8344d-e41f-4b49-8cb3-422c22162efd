import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/config/app_config.dart';
import '../../../../demo/rider_demo_data.dart';

class EarningsDashboardPage extends ConsumerStatefulWidget {
  const EarningsDashboardPage({super.key});

  @override
  ConsumerState<EarningsDashboardPage> createState() =>
      _EarningsDashboardPageState();
}

class _EarningsDashboardPageState extends ConsumerState<EarningsDashboardPage> {
  String selectedPeriod = 'Today';
  Map<String, dynamic>? earningsData;
  List<Map<String, dynamic>>? recentRides;

  @override
  void initState() {
    super.initState();
    _loadEarningsData();
  }

  void _loadEarningsData() {
    setState(() {
      final earnings = RiderDemoData.getRiderEarnings('rider_001');
      if (earnings != null) {
        earningsData = {
          'todayEarnings': earnings.todayEarnings,
          'weeklyEarnings': earnings.weeklyEarnings,
          'monthlyEarnings': earnings.monthlyEarnings,
          'totalEarnings': earnings.totalEarnings,
          'todayRides': 8,
          'averageEarningsPerRide': earnings.stats.averageEarningPerRide,
          'averageRating': 4.8,
          'completionRate': 98.2,
          'totalRides': earnings.stats.totalRidesCompleted,
          'totalBonuses': 2500.0,
          'totalDeductions': 150.0,
        };
      }

      // Convert transactions to recent rides format
      recentRides =
          earnings?.transactions
              .map(
                (transaction) => {
                  'pickup': 'Fancy Bazaar',
                  'drop': 'Airport',
                  'distance': 12.5,
                  'duration': 25,
                  'netEarnings': transaction.netAmount,
                  'userRating': 5,
                  'date': transaction.timestamp,
                },
              )
              .toList() ??
          [];
    });
  }

  @override
  Widget build(BuildContext context) {
    if (earningsData == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'Earnings Dashboard',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              // Handle settings/filters
            },
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Period selector
            _buildPeriodSelector(),

            // Earnings summary cards
            _buildEarningsSummary(),

            // Performance metrics
            _buildPerformanceMetrics(),

            // Recent rides
            _buildRecentRides(),

            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    final periods = ['Today', 'This Week', 'This Month', 'All Time'];

    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: periods.map((period) {
          final isSelected = selectedPeriod == period;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  selectedPeriod = period;
                });
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? AppConfig.primaryColor : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  period,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : Colors.black87,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEarningsSummary() {
    final todayEarnings = earningsData!['todayEarnings'] as double;
    final weeklyEarnings = earningsData!['weeklyEarnings'] as double;
    final monthlyEarnings = earningsData!['monthlyEarnings'] as double;
    final totalEarnings = earningsData!['totalEarnings'] as double;

    double displayEarnings;
    String subtitle;

    switch (selectedPeriod) {
      case 'Today':
        displayEarnings = todayEarnings;
        subtitle = 'Today\'s earnings';
        break;
      case 'This Week':
        displayEarnings = weeklyEarnings;
        subtitle = 'This week\'s earnings';
        break;
      case 'This Month':
        displayEarnings = monthlyEarnings;
        subtitle = 'This month\'s earnings';
        break;
      default:
        displayEarnings = totalEarnings;
        subtitle = 'Total earnings';
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConfig.primaryColor,
            AppConfig.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppConfig.primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            '₹${displayEarnings.toStringAsFixed(2)}',
            style: GoogleFonts.poppins(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            subtitle,
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildEarningsStatItem(
                'Rides',
                earningsData!['todayRides'].toString(),
              ),
              _buildEarningsStatItem(
                'Avg/Ride',
                '₹${earningsData!['averageEarningsPerRide'].toStringAsFixed(0)}',
              ),
              _buildEarningsStatItem(
                'Rating',
                earningsData!['averageRating'].toString(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceMetrics() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Performance Metrics',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Completion Rate',
                  '${earningsData!['completionRate'].toStringAsFixed(1)}%',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildMetricCard(
                  'Total Rides',
                  earningsData!['totalRides'].toString(),
                  Icons.directions_car,
                  AppConfig.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Total Bonuses',
                  '₹${earningsData!['totalBonuses'].toStringAsFixed(0)}',
                  Icons.star,
                  Colors.amber,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildMetricCard(
                  'Deductions',
                  '₹${earningsData!['totalDeductions'].toStringAsFixed(0)}',
                  Icons.remove_circle,
                  Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentRides() {
    if (recentRides == null || recentRides!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Rides',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          ...recentRides!.map((ride) => _buildRideCard(ride)).toList(),
        ],
      ),
    );
  }

  Widget _buildRideCard(Map<String, dynamic> ride) {
    final date = ride['date'] as DateTime;
    final timeAgo = _getTimeAgo(date);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.check_circle, color: Colors.green, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${ride['pickup']} → ${ride['drop']}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      '$timeAgo • ${ride['distance']} km • ${ride['duration']} min',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '₹${ride['netEarnings'].toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        Icons.star,
                        size: 12,
                        color: index < ride['userRating']
                            ? Colors.amber
                            : Colors.grey[300],
                      );
                    }),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
