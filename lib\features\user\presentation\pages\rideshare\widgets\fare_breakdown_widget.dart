import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../core/theme/app_colors.dart';

class FareBreakdownWidget extends StatelessWidget {
  final Map<String, dynamic> fareDetails;
  final bool showPaymentOptions;
  final String? selectedPaymentMethod;
  final Function(String)? onPaymentMethodChanged;

  const FareBreakdownWidget({
    super.key,
    required this.fareDetails,
    this.showPaymentOptions = false,
    this.selectedPaymentMethod,
    this.onPaymentMethodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Fare Breakdown',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // Fare details
          _buildFareRow('Base Fare', fareDetails['baseFare']),
          _buildFareRow('Distance Fare', fareDetails['distanceFare']),
          _buildFareRow('Time Fare', fareDetails['timeFare']),
          _buildFareRow('Platform Fee', fareDetails['platformFee']),
          _buildFareRow('GST (5%)', fareDetails['gst']),

          const Divider(height: 24),

          // Total
          _buildFareRow('Total Amount', fareDetails['total'], isTotal: true),

          // Payment options
          if (showPaymentOptions) ...[
            const SizedBox(height: 20),
            _buildPaymentOptions(),
          ],
        ],
      ),
    );
  }

  Widget _buildFareRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
              color: isTotal ? Colors.black87 : Colors.grey[700],
            ),
          ),
          Text(
            '₹${amount.toStringAsFixed(2)}',
            style: GoogleFonts.poppins(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
              color: isTotal ? AppColors.userPrimary : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOptions() {
    final paymentMethods = [
      {
        'id': 'cash',
        'name': 'Cash',
        'icon': Icons.money,
        'description': 'Pay with cash',
      },
      {
        'id': 'upi',
        'name': 'UPI',
        'icon': Icons.qr_code,
        'description': 'Pay with UPI',
      },
      {
        'id': 'card',
        'name': 'Card',
        'icon': Icons.credit_card,
        'description': 'Pay with card',
      },
      {
        'id': 'wallet',
        'name': 'Wallet',
        'icon': Icons.account_balance_wallet,
        'description': 'Pay with wallet',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Method',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        ...paymentMethods.map((method) {
          final isSelected = selectedPaymentMethod == method['id'];
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: GestureDetector(
              onTap: () => onPaymentMethodChanged?.call(method['id'] as String),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.userPrimary.withValues(alpha: 0.1)
                      : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? AppColors.userPrimary
                        : Colors.grey[300]!,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      method['icon'] as IconData,
                      color: isSelected
                          ? AppColors.userPrimary
                          : Colors.grey[600],
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            method['name'] as String,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: isSelected
                                  ? AppColors.userPrimary
                                  : Colors.black87,
                            ),
                          ),
                          Text(
                            method['description'] as String,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (isSelected)
                      const Icon(
                        Icons.check_circle,
                        color: AppColors.userPrimary,
                        size: 20,
                      ),
                  ],
                ),
              ),
            ),
          );
        }),
      ],
    );
  }
}
