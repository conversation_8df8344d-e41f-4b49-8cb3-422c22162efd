import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async';

import '../../../../tracking/domain/models/real_time_tracking_models.dart';
import '../../../../tracking/data/services/real_time_tracking_service.dart';
import '../../../../tracking/domain/models/unified_order.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';

final trackingProvider = StreamProvider.family<RealTimeTracking, String>((
  ref,
  orderId,
) {
  return RealTimeTrackingService.getTrackingStream(orderId);
});

class RealTimeTrackingPage extends ConsumerStatefulWidget {
  final String orderId;

  const RealTimeTrackingPage({super.key, required this.orderId});

  @override
  ConsumerState<RealTimeTrackingPage> createState() =>
      _RealTimeTrackingPageState();
}

class _RealTimeTrackingPageState extends ConsumerState<RealTimeTrackingPage>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  CameraPosition? _initialCameraPosition;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _pulseAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final trackingAsync = ref.watch(trackingProvider(widget.orderId));

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        title: const Text(
          'Track Your Order',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => _showTrackingDetails(),
            icon: const Icon(Icons.info_outline),
          ),
        ],
      ),
      body: trackingAsync.when(
        data: (tracking) => _buildTrackingContent(tracking),
        loading: () => _buildLoadingState(),
        error: (error, stack) => _buildErrorState(error.toString()),
      ),
    );
  }

  Widget _buildTrackingContent(RealTimeTracking tracking) {
    return Column(
      children: [
        // Map Section
        Expanded(flex: 3, child: _buildMapSection(tracking)),

        // Bottom Sheet with Details
        Expanded(
          flex: 2,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 10,
                  offset: Offset(0, -5),
                ),
              ],
            ),
            child: _buildBottomSheet(tracking),
          ),
        ),
      ],
    );
  }

  Widget _buildMapSection(RealTimeTracking tracking) {
    return Stack(
      children: [
        // Google Map
        GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition:
              _initialCameraPosition ??
              const CameraPosition(
                target: LatLng(28.6139, 77.2090), // Default to Delhi
                zoom: 14,
              ),
          markers: _markers,
          polylines: _polylines,
          myLocationEnabled: false,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: true,
          trafficEnabled: true,
        ),

        // Floating Action Buttons
        Positioned(
          right: 16,
          bottom: 16,
          child: Column(
            children: [
              FloatingActionButton(
                mini: true,
                heroTag: 'center_map',
                onPressed: () => _centerMapOnRider(tracking),
                backgroundColor: Colors.white,
                foregroundColor: AppColors.primaryBlue,
                child: const Icon(Icons.my_location),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                mini: true,
                heroTag: 'refresh_route',
                onPressed: () => _refreshRoute(tracking),
                backgroundColor: Colors.white,
                foregroundColor: AppColors.primaryBlue,
                child: const Icon(Icons.refresh),
              ),
            ],
          ),
        ),

        // Status Overlay
        if (tracking.status != TrackingStatus.active)
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getStatusColor(tracking.status).withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    _getStatusIcon(tracking.status),
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getStatusText(tracking.status),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBottomSheet(RealTimeTracking tracking) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // ETA Section
          if (tracking.currentETA != null)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Text(
                    'Estimated Arrival',
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    tracking.currentETA!.formattedETA,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 20),

          // Order Progress
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order Progress',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Status: ${tracking.status.name}',
                  style: AppTextStyles.bodyMedium,
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Rider Info
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Delivery Partner',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Rider ID: ${tracking.riderId}',
                  style: AppTextStyles.bodyMedium,
                ),
                if (tracking.currentLocation != null)
                  Text(
                    'Last updated: ${tracking.currentLocation!.formattedSpeed}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _callRider(),
                  icon: const Icon(Icons.phone),
                  label: const Text('Call Rider'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primaryBlue,
                    side: BorderSide(color: AppColors.primaryBlue),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _chatWithRider(),
                  icon: const Icon(Icons.chat),
                  label: const Text('Chat'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading tracking information...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppColors.error),
            const SizedBox(height: 16),
            Text(
              'Tracking Unavailable',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => ref.refresh(trackingProvider(widget.orderId)),
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Map methods
  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  void _updateMapMarkers(RealTimeTracking tracking) {
    final markers = <Marker>{};

    // Add rider marker
    if (tracking.currentLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('rider'),
          position: LatLng(
            tracking.currentLocation!.latitude,
            tracking.currentLocation!.longitude,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: const InfoWindow(
            title: 'Your Rider',
            snippet: 'On the way to you',
          ),
        ),
      );
    }

    // Add destination marker
    // This would come from order data

    setState(() {
      _markers = markers;
    });
  }

  void _updateMapRoute(RealTimeTracking tracking) {
    if (tracking.activeRoute != null) {
      final route = tracking.activeRoute!;
      final polylinePoints = route.points
          .map((point) => LatLng(point.latitude, point.longitude))
          .toList();

      setState(() {
        _polylines = {
          Polyline(
            polylineId: const PolylineId('route'),
            points: polylinePoints,
            color: AppColors.primaryBlue,
            width: 4,
            patterns: [PatternItem.dash(20), PatternItem.gap(10)],
          ),
        };
      });
    }
  }

  void _centerMapOnRider(RealTimeTracking tracking) {
    if (tracking.currentLocation != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(
            tracking.currentLocation!.latitude,
            tracking.currentLocation!.longitude,
          ),
          16,
        ),
      );
    }
  }

  void _refreshRoute(RealTimeTracking tracking) {
    // Trigger route recalculation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Refreshing route...'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Helper methods
  Color _getStatusColor(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.active:
        return AppColors.success;
      case TrackingStatus.paused:
        return AppColors.warning;
      case TrackingStatus.completed:
        return AppColors.info;
      case TrackingStatus.failed:
        return AppColors.error;
      case TrackingStatus.inactive:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.active:
        return Icons.location_on;
      case TrackingStatus.paused:
        return Icons.pause;
      case TrackingStatus.completed:
        return Icons.check_circle;
      case TrackingStatus.failed:
        return Icons.error;
      case TrackingStatus.inactive:
        return Icons.location_off;
    }
  }

  String _getStatusText(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.active:
        return 'Rider is on the way';
      case TrackingStatus.paused:
        return 'Delivery paused';
      case TrackingStatus.completed:
        return 'Order delivered';
      case TrackingStatus.failed:
        return 'Tracking failed';
      case TrackingStatus.inactive:
        return 'Tracking inactive';
    }
  }

  // Action methods
  void _callRider() {
    // Implement call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Calling rider...'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _chatWithRider() {
    // Navigate to chat screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening chat...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showTrackingDetails() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.4,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle bar
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              Text(
                'Tracking Details',
                style: AppTextStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Add tracking details here
              const Text(
                'Detailed tracking information will be displayed here.',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
