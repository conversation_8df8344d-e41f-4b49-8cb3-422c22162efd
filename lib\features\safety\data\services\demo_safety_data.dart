import '../../domain/models/safety_models.dart';

class DemoSafetyData {
  // Demo emergency contacts
  static List<EmergencyContact> getDemoEmergencyContacts() {
    final now = DateTime.now();
    
    return [
      EmergencyContact(
        id: 'contact_1',
        userId: 'demo_user_1',
        name: '<PERSON>',
        phoneNumber: '+91 98765 43210',
        relationship: 'Father',
        isPrimary: true,
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      
      EmergencyContact(
        id: 'contact_2',
        userId: 'demo_user_1',
        name: '<PERSON>',
        phoneNumber: '+91 87654 32109',
        relationship: 'Wife',
        createdAt: now.subtract(const Duration(days: 25)),
        updatedAt: now.subtract(const Duration(days: 10)),
      ),
      
      EmergencyContact(
        id: 'contact_3',
        userId: 'demo_user_1',
        name: '<PERSON>',
        phoneNumber: '+91 76543 21098',
        relationship: 'Brother',
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now.subtract(const Duration(days: 15)),
      ),
      
      EmergencyContact(
        id: 'contact_4',
        userId: 'demo_user_1',
        name: 'Dr. <PERSON>',
        phoneNumber: '+91 65432 10987',
        relationship: 'Family Doctor',
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(days: 8)),
      ),
    ];
  }

  // Demo SOS alerts
  static List<SOSAlert> getDemoSOSAlerts() {
    final now = DateTime.now();
    
    return [
      SOSAlert(
        id: 'sos_1',
        userId: 'demo_user_1',
        userName: 'Demo Rider',
        userPhone: '+91 98765 43210',
        type: EmergencyType.vehicleBreakdown,
        latitude: 28.6139,
        longitude: 77.2090,
        address: 'Connaught Place, New Delhi, Delhi 110001',
        message: 'My bike broke down, need assistance',
        triggeredAt: now.subtract(const Duration(hours: 2)),
        acknowledgedAt: now.subtract(const Duration(hours: 1, minutes: 55)),
        resolvedAt: now.subtract(const Duration(hours: 1, minutes: 30)),
        status: 'resolved',
        notifiedContacts: ['contact_1', 'contact_2'],
        orderId: 'ORD12345',
      ),
      
      SOSAlert(
        id: 'sos_2',
        userId: 'demo_user_1',
        userName: 'Demo Rider',
        userPhone: '+91 98765 43210',
        type: EmergencyType.accident,
        latitude: 28.5355,
        longitude: 77.3910,
        address: 'Sector 18, Noida, Uttar Pradesh 201301',
        message: 'Minor accident, need help',
        triggeredAt: now.subtract(const Duration(days: 3)),
        acknowledgedAt: now.subtract(const Duration(days: 3, hours: -5)),
        resolvedAt: now.subtract(const Duration(days: 2, hours: 22)),
        status: 'resolved',
        notifiedContacts: ['contact_1', 'contact_2', 'contact_3'],
        orderId: 'ORD12340',
      ),
      
      SOSAlert(
        id: 'sos_3',
        userId: 'demo_user_1',
        userName: 'Demo Rider',
        userPhone: '+91 98765 43210',
        type: EmergencyType.harassment,
        latitude: 28.4595,
        longitude: 77.0266,
        address: 'Cyber City, Gurugram, Haryana 122002',
        message: 'Feeling unsafe, customer being aggressive',
        triggeredAt: now.subtract(const Duration(days: 7)),
        acknowledgedAt: now.subtract(const Duration(days: 7, hours: -10)),
        resolvedAt: now.subtract(const Duration(days: 6, hours: 20)),
        status: 'resolved',
        notifiedContacts: ['contact_1', 'contact_2'],
        orderId: 'ORD12338',
      ),
    ];
  }

  // Demo safety check-ins
  static List<SafetyCheckIn> getDemoSafetyCheckIns() {
    final now = DateTime.now();
    
    return [
      SafetyCheckIn(
        id: 'checkin_1',
        userId: 'demo_user_1',
        orderId: 'ORD12345',
        latitude: 28.6139,
        longitude: 77.2090,
        address: 'Connaught Place, New Delhi, Delhi 110001',
        scheduledAt: now.subtract(const Duration(hours: 2)),
        completedAt: now.subtract(const Duration(hours: 1, minutes: 58)),
        status: SafetyCheckStatus.completed,
        note: 'All good, delivery completed successfully',
        isAutomatic: true,
        createdAt: now.subtract(const Duration(hours: 2, minutes: 30)),
      ),
      
      SafetyCheckIn(
        id: 'checkin_2',
        userId: 'demo_user_1',
        orderId: 'ORD12344',
        latitude: 28.5355,
        longitude: 77.3910,
        address: 'Sector 18, Noida, Uttar Pradesh 201301',
        scheduledAt: now.subtract(const Duration(minutes: 30)),
        status: SafetyCheckStatus.pending,
        isAutomatic: true,
        createdAt: now.subtract(const Duration(hours: 1)),
      ),
      
      SafetyCheckIn(
        id: 'checkin_3',
        userId: 'demo_user_1',
        orderId: 'ORD12343',
        latitude: 28.4595,
        longitude: 77.0266,
        address: 'Cyber City, Gurugram, Haryana 122002',
        scheduledAt: now.subtract(const Duration(hours: 4)),
        completedAt: now.subtract(const Duration(hours: 3, minutes: 55)),
        status: SafetyCheckStatus.completed,
        note: 'Safe delivery, customer was friendly',
        createdAt: now.subtract(const Duration(hours: 4, minutes: 30)),
      ),
      
      SafetyCheckIn(
        id: 'checkin_4',
        userId: 'demo_user_1',
        orderId: 'ORD12342',
        latitude: 28.7041,
        longitude: 77.1025,
        address: 'Karol Bagh, New Delhi, Delhi 110005',
        scheduledAt: now.subtract(const Duration(hours: 6)),
        status: SafetyCheckStatus.missed,
        isAutomatic: true,
        reminderCount: 2,
        createdAt: now.subtract(const Duration(hours: 6, minutes: 30)),
      ),
      
      SafetyCheckIn(
        id: 'checkin_5',
        userId: 'demo_user_1',
        latitude: 28.6692,
        longitude: 77.4538,
        address: 'Laxmi Nagar, New Delhi, Delhi 110092',
        scheduledAt: now.subtract(const Duration(hours: 8)),
        completedAt: now.subtract(const Duration(hours: 7, minutes: 45)),
        status: SafetyCheckStatus.completed,
        note: 'Manual check-in after completing deliveries',
        createdAt: now.subtract(const Duration(hours: 8, minutes: 15)),
      ),
    ];
  }

  // Demo incident reports
  static List<IncidentReport> getDemoIncidentReports() {
    final now = DateTime.now();
    
    return [
      IncidentReport(
        id: 'incident_1',
        reporterId: 'demo_user_1',
        reporterName: 'Demo Rider',
        reporterType: 'rider',
        incidentType: EmergencyType.harassment,
        title: 'Customer Harassment',
        description: 'Customer was verbally abusive and threatened me when I arrived for delivery. Made inappropriate comments and refused to pay.',
        latitude: 28.6139,
        longitude: 77.2090,
        address: 'Connaught Place, New Delhi, Delhi 110001',
        imageUrls: ['incident_1_photo1.jpg', 'incident_1_photo2.jpg'],
        orderId: 'ORD12345',
        involvedUserId: 'customer_123',
        status: IncidentStatus.resolved,
        reportedAt: now.subtract(const Duration(days: 2)),
        acknowledgedAt: now.subtract(const Duration(days: 2, hours: -1)),
        resolvedAt: now.subtract(const Duration(days: 1)),
        assignedTo: 'support_agent_1',
        resolution: 'Customer account suspended. Rider compensated for the incident.',
      ),
      
      IncidentReport(
        id: 'incident_2',
        reporterId: 'demo_user_1',
        reporterName: 'Demo Rider',
        reporterType: 'rider',
        incidentType: EmergencyType.theft,
        title: 'Attempted Theft',
        description: 'Someone tried to snatch my delivery bag while I was parking my bike. Managed to prevent it but felt unsafe.',
        latitude: 28.5355,
        longitude: 77.3910,
        address: 'Sector 18, Noida, Uttar Pradesh 201301',
        imageUrls: ['incident_2_photo1.jpg'],
        videoUrls: ['incident_2_video1.mp4'],
        status: IncidentStatus.investigating,
        reportedAt: now.subtract(const Duration(days: 5)),
        acknowledgedAt: now.subtract(const Duration(days: 5, hours: -2)),
        assignedTo: 'support_agent_2',
      ),
      
      IncidentReport(
        id: 'incident_3',
        reporterId: 'demo_user_1',
        reporterName: 'Demo Rider',
        reporterType: 'rider',
        incidentType: EmergencyType.vehicleBreakdown,
        title: 'Bike Breakdown in Unsafe Area',
        description: 'My bike broke down in a poorly lit area late at night. Had to wait for assistance for over an hour.',
        latitude: 28.4595,
        longitude: 77.0266,
        address: 'Cyber City, Gurugram, Haryana 122002',
        orderId: 'ORD12340',
        status: IncidentStatus.acknowledged,
        reportedAt: now.subtract(const Duration(days: 8)),
        acknowledgedAt: now.subtract(const Duration(days: 8, hours: -3)),
        assignedTo: 'support_agent_1',
      ),
      
      IncidentReport(
        id: 'incident_4',
        reporterId: 'demo_user_1',
        reporterName: 'Demo Rider',
        reporterType: 'rider',
        incidentType: EmergencyType.other,
        title: 'Unsafe Delivery Location',
        description: 'Customer asked me to deliver to a construction site with no proper lighting or security. Felt unsafe going there alone.',
        latitude: 28.7041,
        longitude: 77.1025,
        address: 'Karol Bagh, New Delhi, Delhi 110005',
        orderId: 'ORD12338',
        involvedUserId: 'customer_456',
        status: IncidentStatus.reported,
        reportedAt: now.subtract(const Duration(days: 1)),
      ),
    ];
  }

  // Demo safety settings
  static SafetySettings getDemoSafetySettings() {
    return SafetySettings(
      id: 'demo_user_1',
      userId: 'demo_user_1',
      sosEnabled: true,
      autoCheckInEnabled: true,
      checkInIntervalMinutes: 30,
      locationSharingEnabled: true,
      emergencyContactsNotification: true,
      adminNotification: true,
      trustedContacts: ['contact_1', 'contact_2'],
      notificationPreferences: {
        'sms': true,
        'call': true,
        'push': true,
        'email': false,
      },
      updatedAt: DateTime.now().subtract(const Duration(days: 7)),
    );
  }

  // Get safety statistics
  static Map<String, dynamic> getSafetyStatistics() {
    return {
      'totalSOSAlerts': 1247,
      'resolvedSOSAlerts': 1198,
      'averageResponseTime': 3.2, // minutes
      'totalIncidentReports': 892,
      'resolvedIncidentReports': 756,
      'totalSafetyCheckIns': 45620,
      'completedCheckIns': 43890,
      'missedCheckIns': 1730,
      'averageCheckInCompliance': 96.2, // percentage
      'emergencyContactsAverage': 2.8,
      'ridersWithEmergencyContacts': 89.5, // percentage
      'mostCommonIncidentType': 'Vehicle Breakdown',
      'safestTimeOfDay': '10:00 AM - 2:00 PM',
      'riskiestTimeOfDay': '10:00 PM - 2:00 AM',
      'safestArea': 'Central Delhi',
      'riskiestArea': 'Industrial Areas',
    };
  }

  // Get incident type distribution
  static Map<EmergencyType, Map<String, dynamic>> getIncidentTypeDistribution() {
    return {
      EmergencyType.vehicleBreakdown: {
        'count': 342,
        'percentage': 38.3,
        'averageResolutionTime': 45, // minutes
        'severity': 'medium',
      },
      EmergencyType.harassment: {
        'count': 198,
        'percentage': 22.2,
        'averageResolutionTime': 120, // minutes
        'severity': 'high',
      },
      EmergencyType.theft: {
        'count': 156,
        'percentage': 17.5,
        'averageResolutionTime': 180, // minutes
        'severity': 'high',
      },
      EmergencyType.accident: {
        'count': 89,
        'percentage': 10.0,
        'averageResolutionTime': 15, // minutes
        'severity': 'critical',
      },
      EmergencyType.medical: {
        'count': 67,
        'percentage': 7.5,
        'averageResolutionTime': 8, // minutes
        'severity': 'critical',
      },
      EmergencyType.other: {
        'count': 40,
        'percentage': 4.5,
        'averageResolutionTime': 90, // minutes
        'severity': 'medium',
      },
    };
  }

  // Get safety trends over time
  static List<Map<String, dynamic>> getSafetyTrends() {
    final now = DateTime.now();
    
    return List.generate(30, (index) {
      final date = now.subtract(Duration(days: 29 - index));
      final random = (index * 7 + 3) % 10; // Pseudo-random for demo
      
      return {
        'date': date,
        'sosAlerts': 2 + random % 5,
        'incidentReports': 1 + random % 4,
        'safetyCheckIns': 150 + random * 10,
        'completionRate': 94.0 + (random % 6),
      };
    });
  }

  // Get area-wise safety data
  static Map<String, Map<String, dynamic>> getAreaWiseSafetyData() {
    return {
      'Central Delhi': {
        'totalIncidents': 45,
        'riskLevel': 'low',
        'averageResponseTime': 2.8,
        'commonIncidents': ['Vehicle Breakdown', 'Other'],
        'safetyScore': 8.5,
      },
      'South Delhi': {
        'totalIncidents': 67,
        'riskLevel': 'low',
        'averageResponseTime': 3.1,
        'commonIncidents': ['Vehicle Breakdown', 'Harassment'],
        'safetyScore': 8.2,
      },
      'North Delhi': {
        'totalIncidents': 89,
        'riskLevel': 'medium',
        'averageResponseTime': 3.8,
        'commonIncidents': ['Theft', 'Vehicle Breakdown'],
        'safetyScore': 7.1,
      },
      'East Delhi': {
        'totalIncidents': 123,
        'riskLevel': 'medium',
        'averageResponseTime': 4.2,
        'commonIncidents': ['Harassment', 'Theft'],
        'safetyScore': 6.8,
      },
      'West Delhi': {
        'totalIncidents': 98,
        'riskLevel': 'medium',
        'averageResponseTime': 3.9,
        'commonIncidents': ['Vehicle Breakdown', 'Theft'],
        'safetyScore': 7.0,
      },
      'Noida': {
        'totalIncidents': 156,
        'riskLevel': 'high',
        'averageResponseTime': 5.1,
        'commonIncidents': ['Theft', 'Harassment'],
        'safetyScore': 6.2,
      },
      'Gurugram': {
        'totalIncidents': 178,
        'riskLevel': 'high',
        'averageResponseTime': 5.8,
        'commonIncidents': ['Harassment', 'Accident'],
        'safetyScore': 5.9,
      },
      'Faridabad': {
        'totalIncidents': 134,
        'riskLevel': 'high',
        'averageResponseTime': 4.9,
        'commonIncidents': ['Vehicle Breakdown', 'Theft'],
        'safetyScore': 6.4,
      },
    };
  }
}
