import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../widgets/safety_widgets.dart';
import '../../domain/models/safety_models.dart';
import '../../data/services/demo_safety_data.dart';

class SafetyDashboardPage extends ConsumerStatefulWidget {
  const SafetyDashboardPage({super.key});

  @override
  ConsumerState<SafetyDashboardPage> createState() =>
      _SafetyDashboardPageState();
}

class _SafetyDashboardPageState extends ConsumerState<SafetyDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<EmergencyContact> _emergencyContacts = [];
  List<SafetyCheckIn> _safetyCheckIns = [];
  List<IncidentReport> _incidentReports = [];
  bool _isLoading = true;
  bool _sosActive = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSafetyData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSafetyData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // For demo purposes, use demo data
      _emergencyContacts = DemoSafetyData.getDemoEmergencyContacts();
      _safetyCheckIns = DemoSafetyData.getDemoSafetyCheckIns();
      _incidentReports = DemoSafetyData.getDemoIncidentReports();
    } catch (e) {
      debugPrint('Error loading safety data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Safety Center'),
        backgroundColor: AppColors.error,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showSafetySettings,
            icon: const Icon(Icons.settings),
            tooltip: 'Safety Settings',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'SOS'),
            Tab(text: 'Contacts'),
            Tab(text: 'Check-ins'),
            Tab(text: 'Reports'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildSOSTab(),
                _buildContactsTab(),
                _buildCheckInsTab(),
                _buildReportsTab(),
              ],
            ),
    );
  }

  Widget _buildSOSTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 32),

          // SOS Button
          SOSButton(onPressed: _triggerSOS, isActive: _sosActive, size: 150),

          const SizedBox(height: 24),

          Text(
            _sosActive ? 'SOS ACTIVATED' : 'Emergency SOS',
            style: AppTextStyles.headlineMedium.copyWith(
              color: _sosActive ? AppColors.error : AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          Text(
            _sosActive
                ? 'Emergency services and contacts have been notified'
                : 'Press and hold for 3 seconds to activate emergency alert',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Emergency Types
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Emergency Types', style: AppTextStyles.headlineSmall),
                  const SizedBox(height: 16),

                  GridView.count(
                    crossAxisCount: 2,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    children: EmergencyType.values.map((type) {
                      final config = SafetyConfig.getEmergencyTypeConfig(type);
                      final color = Color(config?['color'] ?? 0xFF9E9E9E);

                      return InkWell(
                        onTap: () => _showEmergencyTypeDialog(type),
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: color.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: color.withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                _getEmergencyTypeIcon(type),
                                color: color,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  config?['name'] ?? type.toString(),
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: color,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Quick Actions
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Quick Actions', style: AppTextStyles.headlineSmall),
                  const SizedBox(height: 16),

                  ListTile(
                    leading: const Icon(
                      Icons.location_on,
                      color: AppColors.primaryBlue,
                    ),
                    title: const Text('Share Live Location'),
                    subtitle: const Text(
                      'Share your location with emergency contacts',
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: _shareLocation,
                  ),

                  const Divider(),

                  ListTile(
                    leading: const Icon(
                      Icons.check_circle,
                      color: AppColors.success,
                    ),
                    title: const Text('Safety Check-in'),
                    subtitle: const Text('Let contacts know you\'re safe'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: _performSafetyCheckIn,
                  ),

                  const Divider(),

                  ListTile(
                    leading: const Icon(Icons.report, color: AppColors.warning),
                    title: const Text('Report Incident'),
                    subtitle: const Text('Report a safety concern or incident'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: _reportIncident,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('Emergency Contacts', style: AppTextStyles.headlineMedium),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _addEmergencyContact,
                icon: const Icon(Icons.add),
                label: const Text('Add Contact'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (_emergencyContacts.isEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(Icons.contacts, size: 64, color: AppColors.grey400),
                    const SizedBox(height: 16),
                    Text(
                      'No Emergency Contacts',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add emergency contacts to notify them during emergencies',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _emergencyContacts.length,
              itemBuilder: (context, index) {
                final contact = _emergencyContacts[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: EmergencyContactCard(
                    contact: contact,
                    onEdit: () => _editEmergencyContact(contact),
                    onDelete: () => _deleteEmergencyContact(contact),
                    onCall: () => _callEmergencyContact(contact),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCheckInsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('Safety Check-ins', style: AppTextStyles.headlineMedium),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _performSafetyCheckIn,
                icon: const Icon(Icons.check_circle),
                label: const Text('Check In'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Check-in Status
          Card(
            color: AppColors.success.withOpacity(0.1),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: AppColors.success, size: 32),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Last Check-in',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        Text(
                          '2 hours ago',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.success,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    'All Good',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          Text('Recent Check-ins', style: AppTextStyles.headlineSmall),
          const SizedBox(height: 16),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _safetyCheckIns.length,
            itemBuilder: (context, index) {
              final checkIn = _safetyCheckIns[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: SafetyCheckInCard(
                  checkIn: checkIn,
                  onComplete: checkIn.status == SafetyCheckStatus.pending
                      ? () => _completeSafetyCheckIn(checkIn)
                      : null,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('Incident Reports', style: AppTextStyles.headlineMedium),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _reportIncident,
                icon: const Icon(Icons.report),
                label: const Text('Report'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warning,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (_incidentReports.isEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.report_outlined,
                      size: 64,
                      color: AppColors.grey400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Incident Reports',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Report any safety concerns or incidents here',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _incidentReports.length,
              itemBuilder: (context, index) {
                final report = _incidentReports[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: IncidentReportCard(
                    report: report,
                    onTap: () => _viewIncidentReport(report),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  IconData _getEmergencyTypeIcon(EmergencyType type) {
    switch (type) {
      case EmergencyType.accident:
        return Icons.car_crash;
      case EmergencyType.medical:
        return Icons.medical_services;
      case EmergencyType.theft:
        return Icons.security;
      case EmergencyType.harassment:
        return Icons.report_problem;
      case EmergencyType.vehicleBreakdown:
        return Icons.build;
      case EmergencyType.other:
        return Icons.warning;
    }
  }

  void _triggerSOS() {
    setState(() {
      _sosActive = !_sosActive;
    });

    if (_sosActive) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
            'SOS Alert Activated! Emergency contacts notified.',
          ),
          backgroundColor: AppColors.error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _showEmergencyTypeDialog(EmergencyType type) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(SafetyConfig.getEmergencyTypeName(type)),
        content: Text(
          'Trigger SOS alert for ${SafetyConfig.getEmergencyTypeName(type).toLowerCase()}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _triggerSOS();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Trigger SOS'),
          ),
        ],
      ),
    );
  }

  void _shareLocation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Location sharing activated'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _performSafetyCheckIn() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Safety check-in completed'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _reportIncident() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Incident report form would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _addEmergencyContact() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add emergency contact form would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _editEmergencyContact(EmergencyContact contact) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit ${contact.name}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _deleteEmergencyContact(EmergencyContact contact) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Contact'),
        content: Text('Remove ${contact.name} from emergency contacts?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    '${contact.name} removed from emergency contacts',
                  ),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _callEmergencyContact(EmergencyContact contact) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling ${contact.name}...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _completeSafetyCheckIn(SafetyCheckIn checkIn) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Safety check-in completed'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _viewIncidentReport(IncidentReport report) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing report: ${report.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showSafetySettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Safety settings would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
