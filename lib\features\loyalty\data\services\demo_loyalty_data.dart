import '../../domain/models/loyalty_models.dart';

class DemoLoyaltyData {
  // Demo loyalty account
  static LoyaltyAccount getDemoLoyaltyAccount() {
    return LoyaltyAccount(
      id: 'loyalty_account_1',
      userId: 'demo_user_1',
      totalPoints: 2450,
      availablePoints: 1850,
      totalCashback: 485.50,
      availableCashback: 125.75,
      currentTier: UserTier.silver,
      tierProgress: 8500,
      tierThreshold: 15000,
      lifetimeSpending: 13500.0,
      totalReferrals: 3,
      createdAt: DateTime.now().subtract(const Duration(days: 120)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      tierBenefits: TierConfig.getTierConfig(UserTier.silver) ?? {},
    );
  }

  // Demo reward offers
  static List<RewardOffer> getDemoRewardOffers() {
    final now = DateTime.now();
    
    return [
      RewardOffer(
        id: 'offer_1',
        title: 'Free Delivery Voucher',
        description: 'Get free delivery on your next 3 orders. Valid for orders above ₹200.',
        type: RewardType.freeDelivery,
        pointsCost: 200,
        imageUrl: 'assets/images/rewards/free_delivery.png',
        eligibleTiers: [UserTier.bronze, UserTier.silver, UserTier.gold, UserTier.platinum, UserTier.diamond],
        validFrom: now.subtract(const Duration(days: 30)),
        validUntil: now.add(const Duration(days: 60)),
        maxRedemptions: 1000,
        currentRedemptions: 245,
        categories: ['food', 'grocery'],
        terms: {
          'minOrderValue': 200,
          'maxUsage': 3,
          'validityDays': 30,
        },
      ),
      
      RewardOffer(
        id: 'offer_2',
        title: '₹100 Cashback Voucher',
        description: 'Get ₹100 cashback on orders above ₹500. Can be used once per month.',
        type: RewardType.cashback,
        pointsCost: 500,
        cashbackValue: 100.0,
        imageUrl: 'assets/images/rewards/cashback_100.png',
        eligibleTiers: [UserTier.silver, UserTier.gold, UserTier.platinum, UserTier.diamond],
        validFrom: now.subtract(const Duration(days: 15)),
        validUntil: now.add(const Duration(days: 45)),
        maxRedemptions: 500,
        currentRedemptions: 89,
        categories: ['food', 'grocery', 'electronics'],
        terms: {
          'minOrderValue': 500,
          'maxUsagePerMonth': 1,
          'validityDays': 30,
        },
      ),
      
      RewardOffer(
        id: 'offer_3',
        title: '20% Discount on Electronics',
        description: 'Get 20% off on all electronics. Maximum discount of ₹500.',
        type: RewardType.discount,
        pointsCost: 800,
        discountPercentage: 20.0,
        imageUrl: 'assets/images/rewards/electronics_discount.png',
        eligibleTiers: [UserTier.gold, UserTier.platinum, UserTier.diamond],
        validFrom: now.subtract(const Duration(days: 10)),
        validUntil: now.add(const Duration(days: 30)),
        maxRedemptions: 200,
        currentRedemptions: 45,
        categories: ['electronics'],
        terms: {
          'maxDiscount': 500,
          'validityDays': 15,
          'applicableCategories': ['electronics', 'gadgets'],
        },
      ),
      
      RewardOffer(
        id: 'offer_4',
        title: 'Premium Membership Trial',
        description: 'Get 1 month free premium membership with exclusive benefits and priority support.',
        type: RewardType.voucher,
        pointsCost: 1500,
        imageUrl: 'assets/images/rewards/premium_trial.png',
        eligibleTiers: [UserTier.gold, UserTier.platinum, UserTier.diamond],
        validFrom: now.subtract(const Duration(days: 5)),
        validUntil: now.add(const Duration(days: 90)),
        maxRedemptions: 100,
        currentRedemptions: 12,
        categories: ['premium'],
        terms: {
          'trialDuration': 30,
          'autoRenewal': false,
          'benefits': ['Priority support', 'Exclusive offers', 'Free delivery'],
        },
      ),
      
      RewardOffer(
        id: 'offer_5',
        title: '₹50 Food Voucher',
        description: 'Get ₹50 off on food orders. Valid at all partner restaurants.',
        type: RewardType.voucher,
        pointsCost: 300,
        imageUrl: 'assets/images/rewards/food_voucher.png',
        eligibleTiers: [UserTier.bronze, UserTier.silver, UserTier.gold, UserTier.platinum, UserTier.diamond],
        validFrom: now.subtract(const Duration(days: 20)),
        validUntil: now.add(const Duration(days: 40)),
        maxRedemptions: -1, // Unlimited
        currentRedemptions: 567,
        categories: ['food'],
        terms: {
          'minOrderValue': 200,
          'validityDays': 30,
          'applicableRestaurants': 'all',
        },
      ),
      
      RewardOffer(
        id: 'offer_6',
        title: 'Double Points Weekend',
        description: 'Earn double points on all orders during weekends. Valid for next 4 weekends.',
        type: RewardType.points,
        pointsCost: 0, // Free offer
        imageUrl: 'assets/images/rewards/double_points.png',
        eligibleTiers: [UserTier.platinum, UserTier.diamond],
        validFrom: now,
        validUntil: now.add(const Duration(days: 28)),
        maxRedemptions: 1,
        currentRedemptions: 0,
        categories: ['all'],
        terms: {
          'multiplier': 2.0,
          'applicableDays': ['Saturday', 'Sunday'],
          'maxWeekends': 4,
        },
      ),
    ];
  }

  // Demo reward transactions
  static List<RewardTransaction> getDemoRewardTransactions() {
    final now = DateTime.now();
    
    return [
      RewardTransaction(
        id: 'txn_1',
        userId: 'demo_user_1',
        type: TransactionType.earned,
        rewardType: RewardType.points,
        points: 85,
        description: 'Points earned from order #ORD12345',
        orderId: 'ORD12345',
        createdAt: now.subtract(const Duration(hours: 2)),
      ),
      
      RewardTransaction(
        id: 'txn_2',
        userId: 'demo_user_1',
        type: TransactionType.earned,
        rewardType: RewardType.cashback,
        cashback: 25.50,
        description: 'Cashback earned from order #ORD12345',
        orderId: 'ORD12345',
        createdAt: now.subtract(const Duration(hours: 2)),
      ),
      
      RewardTransaction(
        id: 'txn_3',
        userId: 'demo_user_1',
        type: TransactionType.redeemed,
        rewardType: RewardType.points,
        points: -200,
        description: 'Points redeemed for Free Delivery Voucher',
        referenceId: 'redemption_1',
        createdAt: now.subtract(const Duration(days: 1)),
      ),
      
      RewardTransaction(
        id: 'txn_4',
        userId: 'demo_user_1',
        type: TransactionType.bonus,
        rewardType: RewardType.referralBonus,
        points: 500,
        cashback: 50.0,
        description: 'Referral bonus for inviting a friend',
        referenceId: 'referral_1',
        createdAt: now.subtract(const Duration(days: 3)),
      ),
      
      RewardTransaction(
        id: 'txn_5',
        userId: 'demo_user_1',
        type: TransactionType.earned,
        rewardType: RewardType.points,
        points: 120,
        description: 'Points earned from order #ORD12340',
        orderId: 'ORD12340',
        createdAt: now.subtract(const Duration(days: 5)),
      ),
      
      RewardTransaction(
        id: 'txn_6',
        userId: 'demo_user_1',
        type: TransactionType.earned,
        rewardType: RewardType.cashback,
        cashback: 35.75,
        description: 'Cashback earned from order #ORD12340',
        orderId: 'ORD12340',
        createdAt: now.subtract(const Duration(days: 5)),
      ),
      
      RewardTransaction(
        id: 'txn_7',
        userId: 'demo_user_1',
        type: TransactionType.bonus,
        rewardType: RewardType.points,
        points: 100,
        description: 'Welcome bonus for joining Projek!',
        createdAt: now.subtract(const Duration(days: 120)),
      ),
      
      RewardTransaction(
        id: 'txn_8',
        userId: 'demo_user_1',
        type: TransactionType.earned,
        rewardType: RewardType.points,
        points: 95,
        description: 'Points earned from order #ORD12338',
        orderId: 'ORD12338',
        createdAt: now.subtract(const Duration(days: 8)),
      ),
      
      RewardTransaction(
        id: 'txn_9',
        userId: 'demo_user_1',
        type: TransactionType.redeemed,
        rewardType: RewardType.points,
        points: -300,
        description: 'Points redeemed for ₹50 Food Voucher',
        referenceId: 'redemption_2',
        createdAt: now.subtract(const Duration(days: 10)),
      ),
      
      RewardTransaction(
        id: 'txn_10',
        userId: 'demo_user_1',
        type: TransactionType.earned,
        rewardType: RewardType.cashback,
        cashback: 42.25,
        description: 'Cashback earned from order #ORD12335',
        orderId: 'ORD12335',
        createdAt: now.subtract(const Duration(days: 12)),
      ),
    ];
  }

  // Demo referral programs
  static List<ReferralProgram> getDemoReferralPrograms() {
    final now = DateTime.now();
    
    return [
      ReferralProgram(
        id: 'ref_1',
        referrerId: 'demo_user_1',
        refereeId: 'demo_user_2',
        referralCode: 'PROJEK2024',
        referredAt: now.subtract(const Duration(days: 30)),
        rewardedAt: now.subtract(const Duration(days: 28)),
        isCompleted: true,
        completionOrderId: 'ORD12300',
      ),
      
      ReferralProgram(
        id: 'ref_2',
        referrerId: 'demo_user_1',
        refereeId: 'demo_user_3',
        referralCode: 'PROJEK2024',
        referredAt: now.subtract(const Duration(days: 15)),
        rewardedAt: now.subtract(const Duration(days: 13)),
        isCompleted: true,
        completionOrderId: 'ORD12320',
      ),
      
      ReferralProgram(
        id: 'ref_3',
        referrerId: 'demo_user_1',
        refereeId: 'demo_user_4',
        referralCode: 'PROJEK2024',
        referredAt: now.subtract(const Duration(days: 5)),
        isCompleted: false,
      ),
    ];
  }

  // Demo reward redemptions
  static List<RewardRedemption> getDemoRewardRedemptions() {
    final now = DateTime.now();
    
    return [
      RewardRedemption(
        id: 'redemption_1',
        userId: 'demo_user_1',
        offerId: 'offer_1',
        offerTitle: 'Free Delivery Voucher',
        pointsUsed: 200,
        redeemedAt: now.subtract(const Duration(days: 1)),
        expiresAt: now.add(const Duration(days: 29)),
        redemptionCode: 'FD2024ABC123',
      ),
      
      RewardRedemption(
        id: 'redemption_2',
        userId: 'demo_user_1',
        offerId: 'offer_5',
        offerTitle: '₹50 Food Voucher',
        pointsUsed: 300,
        redeemedAt: now.subtract(const Duration(days: 10)),
        usedAt: now.subtract(const Duration(days: 8)),
        expiresAt: now.add(const Duration(days: 20)),
        status: 'used',
        orderId: 'ORD12340',
        redemptionCode: 'FV2024XYZ789',
      ),
      
      RewardRedemption(
        id: 'redemption_3',
        userId: 'demo_user_1',
        offerId: 'offer_2',
        offerTitle: '₹100 Cashback Voucher',
        pointsUsed: 500,
        cashbackUsed: 100.0,
        redeemedAt: now.subtract(const Duration(days: 20)),
        usedAt: now.subtract(const Duration(days: 18)),
        expiresAt: now.add(const Duration(days: 10)),
        status: 'used',
        orderId: 'ORD12335',
        redemptionCode: 'CB2024PQR456',
      ),
    ];
  }

  // Get loyalty statistics
  static Map<String, dynamic> getLoyaltyStatistics() {
    return {
      'totalUsers': 15420,
      'activeUsers': 8750,
      'totalPointsIssued': 2450000,
      'totalPointsRedeemed': 1680000,
      'totalCashbackIssued': 485000.0,
      'totalCashbackRedeemed': 320000.0,
      'averagePointsPerUser': 280,
      'averageCashbackPerUser': 55.5,
      'topRedemptionCategory': 'Food & Dining',
      'mostPopularReward': 'Free Delivery Voucher',
      'referralConversionRate': 68.5,
      'tierDistribution': {
        'bronze': 45.2,
        'silver': 32.8,
        'gold': 15.6,
        'platinum': 5.2,
        'diamond': 1.2,
      },
    };
  }

  // Get tier progression data
  static Map<UserTier, Map<String, dynamic>> getTierProgressionData() {
    return {
      UserTier.bronze: {
        'users': 6970,
        'averageSpending': 2500.0,
        'averagePoints': 150,
        'retentionRate': 72.5,
      },
      UserTier.silver: {
        'users': 5058,
        'averageSpending': 8500.0,
        'averagePoints': 420,
        'retentionRate': 85.2,
      },
      UserTier.gold: {
        'users': 2405,
        'averageSpending': 28000.0,
        'averagePoints': 1250,
        'retentionRate': 92.8,
      },
      UserTier.platinum: {
        'users': 802,
        'averageSpending': 68000.0,
        'averagePoints': 3200,
        'retentionRate': 96.5,
      },
      UserTier.diamond: {
        'users': 185,
        'averageSpending': 150000.0,
        'averagePoints': 8500,
        'retentionRate': 98.9,
      },
    };
  }
}
