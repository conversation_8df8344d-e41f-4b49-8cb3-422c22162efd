import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class OrderSummaryWidget extends StatelessWidget {
  final String orderId;

  const OrderSummaryWidget({
    super.key,
    required this.orderId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                color: AppColors.primaryBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Order #${orderId.substring(0, 8)}',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  'Pickup',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.info,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Customer Info
          _buildInfoRow(
            icon: Icons.person,
            label: 'Customer',
            value: 'John Doe',
          ),
          
          const SizedBox(height: 8),
          
          // Phone Number
          _buildInfoRow(
            icon: Icons.phone,
            label: 'Phone',
            value: '+91 98765 43210',
            isClickable: true,
          ),
          
          const SizedBox(height: 8),
          
          // Delivery Address
          _buildInfoRow(
            icon: Icons.location_on,
            label: 'Address',
            value: '123 Main Street, Sector 15, Noida, UP 201301',
            maxLines: 2,
          ),
          
          const SizedBox(height: 8),
          
          // Order Value
          _buildInfoRow(
            icon: Icons.currency_rupee,
            label: 'Order Value',
            value: '₹450.00',
          ),
          
          const SizedBox(height: 12),
          
          // Special Instructions
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.warning.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info,
                  color: AppColors.warning,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Please call before delivery. Ring the doorbell twice.',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    bool isClickable = false,
    int maxLines = 1,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: AppColors.textSecondary,
          size: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 2),
              GestureDetector(
                onTap: isClickable ? () => _handleClick(label, value) : null,
                child: Text(
                  value,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isClickable ? AppColors.primaryBlue : AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                    decoration: isClickable ? TextDecoration.underline : null,
                  ),
                  maxLines: maxLines,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _handleClick(String label, String value) {
    if (label == 'Phone') {
      // Handle phone call
      debugPrint('Calling $value');
    }
  }
}
