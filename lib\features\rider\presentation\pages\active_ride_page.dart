import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../demo/rideshare_demo_data.dart';
import '../../../rideshare/services/commission_service.dart';
import '../widgets/otp_input_widget.dart';

class ActiveRidePage extends ConsumerStatefulWidget {
  final String rideId;

  const ActiveRidePage({super.key, required this.rideId});

  @override
  ConsumerState<ActiveRidePage> createState() => _ActiveRidePageState();
}

class _ActiveRidePageState extends ConsumerState<ActiveRidePage> {
  String rideStatus = 'in_progress';
  Map<String, dynamic>? rideData;
  CommissionBreakdown? completedRideCommission;
  bool showOTPInput = false;

  @override
  void initState() {
    super.initState();
    _loadRideData();
    _simulateRideProgress();
  }

  void _loadRideData() {
    // Load ride data from demo
    setState(() {
      rideData = {
        'rideId': widget.rideId,
        'customer': {
          'name': 'Priya Sharma',
          'phone': '+91 98765 43210',
          'rating': 4.8,
        },
        'pickup': 'Paltan Bazaar, Guwahati',
        'drop': 'Kamakhya Temple, Guwahati',
        'rideType': 'economy',
        'totalFare': 180.0,
        'distance': 8.5,
        'estimatedDuration': 25,
        'startTime': DateTime.now().subtract(const Duration(minutes: 15)),
      };
    });
  }

  void _simulateRideProgress() {
    // Simulate reaching destination after some time
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          rideStatus = 'arrived_destination';
          showOTPInput = true;
        });
      }
    });
  }

  void _onOTPVerified(bool isValid, CommissionBreakdown? commission) {
    if (isValid && commission != null) {
      setState(() {
        rideStatus = 'completed';
        completedRideCommission = commission;
        showOTPInput = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (rideData == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'Active Ride',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Ride Status Card
            _buildRideStatusCard(),

            // Customer Info Card
            _buildCustomerInfoCard(),

            // Trip Details Card
            _buildTripDetailsCard(),

            // OTP Input (when at destination)
            if (showOTPInput)
              Container(
                margin: const EdgeInsets.all(16),
                child: OTPInputWidget(
                  rideId: widget.rideId,
                  onOTPVerified: _onOTPVerified,
                ),
              ),

            // Commission Display (after completion)
            if (completedRideCommission != null) _buildCommissionCard(),

            // Action Buttons
            _buildActionButtons(),

            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  Widget _buildRideStatusCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getStatusColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getStatusIcon(),
                  color: _getStatusColor(),
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getStatusText(),
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      _getStatusDescription(),
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          if (rideStatus == 'in_progress') ...[
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: 0.7,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor()),
            ),
            const SizedBox(height: 8),
            Text(
              'Trip in progress - 70% completed',
              style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCustomerInfoCard() {
    final customer = rideData!['customer'] as Map<String, dynamic>;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: AppColors.userPrimary.withValues(alpha: 0.1),
            child: Icon(Icons.person, color: AppColors.userPrimary, size: 28),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer['name'],
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Row(
                  children: [
                    Icon(Icons.star, size: 16, color: Colors.amber),
                    const SizedBox(width: 4),
                    Text(
                      customer['rating'].toString(),
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // Handle call customer
            },
            icon: const Icon(Icons.phone, color: AppColors.userPrimary),
          ),
          IconButton(
            onPressed: () {
              // Handle message customer
            },
            icon: const Icon(Icons.message, color: AppColors.userPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildTripDetailsCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trip Details',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Pickup location
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: AppColors.accentGreen,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pickup',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      rideData!['pickup'],
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Drop location
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: AppColors.error,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Drop',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      rideData!['drop'],
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Trip stats
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('Distance', '${rideData!['distance']} km'),
              _buildStatItem('Fare', '₹${rideData!['totalFare']}'),
              _buildStatItem(
                'Type',
                rideData!['rideType'].toString().toUpperCase(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.userPrimary,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildCommissionCard() {
    if (completedRideCommission == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.account_balance_wallet,
                  color: AppColors.success,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Earnings',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      'Commission breakdown for this ride',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Net earnings display
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.success.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Net Earnings',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  '₹${completedRideCommission!.netEarnings.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Commission breakdown
          _buildCommissionItem(
            'Base Commission (${(completedRideCommission!.baseCommissionRate * 100).toInt()}%)',
            completedRideCommission!.baseCommission,
          ),
          if (completedRideCommission!.peakHourBonus > 0)
            _buildCommissionItem(
              'Peak Hour Bonus',
              completedRideCommission!.peakHourBonus,
              isBonus: true,
            ),
          _buildCommissionItem(
            'Completion Bonus',
            completedRideCommission!.completionBonus,
            isBonus: true,
          ),
          if (completedRideCommission!.ratingBonus > 0)
            _buildCommissionItem(
              'Rating Bonus',
              completedRideCommission!.ratingBonus,
              isBonus: true,
            ),
          _buildCommissionItem(
            'Platform Fee',
            completedRideCommission!.platformFee,
            isDeduction: true,
          ),
        ],
      ),
    );
  }

  Widget _buildCommissionItem(
    String label,
    double amount, {
    bool isBonus = false,
    bool isDeduction = false,
  }) {
    Color color = Colors.black87;
    String prefix = '';

    if (isBonus) {
      color = AppColors.success;
      prefix = '+';
    } else if (isDeduction) {
      color = AppColors.error;
      prefix = '-';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[600]),
          ),
          Text(
            '$prefix₹${amount.toStringAsFixed(2)}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    if (rideStatus == 'completed') {
      return Container(
        margin: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  // Navigate to earnings dashboard
                },
                icon: const Icon(Icons.dashboard, size: 20),
                label: Text(
                  'View Earnings Dashboard',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.userPrimary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => context.go('/rider/dashboard'),
                icon: const Icon(Icons.home, size: 20),
                label: Text(
                  'Back to Dashboard',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.userPrimary,
                  side: const BorderSide(color: AppColors.userPrimary),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                // Handle emergency/help
              },
              icon: const Icon(Icons.help_outline, size: 20),
              label: Text('Help', style: GoogleFonts.poppins(fontSize: 14)),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.grey[600],
                side: BorderSide(color: Colors.grey[300]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: rideStatus == 'arrived_destination'
                  ? null
                  : () {
                      // Handle navigation/directions
                    },
              icon: const Icon(Icons.navigation, size: 20),
              label: Text(
                'Navigate',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.userPrimary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getStatusColor() {
    switch (rideStatus) {
      case 'in_progress':
        return AppColors.accentGreen;
      case 'arrived_destination':
        return Colors.orange;
      case 'completed':
        return AppColors.success;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (rideStatus) {
      case 'in_progress':
        return Icons.directions_car;
      case 'arrived_destination':
        return Icons.location_on;
      case 'completed':
        return Icons.check_circle;
      default:
        return Icons.info;
    }
  }

  String _getStatusText() {
    switch (rideStatus) {
      case 'in_progress':
        return 'Trip in Progress';
      case 'arrived_destination':
        return 'Arrived at Destination';
      case 'completed':
        return 'Trip Completed';
      default:
        return 'Unknown Status';
    }
  }

  String _getStatusDescription() {
    switch (rideStatus) {
      case 'in_progress':
        return 'Driving to destination';
      case 'arrived_destination':
        return 'Ask customer for OTP to complete trip';
      case 'completed':
        return 'Trip completed successfully';
      default:
        return '';
    }
  }
}
