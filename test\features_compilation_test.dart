import 'package:flutter_test/flutter_test.dart';

// Test imports for all new features to ensure they compile correctly
import '../lib/features/rating/domain/models/rating_models.dart';
import '../lib/features/rating/data/services/rating_service.dart';
import '../lib/features/rating/presentation/widgets/rating_widget.dart';

import '../lib/features/analytics/domain/models/analytics_models.dart';
import '../lib/features/analytics/data/services/analytics_service.dart';
import '../lib/features/analytics/presentation/widgets/simple_analytics_widgets.dart';

import '../lib/features/payment/domain/models/enhanced_payment_models.dart';
import '../lib/features/payment/data/services/enhanced_payment_service.dart';
import '../lib/features/payment/presentation/widgets/enhanced_payment_widgets.dart';

import '../lib/features/search/domain/models/search_models.dart';
import '../lib/features/search/data/services/advanced_search_service.dart';

void main() {
  group('Features Compilation Tests', () {
    test('Rating models can be instantiated', () {
      final rating = Rating(
        id: 'test-id',
        userId: 'user-id',
        targetId: 'target-id',
        targetType: 'seller',
        orderId: 'order-id',
        rating: 5.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        userName: 'Test User',
      );
      
      expect(rating.id, 'test-id');
      expect(rating.rating, 5.0);
    });

    test('Analytics models can be instantiated', () {
      final analytics = SalesAnalytics(
        sellerId: 'seller-id',
        date: DateTime.now(),
        totalRevenue: 1000.0,
        totalOrders: 10,
        averageOrderValue: 100.0,
        newCustomers: 5,
        returningCustomers: 5,
        conversionRate: 2.5,
        categoryRevenue: {'food': 500.0, 'grocery': 500.0},
        categoryOrders: {'food': 5, 'grocery': 5},
      );
      
      expect(analytics.sellerId, 'seller-id');
      expect(analytics.totalRevenue, 1000.0);
    });

    test('Payment models can be instantiated', () {
      final payment = EnhancedPayment(
        id: 'payment-id',
        userId: 'user-id',
        orderId: 'order-id',
        totalAmount: 100.0,
        status: PaymentStatus.pending,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      expect(payment.id, 'payment-id');
      expect(payment.totalAmount, 100.0);
      expect(payment.status, PaymentStatus.pending);
    });

    test('Search models can be instantiated', () {
      final query = SearchQuery(
        id: 'search-id',
        query: 'test search',
        createdAt: DateTime.now(),
      );
      
      expect(query.id, 'search-id');
      expect(query.query, 'test search');
      expect(query.type, SearchType.all);
    });

    test('Payment method configurations work', () {
      final config = PaymentMethodConfig.getConfig(PaymentMethod.upi);
      expect(config, isNotNull);
      expect(config!['name'], 'UPI');
      
      final methodName = PaymentMethodConfig.getMethodName(PaymentMethod.creditCard);
      expect(methodName, 'Credit Card');
      
      final fee = PaymentMethodConfig.getProcessingFee(PaymentMethod.upi, 100.0);
      expect(fee, 0.0); // UPI has no processing fee
    });

    test('Rating tags are available', () {
      final riderTags = RatingTags.getTagsForType('rider');
      expect(riderTags, isNotEmpty);
      expect(riderTags, contains('Fast Delivery'));
      
      final sellerTags = RatingTags.getTagsForType('seller');
      expect(sellerTags, isNotEmpty);
      expect(sellerTags, contains('Quality Products'));
    });

    test('Search filters are available', () {
      final filters = AdvancedSearchService.getAvailableFilters(SearchType.products);
      expect(filters, isNotEmpty);
      
      final categoryFilter = filters.firstWhere((f) => f.name == 'category');
      expect(categoryFilter.displayName, 'Category');
      expect(categoryFilter.options, isNotEmpty);
    });
  });
}
