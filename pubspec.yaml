name: projek
description: "A comprehensive multi-vendor marketplace platform built with Flutter."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  google_fonts: ^6.2.1

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # Navigation
  go_router: ^14.2.7
  url_strategy: ^0.3.0

  # Dependency Injection
  get_it: ^7.7.0
  injectable: ^2.4.4

  # Network & HTTP
  dio: ^5.7.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.3.2

  # Maps & Location
  google_maps_flutter: ^2.9.0
  geolocator: ^13.0.1
  geocoding: ^3.0.0

  # Firebase
  firebase_core: ^3.15.0
  firebase_auth: ^5.3.0
  firebase_messaging: ^15.2.10
  cloud_firestore: ^5.4.0
  firebase_analytics: ^11.3.0
  firebase_crashlytics: ^4.1.0
  firebase_remote_config: ^5.1.0
  google_sign_in: ^6.2.2

  # Biometric Authentication
  local_auth: ^2.3.0

  # Social Authentication
  flutter_facebook_auth: ^7.1.2
  sign_in_with_apple: ^6.1.2

  # Calendar and Scheduling
  table_calendar: ^3.0.9

  # Maps and Location

  # Audio for Notifications
  audioplayers: ^5.2.1

  # Images & Media
  cached_network_image: ^3.3.1

  # Animations
  lottie: ^3.1.2

  # Utils
  intl: ^0.20.0
  uuid: ^4.5.1
  equatable: ^2.0.5
  dartz: ^0.10.1
  crypto: ^3.0.3

  # File Upload & Media
  image_picker: ^1.1.2
  file_picker: ^8.1.6
  firebase_storage: ^12.3.0
  path: ^1.8.3
  mime: ^1.0.4

  # UI Components
  flutter_svg: ^2.0.10+1
  shimmer: ^3.0.0

  # Payment Integration
  razorpay_flutter: ^1.3.7
  # upi_india: ^3.3.0  # Temporarily disabled due to version conflict
  url_launcher: ^6.3.1

  # Permissions
  permission_handler: ^11.3.1

  # HTTP requests
  http: ^1.2.1

  # Local notifications
  flutter_local_notifications: ^17.1.2

  # Toast messages
  fluttertoast: ^8.2.8

  # Connectivity
  connectivity_plus: ^5.0.2

  # Additional Utils
  collection: ^1.18.0

  # QR Code functionality
  mobile_scanner: ^5.0.3
  qr_flutter: ^4.1.0

  # Enhanced animations and effects
  flutter_staggered_animations: ^1.1.1
  flutter_spinkit: ^5.2.0
  confetti: ^0.7.0

  # Charts and data visualization
  fl_chart: ^0.68.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  riverpod_generator: ^2.4.3
  injectable_generator: ^2.6.2
  retrofit_generator: ^8.2.1
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

  # App Icon & Splash Screen Generation
  flutter_launcher_icons: ^0.14.1
  flutter_native_splash: ^2.4.1

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    # Icons
    - assets/icons/shared/
    - assets/icons/rider/
    - assets/icons/seller/
    - assets/icons/user/

     # Service Images
    - assets/images/services/teaching/
    - assets/images/services/repairs/
    - assets/images/services/plumber/
    - assets/images/services/laundry/
    - assets/images/services/medicine/
    - assets/images/services/cleaning/
    - assets/images/services/beautyService/

    # Common Images
    - assets/images/common/

    # Category Images
    - assets/images/categories/groceries/
    - assets/images/categories/food/
    - assets/images/categories/food/preview/
    - assets/images/categories/food/cover/
    - assets/images/categories/electronics/mobile/
    - assets/images/categories/electronics/headphones/
    - assets/images/categories/clothes/woman/
    - assets/images/categories/clothes/man/

    # Navigation & Payment
    - assets/navigation/
    - assets/payment/

    # Splash Screen
    - assets/splash_screen/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: false
  image_path: "assets/icons/logo/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21

# Flutter Native Splash Configuration
flutter_native_splash:
  color: "#ffffff"
  image: assets/splash_screen/splash_screen1.png
  branding: assets/images/common/app_logo.png
  color_dark: "#000000"
  image_dark: assets/splash_screen/splash_screen2.png
  branding_dark: assets/images/common/app_logo.png

  android_12:
    image: assets/splash_screen/splash_screen1.png
    icon_background_color: "#ffffff"
    image_dark: assets/splash_screen/splash_screen2.png
    icon_background_color_dark: "#000000"

  web: true
  web_image: assets/splash_screen/splash_screen1.png
