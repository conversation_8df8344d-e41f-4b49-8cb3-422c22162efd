import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FilterDialog extends StatefulWidget {
  final Map<String, dynamic>? currentFilters;
  final Function(Map<String, dynamic>) onApplyFilters;

  const FilterDialog({
    super.key,
    this.currentFilters,
    required this.onApplyFilters,
  });

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  String _selectedCategory = 'All';
  String _selectedPriceRange = 'Any';
  String _selectedLocation = 'Any';
  double _selectedRadius = 5.0;
  bool _availableNow = false;
  bool _freeDelivery = false;
  List<String> _selectedTags = [];

  final List<String> _categories = [
    'All',
    'Food',
    'Fashion',
    'Electronics',
    'Home',
    'Outdoors',
    'Books & Media',
    'Health & Beauty',
    'Automotive',
    'Services',
  ];

  final List<String> _priceRanges = [
    'Any',
    'Under \$10',
    '\$10 - \$50',
    '\$50 - \$100',
    '\$100 - \$500',
    'Over \$500',
  ];

  final List<String> _locations = [
    'Any',
    'Nearby (1km)',
    'City Center',
    'North Zone',
    'South Zone',
    'East Zone',
    'West Zone',
  ];

  final List<String> _availableTags = [
    'Popular',
    'New',
    'Trending',
    'Best Seller',
    'Eco-friendly',
    'Premium',
    'Budget',
    'Local',
  ];

  @override
  void initState() {
    super.initState();
    _initializeFilters();
  }

  void _initializeFilters() {
    if (widget.currentFilters != null) {
      _selectedCategory = widget.currentFilters!['category'] ?? 'All';
      _selectedPriceRange = widget.currentFilters!['priceRange'] ?? 'Any';
      _selectedLocation = widget.currentFilters!['location'] ?? 'Any';
      _selectedRadius = widget.currentFilters!['radius'] ?? 5.0;
      _availableNow = widget.currentFilters!['availableNow'] ?? false;
      _freeDelivery = widget.currentFilters!['freeDelivery'] ?? false;
      _selectedTags = List<String>.from(widget.currentFilters!['tags'] ?? []);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 20),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCategoryFilter(),
                    const SizedBox(height: 20),
                    _buildPriceRangeFilter(),
                    const SizedBox(height: 20),
                    _buildLocationFilter(),
                    const SizedBox(height: 20),
                    _buildRadiusFilter(),
                    const SizedBox(height: 20),
                    _buildToggleFilters(),
                    const SizedBox(height: 20),
                    _buildTagsFilter(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Filter Options',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildCategoryFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _categories.map((category) {
            final isSelected = _selectedCategory == category;
            return FilterChip(
              label: Text(
                category,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: isSelected ? Colors.white : Colors.grey[700],
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              backgroundColor: Colors.grey[200],
              selectedColor: Colors.blue,
              checkmarkColor: Colors.white,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPriceRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Price Range',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedPriceRange,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _priceRanges.map((range) {
            return DropdownMenuItem(
              value: range,
              child: Text(
                range,
                style: GoogleFonts.poppins(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPriceRange = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildLocationFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Location',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedLocation,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _locations.map((location) {
            return DropdownMenuItem(
              value: location,
              child: Text(
                location,
                style: GoogleFonts.poppins(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedLocation = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildRadiusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Radius: ${_selectedRadius.toInt()} km',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Slider(
          value: _selectedRadius,
          min: 1.0,
          max: 50.0,
          divisions: 49,
          label: '${_selectedRadius.toInt()} km',
          onChanged: (value) {
            setState(() {
              _selectedRadius = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildToggleFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Filters',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        SwitchListTile(
          title: Text(
            'Available Now',
            style: GoogleFonts.poppins(fontSize: 14),
          ),
          value: _availableNow,
          onChanged: (value) {
            setState(() {
              _availableNow = value;
            });
          },
          contentPadding: EdgeInsets.zero,
        ),
        SwitchListTile(
          title: Text(
            'Free Delivery',
            style: GoogleFonts.poppins(fontSize: 14),
          ),
          value: _freeDelivery,
          onChanged: (value) {
            setState(() {
              _freeDelivery = value;
            });
          },
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildTagsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableTags.map((tag) {
            final isSelected = _selectedTags.contains(tag);
            return FilterChip(
              label: Text(
                tag,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: isSelected ? Colors.white : Colors.grey[700],
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedTags.add(tag);
                  } else {
                    _selectedTags.remove(tag);
                  }
                });
              },
              backgroundColor: Colors.grey[200],
              selectedColor: Colors.blue,
              checkmarkColor: Colors.white,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _clearFilters,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Clear All',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Apply Filters',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedCategory = 'All';
      _selectedPriceRange = 'Any';
      _selectedLocation = 'Any';
      _selectedRadius = 5.0;
      _availableNow = false;
      _freeDelivery = false;
      _selectedTags.clear();
    });
  }

  void _applyFilters() {
    final filters = {
      'category': _selectedCategory,
      'priceRange': _selectedPriceRange,
      'location': _selectedLocation,
      'radius': _selectedRadius,
      'availableNow': _availableNow,
      'freeDelivery': _freeDelivery,
      'tags': _selectedTags,
    };
    
    widget.onApplyFilters(filters);
    Navigator.of(context).pop();
  }
}
