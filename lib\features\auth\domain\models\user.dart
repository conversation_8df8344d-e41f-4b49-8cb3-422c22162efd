import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user.g.dart';

@HiveType(typeId: 10)
@JsonSerializable()
class User extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String firstName;

  @HiveField(2)
  final String lastName;

  @HiveField(3)
  final String email;

  @HiveField(4)
  final String? phone;

  @HiveField(5)
  final String? profileImageUrl;

  @HiveField(6)
  final DateTime? dateOfBirth;

  @HiveField(7)
  final String? gender;

  @HiveField(8)
  final List<Address> addresses;

  @HiveField(9)
  final DateTime createdAt;

  @HiveField(10)
  final DateTime updatedAt;

  @HiveField(11)
  final bool isEmailVerified;

  @HiveField(12)
  final bool isPhoneVerified;

  @HiveField(13)
  final String? bio;

  @HiveField(14)
  final Map<String, dynamic> preferences;

  const User({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phone,
    this.profileImageUrl,
    this.dateOfBirth,
    this.gender,
    this.addresses = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.bio,
    this.preferences = const {},
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  String get fullName => '$firstName $lastName';
  String get initials => '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}';

  User copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? profileImageUrl,
    DateTime? dateOfBirth,
    String? gender,
    List<Address>? addresses,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    String? bio,
    Map<String, dynamic>? preferences,
  }) {
    return User(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      addresses: addresses ?? this.addresses,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      bio: bio ?? this.bio,
      preferences: preferences ?? this.preferences,
    );
  }

  @override
  List<Object?> get props => [
        id,
        firstName,
        lastName,
        email,
        phone,
        profileImageUrl,
        dateOfBirth,
        gender,
        addresses,
        createdAt,
        updatedAt,
        isEmailVerified,
        isPhoneVerified,
        bio,
        preferences,
      ];
}

@HiveType(typeId: 11)
@JsonSerializable()
class Address extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String label;

  @HiveField(2)
  final String fullName;

  @HiveField(3)
  final String addressLine1;

  @HiveField(4)
  final String? addressLine2;

  @HiveField(5)
  final String city;

  @HiveField(6)
  final String state;

  @HiveField(7)
  final String postalCode;

  @HiveField(8)
  final String country;

  @HiveField(9)
  final String? phone;

  @HiveField(10)
  final bool isDefault;

  @HiveField(11)
  final AddressType type;

  const Address({
    required this.id,
    required this.label,
    required this.fullName,
    required this.addressLine1,
    this.addressLine2,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    this.phone,
    this.isDefault = false,
    this.type = AddressType.home,
  });

  factory Address.fromJson(Map<String, dynamic> json) => _$AddressFromJson(json);
  Map<String, dynamic> toJson() => _$AddressToJson(this);

  String get formattedAddress {
    final parts = [
      addressLine1,
      if (addressLine2?.isNotEmpty == true) addressLine2,
      city,
      state,
      postalCode,
      country,
    ];
    return parts.join(', ');
  }

  Address copyWith({
    String? id,
    String? label,
    String? fullName,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    String? phone,
    bool? isDefault,
    AddressType? type,
  }) {
    return Address(
      id: id ?? this.id,
      label: label ?? this.label,
      fullName: fullName ?? this.fullName,
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      city: city ?? this.city,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      phone: phone ?? this.phone,
      isDefault: isDefault ?? this.isDefault,
      type: type ?? this.type,
    );
  }

  @override
  List<Object?> get props => [
        id,
        label,
        fullName,
        addressLine1,
        addressLine2,
        city,
        state,
        postalCode,
        country,
        phone,
        isDefault,
        type,
      ];
}

@HiveType(typeId: 12)
enum AddressType {
  @HiveField(0)
  home,
  @HiveField(1)
  work,
  @HiveField(2)
  other,
}

// Sample user for testing
class SampleUser {
  static User get defaultUser => User(
        id: 'user_1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '****** 567 8900',
        profileImageUrl: null,
        dateOfBirth: DateTime(1990, 5, 15),
        gender: 'Male',
        addresses: [
          Address(
            id: 'addr_1',
            label: 'Home',
            fullName: 'John Doe',
            addressLine1: '123 Main Street',
            addressLine2: 'Apt 4B',
            city: 'New York',
            state: 'NY',
            postalCode: '10001',
            country: 'United States',
            phone: '****** 567 8900',
            isDefault: true,
            type: AddressType.home,
          ),
        ],
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        updatedAt: DateTime.now(),
        isEmailVerified: true,
        isPhoneVerified: true,
        bio: 'Love shopping and discovering new products!',
        preferences: {
          'notifications': true,
          'newsletter': true,
          'theme': 'light',
        },
      );
}
