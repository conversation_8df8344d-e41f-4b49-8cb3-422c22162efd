// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptionsUser.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptionsUser {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'No Web API Key for this project',
    appId: '1:625844282144:web:your_web_app_id_here',
    messagingSenderId: '625844282144',
    projectId: 'projek-user',
    authDomain: 'projek-user.firebaseapp.com',
    storageBucket: 'projek-user.appspot.com',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:625844282144:android:8b57ffd6c4eb27e1682d45',
    messagingSenderId: '625844282144',
    projectId: 'projek-user',
    storageBucket: 'projek-user.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:625844282144:ios:your_ios_app_id_here',
    messagingSenderId: '625844282144',
    projectId: 'projek-user',
    storageBucket: 'projek-user.appspot.com',
    iosBundleId: 'com.projek.user',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDp6LqoiWZGlxFOGL_xKoacVjANsXc6mHQ',
    appId: '1:625844282144:macos:your_macos_app_id_here',
    messagingSenderId: '625844282144',
    projectId: 'projek-user',
    storageBucket: 'projek-user.appspot.com',
    iosBundleId: 'com.projek.user',
  );
}
