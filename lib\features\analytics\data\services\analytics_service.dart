import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../../domain/models/analytics_models.dart';

class AnalyticsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  static const String _salesAnalyticsCollection = 'sales_analytics';
  static const String _performanceMetricsCollection = 'performance_metrics';
  static const String _customerInsightsCollection = 'customer_insights';
  static const String _revenueBreakdownCollection = 'revenue_breakdown';
  static const String _competitorAnalysisCollection = 'competitor_analysis';

  // Get sales analytics for a seller
  static Future<List<SalesAnalytics>> getSalesAnalytics({
    required String sellerId,
    required AnalyticsFilter filter,
  }) async {
    try {
      Query query = _firestore
          .collection(_salesAnalyticsCollection)
          .where('sellerId', isEqualTo: sellerId);

      // Apply date filter
      final dateRange = _getDateRange(filter);
      if (dateRange != null) {
        query = query
            .where('date', isGreaterThanOrEqualTo: dateRange['start'])
            .where('date', isLessThanOrEqualTo: dateRange['end']);
      }

      final snapshot = await query.orderBy('date', descending: true).get();

      return snapshot.docs.map((doc) {
        return SalesAnalytics.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting sales analytics: $e');
      return [];
    }
  }

  // Get performance metrics for a seller
  static Future<List<PerformanceMetrics>> getPerformanceMetrics({
    required String sellerId,
    required AnalyticsFilter filter,
  }) async {
    try {
      Query query = _firestore
          .collection(_performanceMetricsCollection)
          .where('sellerId', isEqualTo: sellerId);

      final dateRange = _getDateRange(filter);
      if (dateRange != null) {
        query = query
            .where('date', isGreaterThanOrEqualTo: dateRange['start'])
            .where('date', isLessThanOrEqualTo: dateRange['end']);
      }

      final snapshot = await query.orderBy('date', descending: true).get();

      return snapshot.docs.map((doc) {
        return PerformanceMetrics.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting performance metrics: $e');
      return [];
    }
  }

  // Get customer insights for a seller
  static Future<CustomerInsights?> getCustomerInsights({
    required String sellerId,
    required AnalyticsFilter filter,
  }) async {
    try {
      Query query = _firestore
          .collection(_customerInsightsCollection)
          .where('sellerId', isEqualTo: sellerId);

      final dateRange = _getDateRange(filter);
      if (dateRange != null) {
        query = query
            .where('date', isGreaterThanOrEqualTo: dateRange['start'])
            .where('date', isLessThanOrEqualTo: dateRange['end']);
      }

      final snapshot = await query
          .orderBy('date', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return CustomerInsights.fromJson(
          snapshot.docs.first.data() as Map<String, dynamic>,
        );
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting customer insights: $e');
      return null;
    }
  }

  // Get revenue breakdown for a seller
  static Future<List<RevenueBreakdown>> getRevenueBreakdown({
    required String sellerId,
    required AnalyticsFilter filter,
  }) async {
    try {
      Query query = _firestore
          .collection(_revenueBreakdownCollection)
          .where('sellerId', isEqualTo: sellerId);

      final dateRange = _getDateRange(filter);
      if (dateRange != null) {
        query = query
            .where('date', isGreaterThanOrEqualTo: dateRange['start'])
            .where('date', isLessThanOrEqualTo: dateRange['end']);
      }

      final snapshot = await query.orderBy('date', descending: true).get();

      return snapshot.docs.map((doc) {
        return RevenueBreakdown.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting revenue breakdown: $e');
      return [];
    }
  }

  // Get competitor analysis for a seller
  static Future<CompetitorAnalysis?> getCompetitorAnalysis({
    required String sellerId,
    required AnalyticsFilter filter,
  }) async {
    try {
      Query query = _firestore
          .collection(_competitorAnalysisCollection)
          .where('sellerId', isEqualTo: sellerId);

      final dateRange = _getDateRange(filter);
      if (dateRange != null) {
        query = query
            .where('date', isGreaterThanOrEqualTo: dateRange['start'])
            .where('date', isLessThanOrEqualTo: dateRange['end']);
      }

      final snapshot = await query
          .orderBy('date', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return CompetitorAnalysis.fromJson(
          snapshot.docs.first.data() as Map<String, dynamic>,
        );
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting competitor analysis: $e');
      return null;
    }
  }

  // Generate trend data from analytics
  static TrendData generateTrendData(
    List<ChartDataPoint> dataPoints,
    String metric,
  ) {
    if (dataPoints.length < 2) {
      return TrendData(
        dataPoints: dataPoints,
        trendPercentage: 0.0,
        isPositiveTrend: true,
        trendDescription: 'Insufficient data for trend analysis',
      );
    }

    // Calculate trend percentage (comparing last two periods)
    final latest = dataPoints.last.value;
    final previous = dataPoints[dataPoints.length - 2].value;

    double trendPercentage = 0.0;
    if (previous != 0) {
      trendPercentage = ((latest - previous) / previous) * 100;
    }

    final isPositiveTrend = trendPercentage >= 0;
    final trendDescription = _generateTrendDescription(trendPercentage, metric);

    return TrendData(
      dataPoints: dataPoints,
      trendPercentage: trendPercentage.abs(),
      isPositiveTrend: isPositiveTrend,
      trendDescription: trendDescription,
    );
  }

  // Helper method to get date range based on filter
  static Map<String, DateTime>? _getDateRange(AnalyticsFilter filter) {
    final now = DateTime.now();
    DateTime? start, end;

    switch (filter.timeRange) {
      case AnalyticsTimeRange.today:
        start = DateTime(now.year, now.month, now.day);
        end = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case AnalyticsTimeRange.yesterday:
        final yesterday = now.subtract(const Duration(days: 1));
        start = DateTime(yesterday.year, yesterday.month, yesterday.day);
        end = DateTime(
          yesterday.year,
          yesterday.month,
          yesterday.day,
          23,
          59,
          59,
        );
        break;
      case AnalyticsTimeRange.last7Days:
        start = now.subtract(const Duration(days: 7));
        end = now;
        break;
      case AnalyticsTimeRange.last30Days:
        start = now.subtract(const Duration(days: 30));
        end = now;
        break;
      case AnalyticsTimeRange.last90Days:
        start = now.subtract(const Duration(days: 90));
        end = now;
        break;
      case AnalyticsTimeRange.thisMonth:
        start = DateTime(now.year, now.month, 1);
        end = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case AnalyticsTimeRange.lastMonth:
        final lastMonth = DateTime(now.year, now.month - 1, 1);
        start = lastMonth;
        end = DateTime(now.year, now.month, 0, 23, 59, 59);
        break;
      case AnalyticsTimeRange.thisYear:
        start = DateTime(now.year, 1, 1);
        end = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      case AnalyticsTimeRange.custom:
        start = filter.startDate;
        end = filter.endDate;
        break;
    }

    if (start != null && end != null) {
      return {'start': start, 'end': end};
    }
    return null;
  }

  // Generate trend description
  static String _generateTrendDescription(
    double trendPercentage,
    String metric,
  ) {
    final absPercentage = trendPercentage.abs();
    final direction = trendPercentage >= 0 ? 'increased' : 'decreased';

    if (absPercentage == 0) {
      return 'No change in $metric';
    } else if (absPercentage < 5) {
      return '$metric $direction slightly (${absPercentage.toStringAsFixed(1)}%)';
    } else if (absPercentage < 20) {
      return '$metric $direction moderately (${absPercentage.toStringAsFixed(1)}%)';
    } else {
      return '$metric $direction significantly (${absPercentage.toStringAsFixed(1)}%)';
    }
  }

  // Export analytics data
  static Future<Map<String, dynamic>> exportAnalyticsData({
    required String sellerId,
    required AnalyticsFilter filter,
  }) async {
    try {
      final salesData = await getSalesAnalytics(
        sellerId: sellerId,
        filter: filter,
      );
      final performanceData = await getPerformanceMetrics(
        sellerId: sellerId,
        filter: filter,
      );
      final customerData = await getCustomerInsights(
        sellerId: sellerId,
        filter: filter,
      );
      final revenueData = await getRevenueBreakdown(
        sellerId: sellerId,
        filter: filter,
      );
      final competitorData = await getCompetitorAnalysis(
        sellerId: sellerId,
        filter: filter,
      );

      return {
        'sellerId': sellerId,
        'filter': filter.toJson(),
        'exportDate': DateTime.now().toIso8601String(),
        'salesAnalytics': salesData.map((e) => e.toJson()).toList(),
        'performanceMetrics': performanceData.map((e) => e.toJson()).toList(),
        'customerInsights': customerData?.toJson(),
        'revenueBreakdown': revenueData.map((e) => e.toJson()).toList(),
        'competitorAnalysis': competitorData?.toJson(),
      };
    } catch (e) {
      debugPrint('❌ Error exporting analytics data: $e');
      throw Exception('Failed to export analytics data: ${e.toString()}');
    }
  }

  // Get analytics summary for dashboard
  static Future<Map<String, dynamic>> getAnalyticsSummary(
    String sellerId,
  ) async {
    try {
      final filter = AnalyticsFilter(timeRange: AnalyticsTimeRange.last30Days);

      final salesData = await getSalesAnalytics(
        sellerId: sellerId,
        filter: filter,
      );
      final performanceData = await getPerformanceMetrics(
        sellerId: sellerId,
        filter: filter,
      );

      double totalRevenue = 0;
      int totalOrders = 0;
      double averageRating = 0;
      double fulfillmentRate = 0;

      if (salesData.isNotEmpty) {
        totalRevenue = salesData
            .map((e) => e.totalRevenue)
            .reduce((a, b) => a + b);
        totalOrders = salesData
            .map((e) => e.totalOrders)
            .reduce((a, b) => a + b);
      }

      if (performanceData.isNotEmpty) {
        averageRating =
            performanceData
                .map((e) => e.averageRating)
                .reduce((a, b) => a + b) /
            performanceData.length;
        fulfillmentRate =
            performanceData
                .map((e) => e.fulfillmentRate)
                .reduce((a, b) => a + b) /
            performanceData.length;
      }

      return {
        'totalRevenue': totalRevenue,
        'totalOrders': totalOrders,
        'averageRating': averageRating,
        'fulfillmentRate': fulfillmentRate,
        'period': 'Last 30 days',
      };
    } catch (e) {
      debugPrint('❌ Error getting analytics summary: $e');
      return {
        'totalRevenue': 0.0,
        'totalOrders': 0,
        'averageRating': 0.0,
        'fulfillmentRate': 0.0,
        'period': 'Last 30 days',
      };
    }
  }
}
