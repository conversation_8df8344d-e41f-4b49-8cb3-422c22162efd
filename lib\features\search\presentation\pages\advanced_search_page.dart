import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/models/search_models.dart';
import '../../data/services/advanced_search_service.dart';

class AdvancedSearchPage extends ConsumerStatefulWidget {
  const AdvancedSearchPage({super.key});

  @override
  ConsumerState<AdvancedSearchPage> createState() => _AdvancedSearchPageState();
}

class _AdvancedSearchPageState extends ConsumerState<AdvancedSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  List<SearchResult> _searchResults = [];
  bool _isLoading = false;
  SearchType _selectedType = SearchType.all;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Advanced Search'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Search Bar
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search for products, services, or sellers...',
                      prefixIcon: const Icon(
                        Icons.search,
                        color: AppColors.primaryBlue,
                      ),
                      suffixIcon: IconButton(
                        onPressed: _performSearch,
                        icon: const Icon(
                          Icons.arrow_forward,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 15,
                      ),
                    ),
                    onSubmitted: (_) => _performSearch(),
                  ),
                ),

                const SizedBox(height: 16),

                // Search Type Filters
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: SearchType.values.map((type) {
                      final isSelected = _selectedType == type;
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(_getSearchTypeLabel(type)),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedType = type;
                            });
                            if (_searchController.text.isNotEmpty) {
                              _performSearch();
                            }
                          },
                          backgroundColor: Colors.white.withOpacity(0.2),
                          selectedColor: Colors.white,
                          labelStyle: TextStyle(
                            color: isSelected
                                ? AppColors.primaryBlue
                                : Colors.white,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),

          // Search Results
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _searchResults.isEmpty
                ? _buildEmptyState()
                : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 80, color: AppColors.grey400),
          const SizedBox(height: 16),
          Text(
            _searchController.text.isEmpty
                ? 'Start typing to search'
                : 'No results found',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchController.text.isEmpty
                ? 'Search for products, services, or sellers'
                : 'Try different keywords or filters',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final result = _searchResults[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getTypeColor(result.type),
              child: Icon(
                _getTypeIcon(result.type),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(
              result.title,
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  result.description,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getTypeColor(result.type).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getSearchTypeLabel(result.type),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: _getTypeColor(result.type),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    if ((result.rating ?? 0) > 0) ...[
                      Icon(Icons.star, size: 14, color: AppColors.warning),
                      const SizedBox(width: 2),
                      Text(
                        (result.rating ?? 0).toStringAsFixed(1),
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
            trailing: (result.price ?? 0) > 0
                ? Text(
                    '₹${(result.price ?? 0).toStringAsFixed(0)}',
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  )
                : null,
            onTap: () => _viewSearchResult(result),
          ),
        );
      },
    );
  }

  String _getSearchTypeLabel(SearchType type) {
    switch (type) {
      case SearchType.all:
        return 'All';
      case SearchType.products:
        return 'Products';
      case SearchType.services:
        return 'Services';
      case SearchType.sellers:
        return 'Sellers';
      case SearchType.categories:
        return 'Categories';
    }
  }

  Color _getTypeColor(SearchType type) {
    switch (type) {
      case SearchType.all:
        return AppColors.primaryBlue;
      case SearchType.products:
        return AppColors.success;
      case SearchType.services:
        return AppColors.warning;
      case SearchType.sellers:
        return Colors.purple;
      case SearchType.categories:
        return Colors.orange;
    }
  }

  IconData _getTypeIcon(SearchType type) {
    switch (type) {
      case SearchType.all:
        return Icons.search;
      case SearchType.products:
        return Icons.shopping_bag;
      case SearchType.services:
        return Icons.build;
      case SearchType.sellers:
        return Icons.store;
      case SearchType.categories:
        return Icons.category;
    }
  }

  Future<void> _performSearch() async {
    if (_searchController.text.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final query = SearchQuery(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        query: _searchController.text.trim(),
        type: _selectedType,
        createdAt: DateTime.now(),
      );

      final results = await _performDemoSearch(query);

      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Search failed: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _viewSearchResult(SearchResult result) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing: ${result.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<List<SearchResult>> _performDemoSearch(SearchQuery query) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    final results = <SearchResult>[];
    final searchTerm = query.query.toLowerCase();

    // Demo search results
    if (searchTerm.contains('phone') || searchTerm.contains('mobile')) {
      results.add(
        SearchResult(
          id: 'phone_1',
          title: 'iPhone 15 Pro Max',
          description: 'Latest iPhone with advanced camera system',
          type: SearchType.products,
          rating: 4.8,
          price: 129900,
          relevanceScore: 0.9,
        ),
      );
    }

    if (searchTerm.contains('service') || searchTerm.contains('clean')) {
      results.add(
        SearchResult(
          id: 'service_1',
          title: 'House Cleaning Service',
          description: 'Professional cleaning service for your home',
          type: SearchType.services,
          rating: 4.5,
          price: 1299,
          relevanceScore: 0.8,
        ),
      );
    }

    if (searchTerm.contains('store') || searchTerm.contains('shop')) {
      results.add(
        SearchResult(
          id: 'seller_1',
          title: 'Fresh Mart Store',
          description: 'Your neighborhood grocery store',
          type: SearchType.sellers,
          rating: 4.3,
          price: 0,
          relevanceScore: 0.7,
        ),
      );
    }

    // If no specific matches, return general results
    if (results.isEmpty && searchTerm.isNotEmpty) {
      results.add(
        SearchResult(
          id: 'general_1',
          title: 'Search result for "$searchTerm"',
          description: 'We found some relevant items for your search',
          type: SearchType.products,
          rating: 4.0,
          price: 999,
          relevanceScore: 0.5,
        ),
      );
    }

    return results;
  }
}
