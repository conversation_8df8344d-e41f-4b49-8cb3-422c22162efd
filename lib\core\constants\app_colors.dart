import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF4CAF50);
  static const Color primaryDark = Color(0xFF45A049);
  static const Color primaryLight = Color(0xFF81C784);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF2196F3);
  static const Color secondaryDark = Color(0xFF1976D2);
  static const Color secondaryLight = Color(0xFF64B5F6);
  
  // Accent Colors
  static const Color accent = Color(0xFFFF9800);
  static const Color accentDark = Color(0xFFF57C00);
  static const Color accentLight = Color(0xFFFFB74D);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color greyLight = Color(0xFFF5F5F5);
  static const Color greyDark = Color(0xFF424242);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  
  // Background Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color card = Color(0xFFFFFFFF);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFBDBDBD);
  
  // Rider Specific Colors
  static const Color riderOnline = Color(0xFF4CAF50);
  static const Color riderOffline = Color(0xFF9E9E9E);
  static const Color riderBusy = Color(0xFFFF9800);
  
  // Order Status Colors
  static const Color orderPending = Color(0xFFFF9800);
  static const Color orderAccepted = Color(0xFF2196F3);
  static const Color orderPickup = Color(0xFFFF5722);
  static const Color orderDelivered = Color(0xFF4CAF50);
  static const Color orderCancelled = Color(0xFFF44336);
  
  // Earnings Colors
  static const Color earnings = Color(0xFF4CAF50);
  static const Color bonus = Color(0xFFFF5722);
  static const Color tips = Color(0xFFFFD700);
  
  // Transparent Colors
  static const Color transparent = Colors.transparent;
  
  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF4CAF50),
    Color(0xFF45A049),
  ];
  
  static const List<Color> secondaryGradient = [
    Color(0xFF2196F3),
    Color(0xFF1976D2),
  ];
  
  static const List<Color> accentGradient = [
    Color(0xFFFF9800),
    Color(0xFFF57C00),
  ];
}
