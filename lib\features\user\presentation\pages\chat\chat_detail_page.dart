import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/chat_service.dart';
import '../../../../../core/models/chat_models.dart';
import '../../../../../core/services/analytics_service.dart';

final chatMessagesProvider = StreamProvider.family<List<ChatMessage>, String>((ref, chatId) {
  return ChatService.getMessages(chatId);
});

final chatParticipantsProvider = FutureProvider.family<List<ChatParticipant>, String>((ref, chatId) {
  return ChatService.getChatParticipants(chatId);
});

class ChatDetailPage extends ConsumerStatefulWidget {
  final Chat chat;

  const ChatDetailPage({super.key, required this.chat});

  @override
  ConsumerState<ChatDetailPage> createState() => _ChatDetailPageState();
}

class _ChatDetailPageState extends ConsumerState<ChatDetailPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isTyping = false;

  @override
  void initState() {
    super.initState();
    _markMessagesAsRead();
    AnalyticsService.logEvent('chat_detail_opened', {
      'chat_id': widget.chat.id,
      'chat_type': widget.chat.chatType.toString(),
    });
  }

  void _markMessagesAsRead() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      ChatService.markMessagesAsRead(widget.chat.id, currentUser.uid);
    }
  }

  @override
  Widget build(BuildContext context) {
    final messagesAsync = ref.watch(chatMessagesProvider(widget.chat.id));
    final participantsAsync = ref.watch(chatParticipantsProvider(widget.chat.id));
    final currentUser = FirebaseAuth.instance.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: participantsAsync.when(
          data: (participants) {
            final chatTitle = widget.chat.getChatTitle(currentUser?.uid ?? '', participants);
            final otherParticipant = participants.firstWhere(
              (p) => p.id != currentUser?.uid,
              orElse: () => ChatParticipant(id: '', name: 'User', userType: UserType.user),
            );
            
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  chatTitle,
                  style: const TextStyle(fontSize: 16),
                ),
                if (otherParticipant.id.isNotEmpty)
                  Text(
                    otherParticipant.getOnlineStatus(),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
              ],
            );
          },
          loading: () => Text(widget.chat.getChatTitle(currentUser?.uid ?? '', [])),
          error: (_, __) => Text(widget.chat.getChatTitle(currentUser?.uid ?? '', [])),
        ),
        backgroundColor: _getChatTypeColor(widget.chat.chatType),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showChatInfo(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // Chat Info Banner (if applicable)
          if (widget.chat.orderId != null || widget.chat.productId != null)
            _buildChatInfoBanner(),

          // Messages
          Expanded(
            child: messagesAsync.when(
              data: (messages) {
                if (messages.isEmpty) {
                  return _buildEmptyMessages();
                }

                return ListView.builder(
                  controller: _scrollController,
                  reverse: true,
                  padding: const EdgeInsets.all(16),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final isFromCurrentUser = message.isFromCurrentUser(currentUser?.uid ?? '');
                    final showSenderName = !isFromCurrentUser && widget.chat.participants.length > 2;
                    
                    return _buildMessageBubble(message, isFromCurrentUser, showSenderName);
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    Text('Error loading messages: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.invalidate(chatMessagesProvider(widget.chat.id)),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Typing Indicator
          if (_isTyping) _buildTypingIndicator(),

          // Message Input
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildChatInfoBanner() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      color: _getChatTypeColor(widget.chat.chatType).withOpacity(0.1),
      child: Row(
        children: [
          Icon(
            _getChatTypeIcon(widget.chat.chatType),
            color: _getChatTypeColor(widget.chat.chatType),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.chat.orderId != null)
                  Text(
                    'Order #${widget.chat.orderId}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                if (widget.chat.productId != null)
                  Text(
                    'Product Inquiry',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                Text(
                  widget.chat.getChatSubtitle(),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyMessages() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getChatTypeIcon(widget.chat.chatType),
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Start the conversation',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getEmptyMessageText(),
            style: TextStyle(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message, bool isFromCurrentUser, bool showSenderName) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: isFromCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isFromCurrentUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: _getChatTypeColor(widget.chat.chatType),
              child: Text(
                message.senderName.substring(0, 1).toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isFromCurrentUser
                    ? AppColors.userPrimary
                    : Colors.grey[200],
                borderRadius: BorderRadius.circular(18),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (showSenderName)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        message.senderName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: isFromCurrentUser ? Colors.white70 : Colors.grey[600],
                        ),
                      ),
                    ),
                  _buildMessageContent(message, isFromCurrentUser),
                  const SizedBox(height: 4),
                  Text(
                    _formatMessageTime(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: isFromCurrentUser ? Colors.white70 : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isFromCurrentUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.userPrimary,
              child: const Icon(Icons.person, color: Colors.white, size: 16),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageContent(ChatMessage message, bool isFromCurrentUser) {
    switch (message.messageType) {
      case MessageType.image:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (message.imageUrl != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  message.imageUrl!,
                  width: 200,
                  height: 150,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 200,
                    height: 150,
                    color: Colors.grey[300],
                    child: const Icon(Icons.broken_image),
                  ),
                ),
              ),
            if (message.message.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  message.message,
                  style: TextStyle(
                    color: isFromCurrentUser ? Colors.white : Colors.black87,
                  ),
                ),
              ),
          ],
        );
      case MessageType.file:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.attach_file,
              color: isFromCurrentUser ? Colors.white : Colors.grey[600],
              size: 16,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                message.message.isNotEmpty ? message.message : 'File attachment',
                style: TextStyle(
                  color: isFromCurrentUser ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ],
        );
      case MessageType.location:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.location_on,
              color: isFromCurrentUser ? Colors.white : Colors.red,
              size: 16,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                message.message.isNotEmpty ? message.message : 'Location shared',
                style: TextStyle(
                  color: isFromCurrentUser ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ],
        );
      case MessageType.system:
        return Text(
          message.message,
          style: TextStyle(
            color: Colors.grey[600],
            fontStyle: FontStyle.italic,
          ),
        );
      default:
        return Text(
          message.getDisplayMessage(),
          style: TextStyle(
            color: isFromCurrentUser ? Colors.white : Colors.black87,
          ),
        );
    }
  }

  Widget _buildTypingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: _getChatTypeColor(widget.chat.chatType),
            child: const Icon(Icons.person, color: Colors.white, size: 16),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(18),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTypingDot(0),
                const SizedBox(width: 4),
                _buildTypingDot(1),
                const SizedBox(width: 4),
                _buildTypingDot(2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingDot(int index) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 600 + (index * 200)),
      curve: Curves.easeInOut,
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: Colors.grey[500],
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.attach_file),
            onPressed: () => _showAttachmentOptions(context),
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
              onSubmitted: (text) => _sendMessage(),
              onChanged: (text) {
                // Implement typing indicator logic here
              },
            ),
          ),
          const SizedBox(width: 8),
          CircleAvatar(
            backgroundColor: AppColors.userPrimary,
            child: IconButton(
              icon: const Icon(Icons.send, color: Colors.white),
              onPressed: _sendMessage,
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    try {
      await ChatService.sendMessage(
        chatId: widget.chat.id,
        message: text,
        messageType: MessageType.text,
      );

      _messageController.clear();
      
      // Scroll to bottom
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to send message: $e')),
      );
    }
  }

  void _showAttachmentOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.photo),
            title: const Text('Photo'),
            onTap: () {
              Navigator.pop(context);
              // Implement photo attachment
            },
          ),
          ListTile(
            leading: const Icon(Icons.attach_file),
            title: const Text('File'),
            onTap: () {
              Navigator.pop(context);
              // Implement file attachment
            },
          ),
          ListTile(
            leading: const Icon(Icons.location_on),
            title: const Text('Location'),
            onTap: () {
              Navigator.pop(context);
              // Implement location sharing
            },
          ),
        ],
      ),
    );
  }

  void _showChatInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chat Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Chat Type: ${widget.chat.chatType.toString().split('.').last}'),
            if (widget.chat.orderId != null)
              Text('Order ID: ${widget.chat.orderId}'),
            if (widget.chat.productId != null)
              Text('Product ID: ${widget.chat.productId}'),
            Text('Created: ${_formatMessageTime(widget.chat.createdAt)}'),
            Text('Participants: ${widget.chat.participants.length}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Color _getChatTypeColor(ChatType type) {
    switch (type) {
      case ChatType.support:
        return AppColors.error;
      case ChatType.userSeller:
        return AppColors.accentGreen;
      case ChatType.userRider:
        return AppColors.info;
      case ChatType.sellerRider:
        return AppColors.warning;
      case ChatType.groupOrder:
        return AppColors.userPrimary;
      default:
        return Colors.grey;
    }
  }

  IconData _getChatTypeIcon(ChatType type) {
    switch (type) {
      case ChatType.support:
        return Icons.support_agent;
      case ChatType.userSeller:
        return Icons.store;
      case ChatType.userRider:
        return Icons.delivery_dining;
      case ChatType.sellerRider:
        return Icons.local_shipping;
      case ChatType.groupOrder:
        return Icons.group;
      default:
        return Icons.chat;
    }
  }

  String _getEmptyMessageText() {
    switch (widget.chat.chatType) {
      case ChatType.support:
        return 'Ask our support team any questions you have';
      case ChatType.userSeller:
        return 'Ask the seller about products, pricing, or availability';
      case ChatType.userRider:
        return 'Coordinate with your rider for smooth delivery';
      case ChatType.sellerRider:
        return 'Coordinate pickup and delivery details';
      case ChatType.groupOrder:
        return 'Discuss order details with all participants';
      default:
        return 'Send your first message to start the conversation';
    }
  }

  String _formatMessageTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
