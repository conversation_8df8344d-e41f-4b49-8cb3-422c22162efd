@echo off
title Projek User App - Build & Deploy to Device
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              PROJEK USER APP - BUILD & DEPLOY               ║
echo ║           Debug APK Build and Device Installation           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 Step 1: Setting up build environment...
set PUB_CACHE=E:\Appdata\flutter_pub_cache
set FLUTTER_WEB_USE_SKIA=true
set FLUTTER_WEB_AUTO_DETECT=true
echo ✅ Custom pub cache configured: %PUB_CACHE%
echo ✅ Flutter optimizations enabled
echo.

echo 📱 Step 2: Verifying device connection...
echo Checking USB debugging connection...
adb devices
echo.

echo Checking Flutter device recognition...
flutter devices
echo.

echo 🎯 Step 3: Verifying target device...
adb -s 1397182984001HG shell echo "Device connection test successful"
if %errorlevel% neq 0 (
    echo ❌ ERROR: Target device V2130 (1397182984001HG) not found!
    echo.
    echo Please ensure:
    echo   • USB debugging is enabled
    echo   • Device is connected via USB
    echo   • USB debugging authorization is granted
    echo   • Try different USB port/cable
    echo.
    pause
    exit /b 1
)
echo ✅ Target device V2130 verified and ready
echo.

echo 🧹 Step 4: Cleaning previous builds...
echo Removing old build artifacts...
flutter clean
echo ✅ Clean completed
echo.

echo 📦 Step 5: Getting dependencies...
echo Installing fresh dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to get dependencies!
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully
echo.

echo 🔍 Step 6: Verifying project structure...
if not exist "lib\main_user.dart" (
    echo ❌ ERROR: User app entry point not found!
    echo Expected: lib\main_user.dart
    pause
    exit /b 1
)
echo ✅ User app entry point found: lib\main_user.dart

if not exist "firebase_options_user.dart" (
    echo ❌ WARNING: Firebase User configuration not found!
    echo Expected: firebase_options_user.dart
)
echo ✅ Firebase configuration verified
echo.

echo 🏗️ Step 7: Building debug APK for User app...
echo Building APK with target: lib/main_user.dart
echo Target device: 1397182984001HG
echo Build type: Debug (development)
echo.

flutter build apk --target lib/main_user.dart --debug --verbose
if %errorlevel% neq 0 (
    echo ❌ ERROR: APK build failed!
    echo.
    echo Common solutions:
    echo   • Run: .\fix_build_issues.bat
    echo   • Check Android SDK installation
    echo   • Verify Gradle configuration
    echo.
    pause
    exit /b 1
)
echo ✅ Debug APK built successfully!
echo.

echo 📱 Step 8: Installing APK on device...
set APK_PATH=build\app\outputs\flutter-apk\app-debug.apk

if not exist "%APK_PATH%" (
    echo ❌ ERROR: APK file not found at expected location!
    echo Expected: %APK_PATH%
    echo.
    dir build\app\outputs\flutter-apk\*.apk 2>nul
    pause
    exit /b 1
)

echo Installing APK on device V2130...
adb -s 1397182984001HG install -r "%APK_PATH%"
if %errorlevel% neq 0 (
    echo ❌ ERROR: APK installation failed!
    echo.
    echo Trying to uninstall existing app first...
    adb -s 1397182984001HG uninstall com.projek.user
    echo Retrying installation...
    adb -s 1397182984001HG install "%APK_PATH%"
    if %errorlevel% neq 0 (
        echo ❌ ERROR: Installation still failed!
        pause
        exit /b 1
    )
)
echo ✅ APK installed successfully on device!
echo.

echo 🎯 Step 9: Verifying installation...
adb -s 1397182984001HG shell pm list packages | findstr com.projek.user
if %errorlevel% neq 0 (
    echo ❌ WARNING: App package not found on device
) else (
    echo ✅ App package verified on device
)
echo.

echo 📊 Step 10: Build summary...
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    BUILD & DEPLOY COMPLETED!                ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  ✅ APK Built: %APK_PATH%                    ║
echo ║  ✅ Target Device: V2130 (1397182984001HG)                  ║
echo ║  ✅ App Package: com.projek.user                            ║
echo ║  ✅ Build Type: Debug (development)                         ║
echo ║  ✅ Entry Point: lib/main_user.dart                         ║
echo ║  ✅ Firebase: projek-user configuration                     ║
echo ║                                                              ║
echo ║  📱 APP IS NOW INSTALLED ON YOUR DEVICE!                   ║
echo ║                                                              ║
echo ║  🚀 TO LAUNCH THE APP:                                     ║
echo ║     • Look for "Projek" app icon on your device            ║
echo ║     • Tap to launch the User app                           ║
echo ║     • App runs independently (no USB needed)               ║
echo ║                                                              ║
echo ║  🔄 TO UPDATE THE APP:                                     ║
echo ║     • Run this script again                                 ║
echo ║     • Or use: .\dev_user_app.bat for hot reload           ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 SUCCESS! Your Projek User app is now installed and ready to use!
echo.
echo Press any key to exit...
pause >nul
