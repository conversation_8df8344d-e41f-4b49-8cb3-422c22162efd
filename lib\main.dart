// Simple main.dart for easier Flutter development
// This file redirects to the user app for easier VS Code integration

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'core/config/app_config.dart';
import 'core/services/notification_service.dart';
import 'core/services/analytics_service.dart';
import 'core/database/hive_service.dart';
import 'core/utils/app_logger.dart';
import 'firebase_options_user.dart';
import 'src/app.dart';

void main() async {
  await _initializeUserApp();
  runApp(const ProviderScope(child: ProjekApp()));
}

Future<void> _initializeUserApp() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // System UI configuration
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
      ),
    );

    // Set app configuration for User app
    AppConfig.setAppType(AppType.user);

    // Initialize Hive
    await Hive.initFlutter();
    await HiveService.initialize();

    // Initialize Firebase
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptionsUser.currentPlatform,
      );
      AppLogger.info('Firebase initialized successfully');
    } catch (e) {
      if (e.toString().contains('duplicate-app')) {
        AppLogger.info('Firebase already initialized: $e');
      } else {
        AppLogger.error('Firebase initialization failed: $e');
        rethrow;
      }
    }

    // Initialize services
    await NotificationService.initialize();
    await AnalyticsService.initialize();

    AppLogger.info('User app initialized successfully');
  } catch (e, stackTrace) {
    AppLogger.error('Failed to initialize user app: $e', stackTrace);
    rethrow;
  }
}
