@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    PROJEK APK BUILD - PERMANENT FIX
echo ========================================
echo.

:: Set colors for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: Check if Flutter is available
flutter --version >nul 2>&1
if errorlevel 1 (
    echo %RED%Error: Flutter is not installed or not in PATH%NC%
    pause
    exit /b 1
)

:: Clean previous builds
echo %BLUE%Cleaning previous builds...%NC%
flutter clean
if errorlevel 1 (
    echo %RED%Error: Failed to clean project%NC%
    pause
    exit /b 1
)

:: Get dependencies
echo %BLUE%Getting dependencies...%NC%
flutter pub get
if errorlevel 1 (
    echo %RED%Error: Failed to get dependencies%NC%
    pause
    exit /b 1
)

:: Create output directories
echo %BLUE%Creating output directories...%NC%
if not exist "build\app\outputs\flutter-apk" mkdir "build\app\outputs\flutter-apk"
if not exist "apk_outputs" mkdir "apk_outputs"

:: Build User App (Production Debug)
echo %BLUE%Building User App (Production Debug)...%NC%
flutter build apk --debug --flavor userProd --target lib/main_user.dart
if errorlevel 1 (
    echo %YELLOW%Warning: User app build failed, trying alternative method...%NC%
    cd android
    gradlew assembleUserProdDebug
    cd ..
)

:: Build Rider App (Production Debug)
echo %BLUE%Building Rider App (Production Debug)...%NC%
flutter build apk --debug --flavor riderProd --target lib/main_rider.dart
if errorlevel 1 (
    echo %YELLOW%Warning: Rider app build failed, trying alternative method...%NC%
    cd android
    gradlew assembleRiderProdDebug
    cd ..
)

:: Build Seller App (Production Debug)
echo %BLUE%Building Seller App (Production Debug)...%NC%
flutter build apk --debug --flavor sellerProd --target lib/main_seller.dart
if errorlevel 1 (
    echo %YELLOW%Warning: Seller app build failed, trying alternative method...%NC%
    cd android
    gradlew assembleSellerProdDebug
    cd ..
)

:: Copy APKs to organized output directory
echo %BLUE%Organizing APK outputs...%NC%

:: Function to copy and rename APKs
set "apk_found=false"

:: Check for APKs in flutter-apk directory
if exist "build\app\outputs\flutter-apk\app-userprod-debug.apk" (
    copy "build\app\outputs\flutter-apk\app-userprod-debug.apk" "apk_outputs\Projek_User_App.apk" >nul
    echo %GREEN%✓ User App APK copied to apk_outputs\Projek_User_App.apk%NC%
    set "apk_found=true"
)

if exist "build\app\outputs\flutter-apk\app-riderprod-debug.apk" (
    copy "build\app\outputs\flutter-apk\app-riderprod-debug.apk" "apk_outputs\Projek_Rider_App.apk" >nul
    echo %GREEN%✓ Rider App APK copied to apk_outputs\Projek_Rider_App.apk%NC%
    set "apk_found=true"
)

if exist "build\app\outputs\flutter-apk\app-sellerprod-debug.apk" (
    copy "build\app\outputs\flutter-apk\app-sellerprod-debug.apk" "apk_outputs\Projek_Seller_App.apk" >nul
    echo %GREEN%✓ Seller App APK copied to apk_outputs\Projek_Seller_App.apk%NC%
    set "apk_found=true"
)

:: Check for APKs in standard apk directory
if exist "build\app\outputs\apk\userProd\debug\app-userProd-debug.apk" (
    copy "build\app\outputs\apk\userProd\debug\app-userProd-debug.apk" "apk_outputs\Projek_User_App.apk" >nul
    echo %GREEN%✓ User App APK found and copied%NC%
    set "apk_found=true"
)

if exist "build\app\outputs\apk\riderProd\debug\app-riderProd-debug.apk" (
    copy "build\app\outputs\apk\riderProd\debug\app-riderProd-debug.apk" "apk_outputs\Projek_Rider_App.apk" >nul
    echo %GREEN%✓ Rider App APK found and copied%NC%
    set "apk_found=true"
)

if exist "build\app\outputs\apk\sellerProd\debug\app-sellerProd-debug.apk" (
    copy "build\app\outputs\apk\sellerProd\debug\app-sellerProd-debug.apk" "apk_outputs\Projek_Seller_App.apk" >nul
    echo %GREEN%✓ Seller App APK found and copied%NC%
    set "apk_found=true"
)

:: Display results
echo.
echo ========================================
if "%apk_found%"=="true" (
    echo %GREEN%✓ BUILD SUCCESSFUL!%NC%
    echo.
    echo APK files are available in the following locations:
    echo.
    if exist "apk_outputs\Projek_User_App.apk" (
        echo %GREEN%• User App:%NC% apk_outputs\Projek_User_App.apk
    )
    if exist "apk_outputs\Projek_Rider_App.apk" (
        echo %GREEN%• Rider App:%NC% apk_outputs\Projek_Rider_App.apk
    )
    if exist "apk_outputs\Projek_Seller_App.apk" (
        echo %GREEN%• Seller App:%NC% apk_outputs\Projek_Seller_App.apk
    )
    echo.
    echo %BLUE%You can now install these APK files on your Android device.%NC%
) else (
    echo %RED%✗ No APK files found!%NC%
    echo.
    echo Checking build directory structure...
    dir /s "build\app\outputs" | findstr "\.apk"
    echo.
    echo %YELLOW%Please check the build logs above for errors.%NC%
)

echo ========================================
echo.
pause
