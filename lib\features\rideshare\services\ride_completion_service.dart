import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';
import 'otp_service.dart';
import 'commission_service.dart';

class RideCompletionService {
  /// Complete a ride with OTP verification
  static Future<RideCompletionResult> completeRideWithOTP({
    required String rideId,
    required String otp,
    required Map<String, dynamic> rideData,
  }) async {
    try {
      // Verify OTP
      final otpResult = OTPService.verifyOTP(rideId, otp);
      
      if (!otpResult.isValid) {
        return RideCompletionResult(
          success: false,
          error: otpResult.error ?? 'Invalid OTP',
          errorType: _mapOTPErrorType(otpResult.errorType),
          remainingAttempts: otpResult.remainingAttempts,
        );
      }

      // Calculate commission
      final commission = CommissionService.calculateCommission(
        rideType: rideData['rideType'] ?? 'economy',
        totalFare: (rideData['totalFare'] ?? 0.0).toDouble(),
        isPeakHour: CommissionService.isPeakHour(),
        userRating: (rideData['userRating'] ?? 5.0).toDouble(),
        completedRides: 1,
        cancelledRides: 0,
      );

      // Simulate payment processing
      await _processPayment(rideData);

      // Clear OTP after successful completion
      OTPService.clearOTP(rideId);

      return RideCompletionResult(
        success: true,
        commission: commission,
        message: 'Ride completed successfully!',
      );

    } catch (e) {
      return RideCompletionResult(
        success: false,
        error: 'Failed to complete ride: ${e.toString()}',
        errorType: RideCompletionErrorType.systemError,
      );
    }
  }

  /// Manual ride completion (fallback option)
  static Future<RideCompletionResult> completeRideManually({
    required String rideId,
    required Map<String, dynamic> rideData,
    required String reason,
  }) async {
    try {
      // Log manual completion reason
      debugPrint('Manual ride completion: $reason');

      // Calculate commission with potential penalty for manual completion
      final commission = CommissionService.calculateCommission(
        rideType: rideData['rideType'] ?? 'economy',
        totalFare: (rideData['totalFare'] ?? 0.0).toDouble(),
        isPeakHour: CommissionService.isPeakHour(),
        userRating: (rideData['userRating'] ?? 4.0).toDouble(), // Lower rating for manual completion
        completedRides: 1,
        cancelledRides: 0,
      );

      // Apply manual completion penalty
      final adjustedCommission = CommissionBreakdown(
        rideType: commission.rideType,
        totalFare: commission.totalFare,
        baseCommission: commission.baseCommission,
        baseCommissionRate: commission.baseCommissionRate,
        peakHourBonus: commission.peakHourBonus,
        completionBonus: commission.completionBonus * 0.5, // Reduced bonus
        ratingBonus: 0.0, // No rating bonus for manual completion
        totalBonuses: commission.peakHourBonus + (commission.completionBonus * 0.5),
        cancellationPenalty: commission.cancellationPenalty,
        platformFee: commission.platformFee + 10.0, // Additional fee for manual completion
        totalDeductions: commission.totalDeductions + 10.0,
        netEarnings: commission.baseCommission + commission.peakHourBonus + 
                    (commission.completionBonus * 0.5) - commission.totalDeductions - 10.0,
        isPeakHour: commission.isPeakHour,
        userRating: 4.0,
      );

      // Process payment
      await _processPayment(rideData);

      // Clear any existing OTP
      OTPService.clearOTP(rideId);

      return RideCompletionResult(
        success: true,
        commission: adjustedCommission,
        message: 'Ride completed manually with adjusted commission',
        isManualCompletion: true,
      );

    } catch (e) {
      return RideCompletionResult(
        success: false,
        error: 'Failed to complete ride manually: ${e.toString()}',
        errorType: RideCompletionErrorType.systemError,
      );
    }
  }

  /// Handle OTP expiration
  static RideCompletionResult handleOTPExpiration(String rideId) {
    // Generate new OTP
    final newOTP = OTPService.regenerateOTP(rideId);
    
    return RideCompletionResult(
      success: false,
      error: 'OTP expired. New OTP generated: ${newOTP.otp}',
      errorType: RideCompletionErrorType.otpExpired,
      newOTPData: newOTP,
    );
  }

  /// Handle network connectivity issues
  static Future<RideCompletionResult> handleNetworkError({
    required String rideId,
    required Map<String, dynamic> rideData,
  }) async {
    // Store ride data locally for retry
    await _storeRideDataLocally(rideId, rideData);
    
    return RideCompletionResult(
      success: false,
      error: 'Network error. Ride data saved locally for retry.',
      errorType: RideCompletionErrorType.networkError,
      canRetry: true,
    );
  }

  /// Retry failed ride completion
  static Future<RideCompletionResult> retryRideCompletion(String rideId) async {
    try {
      final storedData = await _getStoredRideData(rideId);
      if (storedData == null) {
        return RideCompletionResult(
          success: false,
          error: 'No stored ride data found for retry',
          errorType: RideCompletionErrorType.dataNotFound,
        );
      }

      // Check if OTP is still valid
      final otpData = OTPService.getOTPData(rideId);
      if (otpData == null || otpData.isExpired) {
        return handleOTPExpiration(rideId);
      }

      // Retry completion with stored data
      return await completeRideWithOTP(
        rideId: rideId,
        otp: otpData.otp,
        rideData: storedData,
      );

    } catch (e) {
      return RideCompletionResult(
        success: false,
        error: 'Retry failed: ${e.toString()}',
        errorType: RideCompletionErrorType.retryFailed,
      );
    }
  }

  /// Show error dialog with appropriate actions
  static void showErrorDialog({
    required BuildContext context,
    required RideCompletionResult result,
    required VoidCallback onRetry,
    required VoidCallback onManualComplete,
    VoidCallback? onCancel,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getErrorIcon(result.errorType),
              color: _getErrorColor(result.errorType),
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              _getErrorTitle(result.errorType),
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              result.error ?? 'An error occurred',
              style: GoogleFonts.poppins(fontSize: 14),
            ),
            if (result.remainingAttempts != null && result.remainingAttempts! > 0) ...[
              const SizedBox(height: 8),
              Text(
                'Remaining attempts: ${result.remainingAttempts}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.orange,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (onCancel != null)
            TextButton(
              onPressed: onCancel,
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.grey[600]),
              ),
            ),
          if (result.canRetry == true)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                onRetry();
              },
              child: Text(
                'Retry',
                style: GoogleFonts.poppins(color: AppColors.userPrimary),
              ),
            ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onManualComplete();
            },
            child: Text(
              'Complete Manually',
              style: GoogleFonts.poppins(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  // Private helper methods
  static Future<void> _processPayment(Map<String, dynamic> rideData) async {
    // Simulate payment processing delay
    await Future.delayed(const Duration(seconds: 1));
    debugPrint('Payment processed for ride: ${rideData['rideId']}');
  }

  static Future<void> _storeRideDataLocally(String rideId, Map<String, dynamic> rideData) async {
    // In a real app, this would store data in local database
    debugPrint('Storing ride data locally for: $rideId');
  }

  static Future<Map<String, dynamic>?> _getStoredRideData(String rideId) async {
    // In a real app, this would retrieve data from local database
    debugPrint('Retrieving stored ride data for: $rideId');
    return null; // Placeholder
  }

  static RideCompletionErrorType _mapOTPErrorType(OTPErrorType? otpErrorType) {
    switch (otpErrorType) {
      case OTPErrorType.expired:
        return RideCompletionErrorType.otpExpired;
      case OTPErrorType.invalid:
        return RideCompletionErrorType.otpInvalid;
      case OTPErrorType.maxAttemptsExceeded:
        return RideCompletionErrorType.otpMaxAttempts;
      case OTPErrorType.alreadyUsed:
        return RideCompletionErrorType.otpAlreadyUsed;
      case OTPErrorType.notFound:
        return RideCompletionErrorType.otpNotFound;
      default:
        return RideCompletionErrorType.otpInvalid;
    }
  }

  static IconData _getErrorIcon(RideCompletionErrorType? errorType) {
    switch (errorType) {
      case RideCompletionErrorType.networkError:
        return Icons.wifi_off;
      case RideCompletionErrorType.otpExpired:
        return Icons.timer_off;
      case RideCompletionErrorType.otpInvalid:
      case RideCompletionErrorType.otpMaxAttempts:
        return Icons.error_outline;
      default:
        return Icons.warning;
    }
  }

  static Color _getErrorColor(RideCompletionErrorType? errorType) {
    switch (errorType) {
      case RideCompletionErrorType.networkError:
        return Colors.orange;
      case RideCompletionErrorType.otpExpired:
        return Colors.blue;
      default:
        return AppColors.error;
    }
  }

  static String _getErrorTitle(RideCompletionErrorType? errorType) {
    switch (errorType) {
      case RideCompletionErrorType.networkError:
        return 'Network Error';
      case RideCompletionErrorType.otpExpired:
        return 'OTP Expired';
      case RideCompletionErrorType.otpInvalid:
        return 'Invalid OTP';
      case RideCompletionErrorType.otpMaxAttempts:
        return 'Max Attempts Exceeded';
      default:
        return 'Error';
    }
  }
}

class RideCompletionResult {
  final bool success;
  final String? error;
  final String? message;
  final RideCompletionErrorType? errorType;
  final CommissionBreakdown? commission;
  final int? remainingAttempts;
  final bool canRetry;
  final bool isManualCompletion;
  final OTPData? newOTPData;

  RideCompletionResult({
    required this.success,
    this.error,
    this.message,
    this.errorType,
    this.commission,
    this.remainingAttempts,
    this.canRetry = false,
    this.isManualCompletion = false,
    this.newOTPData,
  });
}

enum RideCompletionErrorType {
  otpInvalid,
  otpExpired,
  otpMaxAttempts,
  otpAlreadyUsed,
  otpNotFound,
  networkError,
  systemError,
  dataNotFound,
  retryFailed,
}
