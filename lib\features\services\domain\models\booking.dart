import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'booking.g.dart';

@HiveType(typeId: 40)
enum BookingStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  confirmed,
  @HiveField(2)
  inProgress,
  @HiveField(3)
  completed,
  @HiveField(4)
  cancelled,
  @HiveField(5)
  rejected,
  @HiveField(6)
  rescheduled,
  @HiveField(7)
  noShow,
}

@HiveType(typeId: 41)
enum PaymentStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  paid,
  @HiveField(2)
  failed,
  @HiveField(3)
  refunded,
  @HiveField(4)
  partialRefund,
}

@HiveType(typeId: 42)
enum CancellationReason {
  @HiveField(0)
  userCancelled,
  @HiveField(1)
  providerCancelled,
  @HiveField(2)
  emergencyIssue,
  @HiveField(3)
  weatherConditions,
  @HiveField(4)
  technicalIssue,
  @HiveField(5)
  noShow,
  @HiveField(6)
  other,
}

@HiveType(typeId: 43)
@JsonSerializable()
class BookingAddress extends Equatable {
  @HiveField(0)
  final String street;
  
  @HiveField(1)
  final String city;
  
  @HiveField(2)
  final String state;
  
  @HiveField(3)
  final String pincode;
  
  @HiveField(4)
  final String? landmark;
  
  @HiveField(5)
  final double? latitude;
  
  @HiveField(6)
  final double? longitude;

  const BookingAddress({
    required this.street,
    required this.city,
    required this.state,
    required this.pincode,
    this.landmark,
    this.latitude,
    this.longitude,
  });

  factory BookingAddress.fromJson(Map<String, dynamic> json) =>
      _$BookingAddressFromJson(json);

  Map<String, dynamic> toJson() => _$BookingAddressToJson(this);

  @override
  List<Object?> get props => [street, city, state, pincode, landmark, latitude, longitude];
}

@HiveType(typeId: 44)
@JsonSerializable()
class BookingSchedule extends Equatable {
  @HiveField(0)
  final DateTime scheduledDate;
  
  @HiveField(1)
  final String startTime; // "09:00"
  
  @HiveField(2)
  final String endTime; // "11:00"
  
  @HiveField(3)
  final int durationHours;
  
  @HiveField(4)
  final bool isFlexible;
  
  @HiveField(5)
  final String? notes;

  const BookingSchedule({
    required this.scheduledDate,
    required this.startTime,
    required this.endTime,
    required this.durationHours,
    this.isFlexible = false,
    this.notes,
  });

  factory BookingSchedule.fromJson(Map<String, dynamic> json) =>
      _$BookingScheduleFromJson(json);

  Map<String, dynamic> toJson() => _$BookingScheduleToJson(this);

  @override
  List<Object?> get props => [scheduledDate, startTime, endTime, durationHours, isFlexible, notes];
}

@HiveType(typeId: 45)
@JsonSerializable()
class BookingCancellation extends Equatable {
  @HiveField(0)
  final CancellationReason reason;
  
  @HiveField(1)
  final String? description;
  
  @HiveField(2)
  final DateTime cancelledAt;
  
  @HiveField(3)
  final String cancelledBy; // userId or providerId
  
  @HiveField(4)
  final double refundAmount;
  
  @HiveField(5)
  final double cancellationFee;

  const BookingCancellation({
    required this.reason,
    this.description,
    required this.cancelledAt,
    required this.cancelledBy,
    this.refundAmount = 0.0,
    this.cancellationFee = 0.0,
  });

  factory BookingCancellation.fromJson(Map<String, dynamic> json) =>
      _$BookingCancellationFromJson(json);

  Map<String, dynamic> toJson() => _$BookingCancellationToJson(this);

  @override
  List<Object?> get props => [reason, description, cancelledAt, cancelledBy, refundAmount, cancellationFee];
}

@HiveType(typeId: 46)
@JsonSerializable()
class BookingConfirmation extends Equatable {
  @HiveField(0)
  final DateTime confirmedAt;
  
  @HiveField(1)
  final String confirmedBy; // providerId
  
  @HiveField(2)
  final String? confirmationMessage;
  
  @HiveField(3)
  final Map<String, dynamic> additionalDetails;

  const BookingConfirmation({
    required this.confirmedAt,
    required this.confirmedBy,
    this.confirmationMessage,
    this.additionalDetails = const {},
  });

  factory BookingConfirmation.fromJson(Map<String, dynamic> json) =>
      _$BookingConfirmationFromJson(json);

  Map<String, dynamic> toJson() => _$BookingConfirmationToJson(this);

  @override
  List<Object?> get props => [confirmedAt, confirmedBy, confirmationMessage, additionalDetails];
}

@HiveType(typeId: 47)
@JsonSerializable()
class Booking extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String serviceId;

  @HiveField(2)
  final String userId;

  @HiveField(3)
  final String providerId;

  @HiveField(4)
  final String serviceName;

  @HiveField(5)
  final String providerName;

  @HiveField(6)
  final String providerPhone;

  @HiveField(7)
  final String? providerEmail;

  @HiveField(8)
  final BookingSchedule schedule;

  @HiveField(9)
  final BookingAddress address;

  @HiveField(10)
  final BookingStatus status;

  @HiveField(11)
  final PaymentStatus paymentStatus;

  @HiveField(12)
  final double totalAmount;

  @HiveField(13)
  final double serviceAmount;

  @HiveField(14)
  final double platformFee;

  @HiveField(15)
  final double taxes;

  @HiveField(16)
  final String currency;

  @HiveField(17)
  final String? paymentId;

  @HiveField(18)
  final String? specialInstructions;

  @HiveField(19)
  final List<String> requirements;

  @HiveField(20)
  final BookingConfirmation? confirmation;

  @HiveField(21)
  final BookingCancellation? cancellation;

  @HiveField(22)
  final DateTime createdAt;

  @HiveField(23)
  final DateTime updatedAt;

  @HiveField(24)
  final DateTime? startedAt;

  @HiveField(25)
  final DateTime? completedAt;

  @HiveField(26)
  final double? rating;

  @HiveField(27)
  final String? review;

  @HiveField(28)
  final List<String> attachments;

  @HiveField(29)
  final Map<String, dynamic> metadata;

  @HiveField(30)
  final String bookingNumber;

  const Booking({
    required this.id,
    required this.serviceId,
    required this.userId,
    required this.providerId,
    required this.serviceName,
    required this.providerName,
    required this.providerPhone,
    this.providerEmail,
    required this.schedule,
    required this.address,
    required this.status,
    required this.paymentStatus,
    required this.totalAmount,
    required this.serviceAmount,
    this.platformFee = 0.0,
    this.taxes = 0.0,
    this.currency = 'INR',
    this.paymentId,
    this.specialInstructions,
    this.requirements = const [],
    this.confirmation,
    this.cancellation,
    required this.createdAt,
    required this.updatedAt,
    this.startedAt,
    this.completedAt,
    this.rating,
    this.review,
    this.attachments = const [],
    this.metadata = const {},
    required this.bookingNumber,
  });

  factory Booking.fromJson(Map<String, dynamic> json) => _$BookingFromJson(json);

  Map<String, dynamic> toJson() => _$BookingToJson(this);

  Booking copyWith({
    String? id,
    String? serviceId,
    String? userId,
    String? providerId,
    String? serviceName,
    String? providerName,
    String? providerPhone,
    String? providerEmail,
    BookingSchedule? schedule,
    BookingAddress? address,
    BookingStatus? status,
    PaymentStatus? paymentStatus,
    double? totalAmount,
    double? serviceAmount,
    double? platformFee,
    double? taxes,
    String? currency,
    String? paymentId,
    String? specialInstructions,
    List<String>? requirements,
    BookingConfirmation? confirmation,
    BookingCancellation? cancellation,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startedAt,
    DateTime? completedAt,
    double? rating,
    String? review,
    List<String>? attachments,
    Map<String, dynamic>? metadata,
    String? bookingNumber,
  }) {
    return Booking(
      id: id ?? this.id,
      serviceId: serviceId ?? this.serviceId,
      userId: userId ?? this.userId,
      providerId: providerId ?? this.providerId,
      serviceName: serviceName ?? this.serviceName,
      providerName: providerName ?? this.providerName,
      providerPhone: providerPhone ?? this.providerPhone,
      providerEmail: providerEmail ?? this.providerEmail,
      schedule: schedule ?? this.schedule,
      address: address ?? this.address,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      totalAmount: totalAmount ?? this.totalAmount,
      serviceAmount: serviceAmount ?? this.serviceAmount,
      platformFee: platformFee ?? this.platformFee,
      taxes: taxes ?? this.taxes,
      currency: currency ?? this.currency,
      paymentId: paymentId ?? this.paymentId,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      requirements: requirements ?? this.requirements,
      confirmation: confirmation ?? this.confirmation,
      cancellation: cancellation ?? this.cancellation,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      rating: rating ?? this.rating,
      review: review ?? this.review,
      attachments: attachments ?? this.attachments,
      metadata: metadata ?? this.metadata,
      bookingNumber: bookingNumber ?? this.bookingNumber,
    );
  }

  @override
  List<Object?> get props => [
        id,
        serviceId,
        userId,
        providerId,
        serviceName,
        providerName,
        providerPhone,
        providerEmail,
        schedule,
        address,
        status,
        paymentStatus,
        totalAmount,
        serviceAmount,
        platformFee,
        taxes,
        currency,
        paymentId,
        specialInstructions,
        requirements,
        confirmation,
        cancellation,
        createdAt,
        updatedAt,
        startedAt,
        completedAt,
        rating,
        review,
        attachments,
        metadata,
        bookingNumber,
      ];
}
