@echo off
REM Script for building production APK
echo Building production APK...
echo.

REM Clean previous builds
echo Cleaning previous builds...
flutter clean
flutter pub get
echo.

REM Build production APK
echo Building release APK with optimizations...
flutter build apk --release --split-per-abi

echo.
echo Production APK built successfully!
echo Location: android\app\build\outputs\flutter-apk\
echo.

REM Copy APK outputs to consolidated directory
echo Copying APK outputs to consolidated build directory...
call scripts\copy_apk_outputs.bat

REM List generated APKs
echo Generated APK files:
dir android\app\build\outputs\flutter-apk\*.apk /b 2>nul
dir android\app\build\outputs\apk\release\*.apk /b 2>nul

echo.
echo APK files are also available in consolidated location:
echo - build\android\outputs\apk\debug\
echo - build\android\outputs\apk\release\
echo.
echo Production APK is ready for distribution!
pause
