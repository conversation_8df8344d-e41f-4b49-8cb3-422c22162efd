import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../widgets/enhanced_payment_widgets.dart';
import '../../../payment/domain/models/enhanced_payment_models.dart';
import '../../../payment/data/services/enhanced_payment_service.dart';

class EnhancedPaymentPage extends ConsumerStatefulWidget {
  final String orderId;
  final double totalAmount;
  final String orderTitle;

  const EnhancedPaymentPage({
    super.key,
    required this.orderId,
    required this.totalAmount,
    required this.orderTitle,
  });

  @override
  ConsumerState<EnhancedPaymentPage> createState() => _EnhancedPaymentPageState();
}

class _EnhancedPaymentPageState extends ConsumerState<EnhancedPaymentPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  PaymentMethod? _selectedMethod;
  List<Map<String, dynamic>> _paymentSplits = [];
  bool _isProcessing = false;
  EnhancedPayment? _currentPayment;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _createPayment();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _createPayment() async {
    try {
      final payment = await EnhancedPaymentService.createPayment(
        orderId: widget.orderId,
        totalAmount: widget.totalAmount,
        metadata: {
          'orderTitle': widget.orderTitle,
          'createdFrom': 'enhanced_payment_page',
        },
      );
      
      if (mounted) {
        setState(() {
          _currentPayment = payment;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating payment: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Single Payment'),
            Tab(text: 'Split Payment'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Order Summary
          _buildOrderSummary(),
          
          // Payment Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSinglePaymentTab(),
                _buildSplitPaymentTab(),
              ],
            ),
          ),
          
          // Payment Button
          _buildPaymentButton(),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.orderTitle,
            style: AppTextStyles.headlineSmall.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'Order ID: ${widget.orderId.substring(0, 8)}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white70,
                ),
              ),
              const Spacer(),
              Text(
                '₹${widget.totalAmount.toStringAsFixed(2)}',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (_currentPayment != null) ...[
            const SizedBox(height: 8),
            PaymentStatusWidget(
              status: _currentPayment!.status,
              amount: _currentPayment!.paidAmount,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSinglePaymentTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose Payment Method',
            style: AppTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          
          // Payment Methods
          ...PaymentMethod.values.map((method) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: PaymentMethodCard(
                method: method,
                isSelected: _selectedMethod == method,
                onTap: () {
                  setState(() {
                    _selectedMethod = method;
                  });
                },
                subtitle: _getMethodSubtitle(method),
              ),
            );
          }),
          
          const SizedBox(height: 24),
          
          // Payment Details
          if (_selectedMethod != null) ...[
            _buildPaymentDetails(_selectedMethod!),
          ],
        ],
      ),
    );
  }

  Widget _buildSplitPaymentTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: PaymentSplitWidget(
        totalAmount: widget.totalAmount,
        onSplitsChanged: (splits) {
          setState(() {
            _paymentSplits = splits;
          });
        },
      ),
    );
  }

  Widget _buildPaymentDetails(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return _buildCardDetails();
      case PaymentMethod.upi:
        return _buildUPIDetails();
      case PaymentMethod.netBanking:
        return _buildNetBankingDetails();
      case PaymentMethod.wallet:
        return _buildWalletDetails();
      case PaymentMethod.cashOnDelivery:
        return _buildCODDetails();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildCardDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Card Details',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Card Number',
                hintText: '1234 5678 9012 3456',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Expiry Date',
                      hintText: 'MM/YY',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'CVV',
                      hintText: '123',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    obscureText: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Cardholder Name',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUPIDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'UPI Payment',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'UPI ID',
                hintText: 'yourname@paytm',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.account_balance),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.success.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.success,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'UPI payments are instant and secure with no processing fees',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.success,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetBankingDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Net Banking',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Select Bank',
                border: OutlineInputBorder(),
              ),
              items: [
                'State Bank of India',
                'HDFC Bank',
                'ICICI Bank',
                'Axis Bank',
                'Punjab National Bank',
                'Bank of Baroda',
                'Canara Bank',
                'Union Bank of India',
              ].map((bank) {
                return DropdownMenuItem(
                  value: bank,
                  child: Text(bank),
                );
              }).toList(),
              onChanged: (value) {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWalletDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Wallet Payment',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.grey300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    color: AppColors.primaryBlue,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Projek Wallet',
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'Balance: ₹2,450.00',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.success,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCODDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cash on Delivery',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.warning.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.warning,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Please keep exact change ready. COD orders may take longer to process.',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.warning,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentButton() {
    final canPay = _tabController.index == 0 
        ? _selectedMethod != null 
        : _paymentSplits.isNotEmpty && _getTotalSplitAmount() == widget.totalAmount;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: canPay && !_isProcessing ? _processPayment : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: _isProcessing
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  _tabController.index == 0 
                      ? 'Pay ₹${widget.totalAmount.toStringAsFixed(2)}'
                      : 'Pay with ${_paymentSplits.length} methods',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }

  String? _getMethodSubtitle(PaymentMethod method) {
    final config = PaymentMethodConfig.getConfig(method);
    if (config == null) return null;
    
    final fee = config['processingFee'] as double;
    if (fee > 0) {
      return 'Processing fee: ${fee}%';
    } else {
      return 'No processing fee';
    }
  }

  double _getTotalSplitAmount() {
    return _paymentSplits.fold<double>(
      0.0,
      (sum, split) => sum + (split['amount'] ?? 0.0),
    );
  }

  Future<void> _processPayment() async {
    if (_currentPayment == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      EnhancedPayment result;
      
      if (_tabController.index == 0) {
        // Single payment
        result = await EnhancedPaymentService.processPayment(
          paymentId: _currentPayment!.id,
          method: _selectedMethod!,
          amount: widget.totalAmount,
        );
      } else {
        // Split payment
        result = await EnhancedPaymentService.processSplitPayment(
          paymentId: _currentPayment!.id,
          paymentSplits: _paymentSplits,
        );
      }

      if (mounted) {
        setState(() {
          _currentPayment = result;
        });

        if (result.status == PaymentStatus.completed) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment successful!'),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop(result);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Payment failed: ${result.failureReason ?? 'Unknown error'}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}
