import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../../domain/models/booking.dart';
import '../../domain/models/service.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/services/auth_service.dart';

class BookingService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _bookingsCollection = 'bookings';
  static const String _servicesCollection = 'services';

  // Create a new booking
  static Future<Booking> createBooking({
    required String serviceId,
    required Service service,
    required BookingSchedule schedule,
    required BookingAddress address,
    String? specialInstructions,
    List<String> requirements = const [],
  }) async {
    try {
      final userId = AuthService.currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Generate booking number
      final bookingNumber = _generateBookingNumber();

      // Calculate pricing
      final pricing = _calculatePricing(service, schedule.durationHours);

      final booking = Booking(
        id: '', // Will be set by Firestore
        serviceId: serviceId,
        userId: userId,
        providerId: service.providerId,
        serviceName: service.title,
        providerName: service.providerName,
        providerPhone: service.providerPhone,
        providerEmail: service.providerEmail,
        schedule: schedule,
        address: address,
        status: BookingStatus.pending,
        paymentStatus: PaymentStatus.pending,
        totalAmount: pricing['total']!,
        serviceAmount: pricing['service']!,
        platformFee: pricing['platformFee']!,
        taxes: pricing['taxes']!,
        specialInstructions: specialInstructions,
        requirements: requirements,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        bookingNumber: bookingNumber,
      );

      // Save to Firestore
      final docRef = await _firestore
          .collection(_bookingsCollection)
          .add(booking.toJson());

      final savedBooking = booking.copyWith(id: docRef.id);

      // Update booking with ID
      await docRef.update({'id': docRef.id});

      // Send notifications
      await _sendBookingNotifications(savedBooking, 'created');

      // Log analytics
      await AnalyticsService.logEvent('booking_created', {
        'service_type': service.type.toString(),
        'booking_amount': savedBooking.totalAmount,
        'provider_id': service.providerId,
      });

      return savedBooking;
    } catch (e) {
      debugPrint('Error creating booking: $e');
      throw Exception('Failed to create booking: ${e.toString()}');
    }
  }

  // Confirm booking (by provider)
  static Future<Booking> confirmBooking({
    required String bookingId,
    String? confirmationMessage,
    Map<String, dynamic> additionalDetails = const {},
  }) async {
    try {
      final providerId = AuthService.currentUserId;
      if (providerId == null) throw Exception('Provider not authenticated');

      final confirmation = BookingConfirmation(
        confirmedAt: DateTime.now(),
        confirmedBy: providerId,
        confirmationMessage: confirmationMessage,
        additionalDetails: additionalDetails,
      );

      await _firestore.collection(_bookingsCollection).doc(bookingId).update({
        'status': BookingStatus.confirmed.toString(),
        'confirmation': confirmation.toJson(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      final updatedBooking = await getBookingById(bookingId);

      // Send notifications
      await _sendBookingNotifications(updatedBooking, 'confirmed');

      // Log analytics
      await AnalyticsService.logEvent('booking_confirmed', {
        'booking_id': bookingId,
        'provider_id': providerId,
      });

      return updatedBooking;
    } catch (e) {
      debugPrint('Error confirming booking: $e');
      throw Exception('Failed to confirm booking: ${e.toString()}');
    }
  }

  // Cancel booking
  static Future<Booking> cancelBooking({
    required String bookingId,
    required CancellationReason reason,
    String? description,
  }) async {
    try {
      final userId = AuthService.currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      final booking = await getBookingById(bookingId);

      // Calculate refund amount based on cancellation policy
      final refundInfo = _calculateRefund(booking);

      final cancellation = BookingCancellation(
        reason: reason,
        description: description,
        cancelledAt: DateTime.now(),
        cancelledBy: userId,
        refundAmount: refundInfo['refundAmount']!,
        cancellationFee: refundInfo['cancellationFee']!,
      );

      await _firestore.collection(_bookingsCollection).doc(bookingId).update({
        'status': BookingStatus.cancelled.toString(),
        'paymentStatus': refundInfo['refundAmount']! > 0
            ? PaymentStatus.refunded.toString()
            : PaymentStatus.failed.toString(),
        'cancellation': cancellation.toJson(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      final updatedBooking = await getBookingById(bookingId);

      // Process refund if applicable
      if (refundInfo['refundAmount']! > 0) {
        await _processRefund(updatedBooking, refundInfo['refundAmount']!);
      }

      // Send notifications
      await _sendBookingNotifications(updatedBooking, 'cancelled');

      // Log analytics
      await AnalyticsService.logEvent('booking_cancelled', {
        'booking_id': bookingId,
        'reason': reason.toString(),
        'refund_amount': refundInfo['refundAmount'],
      });

      return updatedBooking;
    } catch (e) {
      debugPrint('Error cancelling booking: $e');
      throw Exception('Failed to cancel booking: ${e.toString()}');
    }
  }

  // Reschedule booking
  static Future<Booking> rescheduleBooking({
    required String bookingId,
    required BookingSchedule newSchedule,
    String? reason,
  }) async {
    try {
      final userId = AuthService.currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      await _firestore.collection(_bookingsCollection).doc(bookingId).update({
        'schedule': newSchedule.toJson(),
        'status': BookingStatus.rescheduled.toString(),
        'updatedAt': DateTime.now().toIso8601String(),
        'metadata.rescheduleReason': reason,
        'metadata.rescheduledAt': DateTime.now().toIso8601String(),
        'metadata.rescheduledBy': userId,
      });

      final updatedBooking = await getBookingById(bookingId);

      // Send notifications
      await _sendBookingNotifications(updatedBooking, 'rescheduled');

      // Log analytics
      await AnalyticsService.logEvent('booking_rescheduled', {
        'booking_id': bookingId,
        'new_date': newSchedule.scheduledDate.toIso8601String(),
      });

      return updatedBooking;
    } catch (e) {
      debugPrint('Error rescheduling booking: $e');
      throw Exception('Failed to reschedule booking: ${e.toString()}');
    }
  }

  // Start booking (mark as in progress)
  static Future<Booking> startBooking(String bookingId) async {
    try {
      await _firestore.collection(_bookingsCollection).doc(bookingId).update({
        'status': BookingStatus.inProgress.toString(),
        'startedAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      final updatedBooking = await getBookingById(bookingId);

      // Send notifications
      await _sendBookingNotifications(updatedBooking, 'started');

      return updatedBooking;
    } catch (e) {
      debugPrint('Error starting booking: $e');
      throw Exception('Failed to start booking: ${e.toString()}');
    }
  }

  // Complete booking
  static Future<Booking> completeBooking({
    required String bookingId,
    List<String> attachments = const [],
    Map<String, dynamic> completionData = const {},
  }) async {
    try {
      await _firestore.collection(_bookingsCollection).doc(bookingId).update({
        'status': BookingStatus.completed.toString(),
        'completedAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'attachments': attachments,
        'metadata.completionData': completionData,
      });

      final updatedBooking = await getBookingById(bookingId);

      // Send notifications
      await _sendBookingNotifications(updatedBooking, 'completed');

      // Log analytics
      await AnalyticsService.logEvent('booking_completed', {
        'booking_id': bookingId,
        'service_type': updatedBooking.serviceName,
      });

      return updatedBooking;
    } catch (e) {
      debugPrint('Error completing booking: $e');
      throw Exception('Failed to complete booking: ${e.toString()}');
    }
  }

  // Get booking by ID
  static Future<Booking> getBookingById(String bookingId) async {
    try {
      final doc = await _firestore
          .collection(_bookingsCollection)
          .doc(bookingId)
          .get();

      if (!doc.exists) {
        throw Exception('Booking not found');
      }

      return Booking.fromJson(doc.data()!);
    } catch (e) {
      debugPrint('Error getting booking: $e');
      throw Exception('Failed to get booking: ${e.toString()}');
    }
  }

  // Get user bookings
  static Future<List<Booking>> getUserBookings({
    String? userId,
    BookingStatus? status,
    int limit = 20,
  }) async {
    try {
      final currentUserId = userId ?? AuthService.currentUserId;
      if (currentUserId == null) throw Exception('User not authenticated');

      Query query = _firestore
          .collection(_bookingsCollection)
          .where('userId', isEqualTo: currentUserId)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString());
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => Booking.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error getting user bookings: $e');
      throw Exception('Failed to get bookings: ${e.toString()}');
    }
  }

  // Get provider bookings
  static Future<List<Booking>> getProviderBookings({
    String? providerId,
    BookingStatus? status,
    int limit = 20,
  }) async {
    try {
      final currentProviderId = providerId ?? AuthService.currentUserId;
      if (currentProviderId == null)
        throw Exception('Provider not authenticated');

      Query query = _firestore
          .collection(_bookingsCollection)
          .where('providerId', isEqualTo: currentProviderId)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString());
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => Booking.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error getting provider bookings: $e');
      throw Exception('Failed to get provider bookings: ${e.toString()}');
    }
  }

  // Helper methods
  static String _generateBookingNumber() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch.toString().substring(8);
    return 'BK${now.year}${now.month.toString().padLeft(2, '0')}$timestamp';
  }

  static Map<String, double> _calculatePricing(Service service, int hours) {
    final serviceAmount = service.basePrice * hours;
    final platformFee = serviceAmount * 0.05; // 5% platform fee
    final taxes = (serviceAmount + platformFee) * 0.18; // 18% GST
    final total = serviceAmount + platformFee + taxes;

    return {
      'service': serviceAmount,
      'platformFee': platformFee,
      'taxes': taxes,
      'total': total,
    };
  }

  static Map<String, double> _calculateRefund(Booking booking) {
    final now = DateTime.now();
    final scheduledTime = booking.schedule.scheduledDate;
    final hoursUntilService = scheduledTime.difference(now).inHours;

    double refundPercentage = 0.0;
    double cancellationFee = 0.0;

    // Cancellation policy
    if (hoursUntilService >= 24) {
      refundPercentage = 1.0; // Full refund
    } else if (hoursUntilService >= 12) {
      refundPercentage = 0.75; // 75% refund
      cancellationFee = booking.totalAmount * 0.25;
    } else if (hoursUntilService >= 6) {
      refundPercentage = 0.5; // 50% refund
      cancellationFee = booking.totalAmount * 0.5;
    } else {
      refundPercentage = 0.0; // No refund
      cancellationFee = booking.totalAmount;
    }

    final refundAmount = booking.totalAmount * refundPercentage;

    return {'refundAmount': refundAmount, 'cancellationFee': cancellationFee};
  }

  static Future<void> _processRefund(Booking booking, double amount) async {
    // Implement refund logic here
    // This would integrate with your payment gateway
    debugPrint('Processing refund of ₹$amount for booking ${booking.id}');
  }

  static Future<void> _sendBookingNotifications(
    Booking booking,
    String action,
  ) async {
    try {
      String title = '';
      String body = '';

      switch (action) {
        case 'created':
          title = 'Booking Created';
          body = 'Your booking for ${booking.serviceName} has been created';
          break;
        case 'confirmed':
          title = 'Booking Confirmed';
          body = 'Your booking for ${booking.serviceName} has been confirmed';
          break;
        case 'cancelled':
          title = 'Booking Cancelled';
          body = 'Your booking for ${booking.serviceName} has been cancelled';
          break;
        case 'rescheduled':
          title = 'Booking Rescheduled';
          body = 'Your booking for ${booking.serviceName} has been rescheduled';
          break;
        case 'started':
          title = 'Service Started';
          body = 'Your ${booking.serviceName} service has started';
          break;
        case 'completed':
          title = 'Service Completed';
          body = 'Your ${booking.serviceName} service has been completed';
          break;
      }

      await NotificationService.showLocalNotification(
        title: title,
        body: body,
        payload: booking.id,
      );
    } catch (e) {
      debugPrint('Error sending notification: $e');
    }
  }

  // Check service availability for a specific date and time
  static Future<bool> checkAvailability({
    required String serviceId,
    required DateTime date,
    required String startTime,
    required int durationHours,
  }) async {
    try {
      // Check if provider has any conflicting bookings
      final endTime = _addHoursToTime(startTime, durationHours);

      final conflictingBookings = await _firestore
          .collection(_bookingsCollection)
          .where('serviceId', isEqualTo: serviceId)
          .where(
            'schedule.scheduledDate',
            isEqualTo: date.toIso8601String().split('T')[0],
          )
          .where(
            'status',
            whereIn: [
              BookingStatus.confirmed.toString(),
              BookingStatus.inProgress.toString(),
            ],
          )
          .get();

      for (final doc in conflictingBookings.docs) {
        final booking = Booking.fromJson(doc.data());
        if (_hasTimeConflict(
          startTime,
          endTime,
          booking.schedule.startTime,
          booking.schedule.endTime,
        )) {
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error checking availability: $e');
      return false;
    }
  }

  // Get available time slots for a service on a specific date
  static Future<List<String>> getAvailableTimeSlots({
    required String serviceId,
    required DateTime date,
    required int durationHours,
  }) async {
    try {
      final service = await _getServiceById(serviceId);
      final dayOfWeek = date.weekday;

      // Get service availability for the day
      final dayAvailability = service.availability
          .where((a) => a.dayOfWeek == dayOfWeek && a.isAvailable)
          .toList();

      if (dayAvailability.isEmpty) return [];

      final availability = dayAvailability.first;
      final allSlots = _generateTimeSlots(
        availability.startTime,
        availability.endTime,
        durationHours,
      );

      // Filter out booked slots
      final availableSlots = <String>[];
      for (final slot in allSlots) {
        final isAvailable = await checkAvailability(
          serviceId: serviceId,
          date: date,
          startTime: slot,
          durationHours: durationHours,
        );
        if (isAvailable) {
          availableSlots.add(slot);
        }
      }

      return availableSlots;
    } catch (e) {
      debugPrint('Error getting available time slots: $e');
      return [];
    }
  }

  // Helper methods for time calculations
  static String _addHoursToTime(String time, int hours) {
    final parts = time.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);

    final newHour = (hour + hours) % 24;
    return '${newHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  static bool _hasTimeConflict(
    String start1,
    String end1,
    String start2,
    String end2,
  ) {
    final start1Minutes = _timeToMinutes(start1);
    final end1Minutes = _timeToMinutes(end1);
    final start2Minutes = _timeToMinutes(start2);
    final end2Minutes = _timeToMinutes(end2);

    return (start1Minutes < end2Minutes && end1Minutes > start2Minutes);
  }

  static int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  static List<String> _generateTimeSlots(
    String startTime,
    String endTime,
    int durationHours,
  ) {
    final slots = <String>[];
    final startMinutes = _timeToMinutes(startTime);
    final endMinutes = _timeToMinutes(endTime);
    final durationMinutes = durationHours * 60;

    for (
      int minutes = startMinutes;
      minutes + durationMinutes <= endMinutes;
      minutes += 60
    ) {
      final hour = minutes ~/ 60;
      final minute = minutes % 60;
      slots.add(
        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}',
      );
    }

    return slots;
  }

  static Future<Service> _getServiceById(String serviceId) async {
    final doc = await _firestore
        .collection(_servicesCollection)
        .doc(serviceId)
        .get();

    if (!doc.exists) {
      throw Exception('Service not found');
    }

    return Service.fromJson(doc.data()!);
  }
}
