class CommissionService {
  // Commission rates by ride type
  static const Map<String, double> _commissionRates = {
    'economy': 0.20, // 20% commission
    'premium': 0.18, // 18% commission
    'shared': 0.22, // 22% commission
  };

  // Bonus rates
  static const double _peakHourBonus = 0.05; // 5% bonus during peak hours
  static const double _completionBonus = 10.0; // ₹10 bonus per completed ride
  static const double _ratingBonus =
      5.0; // ₹5 bonus for rides with 5-star rating

  // Deductions
  static const double _cancellationPenalty =
      25.0; // ₹25 penalty for cancellations
  static const double _platformFee = 5.0; // ₹5 platform fee per ride

  /// Calculate commission for a completed ride
  static CommissionBreakdown calculateCommission({
    required String rideType,
    required double totalFare,
    required bool isPeakHour,
    required double? userRating,
    required int completedRides,
    required int cancelledRides,
  }) {
    final baseCommissionRate = _commissionRates[rideType.toLowerCase()] ?? 0.20;
    final baseCommission = totalFare * baseCommissionRate;

    // Calculate bonuses
    double peakHourBonus = 0.0;
    if (isPeakHour) {
      peakHourBonus = totalFare * _peakHourBonus;
    }

    double completionBonus = _completionBonus;

    double ratingBonus = 0.0;
    if (userRating != null && userRating >= 5.0) {
      ratingBonus = _ratingBonus;
    }

    // Calculate deductions
    double cancellationPenalty = cancelledRides * _cancellationPenalty;
    double platformFee = _platformFee;

    // Calculate net earnings
    final totalBonuses = peakHourBonus + completionBonus + ratingBonus;
    final totalDeductions = cancellationPenalty + platformFee;
    final netEarnings = baseCommission + totalBonuses - totalDeductions;

    return CommissionBreakdown(
      rideType: rideType,
      totalFare: totalFare,
      baseCommission: baseCommission,
      baseCommissionRate: baseCommissionRate,
      peakHourBonus: peakHourBonus,
      completionBonus: completionBonus,
      ratingBonus: ratingBonus,
      totalBonuses: totalBonuses,
      cancellationPenalty: cancellationPenalty,
      platformFee: platformFee,
      totalDeductions: totalDeductions,
      netEarnings: netEarnings,
      isPeakHour: isPeakHour,
      userRating: userRating,
    );
  }

  /// Get rider's total earnings summary
  static RiderEarnings calculateTotalEarnings({
    required List<CommissionBreakdown> completedRides,
    required int totalRides,
    required int cancelledRides,
    required double averageRating,
  }) {
    double totalEarnings = 0.0;
    double totalBonuses = 0.0;
    double totalDeductions = 0.0;
    double totalFares = 0.0;

    for (final ride in completedRides) {
      totalEarnings += ride.netEarnings;
      totalBonuses += ride.totalBonuses;
      totalDeductions += ride.totalDeductions;
      totalFares += ride.totalFare;
    }

    return RiderEarnings(
      totalEarnings: totalEarnings,
      totalRides: totalRides,
      completedRides: completedRides.length,
      cancelledRides: cancelledRides,
      totalBonuses: totalBonuses,
      totalDeductions: totalDeductions,
      totalFares: totalFares,
      averageRating: averageRating,
      averageEarningsPerRide: completedRides.isNotEmpty
          ? totalEarnings / completedRides.length
          : 0.0,
    );
  }

  /// Check if current time is peak hour
  static bool isPeakHour() {
    final now = DateTime.now();
    final hour = now.hour;

    // Peak hours: 7-10 AM and 5-9 PM
    return (hour >= 7 && hour <= 10) || (hour >= 17 && hour <= 21);
  }

  /// Get commission rate for ride type
  static double getCommissionRate(String rideType) {
    return _commissionRates[rideType.toLowerCase()] ?? 0.20;
  }
}

class CommissionBreakdown {
  final String rideType;
  final double totalFare;
  final double baseCommission;
  final double baseCommissionRate;
  final double peakHourBonus;
  final double completionBonus;
  final double ratingBonus;
  final double totalBonuses;
  final double cancellationPenalty;
  final double platformFee;
  final double totalDeductions;
  final double netEarnings;
  final bool isPeakHour;
  final double? userRating;

  CommissionBreakdown({
    required this.rideType,
    required this.totalFare,
    required this.baseCommission,
    required this.baseCommissionRate,
    required this.peakHourBonus,
    required this.completionBonus,
    required this.ratingBonus,
    required this.totalBonuses,
    required this.cancellationPenalty,
    required this.platformFee,
    required this.totalDeductions,
    required this.netEarnings,
    required this.isPeakHour,
    this.userRating,
  });

  Map<String, dynamic> toJson() {
    return {
      'rideType': rideType,
      'totalFare': totalFare,
      'baseCommission': baseCommission,
      'baseCommissionRate': baseCommissionRate,
      'peakHourBonus': peakHourBonus,
      'completionBonus': completionBonus,
      'ratingBonus': ratingBonus,
      'totalBonuses': totalBonuses,
      'cancellationPenalty': cancellationPenalty,
      'platformFee': platformFee,
      'totalDeductions': totalDeductions,
      'netEarnings': netEarnings,
      'isPeakHour': isPeakHour,
      'userRating': userRating,
    };
  }
}

class RiderEarnings {
  final double totalEarnings;
  final int totalRides;
  final int completedRides;
  final int cancelledRides;
  final double totalBonuses;
  final double totalDeductions;
  final double totalFares;
  final double averageRating;
  final double averageEarningsPerRide;

  RiderEarnings({
    required this.totalEarnings,
    required this.totalRides,
    required this.completedRides,
    required this.cancelledRides,
    required this.totalBonuses,
    required this.totalDeductions,
    required this.totalFares,
    required this.averageRating,
    required this.averageEarningsPerRide,
  });

  double get completionRate =>
      totalRides > 0 ? (completedRides / totalRides) * 100 : 0.0;

  Map<String, dynamic> toJson() {
    return {
      'totalEarnings': totalEarnings,
      'totalRides': totalRides,
      'completedRides': completedRides,
      'cancelledRides': cancelledRides,
      'totalBonuses': totalBonuses,
      'totalDeductions': totalDeductions,
      'totalFares': totalFares,
      'averageRating': averageRating,
      'averageEarningsPerRide': averageEarningsPerRide,
      'completionRate': completionRate,
    };
  }
}
