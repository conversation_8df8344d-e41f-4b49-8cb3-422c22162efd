// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ServiceAvailabilityAdapter extends TypeAdapter<ServiceAvailability> {
  @override
  final int typeId = 34;

  @override
  ServiceAvailability read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ServiceAvailability(
      dayOfWeek: fields[0] as int,
      startTime: fields[1] as String,
      endTime: fields[2] as String,
      isAvailable: fields[3] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ServiceAvailability obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.dayOfWeek)
      ..writeByte(1)
      ..write(obj.startTime)
      ..writeByte(2)
      ..write(obj.endTime)
      ..writeByte(3)
      ..write(obj.isAvailable);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceAvailabilityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ServiceAdapter extends TypeAdapter<Service> {
  @override
  final int typeId = 35;

  @override
  Service read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Service(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String,
      type: fields[3] as ServiceType,
      category: fields[4] as ServiceCategory,
      providerId: fields[5] as String,
      providerName: fields[6] as String,
      providerPhone: fields[7] as String,
      providerEmail: fields[8] as String?,
      providerImageUrl: fields[9] as String?,
      basePrice: fields[10] as double,
      pricingType: fields[11] as PricingType,
      currency: fields[12] as String,
      rating: fields[13] as double,
      reviewCount: fields[14] as int,
      images: (fields[15] as List).cast<String>(),
      skills: (fields[16] as List).cast<String>(),
      certifications: (fields[17] as List).cast<String>(),
      availability: (fields[18] as List).cast<ServiceAvailability>(),
      experienceYears: fields[19] as int,
      location: fields[20] as String?,
      latitude: fields[21] as double?,
      longitude: fields[22] as double?,
      serviceRadius: fields[23] as double,
      status: fields[24] as ServiceStatus,
      isVerified: fields[25] as bool,
      isEmergencyService: fields[26] as bool,
      minBookingHours: fields[27] as int,
      maxBookingHours: fields[28] as int,
      languages: (fields[29] as List).cast<String>(),
      additionalInfo: (fields[30] as Map).cast<String, dynamic>(),
      createdAt: fields[31] as DateTime,
      updatedAt: fields[32] as DateTime,
      completedBookings: fields[33] as int,
      distanceFromUser: fields[34] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, Service obj) {
    writer
      ..writeByte(35)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.category)
      ..writeByte(5)
      ..write(obj.providerId)
      ..writeByte(6)
      ..write(obj.providerName)
      ..writeByte(7)
      ..write(obj.providerPhone)
      ..writeByte(8)
      ..write(obj.providerEmail)
      ..writeByte(9)
      ..write(obj.providerImageUrl)
      ..writeByte(10)
      ..write(obj.basePrice)
      ..writeByte(11)
      ..write(obj.pricingType)
      ..writeByte(12)
      ..write(obj.currency)
      ..writeByte(13)
      ..write(obj.rating)
      ..writeByte(14)
      ..write(obj.reviewCount)
      ..writeByte(15)
      ..write(obj.images)
      ..writeByte(16)
      ..write(obj.skills)
      ..writeByte(17)
      ..write(obj.certifications)
      ..writeByte(18)
      ..write(obj.availability)
      ..writeByte(19)
      ..write(obj.experienceYears)
      ..writeByte(20)
      ..write(obj.location)
      ..writeByte(21)
      ..write(obj.latitude)
      ..writeByte(22)
      ..write(obj.longitude)
      ..writeByte(23)
      ..write(obj.serviceRadius)
      ..writeByte(24)
      ..write(obj.status)
      ..writeByte(25)
      ..write(obj.isVerified)
      ..writeByte(26)
      ..write(obj.isEmergencyService)
      ..writeByte(27)
      ..write(obj.minBookingHours)
      ..writeByte(28)
      ..write(obj.maxBookingHours)
      ..writeByte(29)
      ..write(obj.languages)
      ..writeByte(30)
      ..write(obj.additionalInfo)
      ..writeByte(31)
      ..write(obj.createdAt)
      ..writeByte(32)
      ..write(obj.updatedAt)
      ..writeByte(33)
      ..write(obj.completedBookings)
      ..writeByte(34)
      ..write(obj.distanceFromUser);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ServiceTypeAdapter extends TypeAdapter<ServiceType> {
  @override
  final int typeId = 30;

  @override
  ServiceType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ServiceType.teaching;
      case 1:
        return ServiceType.plumber;
      case 2:
        return ServiceType.electrician;
      case 3:
        return ServiceType.cleaning;
      case 4:
        return ServiceType.beautyService;
      case 5:
        return ServiceType.repairs;
      case 6:
        return ServiceType.medicine;
      case 7:
        return ServiceType.laundry;
      case 8:
        return ServiceType.cooking;
      case 9:
        return ServiceType.gardening;
      case 10:
        return ServiceType.painting;
      case 11:
        return ServiceType.carpentry;
      case 12:
        return ServiceType.appliance;
      case 13:
        return ServiceType.pest;
      case 14:
        return ServiceType.security;
      default:
        return ServiceType.teaching;
    }
  }

  @override
  void write(BinaryWriter writer, ServiceType obj) {
    switch (obj) {
      case ServiceType.teaching:
        writer.writeByte(0);
        break;
      case ServiceType.plumber:
        writer.writeByte(1);
        break;
      case ServiceType.electrician:
        writer.writeByte(2);
        break;
      case ServiceType.cleaning:
        writer.writeByte(3);
        break;
      case ServiceType.beautyService:
        writer.writeByte(4);
        break;
      case ServiceType.repairs:
        writer.writeByte(5);
        break;
      case ServiceType.medicine:
        writer.writeByte(6);
        break;
      case ServiceType.laundry:
        writer.writeByte(7);
        break;
      case ServiceType.cooking:
        writer.writeByte(8);
        break;
      case ServiceType.gardening:
        writer.writeByte(9);
        break;
      case ServiceType.painting:
        writer.writeByte(10);
        break;
      case ServiceType.carpentry:
        writer.writeByte(11);
        break;
      case ServiceType.appliance:
        writer.writeByte(12);
        break;
      case ServiceType.pest:
        writer.writeByte(13);
        break;
      case ServiceType.security:
        writer.writeByte(14);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ServiceCategoryAdapter extends TypeAdapter<ServiceCategory> {
  @override
  final int typeId = 31;

  @override
  ServiceCategory read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ServiceCategory.homeServices;
      case 1:
        return ServiceCategory.education;
      case 2:
        return ServiceCategory.healthcare;
      case 3:
        return ServiceCategory.beauty;
      case 4:
        return ServiceCategory.maintenance;
      case 5:
        return ServiceCategory.emergency;
      default:
        return ServiceCategory.homeServices;
    }
  }

  @override
  void write(BinaryWriter writer, ServiceCategory obj) {
    switch (obj) {
      case ServiceCategory.homeServices:
        writer.writeByte(0);
        break;
      case ServiceCategory.education:
        writer.writeByte(1);
        break;
      case ServiceCategory.healthcare:
        writer.writeByte(2);
        break;
      case ServiceCategory.beauty:
        writer.writeByte(3);
        break;
      case ServiceCategory.maintenance:
        writer.writeByte(4);
        break;
      case ServiceCategory.emergency:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ServiceStatusAdapter extends TypeAdapter<ServiceStatus> {
  @override
  final int typeId = 32;

  @override
  ServiceStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ServiceStatus.active;
      case 1:
        return ServiceStatus.inactive;
      case 2:
        return ServiceStatus.suspended;
      case 3:
        return ServiceStatus.underReview;
      default:
        return ServiceStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, ServiceStatus obj) {
    switch (obj) {
      case ServiceStatus.active:
        writer.writeByte(0);
        break;
      case ServiceStatus.inactive:
        writer.writeByte(1);
        break;
      case ServiceStatus.suspended:
        writer.writeByte(2);
        break;
      case ServiceStatus.underReview:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PricingTypeAdapter extends TypeAdapter<PricingType> {
  @override
  final int typeId = 33;

  @override
  PricingType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PricingType.hourly;
      case 1:
        return PricingType.fixed;
      case 2:
        return PricingType.perSession;
      case 3:
        return PricingType.perProject;
      case 4:
        return PricingType.perVisit;
      default:
        return PricingType.hourly;
    }
  }

  @override
  void write(BinaryWriter writer, PricingType obj) {
    switch (obj) {
      case PricingType.hourly:
        writer.writeByte(0);
        break;
      case PricingType.fixed:
        writer.writeByte(1);
        break;
      case PricingType.perSession:
        writer.writeByte(2);
        break;
      case PricingType.perProject:
        writer.writeByte(3);
        break;
      case PricingType.perVisit:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PricingTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceAvailability _$ServiceAvailabilityFromJson(Map<String, dynamic> json) =>
    ServiceAvailability(
      dayOfWeek: (json['dayOfWeek'] as num).toInt(),
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      isAvailable: json['isAvailable'] as bool,
    );

Map<String, dynamic> _$ServiceAvailabilityToJson(
        ServiceAvailability instance) =>
    <String, dynamic>{
      'dayOfWeek': instance.dayOfWeek,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'isAvailable': instance.isAvailable,
    };

Service _$ServiceFromJson(Map<String, dynamic> json) => Service(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$ServiceTypeEnumMap, json['type']),
      category: $enumDecode(_$ServiceCategoryEnumMap, json['category']),
      providerId: json['providerId'] as String,
      providerName: json['providerName'] as String,
      providerPhone: json['providerPhone'] as String,
      providerEmail: json['providerEmail'] as String?,
      providerImageUrl: json['providerImageUrl'] as String?,
      basePrice: (json['basePrice'] as num).toDouble(),
      pricingType: $enumDecode(_$PricingTypeEnumMap, json['pricingType']),
      currency: json['currency'] as String? ?? 'INR',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: (json['reviewCount'] as num?)?.toInt() ?? 0,
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      skills: (json['skills'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      certifications: (json['certifications'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      availability: (json['availability'] as List<dynamic>?)
              ?.map((e) =>
                  ServiceAvailability.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      experienceYears: (json['experienceYears'] as num?)?.toInt() ?? 0,
      location: json['location'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      serviceRadius: (json['serviceRadius'] as num?)?.toDouble() ?? 10.0,
      status: $enumDecodeNullable(_$ServiceStatusEnumMap, json['status']) ??
          ServiceStatus.active,
      isVerified: json['isVerified'] as bool? ?? false,
      isEmergencyService: json['isEmergencyService'] as bool? ?? false,
      minBookingHours: (json['minBookingHours'] as num?)?.toInt() ?? 1,
      maxBookingHours: (json['maxBookingHours'] as num?)?.toInt() ?? 8,
      languages: (json['languages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['Hindi', 'English'],
      additionalInfo:
          json['additionalInfo'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      completedBookings: (json['completedBookings'] as num?)?.toInt() ?? 0,
      distanceFromUser: (json['distanceFromUser'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ServiceToJson(Service instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$ServiceTypeEnumMap[instance.type]!,
      'category': _$ServiceCategoryEnumMap[instance.category]!,
      'providerId': instance.providerId,
      'providerName': instance.providerName,
      'providerPhone': instance.providerPhone,
      'providerEmail': instance.providerEmail,
      'providerImageUrl': instance.providerImageUrl,
      'basePrice': instance.basePrice,
      'pricingType': _$PricingTypeEnumMap[instance.pricingType]!,
      'currency': instance.currency,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'images': instance.images,
      'skills': instance.skills,
      'certifications': instance.certifications,
      'availability': instance.availability,
      'experienceYears': instance.experienceYears,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'serviceRadius': instance.serviceRadius,
      'status': _$ServiceStatusEnumMap[instance.status]!,
      'isVerified': instance.isVerified,
      'isEmergencyService': instance.isEmergencyService,
      'minBookingHours': instance.minBookingHours,
      'maxBookingHours': instance.maxBookingHours,
      'languages': instance.languages,
      'additionalInfo': instance.additionalInfo,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'completedBookings': instance.completedBookings,
      'distanceFromUser': instance.distanceFromUser,
    };

const _$ServiceTypeEnumMap = {
  ServiceType.teaching: 'teaching',
  ServiceType.plumber: 'plumber',
  ServiceType.electrician: 'electrician',
  ServiceType.cleaning: 'cleaning',
  ServiceType.beautyService: 'beautyService',
  ServiceType.repairs: 'repairs',
  ServiceType.medicine: 'medicine',
  ServiceType.laundry: 'laundry',
  ServiceType.cooking: 'cooking',
  ServiceType.gardening: 'gardening',
  ServiceType.painting: 'painting',
  ServiceType.carpentry: 'carpentry',
  ServiceType.appliance: 'appliance',
  ServiceType.pest: 'pest',
  ServiceType.security: 'security',
};

const _$ServiceCategoryEnumMap = {
  ServiceCategory.homeServices: 'homeServices',
  ServiceCategory.education: 'education',
  ServiceCategory.healthcare: 'healthcare',
  ServiceCategory.beauty: 'beauty',
  ServiceCategory.maintenance: 'maintenance',
  ServiceCategory.emergency: 'emergency',
};

const _$PricingTypeEnumMap = {
  PricingType.hourly: 'hourly',
  PricingType.fixed: 'fixed',
  PricingType.perSession: 'perSession',
  PricingType.perProject: 'perProject',
  PricingType.perVisit: 'perVisit',
};

const _$ServiceStatusEnumMap = {
  ServiceStatus.active: 'active',
  ServiceStatus.inactive: 'inactive',
  ServiceStatus.suspended: 'suspended',
  ServiceStatus.underReview: 'underReview',
};
