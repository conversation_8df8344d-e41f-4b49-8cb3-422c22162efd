import 'package:flutter_test/flutter_test.dart';
import 'package:projek/services/auth_service.dart';

void main() {
  group('Authentication Model Tests', () {
    test('AuthService should be a singleton', () {
      final authService1 = AuthService();
      final authService2 = AuthService();
      expect(identical(authService1, authService2), isTrue);
    });

    test('RiderUser should convert to/from Firestore correctly', () {
      final user = RiderUser(
        id: 'test-uid',
        fullName: 'Test Rider',
        phoneNumber: '**********',
        email: '<EMAIL>',
        dateOfBirth: DateTime(1990, 1, 1),
        vehicleType: VehicleType.motorcycle,
        createdAt: DateTime.now(),
      );

      // Test toFirestore conversion
      final firestoreData = user.toFirestore();
      expect(firestoreData['fullName'], equals('Test Rider'));
      expect(firestoreData['phoneNumber'], equals('**********'));
      expect(firestoreData['email'], equals('<EMAIL>'));
      expect(firestoreData['vehicleType'], equals('VehicleType.motorcycle'));
      expect(firestoreData['isVerified'], equals(false));
      expect(firestoreData['isActive'], equals(true));

      // Test fromFirestore conversion
      final reconstructedUser = RiderUser.fromFirestore(
        firestoreData,
        'test-uid',
      );
      expect(reconstructedUser.id, equals('test-uid'));
      expect(reconstructedUser.fullName, equals('Test Rider'));
      expect(reconstructedUser.phoneNumber, equals('**********'));
      expect(reconstructedUser.email, equals('<EMAIL>'));
      expect(reconstructedUser.vehicleType, equals(VehicleType.motorcycle));
      expect(reconstructedUser.isVerified, equals(false));
      expect(reconstructedUser.isActive, equals(true));
    });

    test('RegistrationData should validate correctly', () {
      final validData = RegistrationData()
        ..fullName = 'Test Rider'
        ..phoneNumber = '**********'
        ..email = '<EMAIL>'
        ..dateOfBirth = DateTime(1990, 1, 1)
        ..vehicleType = VehicleType.motorcycle
        ..driverLicenseNumber = 'DL123456789'
        ..password = 'TestPassword123'
        ..aadhaarNumber = '**********12'
        ..termsAccepted = true;

      expect(validData.isValid, isTrue);

      final invalidData = RegistrationData()
        ..fullName =
            '' // Invalid: empty name
        ..phoneNumber =
            '123' // Invalid: wrong format
        ..email =
            'invalid-email' // Invalid: wrong format
        ..dateOfBirth =
            DateTime.now() // Invalid: too young
        ..vehicleType = VehicleType.motorcycle
        ..password =
            '123' // Invalid: too short
        ..termsAccepted = false; // Invalid: terms not accepted

      expect(invalidData.isValid, isFalse);
    });

    test('Phone number formatting should work correctly', () {
      // Test Indian phone number validation
      expect(RegExp(r'^[6-9]\d{9}$').hasMatch('**********'), isTrue);
      expect(RegExp(r'^[6-9]\d{9}$').hasMatch('**********'), isFalse);
      expect(RegExp(r'^[6-9]\d{9}$').hasMatch('98765432'), isFalse);
      expect(RegExp(r'^[6-9]\d{9}$').hasMatch('**********1'), isFalse);
    });

    test('Email validation should work correctly', () {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');

      expect(emailRegex.hasMatch('<EMAIL>'), isTrue);
      expect(emailRegex.hasMatch('<EMAIL>'), isTrue);
      expect(emailRegex.hasMatch('invalid-email'), isFalse);
      expect(emailRegex.hasMatch('@domain.com'), isFalse);
      expect(emailRegex.hasMatch('test@'), isFalse);
    });

    test('Age calculation should work correctly', () {
      final user = RiderUser(
        id: 'test-uid',
        fullName: 'Test Rider',
        phoneNumber: '**********',
        email: '<EMAIL>',
        dateOfBirth: DateTime(1990, 1, 1),
        vehicleType: VehicleType.motorcycle,
        createdAt: DateTime.now(),
      );

      final currentYear = DateTime.now().year;
      final expectedAge = currentYear - 1990;
      expect(user.age, equals(expectedAge));
      expect(user.isEligible, isTrue);

      final youngUser = RiderUser(
        id: 'test-uid-2',
        fullName: 'Young Rider',
        phoneNumber: '9876543211',
        email: '<EMAIL>',
        dateOfBirth: DateTime(2010, 1, 1), // Too young
        vehicleType: VehicleType.motorcycle,
        createdAt: DateTime.now(),
      );

      expect(youngUser.isEligible, isFalse);
    });

    test('AuthResult should work correctly', () {
      final successResult = AuthResult.success();
      expect(successResult.success, isTrue);
      expect(successResult.message, isNull);

      final failureResult = AuthResult.failure('Test error');
      expect(failureResult.success, isFalse);
      expect(failureResult.message, equals('Test error'));

      final successWithUser = AuthResult.success(
        user: RiderUser(
          id: 'test-uid',
          fullName: 'Test Rider',
          phoneNumber: '**********',
          email: '<EMAIL>',
          dateOfBirth: DateTime(1990, 1, 1),
          vehicleType: VehicleType.motorcycle,
          createdAt: DateTime.now(),
        ),
      );
      expect(successWithUser.success, isTrue);
      expect(successWithUser.user, isNotNull);
      expect(successWithUser.user!.fullName, equals('Test Rider'));
    });
  });
}
