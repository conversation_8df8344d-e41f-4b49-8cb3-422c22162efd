import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'rider_kyc.g.dart';

// Enums
@HiveType(typeId: 100)
enum KYCStatus {
  @HiveField(0)
  notStarted,
  @HiveField(1)
  inProgress,
  @HiveField(2)
  underReview,
  @HiveField(3)
  approved,
  @HiveField(4)
  rejected,
  @HiveField(5)
  resubmissionRequired,
  @HiveField(6)
  pending,
  @HiveField(7)
  incomplete,
}

@HiveType(typeId: 101)
enum DocumentType {
  @HiveField(0)
  aadhaar,
  @HiveField(1)
  pan,
  @HiveField(2)
  drivingLicense,
  @HiveField(3)
  vehicleRC,
  @HiveField(4)
  insurance,
  @HiveField(5)
  pollution,
  @HiveField(6)
  profilePhoto,
  @HiveField(7)
  vehiclePhoto,
  @HiveField(8)
  bankPassbook,
}

@HiveType(typeId: 102)
enum WithdrawalMethod {
  @HiveField(0)
  bankTransfer,
  @HiveField(1)
  upi,
  @HiveField(2)
  paytm,
  @HiveField(3)
  phonepe,
  @HiveField(4)
  googlepay,
  @HiveField(5)
  amazonpay,
}

@HiveType(typeId: 103)
enum WithdrawalStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  processing,
  @HiveField(2)
  completed,
  @HiveField(3)
  failed,
  @HiveField(4)
  cancelled,
}

@HiveType(typeId: 104)
enum BonusType {
  @HiveField(0)
  peakHour,
  @HiveField(1)
  dailyTarget,
  @HiveField(2)
  weeklyTarget,
  @HiveField(3)
  ratingBonus,
  @HiveField(4)
  referral,
  @HiveField(5)
  loyalty,
  @HiveField(6)
  surge,
  @HiveField(7)
  special,
}

@HiveType(typeId: 105)
enum BonusStatus {
  @HiveField(0)
  active,
  @HiveField(1)
  earned,
  @HiveField(2)
  expired,
  @HiveField(3)
  cancelled,
}

@HiveType(typeId: 34)
@JsonSerializable()
class RiderKYC {
  @HiveField(0)
  final String riderId;

  @HiveField(1)
  final KYCStatus status;

  @HiveField(2)
  final List<KYCDocument> documents;

  @HiveField(3)
  final DateTime? submittedAt;

  @HiveField(4)
  final DateTime? reviewedAt;

  @HiveField(5)
  final DateTime? approvedAt;

  @HiveField(6)
  final String? rejectionReason;

  @HiveField(7)
  final List<String> pendingDocuments;

  @HiveField(8)
  final double completionPercentage;

  @HiveField(9)
  final KYCReviewer? reviewer;

  @HiveField(10)
  final List<KYCComment> comments;

  const RiderKYC({
    required this.riderId,
    required this.status,
    required this.documents,
    this.submittedAt,
    this.reviewedAt,
    this.approvedAt,
    this.rejectionReason,
    required this.pendingDocuments,
    required this.completionPercentage,
    this.reviewer,
    required this.comments,
  });

  factory RiderKYC.fromJson(Map<String, dynamic> json) =>
      _$RiderKYCFromJson(json);

  Map<String, dynamic> toJson() => _$RiderKYCToJson(this);

  RiderKYC copyWith({
    String? riderId,
    KYCStatus? status,
    List<KYCDocument>? documents,
    DateTime? submittedAt,
    DateTime? reviewedAt,
    DateTime? approvedAt,
    String? rejectionReason,
    List<String>? pendingDocuments,
    double? completionPercentage,
    KYCReviewer? reviewer,
    List<KYCComment>? comments,
  }) {
    return RiderKYC(
      riderId: riderId ?? this.riderId,
      status: status ?? this.status,
      documents: documents ?? this.documents,
      submittedAt: submittedAt ?? this.submittedAt,
      reviewedAt: reviewedAt ?? this.reviewedAt,
      approvedAt: approvedAt ?? this.approvedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      pendingDocuments: pendingDocuments ?? this.pendingDocuments,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      reviewer: reviewer ?? this.reviewer,
      comments: comments ?? this.comments,
    );
  }
}

@HiveType(typeId: 35)
@JsonSerializable()
class KYCDocument {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final DocumentType type;

  @HiveField(2)
  final String documentNumber;

  @HiveField(3)
  final List<String> imageUrls;

  @HiveField(4)
  final DocumentStatus status;

  @HiveField(5)
  final DateTime uploadedAt;

  @HiveField(6)
  final DateTime? verifiedAt;

  @HiveField(7)
  final String? rejectionReason;

  @HiveField(8)
  final Map<String, dynamic> extractedData;

  @HiveField(9)
  final double confidenceScore;

  const KYCDocument({
    required this.id,
    required this.type,
    required this.documentNumber,
    required this.imageUrls,
    required this.status,
    required this.uploadedAt,
    this.verifiedAt,
    this.rejectionReason,
    required this.extractedData,
    required this.confidenceScore,
  });

  factory KYCDocument.fromJson(Map<String, dynamic> json) =>
      _$KYCDocumentFromJson(json);

  Map<String, dynamic> toJson() => _$KYCDocumentToJson(this);
}

@HiveType(typeId: 37)
enum DocumentStatus {
  @HiveField(0)
  notUploaded,
  @HiveField(1)
  uploaded,
  @HiveField(2)
  underReview,
  @HiveField(3)
  verified,
  @HiveField(4)
  rejected,
  @HiveField(5)
  expired,
}

@HiveType(typeId: 38)
@JsonSerializable()
class KYCReviewer {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String email;

  @HiveField(3)
  final DateTime reviewedAt;

  const KYCReviewer({
    required this.id,
    required this.name,
    required this.email,
    required this.reviewedAt,
  });

  factory KYCReviewer.fromJson(Map<String, dynamic> json) =>
      _$KYCReviewerFromJson(json);

  Map<String, dynamic> toJson() => _$KYCReviewerToJson(this);
}

@HiveType(typeId: 39)
@JsonSerializable()
class KYCComment {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String message;

  @HiveField(2)
  final String authorId;

  @HiveField(3)
  final String authorName;

  @HiveField(4)
  final DateTime timestamp;

  @HiveField(5)
  final CommentType type;

  const KYCComment({
    required this.id,
    required this.message,
    required this.authorId,
    required this.authorName,
    required this.timestamp,
    required this.type,
  });

  factory KYCComment.fromJson(Map<String, dynamic> json) =>
      _$KYCCommentFromJson(json);

  Map<String, dynamic> toJson() => _$KYCCommentToJson(this);
}

@HiveType(typeId: 40)
enum CommentType {
  @HiveField(0)
  info,
  @HiveField(1)
  warning,
  @HiveField(2)
  error,
  @HiveField(3)
  approval,
  @HiveField(4)
  rejection,
}

@HiveType(typeId: 41)
@JsonSerializable()
class WithdrawalRequest {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String riderId;

  @HiveField(2)
  final double amount;

  @HiveField(3)
  final WithdrawalMethod method;

  @HiveField(4)
  final WithdrawalStatus status;

  @HiveField(5)
  final DateTime requestedAt;

  @HiveField(6)
  final DateTime? processedAt;

  @HiveField(7)
  final String? transactionId;

  @HiveField(8)
  final String? failureReason;

  @HiveField(9)
  final double processingFee;

  @HiveField(10)
  final double netAmount;

  @HiveField(11)
  final Map<String, dynamic> paymentDetails;

  const WithdrawalRequest({
    required this.id,
    required this.riderId,
    required this.amount,
    required this.method,
    required this.status,
    required this.requestedAt,
    this.processedAt,
    this.transactionId,
    this.failureReason,
    required this.processingFee,
    required this.netAmount,
    required this.paymentDetails,
  });

  factory WithdrawalRequest.fromJson(Map<String, dynamic> json) =>
      _$WithdrawalRequestFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawalRequestToJson(this);
}

@HiveType(typeId: 44)
@JsonSerializable()
class BonusIncentive {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String riderId;

  @HiveField(2)
  final BonusType type;

  @HiveField(3)
  final String title;

  @HiveField(4)
  final String description;

  @HiveField(5)
  final double amount;

  @HiveField(6)
  final BonusStatus status;

  @HiveField(7)
  final DateTime validFrom;

  @HiveField(8)
  final DateTime validUntil;

  @HiveField(9)
  final Map<String, dynamic> criteria;

  @HiveField(10)
  final double progress;

  @HiveField(11)
  final DateTime? earnedAt;

  const BonusIncentive({
    required this.id,
    required this.riderId,
    required this.type,
    required this.title,
    required this.description,
    required this.amount,
    required this.status,
    required this.validFrom,
    required this.validUntil,
    required this.criteria,
    required this.progress,
    this.earnedAt,
  });

  factory BonusIncentive.fromJson(Map<String, dynamic> json) =>
      _$BonusIncentiveFromJson(json);

  Map<String, dynamic> toJson() => _$BonusIncentiveToJson(this);
}
