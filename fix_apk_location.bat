@echo off
echo ========================================
echo    APK LOCATION FIX UTILITY
echo ========================================
echo.

:: Create the expected flutter-apk directory
if not exist "build\app\outputs\flutter-apk" mkdir "build\app\outputs\flutter-apk"

:: Copy APKs from various locations to the expected location
echo Searching for APK files and copying to expected location...

:: Check userProd debug APK
if exist "build\app\outputs\apk\userProd\debug\app-userProd-debug.apk" (
    copy "build\app\outputs\apk\userProd\debug\app-userProd-debug.apk" "build\app\outputs\flutter-apk\app-debug.apk" >nul
    echo ✓ User APK copied to flutter-apk directory
)

:: Check riderProd debug APK
if exist "build\app\outputs\apk\riderProd\debug\app-riderProd-debug.apk" (
    copy "build\app\outputs\apk\riderProd\debug\app-riderProd-debug.apk" "build\app\outputs\flutter-apk\app-debug.apk" >nul
    echo ✓ Rider APK copied to flutter-apk directory
)

:: Check sellerProd debug APK
if exist "build\app\outputs\apk\sellerProd\debug\app-sellerProd-debug.apk" (
    copy "build\app\outputs\apk\sellerProd\debug\app-sellerProd-debug.apk" "build\app\outputs\flutter-apk\app-debug.apk" >nul
    echo ✓ Seller APK copied to flutter-apk directory
)

:: Check if any APK exists in flutter-apk directory
if exist "build\app\outputs\flutter-apk\*.apk" (
    echo.
    echo ✓ APK files found in flutter-apk directory:
    dir "build\app\outputs\flutter-apk\*.apk" /b
    echo.
    echo Flutter should now be able to find the APK files.
) else (
    echo.
    echo ✗ No APK files found. Please run the build first:
    echo   flutter build apk --debug
    echo.
)

echo ========================================
pause
