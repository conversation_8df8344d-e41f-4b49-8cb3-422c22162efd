import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:audioplayers/audioplayers.dart';

import '../../../../core/services/multi_app_integration_service.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/services/auth_service.dart';

class CrossAppNotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static final AudioPlayer _audioPlayer = AudioPlayer();
  
  static final Map<String, Function(Map<String, dynamic>)> _actionHandlers = {};
  static final List<CrossAppNotification> _notificationHistory = [];
  
  // Initialize cross-app notifications
  static Future<void> initialize() async {
    try {
      await _setupLocalNotifications();
      await _setupFirebaseMessaging();
      _registerEventHandlers();
      
      debugPrint('✅ Cross-App Notification Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Cross-App Notifications: $e');
    }
  }

  // Setup local notifications
  static Future<void> _setupLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _handleNotificationTap,
    );
  }

  // Setup Firebase messaging for cross-app communication
  static Future<void> _setupFirebaseMessaging() async {
    // Request permissions
    await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle message taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);
  }

  // Register event handlers for cross-app events
  static void _registerEventHandlers() {
    // Order events
    MultiAppIntegrationService.registerEventHandler(
      CrossAppEventType.orderCreated,
      _handleOrderCreated,
    );
    
    MultiAppIntegrationService.registerEventHandler(
      CrossAppEventType.orderAccepted,
      _handleOrderAccepted,
    );
    
    MultiAppIntegrationService.registerEventHandler(
      CrossAppEventType.orderPickedUp,
      _handleOrderPickedUp,
    );
    
    MultiAppIntegrationService.registerEventHandler(
      CrossAppEventType.orderDelivered,
      _handleOrderDelivered,
    );
    
    // Service events
    MultiAppIntegrationService.registerEventHandler(
      CrossAppEventType.serviceBooked,
      _handleServiceBooked,
    );
    
    MultiAppIntegrationService.registerEventHandler(
      CrossAppEventType.serviceConfirmed,
      _handleServiceConfirmed,
    );
    
    // Location updates
    MultiAppIntegrationService.registerEventHandler(
      CrossAppEventType.riderLocationUpdate,
      _handleRiderLocationUpdate,
    );
    
    // Emergency alerts
    MultiAppIntegrationService.registerEventHandler(
      CrossAppEventType.emergencyAlert,
      _handleEmergencyAlert,
    );
  }

  // Handle background message
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Background message: ${message.messageId}');
    
    if (message.data.containsKey('cross_app_event')) {
      final eventData = jsonDecode(message.data['cross_app_event']);
      final event = CrossAppEvent.fromJson(eventData);
      
      await _processBackgroundEvent(event);
    }
  }

  // Handle foreground message
  static void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Foreground message: ${message.messageId}');
    
    if (message.data.containsKey('cross_app_event')) {
      final eventData = jsonDecode(message.data['cross_app_event']);
      final event = CrossAppEvent.fromJson(eventData);
      
      _showInAppNotification(event);
    }
  }

  // Handle message opened app
  static void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('Message opened app: ${message.messageId}');
    
    if (message.data.containsKey('action')) {
      final action = message.data['action'];
      final actionData = message.data;
      
      _executeAction(action, actionData);
    }
  }

  // Handle notification tap
  static void _handleNotificationTap(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      final action = data['action'];
      
      if (action != null) {
        _executeAction(action, data);
      }
    }
  }

  // Event handlers
  static void _handleOrderCreated(CrossAppEvent event) {
    final orderId = event.data['orderId'];
    final orderData = event.data['orderData'];
    
    _showCrossAppNotification(
      title: '🛍️ New Order Received',
      body: 'Order #${orderId.substring(0, 8)} has been placed',
      data: {
        'action': 'view_order',
        'orderId': orderId,
        'orderData': orderData,
      },
      priority: NotificationPriority.high,
      playSound: true,
    );
  }

  static void _handleOrderAccepted(CrossAppEvent event) {
    final orderId = event.data['orderId'];
    
    _showCrossAppNotification(
      title: '✅ Order Confirmed',
      body: 'Your order has been confirmed and is being prepared',
      data: {
        'action': 'track_order',
        'orderId': orderId,
      },
      priority: NotificationPriority.high,
    );
  }

  static void _handleOrderPickedUp(CrossAppEvent event) {
    final orderId = event.data['orderId'];
    final riderName = event.data['riderName'];
    
    _showCrossAppNotification(
      title: '🚗 Order Picked Up',
      body: '$riderName has picked up your order and is on the way',
      data: {
        'action': 'track_order',
        'orderId': orderId,
      },
      priority: NotificationPriority.high,
      playSound: true,
    );
  }

  static void _handleOrderDelivered(CrossAppEvent event) {
    final orderId = event.data['orderId'];
    
    _showCrossAppNotification(
      title: '🎉 Order Delivered',
      body: 'Your order has been delivered successfully!',
      data: {
        'action': 'rate_order',
        'orderId': orderId,
      },
      priority: NotificationPriority.max,
      playSound: true,
      vibrate: true,
    );
  }

  static void _handleServiceBooked(CrossAppEvent event) {
    final bookingId = event.data['bookingId'];
    final bookingData = event.data['bookingData'];
    
    _showCrossAppNotification(
      title: '🔧 New Service Booking',
      body: 'A new service has been booked',
      data: {
        'action': 'view_booking',
        'bookingId': bookingId,
        'bookingData': bookingData,
      },
      priority: NotificationPriority.high,
      playSound: true,
    );
  }

  static void _handleServiceConfirmed(CrossAppEvent event) {
    final bookingId = event.data['bookingId'];
    
    _showCrossAppNotification(
      title: '✅ Service Confirmed',
      body: 'Your service booking has been confirmed',
      data: {
        'action': 'view_booking',
        'bookingId': bookingId,
      },
      priority: NotificationPriority.high,
    );
  }

  static void _handleRiderLocationUpdate(CrossAppEvent event) {
    // Only show location updates for active tracking
    // This is handled silently in the tracking page
    debugPrint('Rider location updated');
  }

  static void _handleEmergencyAlert(CrossAppEvent event) {
    final message = event.data['message'];
    final alertType = event.data['alertType'];
    
    _showCrossAppNotification(
      title: '🚨 Emergency Alert',
      body: message ?? 'Emergency situation detected',
      data: {
        'action': 'handle_emergency',
        'alertType': alertType,
        'eventData': event.data,
      },
      priority: NotificationPriority.max,
      playSound: true,
      vibrate: true,
      persistent: true,
    );
  }

  // Show cross-app notification
  static Future<void> _showCrossAppNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
    NotificationPriority priority = NotificationPriority.defaultPriority,
    bool playSound = false,
    bool vibrate = false,
    bool persistent = false,
  }) async {
    try {
      final notification = CrossAppNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: title,
        body: body,
        data: data ?? {},
        timestamp: DateTime.now(),
        priority: priority,
        isRead: false,
      );

      // Add to history
      _notificationHistory.insert(0, notification);
      if (_notificationHistory.length > 100) {
        _notificationHistory.removeLast();
      }

      // Play sound if requested
      if (playSound) {
        await _playNotificationSound(priority);
      }

      // Show local notification
      await _showLocalNotification(notification, vibrate, persistent);

      // Show in-app notification if app is active
      _showInAppBanner(notification);

    } catch (e) {
      debugPrint('❌ Error showing cross-app notification: $e');
    }
  }

  // Show local notification
  static Future<void> _showLocalNotification(
    CrossAppNotification notification,
    bool vibrate,
    bool persistent,
  ) async {
    final androidDetails = AndroidNotificationDetails(
      'cross_app_notifications',
      'Cross-App Notifications',
      channelDescription: 'Notifications from other Projek apps',
      importance: _getAndroidImportance(notification.priority),
      priority: _getAndroidPriority(notification.priority),
      enableVibration: vibrate,
      ongoing: persistent,
      autoCancel: !persistent,
      styleInformation: const BigTextStyleInformation(''),
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      notification.id,
      notification.title,
      notification.body,
      details,
      payload: jsonEncode(notification.data),
    );
  }

  // Show in-app notification
  static void _showInAppNotification(CrossAppEvent event) {
    // This would show a banner or overlay notification
    debugPrint('Showing in-app notification for: ${event.type}');
  }

  // Show in-app banner
  static void _showInAppBanner(CrossAppNotification notification) {
    // This would show a banner at the top of the screen
    debugPrint('Showing in-app banner: ${notification.title}');
  }

  // Play notification sound
  static Future<void> _playNotificationSound(NotificationPriority priority) async {
    try {
      String soundFile = 'notification.mp3';
      
      switch (priority) {
        case NotificationPriority.max:
          soundFile = 'urgent_notification.mp3';
          break;
        case NotificationPriority.high:
          soundFile = 'important_notification.mp3';
          break;
        default:
          soundFile = 'notification.mp3';
      }

      await _audioPlayer.play(AssetSource('sounds/$soundFile'));
    } catch (e) {
      debugPrint('❌ Error playing notification sound: $e');
    }
  }

  // Process background event
  static Future<void> _processBackgroundEvent(CrossAppEvent event) async {
    // Handle events that need processing even when app is closed
    switch (event.type) {
      case CrossAppEventType.emergencyAlert:
        // Always show emergency alerts
        await _showCrossAppNotification(
          title: '🚨 Emergency Alert',
          body: event.data['message'] ?? 'Emergency situation',
          priority: NotificationPriority.max,
          playSound: true,
          vibrate: true,
          persistent: true,
        );
        break;
      case CrossAppEventType.orderDelivered:
        // Always notify about deliveries
        await _showCrossAppNotification(
          title: '🎉 Order Delivered',
          body: 'Your order has been delivered!',
          priority: NotificationPriority.high,
          playSound: true,
        );
        break;
      default:
        break;
    }
  }

  // Execute action
  static void _executeAction(String action, Map<String, dynamic> data) {
    final handler = _actionHandlers[action];
    if (handler != null) {
      handler(data);
    } else {
      debugPrint('No handler found for action: $action');
    }
  }

  // Register action handler
  static void registerActionHandler(
    String action,
    Function(Map<String, dynamic>) handler,
  ) {
    _actionHandlers[action] = handler;
  }

  // Get notification history
  static List<CrossAppNotification> getNotificationHistory() {
    return List.unmodifiable(_notificationHistory);
  }

  // Mark notification as read
  static void markAsRead(int notificationId) {
    final index = _notificationHistory.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notificationHistory[index] = _notificationHistory[index].copyWith(isRead: true);
    }
  }

  // Clear all notifications
  static void clearAllNotifications() {
    _notificationHistory.clear();
    _localNotifications.cancelAll();
  }

  // Helper methods
  static Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.max:
        return Importance.max;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.defaultPriority:
        return Importance.defaultImportance;
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.min:
        return Importance.min;
    }
  }

  static Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.max:
        return Priority.max;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.defaultPriority:
        return Priority.defaultPriority;
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.min:
        return Priority.min;
    }
  }
}

// Notification priority enum
enum NotificationPriority {
  min,
  low,
  defaultPriority,
  high,
  max,
}

// Cross-app notification model
class CrossAppNotification {
  final int id;
  final String title;
  final String body;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final NotificationPriority priority;
  final bool isRead;

  const CrossAppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.data,
    required this.timestamp,
    required this.priority,
    required this.isRead,
  });

  CrossAppNotification copyWith({
    int? id,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    NotificationPriority? priority,
    bool? isRead,
  }) {
    return CrossAppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
    );
  }
}
