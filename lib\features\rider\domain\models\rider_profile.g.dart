// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rider_profile.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RiderProfileAdapter extends TypeAdapter<RiderProfile> {
  @override
  final int typeId = 20;

  @override
  RiderProfile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RiderProfile(
      id: fields[0] as String,
      firstName: fields[1] as String,
      lastName: fields[2] as String,
      phoneNumber: fields[3] as String,
      email: fields[4] as String,
      profileImageUrl: fields[5] as String?,
      dateOfBirth: fields[6] as DateTime,
      address: fields[7] as String,
      city: fields[8] as String,
      state: fields[9] as String,
      pincode: fields[10] as String,
      status: fields[11] as RiderStatus,
      kycStatus: fields[12] as KYCStatus,
      vehicleInfo: fields[13] as VehicleInfo?,
      bankDetails: fields[14] as BankDetails?,
      rating: fields[15] as double,
      totalRides: fields[16] as int,
      joinedDate: fields[17] as DateTime,
      isOnline: fields[18] as bool,
      currentLocation: fields[19] as String?,
      languages: (fields[20] as List).cast<String>(),
      preferences: fields[21] as RiderPreferences,
    );
  }

  @override
  void write(BinaryWriter writer, RiderProfile obj) {
    writer
      ..writeByte(22)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.firstName)
      ..writeByte(2)
      ..write(obj.lastName)
      ..writeByte(3)
      ..write(obj.phoneNumber)
      ..writeByte(4)
      ..write(obj.email)
      ..writeByte(5)
      ..write(obj.profileImageUrl)
      ..writeByte(6)
      ..write(obj.dateOfBirth)
      ..writeByte(7)
      ..write(obj.address)
      ..writeByte(8)
      ..write(obj.city)
      ..writeByte(9)
      ..write(obj.state)
      ..writeByte(10)
      ..write(obj.pincode)
      ..writeByte(11)
      ..write(obj.status)
      ..writeByte(12)
      ..write(obj.kycStatus)
      ..writeByte(13)
      ..write(obj.vehicleInfo)
      ..writeByte(14)
      ..write(obj.bankDetails)
      ..writeByte(15)
      ..write(obj.rating)
      ..writeByte(16)
      ..write(obj.totalRides)
      ..writeByte(17)
      ..write(obj.joinedDate)
      ..writeByte(18)
      ..write(obj.isOnline)
      ..writeByte(19)
      ..write(obj.currentLocation)
      ..writeByte(20)
      ..write(obj.languages)
      ..writeByte(21)
      ..write(obj.preferences);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RiderProfileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VehicleInfoAdapter extends TypeAdapter<VehicleInfo> {
  @override
  final int typeId = 23;

  @override
  VehicleInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VehicleInfo(
      vehicleType: fields[0] as String,
      make: fields[1] as String,
      model: fields[2] as String,
      year: fields[3] as String,
      registrationNumber: fields[4] as String,
      color: fields[5] as String,
      vehicleImages: (fields[6] as List).cast<String>(),
      insuranceNumber: fields[7] as String?,
      insuranceExpiry: fields[8] as DateTime?,
      rcNumber: fields[9] as String?,
      rcExpiry: fields[10] as DateTime?,
      pollutionCertNumber: fields[11] as String?,
      pollutionExpiry: fields[12] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, VehicleInfo obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.vehicleType)
      ..writeByte(1)
      ..write(obj.make)
      ..writeByte(2)
      ..write(obj.model)
      ..writeByte(3)
      ..write(obj.year)
      ..writeByte(4)
      ..write(obj.registrationNumber)
      ..writeByte(5)
      ..write(obj.color)
      ..writeByte(6)
      ..write(obj.vehicleImages)
      ..writeByte(7)
      ..write(obj.insuranceNumber)
      ..writeByte(8)
      ..write(obj.insuranceExpiry)
      ..writeByte(9)
      ..write(obj.rcNumber)
      ..writeByte(10)
      ..write(obj.rcExpiry)
      ..writeByte(11)
      ..write(obj.pollutionCertNumber)
      ..writeByte(12)
      ..write(obj.pollutionExpiry);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VehicleInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BankDetailsAdapter extends TypeAdapter<BankDetails> {
  @override
  final int typeId = 24;

  @override
  BankDetails read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BankDetails(
      accountHolderName: fields[0] as String,
      accountNumber: fields[1] as String,
      ifscCode: fields[2] as String,
      bankName: fields[3] as String,
      branchName: fields[4] as String,
      isVerified: fields[5] as bool,
      upiId: fields[6] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, BankDetails obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.accountHolderName)
      ..writeByte(1)
      ..write(obj.accountNumber)
      ..writeByte(2)
      ..write(obj.ifscCode)
      ..writeByte(3)
      ..write(obj.bankName)
      ..writeByte(4)
      ..write(obj.branchName)
      ..writeByte(5)
      ..write(obj.isVerified)
      ..writeByte(6)
      ..write(obj.upiId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BankDetailsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RiderPreferencesAdapter extends TypeAdapter<RiderPreferences> {
  @override
  final int typeId = 25;

  @override
  RiderPreferences read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RiderPreferences(
      acceptSharedRides: fields[0] as bool,
      acceptLongDistanceRides: fields[1] as bool,
      preferredAreas: (fields[2] as List).cast<String>(),
      workingHoursStart: fields[3] as String,
      workingHoursEnd: fields[4] as String,
      workingDays: (fields[5] as List).cast<String>(),
      enableNotifications: fields[6] as bool,
      enableLocationTracking: fields[7] as bool,
      preferredLanguage: fields[8] as String,
    );
  }

  @override
  void write(BinaryWriter writer, RiderPreferences obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.acceptSharedRides)
      ..writeByte(1)
      ..write(obj.acceptLongDistanceRides)
      ..writeByte(2)
      ..write(obj.preferredAreas)
      ..writeByte(3)
      ..write(obj.workingHoursStart)
      ..writeByte(4)
      ..write(obj.workingHoursEnd)
      ..writeByte(5)
      ..write(obj.workingDays)
      ..writeByte(6)
      ..write(obj.enableNotifications)
      ..writeByte(7)
      ..write(obj.enableLocationTracking)
      ..writeByte(8)
      ..write(obj.preferredLanguage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RiderPreferencesAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RiderStatusAdapter extends TypeAdapter<RiderStatus> {
  @override
  final int typeId = 21;

  @override
  RiderStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RiderStatus.pending;
      case 1:
        return RiderStatus.active;
      case 2:
        return RiderStatus.suspended;
      case 3:
        return RiderStatus.blocked;
      case 4:
        return RiderStatus.inactive;
      default:
        return RiderStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, RiderStatus obj) {
    switch (obj) {
      case RiderStatus.pending:
        writer.writeByte(0);
        break;
      case RiderStatus.active:
        writer.writeByte(1);
        break;
      case RiderStatus.suspended:
        writer.writeByte(2);
        break;
      case RiderStatus.blocked:
        writer.writeByte(3);
        break;
      case RiderStatus.inactive:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RiderStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RiderProfile _$RiderProfileFromJson(Map<String, dynamic> json) => RiderProfile(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String,
      profileImageUrl: json['profileImageUrl'] as String?,
      dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
      address: json['address'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      pincode: json['pincode'] as String,
      status: $enumDecode(_$RiderStatusEnumMap, json['status']),
      kycStatus: $enumDecode(_$KYCStatusEnumMap, json['kycStatus']),
      vehicleInfo: json['vehicleInfo'] == null
          ? null
          : VehicleInfo.fromJson(json['vehicleInfo'] as Map<String, dynamic>),
      bankDetails: json['bankDetails'] == null
          ? null
          : BankDetails.fromJson(json['bankDetails'] as Map<String, dynamic>),
      rating: (json['rating'] as num).toDouble(),
      totalRides: (json['totalRides'] as num).toInt(),
      joinedDate: DateTime.parse(json['joinedDate'] as String),
      isOnline: json['isOnline'] as bool,
      currentLocation: json['currentLocation'] as String?,
      languages:
          (json['languages'] as List<dynamic>).map((e) => e as String).toList(),
      preferences: RiderPreferences.fromJson(
          json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RiderProfileToJson(RiderProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'profileImageUrl': instance.profileImageUrl,
      'dateOfBirth': instance.dateOfBirth.toIso8601String(),
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'pincode': instance.pincode,
      'status': _$RiderStatusEnumMap[instance.status]!,
      'kycStatus': _$KYCStatusEnumMap[instance.kycStatus]!,
      'vehicleInfo': instance.vehicleInfo,
      'bankDetails': instance.bankDetails,
      'rating': instance.rating,
      'totalRides': instance.totalRides,
      'joinedDate': instance.joinedDate.toIso8601String(),
      'isOnline': instance.isOnline,
      'currentLocation': instance.currentLocation,
      'languages': instance.languages,
      'preferences': instance.preferences,
    };

const _$RiderStatusEnumMap = {
  RiderStatus.pending: 'pending',
  RiderStatus.active: 'active',
  RiderStatus.suspended: 'suspended',
  RiderStatus.blocked: 'blocked',
  RiderStatus.inactive: 'inactive',
};

const _$KYCStatusEnumMap = {
  KYCStatus.notStarted: 'notStarted',
  KYCStatus.inProgress: 'inProgress',
  KYCStatus.underReview: 'underReview',
  KYCStatus.approved: 'approved',
  KYCStatus.rejected: 'rejected',
  KYCStatus.resubmissionRequired: 'resubmissionRequired',
  KYCStatus.pending: 'pending',
  KYCStatus.incomplete: 'incomplete',
};

VehicleInfo _$VehicleInfoFromJson(Map<String, dynamic> json) => VehicleInfo(
      vehicleType: json['vehicleType'] as String,
      make: json['make'] as String,
      model: json['model'] as String,
      year: json['year'] as String,
      registrationNumber: json['registrationNumber'] as String,
      color: json['color'] as String,
      vehicleImages: (json['vehicleImages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      insuranceNumber: json['insuranceNumber'] as String?,
      insuranceExpiry: json['insuranceExpiry'] == null
          ? null
          : DateTime.parse(json['insuranceExpiry'] as String),
      rcNumber: json['rcNumber'] as String?,
      rcExpiry: json['rcExpiry'] == null
          ? null
          : DateTime.parse(json['rcExpiry'] as String),
      pollutionCertNumber: json['pollutionCertNumber'] as String?,
      pollutionExpiry: json['pollutionExpiry'] == null
          ? null
          : DateTime.parse(json['pollutionExpiry'] as String),
    );

Map<String, dynamic> _$VehicleInfoToJson(VehicleInfo instance) =>
    <String, dynamic>{
      'vehicleType': instance.vehicleType,
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'registrationNumber': instance.registrationNumber,
      'color': instance.color,
      'vehicleImages': instance.vehicleImages,
      'insuranceNumber': instance.insuranceNumber,
      'insuranceExpiry': instance.insuranceExpiry?.toIso8601String(),
      'rcNumber': instance.rcNumber,
      'rcExpiry': instance.rcExpiry?.toIso8601String(),
      'pollutionCertNumber': instance.pollutionCertNumber,
      'pollutionExpiry': instance.pollutionExpiry?.toIso8601String(),
    };

BankDetails _$BankDetailsFromJson(Map<String, dynamic> json) => BankDetails(
      accountHolderName: json['accountHolderName'] as String,
      accountNumber: json['accountNumber'] as String,
      ifscCode: json['ifscCode'] as String,
      bankName: json['bankName'] as String,
      branchName: json['branchName'] as String,
      isVerified: json['isVerified'] as bool,
      upiId: json['upiId'] as String?,
    );

Map<String, dynamic> _$BankDetailsToJson(BankDetails instance) =>
    <String, dynamic>{
      'accountHolderName': instance.accountHolderName,
      'accountNumber': instance.accountNumber,
      'ifscCode': instance.ifscCode,
      'bankName': instance.bankName,
      'branchName': instance.branchName,
      'isVerified': instance.isVerified,
      'upiId': instance.upiId,
    };

RiderPreferences _$RiderPreferencesFromJson(Map<String, dynamic> json) =>
    RiderPreferences(
      acceptSharedRides: json['acceptSharedRides'] as bool,
      acceptLongDistanceRides: json['acceptLongDistanceRides'] as bool,
      preferredAreas: (json['preferredAreas'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      workingHoursStart: json['workingHoursStart'] as String,
      workingHoursEnd: json['workingHoursEnd'] as String,
      workingDays: (json['workingDays'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      enableNotifications: json['enableNotifications'] as bool,
      enableLocationTracking: json['enableLocationTracking'] as bool,
      preferredLanguage: json['preferredLanguage'] as String,
    );

Map<String, dynamic> _$RiderPreferencesToJson(RiderPreferences instance) =>
    <String, dynamic>{
      'acceptSharedRides': instance.acceptSharedRides,
      'acceptLongDistanceRides': instance.acceptLongDistanceRides,
      'preferredAreas': instance.preferredAreas,
      'workingHoursStart': instance.workingHoursStart,
      'workingHoursEnd': instance.workingHoursEnd,
      'workingDays': instance.workingDays,
      'enableNotifications': instance.enableNotifications,
      'enableLocationTracking': instance.enableLocationTracking,
      'preferredLanguage': instance.preferredLanguage,
    };
