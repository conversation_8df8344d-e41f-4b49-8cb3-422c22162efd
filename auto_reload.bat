@echo off
echo 🚀 Flutter Auto Reload Helper
echo.
echo This script will remind you to press 'r' when files change
echo.
echo 💡 Instructions:
echo 1. Keep this window open
echo 2. Edit your .dart files
echo 3. Save the file (Ctrl+S)
echo 4. Press 'r' in your Flutter terminal
echo.
echo Press Ctrl+C to stop watching
echo.

:watch_loop
for /r lib %%f in (*.dart) do (
    echo Watching: %%f
)
echo.
echo ⏰ Watching for changes... Save any .dart file!
timeout /t 5 /nobreak >nul
goto watch_loop
