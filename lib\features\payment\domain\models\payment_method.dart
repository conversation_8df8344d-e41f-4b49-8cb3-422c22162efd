import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'payment_method.g.dart';

@HiveType(typeId: 5)
enum PaymentType {
  @HiveField(0)
  upi,
  @HiveField(1)
  wallet,
  @HiveField(2)
  card,
  @HiveField(3)
  netBanking,
  @HiveField(4)
  cod,
  @HiveField(5)
  emi,
}

@HiveType(typeId: 6)
@JsonSerializable()
class PaymentMethod extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String displayName;

  @HiveField(3)
  final PaymentType type;

  @HiveField(4)
  final String iconPath;

  @HiveField(5)
  final bool isEnabled;

  @HiveField(6)
  final bool isPopular;

  @HiveField(7)
  final String? description;

  @HiveField(8)
  final double? processingFee;

  @HiveField(9)
  final double? minAmount;

  @HiveField(10)
  final double? maxAmount;

  @HiveField(11)
  final List<String> supportedCurrencies;

  const PaymentMethod({
    required this.id,
    required this.name,
    required this.displayName,
    required this.type,
    required this.iconPath,
    this.isEnabled = true,
    this.isPopular = false,
    this.description,
    this.processingFee,
    this.minAmount,
    this.maxAmount,
    this.supportedCurrencies = const ['INR'],
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentMethodToJson(this);

  PaymentMethod copyWith({
    String? id,
    String? name,
    String? displayName,
    PaymentType? type,
    String? iconPath,
    bool? isEnabled,
    bool? isPopular,
    String? description,
    double? processingFee,
    double? minAmount,
    double? maxAmount,
    List<String>? supportedCurrencies,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      type: type ?? this.type,
      iconPath: iconPath ?? this.iconPath,
      isEnabled: isEnabled ?? this.isEnabled,
      isPopular: isPopular ?? this.isPopular,
      description: description ?? this.description,
      processingFee: processingFee ?? this.processingFee,
      minAmount: minAmount ?? this.minAmount,
      maxAmount: maxAmount ?? this.maxAmount,
      supportedCurrencies: supportedCurrencies ?? this.supportedCurrencies,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    displayName,
    type,
    iconPath,
    isEnabled,
    isPopular,
    description,
    processingFee,
    minAmount,
    maxAmount,
    supportedCurrencies,
  ];
}

// Predefined Indian payment methods
class IndianPaymentMethods {
  static List<PaymentMethod> get all => [
    // UPI Methods
    PaymentMethod(
      id: 'gpay',
      name: 'gpay',
      displayName: 'Google Pay',
      type: PaymentType.upi,
      iconPath: 'assets/icons/payment/gpay.png',
      isPopular: true,
      description: 'Pay using Google Pay UPI',
    ),
    PaymentMethod(
      id: 'phonepe',
      name: 'phonepe',
      displayName: 'PhonePe',
      type: PaymentType.upi,
      iconPath: 'assets/icons/payment/phonepe.png',
      isPopular: true,
      description: 'Pay using PhonePe UPI',
    ),
    PaymentMethod(
      id: 'paytm',
      name: 'paytm',
      displayName: 'Paytm UPI',
      type: PaymentType.upi,
      iconPath: 'assets/icons/payment/paytm.png',
      isPopular: true,
      description: 'Pay using Paytm UPI',
    ),
    PaymentMethod(
      id: 'upi',
      name: 'upi',
      displayName: 'UPI',
      type: PaymentType.upi,
      iconPath: 'assets/icons/payment/upi.png',
      description: 'Pay using any UPI app',
    ),

    // Wallet Methods
    PaymentMethod(
      id: 'projek_coin',
      name: 'projek_coin',
      displayName: 'Projek Coin',
      type: PaymentType.wallet,
      iconPath: 'assets/icons/payment/projek_coin.png',
      isPopular: true,
      description: 'Pay using your Projek Coin balance',
      processingFee: 0.0, // No processing fee for Projek Coins
    ),
    PaymentMethod(
      id: 'paytm_wallet',
      name: 'paytm_wallet',
      displayName: 'Paytm Wallet',
      type: PaymentType.wallet,
      iconPath: 'assets/icons/payment/paytm.png',
      description: 'Pay using Paytm Wallet',
    ),
    PaymentMethod(
      id: 'mobikwik',
      name: 'mobikwik',
      displayName: 'MobiKwik',
      type: PaymentType.wallet,
      iconPath: 'assets/icons/payment/mobikwik.png',
      description: 'Pay using MobiKwik Wallet',
    ),
    PaymentMethod(
      id: 'freecharge',
      name: 'freecharge',
      displayName: 'FreeCharge',
      type: PaymentType.wallet,
      iconPath: 'assets/icons/payment/freecharge.png',
      description: 'Pay using FreeCharge Wallet',
    ),

    // Card Methods
    PaymentMethod(
      id: 'credit_card',
      name: 'credit_card',
      displayName: 'Credit Card',
      type: PaymentType.card,
      iconPath: 'assets/icons/payment/credit_card.png',
      description: 'Pay using Credit Card',
      processingFee: 2.0, // 2% processing fee
    ),
    PaymentMethod(
      id: 'debit_card',
      name: 'debit_card',
      displayName: 'Debit Card',
      type: PaymentType.card,
      iconPath: 'assets/icons/payment/debit_card.png',
      description: 'Pay using Debit Card',
    ),

    // Net Banking
    PaymentMethod(
      id: 'net_banking',
      name: 'net_banking',
      displayName: 'Net Banking',
      type: PaymentType.netBanking,
      iconPath: 'assets/icons/payment/net_banking.png',
      description: 'Pay using Internet Banking',
    ),

    // Cash on Delivery
    PaymentMethod(
      id: 'cod',
      name: 'cod',
      displayName: 'Cash on Delivery',
      type: PaymentType.cod,
      iconPath: 'assets/icons/payment/cod.png',
      description: 'Pay when you receive your order',
      maxAmount: 5000.0, // COD limit
    ),

    // EMI
    PaymentMethod(
      id: 'emi',
      name: 'emi',
      displayName: 'EMI',
      type: PaymentType.emi,
      iconPath: 'assets/icons/payment/emi.png',
      description: 'Pay in easy monthly installments',
      minAmount: 3000.0, // Minimum EMI amount
    ),
  ];

  static List<PaymentMethod> get popular =>
      all.where((method) => method.isPopular).toList();

  static List<PaymentMethod> getByType(PaymentType type) {
    return all.where((method) => method.type == type).toList();
  }

  static PaymentMethod? getById(String id) {
    try {
      return all.firstWhere((method) => method.id == id);
    } catch (e) {
      return null;
    }
  }
}

@HiveType(typeId: 7)
@JsonSerializable()
class PaymentTransaction extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String orderId;

  @HiveField(2)
  final String paymentMethodId;

  @HiveField(3)
  final double amount;

  @HiveField(4)
  final String currency;

  @HiveField(5)
  final PaymentStatus status;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime? completedAt;

  @HiveField(8)
  final String? transactionId;

  @HiveField(9)
  final String? gatewayResponse;

  @HiveField(10)
  final String? failureReason;

  const PaymentTransaction({
    required this.id,
    required this.orderId,
    required this.paymentMethodId,
    required this.amount,
    this.currency = 'INR',
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.transactionId,
    this.gatewayResponse,
    this.failureReason,
  });

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) =>
      _$PaymentTransactionFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentTransactionToJson(this);

  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';

  @override
  List<Object?> get props => [
    id,
    orderId,
    paymentMethodId,
    amount,
    currency,
    status,
    createdAt,
    completedAt,
    transactionId,
    gatewayResponse,
    failureReason,
  ];
}

@HiveType(typeId: 8)
enum PaymentStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  processing,
  @HiveField(2)
  success,
  @HiveField(3)
  failed,
  @HiveField(4)
  cancelled,
  @HiveField(5)
  refunded,
}
