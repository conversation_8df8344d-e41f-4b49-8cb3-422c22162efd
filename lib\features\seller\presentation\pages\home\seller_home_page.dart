import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../../core/config/app_config.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../products/products_page.dart';
import '../orders/orders_page.dart';
import '../profile/profile_page.dart';
import '../../../../../features/user/presentation/pages/chat/chat_list_page.dart';

class SellerHomePage extends ConsumerStatefulWidget {
  const SellerHomePage({super.key});

  @override
  ConsumerState<SellerHomePage> createState() => _SellerHomePageState();
}

class _SellerHomePageState extends ConsumerState<SellerHomePage> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const SellerDashboardTab(),
    const SellerProductsPage(),
    const SellerOrdersPage(),
    const ChatListPage(),
    const SellerProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        selectedItemColor: AppConfig.primaryColor,
        unselectedItemColor: AppColors.grey500,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: 'Products',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_bag),
            label: 'Orders',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.chat), label: 'Messages'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}

class SellerDashboardTab extends ConsumerStatefulWidget {
  const SellerDashboardTab({super.key});

  @override
  ConsumerState<SellerDashboardTab> createState() => _SellerDashboardTabState();
}

class _SellerDashboardTabState extends ConsumerState<SellerDashboardTab> {
  bool _isBusinessOpen = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Business Dashboard'),
            Text(
              _isBusinessOpen ? 'Business Open 🟢' : 'Business Closed 🔴',
              style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
            ),
          ],
        ),
        backgroundColor: AppConfig.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          Switch(
            value: _isBusinessOpen,
            onChanged: (value) {
              setState(() {
                _isBusinessOpen = value;
              });
              // TODO: Update business status in backend
            },
            activeColor: Colors.white,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              Navigator.pushNamed(context, '/analytics');
            },
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Navigate to notifications
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Business Stats
            Text('Today\'s Overview', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 12),
            _buildStatsGrid(),

            const SizedBox(height: 24),

            // Quick Actions
            Text('Quick Actions', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 12),
            _buildQuickActions(),

            const SizedBox(height: 24),

            // Recent Orders
            Text('Recent Orders', style: AppTextStyles.headlineMedium),
            const SizedBox(height: 12),
            _buildRecentOrders(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid() {
    final stats = [
      {
        'title': 'Orders',
        'value': '24',
        'icon': Icons.shopping_bag,
        'color': Colors.blue,
      },
      {
        'title': 'Revenue',
        'value': '₹12,450',
        'icon': Icons.currency_rupee,
        'color': Colors.green,
      },
      {
        'title': 'Products',
        'value': '156',
        'icon': Icons.inventory,
        'color': Colors.orange,
      },
      {
        'title': 'Rating',
        'value': '4.7',
        'icon': Icons.star,
        'color': Colors.amber,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.5,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  stat['icon'] as IconData,
                  size: 32,
                  color: stat['color'] as Color,
                ),
                const SizedBox(height: 8),
                Text(
                  stat['value'] as String,
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: stat['color'] as Color,
                  ),
                ),
                Text(
                  stat['title'] as String,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      {'title': 'Add Product', 'icon': Icons.add_box, 'route': '/add-product'},
      {'title': 'Manage Orders', 'icon': Icons.list_alt, 'route': '/orders'},
      {
        'title': 'Assign Rider',
        'icon': Icons.delivery_dining,
        'route': '/assign-rider',
      },
      {'title': 'Analytics', 'icon': Icons.analytics, 'route': '/analytics'},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return Card(
          elevation: 1,
          child: InkWell(
            onTap: () {
              // Navigate to respective page
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Icon(
                    action['icon'] as IconData,
                    size: 24,
                    color: AppConfig.primaryColor,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    action['title'] as String,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentOrders() {
    // Mock recent orders data
    final orders = [
      {
        'id': 'ORD001',
        'customer': 'John Doe',
        'items': 'House Cleaning Service',
        'amount': 299.0,
        'status': 'Pending',
        'time': '10 mins ago',
      },
      {
        'id': 'ORD002',
        'customer': 'Jane Smith',
        'items': 'AC Repair Service',
        'amount': 450.0,
        'status': 'Confirmed',
        'time': '25 mins ago',
      },
      {
        'id': 'ORD003',
        'customer': 'Mike Johnson',
        'items': 'Plumbing Service',
        'amount': 350.0,
        'status': 'In Progress',
        'time': '1 hour ago',
      },
    ];

    if (orders.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(
              Icons.shopping_bag_outlined,
              size: 64,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              'No recent orders',
              style: AppTextStyles.bodyLarge.copyWith(color: AppColors.grey600),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getStatusColor(
                  order['status'] as String,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.shopping_bag,
                color: _getStatusColor(order['status'] as String),
              ),
            ),
            title: Text(
              order['customer'] as String,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(order['items'] as String),
                Text(
                  order['time'] as String,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '₹${(order['amount'] as double).toStringAsFixed(0)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppConfig.primaryColor,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      order['status'] as String,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    order['status'] as String,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: _getStatusColor(order['status'] as String),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {
              // Navigate to order details
            },
          ),
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'in progress':
        return Colors.green;
      case 'completed':
        return Colors.grey;
      case 'cancelled':
        return Colors.red;
      default:
        return AppColors.grey600;
    }
  }
}
