@echo off
REM Development script for running Flutter app with hot reload on Android device
echo Starting Flutter app in development mode with hot reload...
echo.

REM Check if device is connected
flutter devices
echo.

REM Ask user which mode to run
echo Choose running mode:
echo 1. Debug mode (default)
echo 2. Development build variant
echo 3. Profile mode
echo.
set /p choice="Enter your choice (1-3, default is 1): "

if "%choice%"=="2" (
    echo Running in development build variant...
    flutter run --flavor development --debug
) else if "%choice%"=="3" (
    echo Running in profile mode...
    flutter run --profile
) else (
    echo Running in debug mode...
    flutter run --debug
)

echo.
echo Hot reload is now active! 
echo - Press 'r' to hot reload
echo - Press 'R' to hot restart
echo - Press 'q' to quit
pause
