import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'analytics_models.g.dart';

@HiveType(typeId: 30)
@JsonSerializable()
class SalesAnalytics extends Equatable {
  @HiveField(0)
  final String sellerId;
  
  @HiveField(1)
  final DateTime date;
  
  @HiveField(2)
  final double totalRevenue;
  
  @HiveField(3)
  final int totalOrders;
  
  @HiveField(4)
  final double averageOrderValue;
  
  @HiveField(5)
  final int newCustomers;
  
  @HiveField(6)
  final int returningCustomers;
  
  @HiveField(7)
  final double conversionRate;
  
  @HiveField(8)
  final Map<String, double> categoryRevenue;
  
  @HiveField(9)
  final Map<String, int> categoryOrders;

  const SalesAnalytics({
    required this.sellerId,
    required this.date,
    required this.totalRevenue,
    required this.totalOrders,
    required this.averageOrderValue,
    required this.newCustomers,
    required this.returningCustomers,
    required this.conversionRate,
    required this.categoryRevenue,
    required this.categoryOrders,
  });

  factory SalesAnalytics.fromJson(Map<String, dynamic> json) => _$SalesAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$SalesAnalyticsToJson(this);

  @override
  List<Object?> get props => [
    sellerId, date, totalRevenue, totalOrders, averageOrderValue,
    newCustomers, returningCustomers, conversionRate, categoryRevenue, categoryOrders,
  ];
}

@HiveType(typeId: 31)
@JsonSerializable()
class PerformanceMetrics extends Equatable {
  @HiveField(0)
  final String sellerId;
  
  @HiveField(1)
  final DateTime date;
  
  @HiveField(2)
  final double averageRating;
  
  @HiveField(3)
  final int totalReviews;
  
  @HiveField(4)
  final double responseTime; // in minutes
  
  @HiveField(5)
  final double fulfillmentRate; // percentage
  
  @HiveField(6)
  final double onTimeDeliveryRate;
  
  @HiveField(7)
  final int totalViews;
  
  @HiveField(8)
  final int totalClicks;
  
  @HiveField(9)
  final double clickThroughRate;
  
  @HiveField(10)
  final Map<String, int> topProducts; // product_id -> order_count

  const PerformanceMetrics({
    required this.sellerId,
    required this.date,
    required this.averageRating,
    required this.totalReviews,
    required this.responseTime,
    required this.fulfillmentRate,
    required this.onTimeDeliveryRate,
    required this.totalViews,
    required this.totalClicks,
    required this.clickThroughRate,
    required this.topProducts,
  });

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) => _$PerformanceMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$PerformanceMetricsToJson(this);

  @override
  List<Object?> get props => [
    sellerId, date, averageRating, totalReviews, responseTime,
    fulfillmentRate, onTimeDeliveryRate, totalViews, totalClicks,
    clickThroughRate, topProducts,
  ];
}

@HiveType(typeId: 32)
@JsonSerializable()
class CustomerInsights extends Equatable {
  @HiveField(0)
  final String sellerId;
  
  @HiveField(1)
  final DateTime date;
  
  @HiveField(2)
  final Map<String, int> ageGroups; // "18-25" -> count
  
  @HiveField(3)
  final Map<String, int> genderDistribution;
  
  @HiveField(4)
  final Map<String, int> locationDistribution;
  
  @HiveField(5)
  final Map<int, int> orderFrequency; // orders_per_month -> customer_count
  
  @HiveField(6)
  final double customerRetentionRate;
  
  @HiveField(7)
  final double customerLifetimeValue;
  
  @HiveField(8)
  final List<String> topCustomerSegments;

  const CustomerInsights({
    required this.sellerId,
    required this.date,
    required this.ageGroups,
    required this.genderDistribution,
    required this.locationDistribution,
    required this.orderFrequency,
    required this.customerRetentionRate,
    required this.customerLifetimeValue,
    required this.topCustomerSegments,
  });

  factory CustomerInsights.fromJson(Map<String, dynamic> json) => _$CustomerInsightsFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerInsightsToJson(this);

  @override
  List<Object?> get props => [
    sellerId, date, ageGroups, genderDistribution, locationDistribution,
    orderFrequency, customerRetentionRate, customerLifetimeValue, topCustomerSegments,
  ];
}

@HiveType(typeId: 33)
@JsonSerializable()
class RevenueBreakdown extends Equatable {
  @HiveField(0)
  final String sellerId;
  
  @HiveField(1)
  final DateTime date;
  
  @HiveField(2)
  final double grossRevenue;
  
  @HiveField(3)
  final double platformFees;
  
  @HiveField(4)
  final double deliveryCharges;
  
  @HiveField(5)
  final double taxes;
  
  @HiveField(6)
  final double discounts;
  
  @HiveField(7)
  final double netRevenue;
  
  @HiveField(8)
  final Map<String, double> hourlyRevenue; // "09:00" -> revenue
  
  @HiveField(9)
  final Map<String, double> dailyRevenue; // "Monday" -> revenue

  const RevenueBreakdown({
    required this.sellerId,
    required this.date,
    required this.grossRevenue,
    required this.platformFees,
    required this.deliveryCharges,
    required this.taxes,
    required this.discounts,
    required this.netRevenue,
    required this.hourlyRevenue,
    required this.dailyRevenue,
  });

  factory RevenueBreakdown.fromJson(Map<String, dynamic> json) => _$RevenueBreakdownFromJson(json);
  Map<String, dynamic> toJson() => _$RevenueBreakdownToJson(this);

  double get profitMargin => grossRevenue > 0 ? (netRevenue / grossRevenue) * 100 : 0;

  @override
  List<Object?> get props => [
    sellerId, date, grossRevenue, platformFees, deliveryCharges,
    taxes, discounts, netRevenue, hourlyRevenue, dailyRevenue,
  ];
}

@HiveType(typeId: 34)
@JsonSerializable()
class CompetitorAnalysis extends Equatable {
  @HiveField(0)
  final String sellerId;
  
  @HiveField(1)
  final DateTime date;
  
  @HiveField(2)
  final double marketShare;
  
  @HiveField(3)
  final int marketRanking;
  
  @HiveField(4)
  final double averageCompetitorRating;
  
  @HiveField(5)
  final double averageCompetitorPrice;
  
  @HiveField(6)
  final Map<String, double> categoryMarketShare;
  
  @HiveField(7)
  final List<String> competitorStrengths;
  
  @HiveField(8)
  final List<String> improvementOpportunities;

  const CompetitorAnalysis({
    required this.sellerId,
    required this.date,
    required this.marketShare,
    required this.marketRanking,
    required this.averageCompetitorRating,
    required this.averageCompetitorPrice,
    required this.categoryMarketShare,
    required this.competitorStrengths,
    required this.improvementOpportunities,
  });

  factory CompetitorAnalysis.fromJson(Map<String, dynamic> json) => _$CompetitorAnalysisFromJson(json);
  Map<String, dynamic> toJson() => _$CompetitorAnalysisToJson(this);

  @override
  List<Object?> get props => [
    sellerId, date, marketShare, marketRanking, averageCompetitorRating,
    averageCompetitorPrice, categoryMarketShare, competitorStrengths, improvementOpportunities,
  ];
}

@HiveType(typeId: 35)
enum AnalyticsTimeRange {
  @HiveField(0)
  today,
  @HiveField(1)
  yesterday,
  @HiveField(2)
  last7Days,
  @HiveField(3)
  last30Days,
  @HiveField(4)
  last90Days,
  @HiveField(5)
  thisMonth,
  @HiveField(6)
  lastMonth,
  @HiveField(7)
  thisYear,
  @HiveField(8)
  custom,
}

@HiveType(typeId: 36)
@JsonSerializable()
class AnalyticsFilter extends Equatable {
  @HiveField(0)
  final AnalyticsTimeRange timeRange;
  
  @HiveField(1)
  final DateTime? startDate;
  
  @HiveField(2)
  final DateTime? endDate;
  
  @HiveField(3)
  final List<String> categories;
  
  @HiveField(4)
  final List<String> products;
  
  @HiveField(5)
  final String? location;

  const AnalyticsFilter({
    required this.timeRange,
    this.startDate,
    this.endDate,
    this.categories = const [],
    this.products = const [],
    this.location,
  });

  factory AnalyticsFilter.fromJson(Map<String, dynamic> json) => _$AnalyticsFilterFromJson(json);
  Map<String, dynamic> toJson() => _$AnalyticsFilterToJson(this);

  @override
  List<Object?> get props => [timeRange, startDate, endDate, categories, products, location];
}

// Chart data models
@JsonSerializable()
class ChartDataPoint extends Equatable {
  final String label;
  final double value;
  final DateTime? date;
  final String? category;

  const ChartDataPoint({
    required this.label,
    required this.value,
    this.date,
    this.category,
  });

  factory ChartDataPoint.fromJson(Map<String, dynamic> json) => _$ChartDataPointFromJson(json);
  Map<String, dynamic> toJson() => _$ChartDataPointToJson(this);

  @override
  List<Object?> get props => [label, value, date, category];
}

@JsonSerializable()
class TrendData extends Equatable {
  final List<ChartDataPoint> dataPoints;
  final double trendPercentage;
  final bool isPositiveTrend;
  final String trendDescription;

  const TrendData({
    required this.dataPoints,
    required this.trendPercentage,
    required this.isPositiveTrend,
    required this.trendDescription,
  });

  factory TrendData.fromJson(Map<String, dynamic> json) => _$TrendDataFromJson(json);
  Map<String, dynamic> toJson() => _$TrendDataToJson(this);

  @override
  List<Object?> get props => [dataPoints, trendPercentage, isPositiveTrend, trendDescription];
}
