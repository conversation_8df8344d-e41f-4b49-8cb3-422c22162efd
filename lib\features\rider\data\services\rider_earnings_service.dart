import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/rider_earnings.dart';
import '../../domain/models/rider_kyc.dart';
import '../../../../demo/rider_demo_data.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/utils/app_logger.dart';

class RiderEarningsService {
  final StorageService _storageService;
  final StreamController<RiderEarnings?> _earningsController = StreamController<RiderEarnings?>.broadcast();

  RiderEarningsService(this._storageService);

  Stream<RiderEarnings?> get earningsStream => _earningsController.stream;

  Future<RiderEarnings?> getRiderEarnings(String riderId) async {
    try {
      // In a real app, this would fetch from API
      final earnings = RiderDemoData.getRiderEarnings(riderId);
      if (earnings != null) {
        _earningsController.add(earnings);
      }
      return earnings;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get rider earnings', e, stackTrace);
      return null;
    }
  }

  Future<List<EarningsTransaction>> getTransactionHistory(
    String riderId, {
    DateTime? startDate,
    DateTime? endDate,
    TransactionType? type,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      final earnings = RiderDemoData.getRiderEarnings(riderId);
      if (earnings == null) return [];

      var transactions = earnings.transactions;

      // Apply filters
      if (startDate != null) {
        transactions = transactions.where((t) => t.timestamp.isAfter(startDate)).toList();
      }
      if (endDate != null) {
        transactions = transactions.where((t) => t.timestamp.isBefore(endDate)).toList();
      }
      if (type != null) {
        transactions = transactions.where((t) => t.type == type).toList();
      }

      // Apply pagination
      final startIndex = offset;
      final endIndex = (offset + limit).clamp(0, transactions.length);
      
      return transactions.sublist(startIndex, endIndex);
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get transaction history', e, stackTrace);
      return [];
    }
  }

  Future<List<CommissionBreakdown>> getCommissionHistory(
    String riderId, {
    DateTime? startDate,
    DateTime? endDate,
    String? rideType,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      final earnings = RiderDemoData.getRiderEarnings(riderId);
      if (earnings == null) return [];

      var commissions = earnings.commissionHistory;

      // Apply filters
      if (startDate != null) {
        commissions = commissions.where((c) => c.timestamp.isAfter(startDate)).toList();
      }
      if (endDate != null) {
        commissions = commissions.where((c) => c.timestamp.isBefore(endDate)).toList();
      }
      if (rideType != null) {
        commissions = commissions.where((c) => c.rideType == rideType).toList();
      }

      // Apply pagination
      final startIndex = offset;
      final endIndex = (offset + limit).clamp(0, commissions.length);
      
      return commissions.sublist(startIndex, endIndex);
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get commission history', e, stackTrace);
      return [];
    }
  }

  Future<Map<String, double>> getEarningsByPeriod(
    String riderId,
    String period, // 'daily', 'weekly', 'monthly', 'yearly'
  ) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final earnings = RiderDemoData.getRiderEarnings(riderId);
      if (earnings == null) return {};

      // In a real app, this would calculate based on actual data
      switch (period) {
        case 'daily':
          return earnings.stats.earningsByDay;
        case 'weekly':
          return {
            'Week 1': 8750.25,
            'Week 2': 9234.50,
            'Week 3': 8456.75,
            'Week 4': 9978.30,
          };
        case 'monthly':
          return {
            'Jan': 35420.80,
            'Feb': 32156.45,
            'Mar': 38967.20,
            'Apr': 41234.60,
          };
        case 'yearly':
          return {
            '2023': 456789.50,
            '2024': 234567.80,
          };
        default:
          return {};
      }
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get earnings by period', e, stackTrace);
      return {};
    }
  }

  Future<List<WithdrawalRequest>> getWithdrawalHistory(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      return RiderDemoData.getRiderWithdrawals(riderId);
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get withdrawal history', e, stackTrace);
      return [];
    }
  }

  Future<WithdrawalRequest?> requestWithdrawal(
    String riderId,
    double amount,
    WithdrawalMethod method,
    Map<String, dynamic> paymentDetails,
  ) async {
    try {
      await Future.delayed(Duration(seconds: 1));
      
      // Calculate processing fee
      double processingFee = 0.0;
      switch (method) {
        case WithdrawalMethod.bankTransfer:
          processingFee = amount * 0.005; // 0.5%
          break;
        case WithdrawalMethod.upi:
          processingFee = amount * 0.004; // 0.4%
          break;
        default:
          processingFee = amount * 0.003; // 0.3%
      }

      final withdrawal = WithdrawalRequest(
        id: 'withdrawal_${DateTime.now().millisecondsSinceEpoch}',
        riderId: riderId,
        amount: amount,
        method: method,
        status: WithdrawalStatus.pending,
        requestedAt: DateTime.now(),
        processingFee: processingFee,
        netAmount: amount - processingFee,
        paymentDetails: paymentDetails,
      );

      AppLogger.info('Withdrawal requested: ${withdrawal.id}');
      return withdrawal;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to request withdrawal', e, stackTrace);
      return null;
    }
  }

  Future<bool> cancelWithdrawal(String withdrawalId) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      // In a real app, this would update via API
      AppLogger.info('Withdrawal cancelled: $withdrawalId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to cancel withdrawal', e, stackTrace);
      return false;
    }
  }

  Future<List<BonusIncentive>> getActiveBonuses(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final bonuses = RiderDemoData.getRiderBonuses(riderId);
      return bonuses.where((b) => b.status == BonusStatus.active).toList();
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get active bonuses', e, stackTrace);
      return [];
    }
  }

  Future<List<BonusIncentive>> getBonusHistory(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      return RiderDemoData.getRiderBonuses(riderId);
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get bonus history', e, stackTrace);
      return [];
    }
  }

  Future<Map<String, dynamic>> getEarningsAnalytics(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      final earnings = RiderDemoData.getRiderEarnings(riderId);
      if (earnings == null) return {};

      return {
        'totalEarnings': earnings.totalEarnings,
        'averagePerRide': earnings.stats.averageEarningPerRide,
        'averagePerHour': earnings.stats.averageEarningPerHour,
        'averagePerDay': earnings.stats.averageEarningPerDay,
        'peakHourEarnings': earnings.stats.peakHourEarnings,
        'offPeakEarnings': earnings.stats.offPeakEarnings,
        'earningsByType': earnings.stats.earningsByRideType,
        'earningsByDay': earnings.stats.earningsByDay,
        'totalRides': earnings.stats.totalRidesCompleted,
        'totalDistance': earnings.stats.totalDistanceCovered,
        'totalTime': earnings.stats.totalTimeOnline,
      };
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get earnings analytics', e, stackTrace);
      return {};
    }
  }

  Future<List<TaxDocument>> getTaxDocuments(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final earnings = RiderDemoData.getRiderEarnings(riderId);
      return earnings?.taxInfo.taxDocuments ?? [];
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get tax documents', e, stackTrace);
      return [];
    }
  }

  Future<String?> generateTaxDocument(
    String riderId,
    String documentType,
    String financialYear,
  ) async {
    try {
      await Future.delayed(Duration(seconds: 2));
      
      // In a real app, this would generate and return document URL
      final documentUrl = 'https://example.com/${documentType.toLowerCase()}_$financialYear.pdf';
      
      AppLogger.info('Tax document generated: $documentUrl');
      return documentUrl;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to generate tax document', e, stackTrace);
      return null;
    }
  }

  Future<double> getWithdrawableAmount(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      
      final earnings = RiderDemoData.getRiderEarnings(riderId);
      return earnings?.availableBalance ?? 0.0;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get withdrawable amount', e, stackTrace);
      return 0.0;
    }
  }

  Future<Map<String, double>> getWithdrawalLimits(WithdrawalMethod method) async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      
      // Return method-specific limits
      switch (method) {
        case WithdrawalMethod.bankTransfer:
          return {'min': 100.0, 'max': 50000.0, 'daily': 25000.0};
        case WithdrawalMethod.upi:
          return {'min': 50.0, 'max': 10000.0, 'daily': 10000.0};
        default:
          return {'min': 50.0, 'max': 5000.0, 'daily': 5000.0};
      }
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get withdrawal limits', e, stackTrace);
      return {'min': 0.0, 'max': 0.0, 'daily': 0.0};
    }
  }

  void dispose() {
    _earningsController.close();
  }
}

// Provider for RiderEarningsService
final riderEarningsServiceProvider = Provider<RiderEarningsService>((ref) {
  final storageService = ref.read(storageServiceProvider);
  return RiderEarningsService(storageService);
});

// Provider for rider earnings
final riderEarningsProvider = FutureProvider.family<RiderEarnings?, String>((ref, riderId) {
  final service = ref.read(riderEarningsServiceProvider);
  return service.getRiderEarnings(riderId);
});

// Provider for transaction history
final transactionHistoryProvider = FutureProvider.family<List<EarningsTransaction>, String>((ref, riderId) {
  final service = ref.read(riderEarningsServiceProvider);
  return service.getTransactionHistory(riderId);
});

// Provider for withdrawal history
final withdrawalHistoryProvider = FutureProvider.family<List<WithdrawalRequest>, String>((ref, riderId) {
  final service = ref.read(riderEarningsServiceProvider);
  return service.getWithdrawalHistory(riderId);
});

// Provider for active bonuses
final activeBonusesProvider = FutureProvider.family<List<BonusIncentive>, String>((ref, riderId) {
  final service = ref.read(riderEarningsServiceProvider);
  return service.getActiveBonuses(riderId);
});

// Provider for earnings analytics
final earningsAnalyticsProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, riderId) {
  final service = ref.read(riderEarningsServiceProvider);
  return service.getEarningsAnalytics(riderId);
});

// Provider for withdrawable amount
final withdrawableAmountProvider = FutureProvider.family<double, String>((ref, riderId) {
  final service = ref.read(riderEarningsServiceProvider);
  return service.getWithdrawableAmount(riderId);
});
