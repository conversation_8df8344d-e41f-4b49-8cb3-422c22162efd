import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'rider_analytics.g.dart';

@HiveType(typeId: 47)
@JsonSerializable()
class RiderAnalytics {
  @HiveField(0)
  final String riderId;

  @HiveField(1)
  final PerformanceMetrics performance;

  @HiveField(2)
  final RideStatistics rideStats;

  @HiveField(3)
  final EarningsAnalytics earnings;

  @HiveField(4)
  final CustomerFeedback feedback;

  @HiveField(5)
  final LocationAnalytics location;

  @HiveField(6)
  final TimeAnalytics timeAnalytics;

  @HiveField(7)
  final DateTime lastUpdated;

  const RiderAnalytics({
    required this.riderId,
    required this.performance,
    required this.rideStats,
    required this.earnings,
    required this.feedback,
    required this.location,
    required this.timeAnalytics,
    required this.lastUpdated,
  });

  factory RiderAnalytics.fromJson(Map<String, dynamic> json) =>
      _$RiderAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$RiderAnalyticsToJson(this);
}

@HiveType(typeId: 48)
@JsonSerializable()
class PerformanceMetrics {
  @HiveField(0)
  final double overallRating;

  @HiveField(1)
  final double acceptanceRate;

  @HiveField(2)
  final double completionRate;

  @HiveField(3)
  final double cancellationRate;

  @HiveField(4)
  final double onTimePercentage;

  @HiveField(5)
  final double averageResponseTime;

  @HiveField(6)
  final int totalRidesCompleted;

  @HiveField(7)
  final int totalRidesCancelled;

  @HiveField(8)
  final double customerSatisfactionScore;

  @HiveField(9)
  final List<PerformanceTrend> trends;

  const PerformanceMetrics({
    required this.overallRating,
    required this.acceptanceRate,
    required this.completionRate,
    required this.cancellationRate,
    required this.onTimePercentage,
    required this.averageResponseTime,
    required this.totalRidesCompleted,
    required this.totalRidesCancelled,
    required this.customerSatisfactionScore,
    required this.trends,
  });

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) =>
      _$PerformanceMetricsFromJson(json);

  Map<String, dynamic> toJson() => _$PerformanceMetricsToJson(this);
}

@HiveType(typeId: 49)
@JsonSerializable()
class PerformanceTrend {
  @HiveField(0)
  final DateTime date;

  @HiveField(1)
  final double rating;

  @HiveField(2)
  final int ridesCompleted;

  @HiveField(3)
  final double earnings;

  @HiveField(4)
  final double acceptanceRate;

  const PerformanceTrend({
    required this.date,
    required this.rating,
    required this.ridesCompleted,
    required this.earnings,
    required this.acceptanceRate,
  });

  factory PerformanceTrend.fromJson(Map<String, dynamic> json) =>
      _$PerformanceTrendFromJson(json);

  Map<String, dynamic> toJson() => _$PerformanceTrendToJson(this);
}

@HiveType(typeId: 50)
@JsonSerializable()
class RideStatistics {
  @HiveField(0)
  final int totalRides;

  @HiveField(1)
  final int todayRides;

  @HiveField(2)
  final int weeklyRides;

  @HiveField(3)
  final int monthlyRides;

  @HiveField(4)
  final double totalDistance;

  @HiveField(5)
  final double averageRideDistance;

  @HiveField(6)
  final double totalDuration;

  @HiveField(7)
  final double averageRideDuration;

  @HiveField(8)
  final Map<String, int> ridesByType;

  @HiveField(9)
  final Map<String, int> ridesByHour;

  @HiveField(10)
  final Map<String, int> ridesByDay;

  @HiveField(11)
  final List<PopularRoute> popularRoutes;

  const RideStatistics({
    required this.totalRides,
    required this.todayRides,
    required this.weeklyRides,
    required this.monthlyRides,
    required this.totalDistance,
    required this.averageRideDistance,
    required this.totalDuration,
    required this.averageRideDuration,
    required this.ridesByType,
    required this.ridesByHour,
    required this.ridesByDay,
    required this.popularRoutes,
  });

  factory RideStatistics.fromJson(Map<String, dynamic> json) =>
      _$RideStatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$RideStatisticsToJson(this);
}

@HiveType(typeId: 51)
@JsonSerializable()
class PopularRoute {
  @HiveField(0)
  final String fromLocation;

  @HiveField(1)
  final String toLocation;

  @HiveField(2)
  final int rideCount;

  @HiveField(3)
  final double averageEarning;

  @HiveField(4)
  final double averageDuration;

  const PopularRoute({
    required this.fromLocation,
    required this.toLocation,
    required this.rideCount,
    required this.averageEarning,
    required this.averageDuration,
  });

  factory PopularRoute.fromJson(Map<String, dynamic> json) =>
      _$PopularRouteFromJson(json);

  Map<String, dynamic> toJson() => _$PopularRouteToJson(this);
}

@HiveType(typeId: 52)
@JsonSerializable()
class EarningsAnalytics {
  @HiveField(0)
  final double totalEarnings;

  @HiveField(1)
  final double averageEarningPerRide;

  @HiveField(2)
  final double averageEarningPerHour;

  @HiveField(3)
  final double peakHourEarnings;

  @HiveField(4)
  final double offPeakEarnings;

  @HiveField(5)
  final Map<String, double> earningsByHour;

  @HiveField(6)
  final Map<String, double> earningsByDay;

  @HiveField(7)
  final Map<String, double> earningsByRideType;

  @HiveField(8)
  final List<EarningsTrend> trends;

  const EarningsAnalytics({
    required this.totalEarnings,
    required this.averageEarningPerRide,
    required this.averageEarningPerHour,
    required this.peakHourEarnings,
    required this.offPeakEarnings,
    required this.earningsByHour,
    required this.earningsByDay,
    required this.earningsByRideType,
    required this.trends,
  });

  factory EarningsAnalytics.fromJson(Map<String, dynamic> json) =>
      _$EarningsAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$EarningsAnalyticsToJson(this);
}

@HiveType(typeId: 53)
@JsonSerializable()
class EarningsTrend {
  @HiveField(0)
  final DateTime date;

  @HiveField(1)
  final double earnings;

  @HiveField(2)
  final int rides;

  @HiveField(3)
  final double hoursOnline;

  const EarningsTrend({
    required this.date,
    required this.earnings,
    required this.rides,
    required this.hoursOnline,
  });

  factory EarningsTrend.fromJson(Map<String, dynamic> json) =>
      _$EarningsTrendFromJson(json);

  Map<String, dynamic> toJson() => _$EarningsTrendToJson(this);
}

@HiveType(typeId: 54)
@JsonSerializable()
class CustomerFeedback {
  @HiveField(0)
  final double averageRating;

  @HiveField(1)
  final int totalReviews;

  @HiveField(2)
  final Map<int, int> ratingDistribution;

  @HiveField(3)
  final List<String> positiveComments;

  @HiveField(4)
  final List<String> negativeComments;

  @HiveField(5)
  final Map<String, int> feedbackCategories;

  @HiveField(6)
  final List<CustomerReview> recentReviews;

  const CustomerFeedback({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
    required this.positiveComments,
    required this.negativeComments,
    required this.feedbackCategories,
    required this.recentReviews,
  });

  factory CustomerFeedback.fromJson(Map<String, dynamic> json) =>
      _$CustomerFeedbackFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerFeedbackToJson(this);
}

@HiveType(typeId: 55)
@JsonSerializable()
class CustomerReview {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String customerId;

  @HiveField(2)
  final String customerName;

  @HiveField(3)
  final int rating;

  @HiveField(4)
  final String comment;

  @HiveField(5)
  final DateTime timestamp;

  @HiveField(6)
  final String rideId;

  const CustomerReview({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.rating,
    required this.comment,
    required this.timestamp,
    required this.rideId,
  });

  factory CustomerReview.fromJson(Map<String, dynamic> json) =>
      _$CustomerReviewFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerReviewToJson(this);
}

@HiveType(typeId: 56)
@JsonSerializable()
class LocationAnalytics {
  @HiveField(0)
  final List<HotspotArea> hotspots;

  @HiveField(1)
  final Map<String, int> ridesByArea;

  @HiveField(2)
  final Map<String, double> earningsByArea;

  @HiveField(3)
  final List<String> recommendedAreas;

  @HiveField(4)
  final double totalDistanceCovered;

  const LocationAnalytics({
    required this.hotspots,
    required this.ridesByArea,
    required this.earningsByArea,
    required this.recommendedAreas,
    required this.totalDistanceCovered,
  });

  factory LocationAnalytics.fromJson(Map<String, dynamic> json) =>
      _$LocationAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$LocationAnalyticsToJson(this);
}

@HiveType(typeId: 57)
@JsonSerializable()
class HotspotArea {
  @HiveField(0)
  final String name;

  @HiveField(1)
  final double latitude;

  @HiveField(2)
  final double longitude;

  @HiveField(3)
  final double radius;

  @HiveField(4)
  final int rideCount;

  @HiveField(5)
  final double averageEarning;

  @HiveField(6)
  final List<String> peakHours;

  const HotspotArea({
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.radius,
    required this.rideCount,
    required this.averageEarning,
    required this.peakHours,
  });

  factory HotspotArea.fromJson(Map<String, dynamic> json) =>
      _$HotspotAreaFromJson(json);

  Map<String, dynamic> toJson() => _$HotspotAreaToJson(this);
}

@HiveType(typeId: 58)
@JsonSerializable()
class TimeAnalytics {
  @HiveField(0)
  final double totalOnlineHours;

  @HiveField(1)
  final double averageOnlineHoursPerDay;

  @HiveField(2)
  final Map<String, double> onlineHoursByDay;

  @HiveField(3)
  final Map<String, double> onlineHoursByHour;

  @HiveField(4)
  final List<String> mostActiveHours;

  @HiveField(5)
  final List<String> leastActiveHours;

  @HiveField(6)
  final double utilizationRate;

  const TimeAnalytics({
    required this.totalOnlineHours,
    required this.averageOnlineHoursPerDay,
    required this.onlineHoursByDay,
    required this.onlineHoursByHour,
    required this.mostActiveHours,
    required this.leastActiveHours,
    required this.utilizationRate,
  });

  factory TimeAnalytics.fromJson(Map<String, dynamic> json) =>
      _$TimeAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$TimeAnalyticsToJson(this);
}
