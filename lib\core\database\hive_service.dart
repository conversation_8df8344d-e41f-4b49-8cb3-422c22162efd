import 'package:hive_flutter/hive_flutter.dart';
import '../config/constants.dart';

class HiveService {
  static Box? _userBox;
  static Box? _settingsBox;
  static Box? _cacheBox;
  static bool _initialized = false;

  static Future<void> initialize() async {
    try {
      // Open boxes
      _userBox = await Hive.openBox('user_data');
      _settingsBox = await Hive.openBox('app_settings');
      _cacheBox = await Hive.openBox('cache_data');
      _initialized = true;
    } catch (e) {
      print('HiveService initialization failed: $e');
      // Continue without Hive - app will work with reduced functionality
      _initialized = false;
    }
  }

  // User Data Methods
  static Future<void> saveUserToken(String token) async {
    if (_initialized && _userBox != null) {
      await _userBox!.put(AppConstants.userTokenKey, token);
    }
  }

  static String? getUserToken() {
    if (_initialized && _userBox != null) {
      return _userBox!.get(AppConstants.userTokenKey);
    }
    return null;
  }

  static Future<void> saveUserId(String userId) async {
    if (_initialized && _userBox != null) {
      await _userBox!.put(AppConstants.userIdKey, userId);
    }
  }

  static String? getUserId() {
    if (_initialized && _userBox != null) {
      return _userBox!.get(AppConstants.userIdKey);
    }
    return null;
  }

  static Future<void> clearUserData() async {
    if (_initialized && _userBox != null) {
      await _userBox!.clear();
    }
  }

  // Settings Methods
  static Future<void> saveThemeMode(String themeMode) async {
    if (_initialized && _settingsBox != null) {
      await _settingsBox!.put(AppConstants.themeKey, themeMode);
    }
  }

  static String getThemeMode() {
    if (_initialized && _settingsBox != null) {
      return _settingsBox!.get(AppConstants.themeKey, defaultValue: 'system');
    }
    return 'system';
  }

  static Future<void> saveLanguage(String languageCode) async {
    if (_initialized && _settingsBox != null) {
      await _settingsBox!.put(AppConstants.languageKey, languageCode);
    }
  }

  static String getLanguage() {
    if (_initialized && _settingsBox != null) {
      return _settingsBox!.get(AppConstants.languageKey, defaultValue: 'en');
    }
    return 'en';
  }

  static Future<void> saveOnboardingStatus(bool completed) async {
    if (_initialized && _settingsBox != null) {
      await _settingsBox!.put(AppConstants.onboardingKey, completed);
    }
  }

  static bool getOnboardingStatus() {
    if (_initialized && _settingsBox != null) {
      return _settingsBox!.get(AppConstants.onboardingKey, defaultValue: false);
    }
    return false;
  }

  static Future<void> saveLocationPermission(bool granted) async {
    if (_initialized && _settingsBox != null) {
      await _settingsBox!.put(AppConstants.locationPermissionKey, granted);
    }
  }

  static bool getLocationPermission() {
    if (_initialized && _settingsBox != null) {
      return _settingsBox!.get(
        AppConstants.locationPermissionKey,
        defaultValue: false,
      );
    }
    return false;
  }

  // Cache Methods
  static Future<void> saveToCache(String key, dynamic value) async {
    if (_initialized && _cacheBox != null) {
      await _cacheBox!.put(key, {
        'data': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    }
  }

  static T? getFromCache<T>(String key, {Duration? maxAge}) {
    if (!_initialized || _cacheBox == null) return null;

    final cached = _cacheBox!.get(key);
    if (cached == null) return null;

    if (maxAge != null) {
      final timestamp = cached['timestamp'] as int;
      final age = DateTime.now().millisecondsSinceEpoch - timestamp;
      if (age > maxAge.inMilliseconds) {
        _cacheBox!.delete(key);
        return null;
      }
    }

    return cached['data'] as T?;
  }

  static Future<void> clearCache() async {
    if (_initialized && _cacheBox != null) {
      await _cacheBox!.clear();
    }
  }

  static Future<void> clearExpiredCache() async {
    if (!_initialized || _cacheBox == null) return;

    final keys = _cacheBox!.keys.toList();
    final now = DateTime.now().millisecondsSinceEpoch;

    for (final key in keys) {
      final cached = _cacheBox!.get(key);
      if (cached != null) {
        final timestamp = cached['timestamp'] as int;
        final age = now - timestamp;
        if (age > AppConstants.cacheExpiry.inMilliseconds) {
          await _cacheBox!.delete(key);
        }
      }
    }
  }

  // Generic Methods
  static Future<void> saveData(
    String boxName,
    String key,
    dynamic value,
  ) async {
    final box = await Hive.openBox(boxName);
    await box.put(key, value);
  }

  static T? getData<T>(String boxName, String key, {T? defaultValue}) {
    final box = Hive.box(boxName);
    return box.get(key, defaultValue: defaultValue);
  }

  static Future<void> deleteData(String boxName, String key) async {
    final box = await Hive.openBox(boxName);
    await box.delete(key);
  }

  static Future<void> clearBox(String boxName) async {
    final box = await Hive.openBox(boxName);
    await box.clear();
  }

  // Offline Data Methods
  static Future<void> saveOfflineData(
    String collection,
    String id,
    Map<String, dynamic> data,
  ) async {
    final box = await Hive.openBox('offline_$collection');
    await box.put(id, {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'synced': false,
    });
  }

  static List<Map<String, dynamic>> getUnsyncedData(String collection) {
    final box = Hive.box('offline_$collection');
    final unsyncedData = <Map<String, dynamic>>[];

    for (final key in box.keys) {
      final item = box.get(key);
      if (item != null && !(item['synced'] as bool)) {
        unsyncedData.add({
          'id': key,
          'data': item['data'],
          'timestamp': item['timestamp'],
        });
      }
    }

    return unsyncedData;
  }

  static Future<void> markAsSynced(String collection, String id) async {
    final box = await Hive.openBox('offline_$collection');
    final item = box.get(id);
    if (item != null) {
      item['synced'] = true;
      await box.put(id, item);
    }
  }

  static Future<void> clearSyncedData(String collection) async {
    final box = await Hive.openBox('offline_$collection');
    final keysToDelete = <dynamic>[];

    for (final key in box.keys) {
      final item = box.get(key);
      if (item != null && (item['synced'] as bool)) {
        keysToDelete.add(key);
      }
    }

    for (final key in keysToDelete) {
      await box.delete(key);
    }
  }
}
