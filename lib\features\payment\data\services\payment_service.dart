import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../domain/models/payment_method.dart';

// Mock classes for payment integration when packages are not available
class MockRazorpay {
  static const String eventPaymentSuccess = 'payment.success';
  static const String eventPaymentError = 'payment.error';
  static const String eventExternalWallet = 'payment.external_wallet';

  void on(String event, Function callback) {
    // Mock implementation
  }

  void open(Map<String, dynamic> options) {
    // Mock implementation - in real app this would open Razorpay
    debugPrint(
      'Mock Razorpay payment initiated with amount: ${options['amount']}',
    );
  }

  void clear() {
    // Mock implementation
  }
}

class MockUpiIndia {
  Future<List<MockUpiApp>> getAllUpiApps({
    bool mandatoryTransactionId = false,
  }) async {
    // Mock implementation - return common UPI apps
    return [
      MockUpiApp('Google Pay', 'com.google.android.apps.nbu.paisa.user'),
      MockUpiApp('PhonePe', 'com.phonepe.app'),
      MockUpiApp('Paytm', 'net.one97.paytm'),
      MockUpiApp('BHIM', 'in.org.npci.upiapp'),
    ];
  }

  Future<MockUpiResponse> startTransaction({
    required MockUpiApp app,
    required String receiverUpiId,
    required String receiverName,
    required String transactionRefId,
    required String transactionNote,
    required double amount,
  }) async {
    // Mock implementation
    await Future.delayed(const Duration(seconds: 2));
    return MockUpiResponse(
      status: MockUpiPaymentStatus.success,
      transactionId: 'mock_${DateTime.now().millisecondsSinceEpoch}',
      responseCode: 'SUCCESS',
    );
  }
}

class MockUpiApp {
  final String name;
  final String packageName;

  MockUpiApp(this.name, this.packageName);

  static MockUpiApp get googlePay =>
      MockUpiApp('Google Pay', 'com.google.android.apps.nbu.paisa.user');
  static MockUpiApp get phonePe => MockUpiApp('PhonePe', 'com.phonepe.app');
  static MockUpiApp get paytm => MockUpiApp('Paytm', 'net.one97.paytm');
  static MockUpiApp get bhimUpi => MockUpiApp('BHIM', 'in.org.npci.upiapp');
  static MockUpiApp get amazonPay =>
      MockUpiApp('Amazon Pay', 'in.amazon.mShop.android.shopping');
}

class MockUpiResponse {
  final MockUpiPaymentStatus status;
  final String? transactionId;
  final String? responseCode;

  MockUpiResponse({
    required this.status,
    this.transactionId,
    this.responseCode,
  });
}

enum MockUpiPaymentStatus { success, failure, submitted }

class MockPaymentSuccessResponse {
  final String? paymentId;
  final String? orderId;
  final String? signature;

  MockPaymentSuccessResponse({this.paymentId, this.orderId, this.signature});
}

class MockPaymentFailureResponse {
  final int? code;
  final String? message;
  final String? description;

  MockPaymentFailureResponse({this.code, this.message, this.description});
}

class MockExternalWalletResponse {
  final String? walletName;

  MockExternalWalletResponse({this.walletName});
}

class PaymentService {
  late MockRazorpay _razorpay;
  final MockUpiIndia _upiIndia = MockUpiIndia();

  PaymentService() {
    _razorpay = MockRazorpay();
    _razorpay.on(MockRazorpay.eventPaymentSuccess, _handlePaymentSuccess);
    _razorpay.on(MockRazorpay.eventPaymentError, _handlePaymentError);
    _razorpay.on(MockRazorpay.eventExternalWallet, _handleExternalWallet);
  }

  // Razorpay Payment
  Future<void> initiateRazorpayPayment({
    required double amount,
    required String orderId,
    required String description,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
  }) async {
    var options = {
      'key': 'YOUR_RAZORPAY_KEY_ID', // Replace with your Razorpay key
      'amount': (amount * 100).toInt(), // Amount in paise
      'name': 'Projek',
      'description': description,
      'order_id': orderId,
      'prefill': {
        'contact': customerPhone,
        'email': customerEmail,
        'name': customerName,
      },
      'theme': {'color': '#3399cc'},
    };

    try {
      _razorpay.open(options);
    } catch (e) {
      debugPrint('Error: $e');
    }
  }

  // UPI Payment
  Future<MockUpiResponse?> initiateUpiPayment({
    required String app,
    required double amount,
    required String receiverUpiId,
    required String receiverName,
    required String transactionNote,
    required String transactionRefId,
  }) async {
    try {
      final MockUpiApp? upiApp = _getUpiApp(app);
      if (upiApp == null) return null;

      final MockUpiResponse response = await _upiIndia.startTransaction(
        app: upiApp,
        receiverUpiId: receiverUpiId,
        receiverName: receiverName,
        transactionRefId: transactionRefId,
        transactionNote: transactionNote,
        amount: amount,
      );

      return response;
    } catch (e) {
      debugPrint('UPI Error: $e');
      return null;
    }
  }

  // Get available UPI apps
  Future<List<MockUpiApp>> getAvailableUpiApps() async {
    try {
      return await _upiIndia.getAllUpiApps(mandatoryTransactionId: false);
    } catch (e) {
      debugPrint('Error getting UPI apps: $e');
      return [];
    }
  }

  // Launch UPI app directly
  Future<void> launchUpiApp(
    String app, {
    required double amount,
    required String upiId,
    required String name,
    required String note,
  }) async {
    final String upiUrl = 'upi://pay?pa=$upiId&pn=$name&am=$amount&tn=$note';

    try {
      if (await canLaunchUrl(Uri.parse(upiUrl))) {
        await launchUrl(Uri.parse(upiUrl));
      } else {
        throw 'Could not launch UPI app';
      }
    } catch (e) {
      debugPrint('Error launching UPI app: $e');
    }
  }

  // Cash on Delivery
  Future<PaymentTransaction> processCodPayment({
    required String orderId,
    required double amount,
  }) async {
    // Simulate COD processing
    await Future.delayed(const Duration(seconds: 1));

    return PaymentTransaction(
      id: 'cod_${DateTime.now().millisecondsSinceEpoch}',
      orderId: orderId,
      paymentMethodId: 'cod',
      amount: amount,
      status: PaymentStatus.success,
      createdAt: DateTime.now(),
      completedAt: DateTime.now(),
    );
  }

  // Helper method to get UPI app
  MockUpiApp? _getUpiApp(String appName) {
    switch (appName.toLowerCase()) {
      case 'gpay':
      case 'googlepay':
        return MockUpiApp.googlePay;
      case 'phonepe':
        return MockUpiApp.phonePe;
      case 'paytm':
        return MockUpiApp.paytm;
      case 'bhim':
        return MockUpiApp.bhimUpi;
      case 'amazonpay':
        return MockUpiApp.amazonPay;
      default:
        return null;
    }
  }

  // Razorpay event handlers
  void _handlePaymentSuccess(MockPaymentSuccessResponse response) {
    debugPrint('Payment Success: ${response.paymentId}');
    // Handle success
  }

  void _handlePaymentError(MockPaymentFailureResponse response) {
    debugPrint('Payment Error: ${response.code} - ${response.message}');
    // Handle error
  }

  void _handleExternalWallet(MockExternalWalletResponse response) {
    debugPrint('External Wallet: ${response.walletName}');
    // Handle external wallet
  }

  void dispose() {
    _razorpay.clear();
  }
}

// Payment result callback
typedef PaymentCallback = void Function(PaymentResult result);

class PaymentResult {
  final bool isSuccess;
  final String? transactionId;
  final String? orderId;
  final String? errorMessage;
  final PaymentMethod paymentMethod;

  PaymentResult({
    required this.isSuccess,
    this.transactionId,
    this.orderId,
    this.errorMessage,
    required this.paymentMethod,
  });
}
