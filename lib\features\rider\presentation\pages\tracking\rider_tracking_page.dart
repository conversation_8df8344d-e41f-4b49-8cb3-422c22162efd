import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:projek/features/rider/presentation/widgets/order_summary_widget.dart';
import 'package:projek/features/tracking/presentation/providers/tracking_providers.dart';

import '../../../../tracking/domain/models/real_time_tracking_models.dart';
import '../../../../tracking/data/services/real_time_tracking_service.dart';
import '../../../../tracking/domain/models/unified_order.dart';
import '../../widgets/navigation_widget.dart';
import '../../widgets/delivery_actions_widget.dart';
import '../widgets/order_summary_widget.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';

final activeTrackingProvider = StreamProvider.family<List<RealTimeTracking>, String>((ref, riderId) {
  return RealTimeTrackingService.getRiderActiveTracking(riderId);
});

class RiderTrackingPage extends ConsumerStatefulWidget {
  final String orderId;
  final String riderId;

  const RiderTrackingPage({
    super.key,
    required this.orderId,
    required this.riderId,
  });

  @override
  ConsumerState<RiderTrackingPage> createState() => _RiderTrackingPageState();
}

class _RiderTrackingPageState extends ConsumerState<RiderTrackingPage>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  late AnimationController _speedometerController;
  late Animation<double> _speedometerAnimation;
  
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  bool _isNavigating = false;
  
  @override
  void initState() {
    super.initState();
    
    _speedometerController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _speedometerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _speedometerController,
      curve: Curves.easeInOut,
    ));
    
    _initializeTracking();
  }

  @override
  void dispose() {
    _speedometerController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final trackingAsync = ref.watch(trackingProvider(widget.orderId));

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _buildAppBar(),
      body: trackingAsync.when(
        data: (tracking) => _buildTrackingInterface(tracking),
        loading: () => _buildLoadingState(),
        error: (error, stack) => _buildErrorState(error.toString()),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.black,
      foregroundColor: Colors.white,
      title: const Text(
        'Delivery Navigation',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      actions: [
        IconButton(
          onPressed: () => _toggleTracking(),
          icon: Icon(_isNavigating ? Icons.pause : Icons.play_arrow),
        ),
        IconButton(
          onPressed: () => _showTrackingSettings(),
          icon: const Icon(Icons.settings),
        ),
      ],
    );
  }

  Widget _buildTrackingInterface(RealTimeTracking tracking) {
    return Column(
      children: [
        // Map and Navigation
        Expanded(
          flex: 3,
          child: _buildNavigationMap(tracking),
        ),
        
        // Control Panel
        Container(
          height: 280,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: _buildControlPanel(tracking),
        ),
      ],
    );
  }

  Widget _buildNavigationMap(RealTimeTracking tracking) {
    return Stack(
      children: [
        // Google Map with dark theme
        GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition: const CameraPosition(
            target: LatLng(28.6139, 77.2090),
            zoom: 16,
          ),
          markers: _markers,
          polylines: _polylines,
          myLocationEnabled: true,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: true,
          trafficEnabled: true,
          style: _darkMapStyle,
        ),
        
        // Speed and Status Overlay
        Positioned(
          top: 16,
          left: 16,
          child: _buildSpeedometer(tracking),
        ),
        
        // Navigation Instructions
        if (tracking.activeRoute != null)
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: NavigationWidget(route: tracking.activeRoute!),
          ),
        
        // Emergency Button
        Positioned(
          bottom: 16,
          right: 16,
          child: FloatingActionButton(
            heroTag: 'emergency',
            onPressed: () => _showEmergencyOptions(),
            backgroundColor: AppColors.error,
            foregroundColor: Colors.white,
            child: const Icon(Icons.emergency),
          ),
        ),
      ],
    );
  }

  Widget _buildSpeedometer(RealTimeTracking tracking) {
    final speed = tracking.currentLocation?.speedKmh ?? 0.0;
    
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            speed.toStringAsFixed(0),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Text(
            'km/h',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlPanel(RealTimeTracking tracking) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Order Summary
          OrderSummaryWidget(orderId: widget.orderId),
          
          const SizedBox(height: 16),
          
          // Delivery Actions
          DeliveryActionsWidget(
            tracking: tracking,
            onStartDelivery: () => _startDelivery(),
            onPauseDelivery: () => _pauseDelivery(),
            onCompleteDelivery: () => _completeDelivery(),
            onReportIssue: () => _reportIssue(),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.white),
          SizedBox(height: 16),
          Text(
            'Initializing navigation...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            const Text(
              'Navigation Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _retryInitialization(),
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Map methods
  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _mapController!.setMapStyle(_darkMapStyle);
  }

  // Tracking methods
  Future<void> _initializeTracking() async {
    try {
      await RealTimeTrackingService.startTracking(
        orderId: widget.orderId,
        riderId: widget.riderId,
      );
      setState(() => _isNavigating = true);
    } catch (e) {
      debugPrint('❌ Error initializing tracking: $e');
    }
  }

  void _toggleTracking() async {
    if (_isNavigating) {
      await RealTimeTrackingService.pauseTracking(widget.orderId);
      setState(() => _isNavigating = false);
    } else {
      await RealTimeTrackingService.resumeTracking(widget.orderId, widget.riderId);
      setState(() => _isNavigating = true);
    }
  }

  // Delivery actions
  void _startDelivery() async {
    try {
      await _initializeTracking();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Delivery started'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to start delivery: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _pauseDelivery() async {
    await RealTimeTrackingService.pauseTracking(widget.orderId);
    setState(() => _isNavigating = false);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Delivery paused'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _completeDelivery() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Delivery'),
        content: const Text('Are you sure you want to mark this delivery as completed?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await RealTimeTrackingService.markDelivered(widget.orderId);
              if (mounted) {
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
            ),
            child: const Text('Complete'),
          ),
        ],
      ),
    );
  }

  void _reportIssue() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Report Issue',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // Issue options
            ListTile(
              leading: const Icon(Icons.location_off),
              title: const Text('GPS Issues'),
              onTap: () => _handleIssue('gps'),
            ),
            ListTile(
              leading: const Icon(Icons.traffic),
              title: const Text('Traffic Jam'),
              onTap: () => _handleIssue('traffic'),
            ),
            ListTile(
              leading: const Icon(Icons.home),
              title: const Text('Address Not Found'),
              onTap: () => _handleIssue('address'),
            ),
            ListTile(
              leading: const Icon(Icons.phone_missed),
              title: const Text('Customer Not Responding'),
              onTap: () => _handleIssue('customer'),
            ),
            ListTile(
              leading: const Icon(Icons.error),
              title: const Text('Other Issue'),
              onTap: () => _handleIssue('other'),
            ),
          ],
        ),
      ),
    );
  }

  void _handleIssue(String issueType) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Issue reported: $issueType'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showEmergencyOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Emergency'),
        content: const Text('Do you need emergency assistance?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement emergency call
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Call Emergency'),
          ),
        ],
      ),
    );
  }

  void _showTrackingSettings() {
    // Implement tracking settings
  }

  void _retryInitialization() {
    _initializeTracking();
  }

  // Dark map style for night navigation
  static const String _darkMapStyle = '''
  [
    {
      "elementType": "geometry",
      "stylers": [
        {
          "color": "#212121"
        }
      ]
    },
    {
      "elementType": "labels.icon",
      "stylers": [
        {
          "visibility": "off"
        }
      ]
    },
    {
      "elementType": "labels.text.fill",
      "stylers": [
        {
          "color": "#757575"
        }
      ]
    },
    {
      "elementType": "labels.text.stroke",
      "stylers": [
        {
          "color": "#212121"
        }
      ]
    }
  ]
  ''';
}


