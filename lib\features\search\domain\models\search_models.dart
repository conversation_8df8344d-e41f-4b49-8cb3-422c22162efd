import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'search_models.g.dart';

@HiveType(typeId: 50)
enum SearchType {
  @HiveField(0)
  products,
  @HiveField(1)
  services,
  @HiveField(2)
  sellers,
  @HiveField(3)
  categories,
  @HiveField(4)
  all,
}

@HiveType(typeId: 51)
enum SortOption {
  @HiveField(0)
  relevance,
  @HiveField(1)
  priceLowToHigh,
  @HiveField(2)
  priceHighToLow,
  @HiveField(3)
  rating,
  @HiveField(4)
  newest,
  @HiveField(5)
  popularity,
  @HiveField(6)
  distance,
}

@HiveType(typeId: 52)
@JsonSerializable()
class SearchQuery extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String query;
  
  @HiveField(2)
  final SearchType type;
  
  @HiveField(3)
  final List<String> categories;
  
  @HiveField(4)
  final double? minPrice;
  
  @HiveField(5)
  final double? maxPrice;
  
  @HiveField(6)
  final double? minRating;
  
  @HiveField(7)
  final double? maxDistance; // in km
  
  @HiveField(8)
  final String? location;
  
  @HiveField(9)
  final SortOption sortBy;
  
  @HiveField(10)
  final List<String> tags;
  
  @HiveField(11)
  final bool isVoiceSearch;
  
  @HiveField(12)
  final DateTime createdAt;
  
  @HiveField(13)
  final Map<String, dynamic> filters;

  const SearchQuery({
    required this.id,
    required this.query,
    this.type = SearchType.all,
    this.categories = const [],
    this.minPrice,
    this.maxPrice,
    this.minRating,
    this.maxDistance,
    this.location,
    this.sortBy = SortOption.relevance,
    this.tags = const [],
    this.isVoiceSearch = false,
    required this.createdAt,
    this.filters = const {},
  });

  factory SearchQuery.fromJson(Map<String, dynamic> json) => _$SearchQueryFromJson(json);
  Map<String, dynamic> toJson() => _$SearchQueryToJson(this);

  SearchQuery copyWith({
    String? id,
    String? query,
    SearchType? type,
    List<String>? categories,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    double? maxDistance,
    String? location,
    SortOption? sortBy,
    List<String>? tags,
    bool? isVoiceSearch,
    DateTime? createdAt,
    Map<String, dynamic>? filters,
  }) {
    return SearchQuery(
      id: id ?? this.id,
      query: query ?? this.query,
      type: type ?? this.type,
      categories: categories ?? this.categories,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minRating: minRating ?? this.minRating,
      maxDistance: maxDistance ?? this.maxDistance,
      location: location ?? this.location,
      sortBy: sortBy ?? this.sortBy,
      tags: tags ?? this.tags,
      isVoiceSearch: isVoiceSearch ?? this.isVoiceSearch,
      createdAt: createdAt ?? this.createdAt,
      filters: filters ?? this.filters,
    );
  }

  @override
  List<Object?> get props => [
    id, query, type, categories, minPrice, maxPrice, minRating,
    maxDistance, location, sortBy, tags, isVoiceSearch, createdAt, filters,
  ];
}

@HiveType(typeId: 53)
@JsonSerializable()
class SearchResult extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String title;
  
  @HiveField(2)
  final String description;
  
  @HiveField(3)
  final SearchType type;
  
  @HiveField(4)
  final String? imageUrl;
  
  @HiveField(5)
  final double? price;
  
  @HiveField(6)
  final double? rating;
  
  @HiveField(7)
  final int? reviewCount;
  
  @HiveField(8)
  final String? category;
  
  @HiveField(9)
  final String? sellerId;
  
  @HiveField(10)
  final String? sellerName;
  
  @HiveField(11)
  final double? distance; // in km
  
  @HiveField(12)
  final List<String> tags;
  
  @HiveField(13)
  final double relevanceScore;
  
  @HiveField(14)
  final Map<String, dynamic> metadata;
  
  @HiveField(15)
  final DateTime? lastUpdated;

  const SearchResult({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.imageUrl,
    this.price,
    this.rating,
    this.reviewCount,
    this.category,
    this.sellerId,
    this.sellerName,
    this.distance,
    this.tags = const [],
    required this.relevanceScore,
    this.metadata = const {},
    this.lastUpdated,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) => _$SearchResultFromJson(json);
  Map<String, dynamic> toJson() => _$SearchResultToJson(this);

  @override
  List<Object?> get props => [
    id, title, description, type, imageUrl, price, rating, reviewCount,
    category, sellerId, sellerName, distance, tags, relevanceScore, metadata, lastUpdated,
  ];
}

@HiveType(typeId: 54)
@JsonSerializable()
class SearchSuggestion extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String text;
  
  @HiveField(2)
  final SearchType type;
  
  @HiveField(3)
  final int frequency;
  
  @HiveField(4)
  final double relevanceScore;
  
  @HiveField(5)
  final String? category;
  
  @HiveField(6)
  final List<String> keywords;
  
  @HiveField(7)
  final DateTime lastUsed;

  const SearchSuggestion({
    required this.id,
    required this.text,
    required this.type,
    this.frequency = 1,
    required this.relevanceScore,
    this.category,
    this.keywords = const [],
    required this.lastUsed,
  });

  factory SearchSuggestion.fromJson(Map<String, dynamic> json) => _$SearchSuggestionFromJson(json);
  Map<String, dynamic> toJson() => _$SearchSuggestionToJson(this);

  @override
  List<Object?> get props => [
    id, text, type, frequency, relevanceScore, category, keywords, lastUsed,
  ];
}

@HiveType(typeId: 55)
@JsonSerializable()
class SearchHistory extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final SearchQuery query;
  
  @HiveField(3)
  final int resultCount;
  
  @HiveField(4)
  final DateTime searchedAt;
  
  @HiveField(5)
  final List<String> clickedResults;
  
  @HiveField(6)
  final Duration searchDuration;

  const SearchHistory({
    required this.id,
    required this.userId,
    required this.query,
    required this.resultCount,
    required this.searchedAt,
    this.clickedResults = const [],
    required this.searchDuration,
  });

  factory SearchHistory.fromJson(Map<String, dynamic> json) => _$SearchHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$SearchHistoryToJson(this);

  @override
  List<Object?> get props => [
    id, userId, query, resultCount, searchedAt, clickedResults, searchDuration,
  ];
}

@HiveType(typeId: 56)
@JsonSerializable()
class SearchFilter extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String displayName;
  
  @HiveField(3)
  final FilterType type;
  
  @HiveField(4)
  final List<FilterOption> options;
  
  @HiveField(5)
  final bool isMultiSelect;
  
  @HiveField(6)
  final int sortOrder;

  const SearchFilter({
    required this.id,
    required this.name,
    required this.displayName,
    required this.type,
    required this.options,
    this.isMultiSelect = false,
    this.sortOrder = 0,
  });

  factory SearchFilter.fromJson(Map<String, dynamic> json) => _$SearchFilterFromJson(json);
  Map<String, dynamic> toJson() => _$SearchFilterToJson(this);

  @override
  List<Object?> get props => [id, name, displayName, type, options, isMultiSelect, sortOrder];
}

@HiveType(typeId: 57)
enum FilterType {
  @HiveField(0)
  checkbox,
  @HiveField(1)
  radio,
  @HiveField(2)
  range,
  @HiveField(3)
  dropdown,
  @HiveField(4)
  slider,
}

@HiveType(typeId: 58)
@JsonSerializable()
class FilterOption extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String value;
  
  @HiveField(2)
  final String displayName;
  
  @HiveField(3)
  final int count;
  
  @HiveField(4)
  final bool isSelected;

  const FilterOption({
    required this.id,
    required this.value,
    required this.displayName,
    this.count = 0,
    this.isSelected = false,
  });

  factory FilterOption.fromJson(Map<String, dynamic> json) => _$FilterOptionFromJson(json);
  Map<String, dynamic> toJson() => _$FilterOptionToJson(this);

  FilterOption copyWith({
    String? id,
    String? value,
    String? displayName,
    int? count,
    bool? isSelected,
  }) {
    return FilterOption(
      id: id ?? this.id,
      value: value ?? this.value,
      displayName: displayName ?? this.displayName,
      count: count ?? this.count,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  List<Object?> get props => [id, value, displayName, count, isSelected];
}

// Search analytics
@HiveType(typeId: 59)
@JsonSerializable()
class SearchAnalytics extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final Map<String, int> topQueries;
  
  @HiveField(3)
  final Map<String, int> topCategories;
  
  @HiveField(4)
  final Map<String, int> topFilters;
  
  @HiveField(5)
  final double averageSearchTime;
  
  @HiveField(6)
  final int totalSearches;
  
  @HiveField(7)
  final double clickThroughRate;
  
  @HiveField(8)
  final DateTime lastUpdated;

  const SearchAnalytics({
    required this.id,
    required this.userId,
    required this.topQueries,
    required this.topCategories,
    required this.topFilters,
    required this.averageSearchTime,
    required this.totalSearches,
    required this.clickThroughRate,
    required this.lastUpdated,
  });

  factory SearchAnalytics.fromJson(Map<String, dynamic> json) => _$SearchAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$SearchAnalyticsToJson(this);

  @override
  List<Object?> get props => [
    id, userId, topQueries, topCategories, topFilters,
    averageSearchTime, totalSearches, clickThroughRate, lastUpdated,
  ];
}
