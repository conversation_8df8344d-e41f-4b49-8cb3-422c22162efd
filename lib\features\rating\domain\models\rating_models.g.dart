// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rating_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RatingAdapter extends TypeAdapter<Rating> {
  @override
  final int typeId = 20;

  @override
  Rating read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Rating(
      id: fields[0] as String,
      userId: fields[1] as String,
      targetId: fields[2] as String,
      targetType: fields[3] as String,
      orderId: fields[4] as String,
      rating: fields[5] as double,
      review: fields[6] as String?,
      tags: (fields[7] as List).cast<String>(),
      images: (fields[8] as List).cast<String>(),
      createdAt: fields[9] as DateTime,
      updatedAt: fields[10] as DateTime,
      isVerified: fields[11] as bool,
      helpfulCount: fields[12] as int,
      userName: fields[13] as String,
      userAvatar: fields[14] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Rating obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.targetId)
      ..writeByte(3)
      ..write(obj.targetType)
      ..writeByte(4)
      ..write(obj.orderId)
      ..writeByte(5)
      ..write(obj.rating)
      ..writeByte(6)
      ..write(obj.review)
      ..writeByte(7)
      ..write(obj.tags)
      ..writeByte(8)
      ..write(obj.images)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt)
      ..writeByte(11)
      ..write(obj.isVerified)
      ..writeByte(12)
      ..write(obj.helpfulCount)
      ..writeByte(13)
      ..write(obj.userName)
      ..writeByte(14)
      ..write(obj.userAvatar);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RatingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RatingSummaryAdapter extends TypeAdapter<RatingSummary> {
  @override
  final int typeId = 21;

  @override
  RatingSummary read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RatingSummary(
      targetId: fields[0] as String,
      targetType: fields[1] as String,
      averageRating: fields[2] as double,
      totalRatings: fields[3] as int,
      ratingDistribution: (fields[4] as Map).cast<int, int>(),
      topTags: (fields[5] as List).cast<String>(),
      lastUpdated: fields[6] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, RatingSummary obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.targetId)
      ..writeByte(1)
      ..write(obj.targetType)
      ..writeByte(2)
      ..write(obj.averageRating)
      ..writeByte(3)
      ..write(obj.totalRatings)
      ..writeByte(4)
      ..write(obj.ratingDistribution)
      ..writeByte(5)
      ..write(obj.topTags)
      ..writeByte(6)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RatingSummaryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RatingFilterAdapter extends TypeAdapter<RatingFilter> {
  @override
  final int typeId = 22;

  @override
  RatingFilter read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RatingFilter.all;
      case 1:
        return RatingFilter.fiveStar;
      case 2:
        return RatingFilter.fourStar;
      case 3:
        return RatingFilter.threeStar;
      case 4:
        return RatingFilter.twoStar;
      case 5:
        return RatingFilter.oneStar;
      case 6:
        return RatingFilter.withReviews;
      case 7:
        return RatingFilter.withImages;
      case 8:
        return RatingFilter.verified;
      default:
        return RatingFilter.all;
    }
  }

  @override
  void write(BinaryWriter writer, RatingFilter obj) {
    switch (obj) {
      case RatingFilter.all:
        writer.writeByte(0);
        break;
      case RatingFilter.fiveStar:
        writer.writeByte(1);
        break;
      case RatingFilter.fourStar:
        writer.writeByte(2);
        break;
      case RatingFilter.threeStar:
        writer.writeByte(3);
        break;
      case RatingFilter.twoStar:
        writer.writeByte(4);
        break;
      case RatingFilter.oneStar:
        writer.writeByte(5);
        break;
      case RatingFilter.withReviews:
        writer.writeByte(6);
        break;
      case RatingFilter.withImages:
        writer.writeByte(7);
        break;
      case RatingFilter.verified:
        writer.writeByte(8);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RatingFilterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Rating _$RatingFromJson(Map<String, dynamic> json) => Rating(
      id: json['id'] as String,
      userId: json['userId'] as String,
      targetId: json['targetId'] as String,
      targetType: json['targetType'] as String,
      orderId: json['orderId'] as String,
      rating: (json['rating'] as num).toDouble(),
      review: json['review'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isVerified: json['isVerified'] as bool? ?? false,
      helpfulCount: (json['helpfulCount'] as num?)?.toInt() ?? 0,
      userName: json['userName'] as String,
      userAvatar: json['userAvatar'] as String?,
    );

Map<String, dynamic> _$RatingToJson(Rating instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'targetId': instance.targetId,
      'targetType': instance.targetType,
      'orderId': instance.orderId,
      'rating': instance.rating,
      'review': instance.review,
      'tags': instance.tags,
      'images': instance.images,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isVerified': instance.isVerified,
      'helpfulCount': instance.helpfulCount,
      'userName': instance.userName,
      'userAvatar': instance.userAvatar,
    };

RatingSummary _$RatingSummaryFromJson(Map<String, dynamic> json) =>
    RatingSummary(
      targetId: json['targetId'] as String,
      targetType: json['targetType'] as String,
      averageRating: (json['averageRating'] as num).toDouble(),
      totalRatings: (json['totalRatings'] as num).toInt(),
      ratingDistribution:
          (json['ratingDistribution'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(int.parse(k), (e as num).toInt()),
      ),
      topTags:
          (json['topTags'] as List<dynamic>).map((e) => e as String).toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$RatingSummaryToJson(RatingSummary instance) =>
    <String, dynamic>{
      'targetId': instance.targetId,
      'targetType': instance.targetType,
      'averageRating': instance.averageRating,
      'totalRatings': instance.totalRatings,
      'ratingDistribution':
          instance.ratingDistribution.map((k, e) => MapEntry(k.toString(), e)),
      'topTags': instance.topTags,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
