import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../domain/models/enhanced_chat_models.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/services/auth_service.dart';

class MessageBubbleWidget extends StatelessWidget {
  final EnhancedChatMessage message;
  final EnhancedChatMessage? previousMessage;
  final EnhancedChatMessage? nextMessage;
  final Function(EnhancedChatMessage) onReply;
  final Function(String) onReaction;

  const MessageBubbleWidget({
    super.key,
    required this.message,
    this.previousMessage,
    this.nextMessage,
    required this.onReply,
    required this.onReaction,
  });

  @override
  Widget build(BuildContext context) {
    final isMe = message.senderId == AuthService.currentUserId;
    final showAvatar = _shouldShowAvatar();
    final showSenderName = _shouldShowSenderName();
    final showTimestamp = _shouldShowTimestamp();

    return Container(
      margin: EdgeInsets.only(
        bottom: showTimestamp ? 16 : 4,
        top: showSenderName ? 16 : 0,
      ),
      child: Column(
        crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Sender name (for group chats)
          if (showSenderName && !isMe)
            Padding(
              padding: const EdgeInsets.only(left: 48, bottom: 4),
              child: Text(
                message.senderName,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

          // Message bubble
          Row(
            mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Avatar (for others)
              if (!isMe && showAvatar) _buildAvatar(),
              if (!isMe && !showAvatar) const SizedBox(width: 40),

              // Message content
              Flexible(
                child: GestureDetector(
                  onLongPress: () => _showMessageOptions(context),
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width * 0.75,
                    ),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isMe ? AppColors.primaryBlue : Colors.grey[100],
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(16),
                        topRight: const Radius.circular(16),
                        bottomLeft: Radius.circular(isMe ? 16 : 4),
                        bottomRight: Radius.circular(isMe ? 4 : 16),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Reply preview
                        if (message.replyToMessageId != null) _buildReplyPreview(),

                        // Message content
                        _buildMessageContent(isMe),

                        // Reactions
                        if (message.reactions.isNotEmpty) _buildReactions(),

                        // Message status and time
                        _buildMessageFooter(isMe),
                      ],
                    ),
                  ),
                ),
              ),

              // Avatar (for me)
              if (isMe && showAvatar) _buildAvatar(),
              if (isMe && !showAvatar) const SizedBox(width: 40),
            ],
          ),

          // Timestamp
          if (showTimestamp)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                _formatTimestamp(message.timestamp),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                  fontSize: 11,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: CircleAvatar(
        radius: 16,
        backgroundColor: AppColors.primaryBlue,
        backgroundImage: message.senderAvatarUrl != null
            ? CachedNetworkImageProvider(message.senderAvatarUrl!)
            : null,
        child: message.senderAvatarUrl == null
            ? Text(
                message.senderName[0].toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              )
            : null,
      ),
    );
  }

  Widget _buildReplyPreview() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(
            color: AppColors.secondaryOrange,
            width: 3,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Replying to message',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
              fontSize: 10,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            'Original message content...', // You'd fetch this from the replied message
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageContent(bool isMe) {
    switch (message.type) {
      case EnhancedMessageType.text:
        return _buildTextContent(isMe);
      case EnhancedMessageType.image:
        return _buildImageContent();
      case EnhancedMessageType.video:
        return _buildVideoContent();
      case EnhancedMessageType.audio:
        return _buildAudioContent();
      case EnhancedMessageType.file:
        return _buildDocumentContent();
      case EnhancedMessageType.location:
        return _buildLocationContent();
      case EnhancedMessageType.system:
        return _buildSystemContent();
      default:
        return _buildTextContent(isMe);
    }
  }

  Widget _buildTextContent(bool isMe) {
    return Text(
      message.content,
      style: AppTextStyles.bodyMedium.copyWith(
        color: isMe ? Colors.white : AppColors.textPrimary,
      ),
    );
  }

  Widget _buildImageContent() {
    if (message.attachments.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...message.attachments.map((attachment) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CachedNetworkImage(
              imageUrl: attachment.url,
              width: 200,
              height: 150,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: 200,
                height: 150,
                color: Colors.grey[300],
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => Container(
                width: 200,
                height: 150,
                color: Colors.grey[300],
                child: const Icon(Icons.error),
              ),
            ),
          ),
        )),
        if (message.content.isNotEmpty)
          Text(
            message.content,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white,
            ),
          ),
      ],
    );
  }

  Widget _buildVideoContent() {
    if (message.attachments.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...message.attachments.map((attachment) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 200,
                  height: 150,
                  color: Colors.black,
                  child: attachment.thumbnailUrl != null
                      ? CachedNetworkImage(
                          imageUrl: attachment.thumbnailUrl!,
                          fit: BoxFit.cover,
                        )
                      : const Icon(
                          Icons.video_library,
                          color: Colors.white,
                          size: 48,
                        ),
                ),
              ),
              Positioned.fill(
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
            ],
          ),
        )),
        if (message.content.isNotEmpty)
          Text(
            message.content,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white,
            ),
          ),
      ],
    );
  }

  Widget _buildAudioContent() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.play_arrow,
            color: AppColors.primaryBlue,
          ),
          const SizedBox(width: 8),
          Text(
            'Audio Message',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(width: 8),
          if (message.attachments.isNotEmpty && message.attachments.first.duration != null)
            Text(
              _formatDuration(message.attachments.first.duration!),
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDocumentContent() {
    if (message.attachments.isEmpty) return const SizedBox.shrink();

    return Column(
      children: message.attachments.map((attachment) => Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getDocumentIcon(attachment.fileName),
              color: AppColors.primaryBlue,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    attachment.fileName,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    _formatFileSize(attachment.fileSize),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => _openDocument(attachment.url),
              icon: Icon(
                Icons.download,
                color: AppColors.primaryBlue,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildLocationContent() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.location_on,
            color: AppColors.error,
          ),
          const SizedBox(width: 8),
          Text(
            'Location shared',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemContent() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        message.content,
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.info,
          fontStyle: FontStyle.italic,
        ),
      ),
    );
  }

  Widget _buildReactions() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Wrap(
        spacing: 4,
        children: message.reactions.map((reaction) => Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(reaction.emoji, style: const TextStyle(fontSize: 12)),
              const SizedBox(width: 2),
              Text(
                '${reaction.userIds.length}',
                style: AppTextStyles.bodySmall.copyWith(fontSize: 10),
              ),
            ],
          ),
        )).toList(),
      ),
    );
  }

  Widget _buildMessageFooter(bool isMe) {
    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _formatTime(message.timestamp),
            style: AppTextStyles.bodySmall.copyWith(
              color: isMe ? Colors.white.withValues(alpha: 0.7) : AppColors.textSecondary,
              fontSize: 10,
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 4),
            Icon(
              _getStatusIcon(message.status),
              size: 12,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ],
        ],
      ),
    );
  }

  void _showMessageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.reply),
              title: const Text('Reply'),
              onTap: () {
                Navigator.pop(context);
                onReply(message);
              },
            ),
            ListTile(
              leading: const Icon(Icons.emoji_emotions),
              title: const Text('Add Reaction'),
              onTap: () {
                Navigator.pop(context);
                _showReactionPicker(context);
              },
            ),
            if (message.senderId == AuthService.currentUserId)
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Delete'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement delete message
                },
              ),
          ],
        ),
      ),
    );
  }

  void _showReactionPicker(BuildContext context) {
    final reactions = ['👍', '❤️', '😂', '😮', '😢', '😡'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Reaction'),
        content: Wrap(
          children: reactions.map((emoji) => GestureDetector(
            onTap: () {
              Navigator.pop(context);
              onReaction(emoji);
            },
            child: Container(
              margin: const EdgeInsets.all(4),
              padding: const EdgeInsets.all(8),
              child: Text(emoji, style: const TextStyle(fontSize: 24)),
            ),
          )).toList(),
        ),
      ),
    );
  }

  void _openDocument(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  // Helper methods
  bool _shouldShowAvatar() {
    if (nextMessage == null) return true;
    if (nextMessage!.senderId != message.senderId) return true;
    if (_getTimeDifference(message.timestamp, nextMessage!.timestamp) > 5) return true;
    return false;
  }

  bool _shouldShowSenderName() {
    if (previousMessage == null) return true;
    if (previousMessage!.senderId != message.senderId) return true;
    if (_getTimeDifference(previousMessage!.timestamp, message.timestamp) > 5) return true;
    return false;
  }

  bool _shouldShowTimestamp() {
    if (nextMessage == null) return true;
    if (_getTimeDifference(message.timestamp, nextMessage!.timestamp) > 60) return true;
    return false;
  }

  int _getTimeDifference(DateTime time1, DateTime time2) {
    return time1.difference(time2).inMinutes.abs();
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour;
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    return '${hour == 0 ? 12 : hour}:${dateTime.minute.toString().padLeft(2, '0')} $period';
  }

  String _formatTimestamp(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return 'Today ${_formatTime(dateTime)}';
    } else if (difference.inDays == 1) {
      return 'Yesterday ${_formatTime(dateTime)}';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${_formatTime(dateTime)}';
    }
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  IconData _getDocumentIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      default:
        return Icons.insert_drive_file;
    }
  }

  IconData _getStatusIcon(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return Icons.access_time;
      case MessageStatus.sent:
        return Icons.check;
      case MessageStatus.delivered:
        return Icons.done_all;
      case MessageStatus.read:
        return Icons.done_all;
      case MessageStatus.failed:
        return Icons.error;
    }
  }
}

