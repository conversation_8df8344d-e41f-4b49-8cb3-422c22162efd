import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import 'package:mime/mime.dart';

/// File Upload Service for Chat System
/// Handles photo, PDF, and document uploads to Firebase Storage
class FileUploadService {
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final ImagePicker _imagePicker = ImagePicker();

  /// Upload file to Firebase Storage and return download URL
  static Future<String> uploadFile({
    required File file,
    required String chatId,
    required String userId,
    String? customFileName,
  }) async {
    try {
      final fileName = customFileName ?? _generateFileName(file.path);
      final ref = _storage.ref().child('chat_files/$chatId/$userId/$fileName');

      // Upload file
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;

      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  /// Upload file from bytes (for web)
  static Future<String> uploadFileFromBytes({
    required Uint8List bytes,
    required String fileName,
    required String chatId,
    required String userId,
  }) async {
    try {
      final ref = _storage.ref().child('chat_files/$chatId/$userId/$fileName');

      // Upload bytes
      final uploadTask = ref.putData(bytes);
      final snapshot = await uploadTask;

      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  /// Pick and upload image from camera
  static Future<ChatFileUploadResult?> pickAndUploadImageFromCamera({
    required String chatId,
    required String userId,
  }) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image == null) return null;

      final file = File(image.path);
      final downloadUrl = await uploadFile(
        file: file,
        chatId: chatId,
        userId: userId,
      );

      return ChatFileUploadResult(
        downloadUrl: downloadUrl,
        fileName: path.basename(image.path),
        fileSize: await file.length(),
        fileType: 'image',
        mimeType: lookupMimeType(image.path) ?? 'image/jpeg',
      );
    } catch (e) {
      throw Exception('Failed to pick and upload image from camera: $e');
    }
  }

  /// Pick and upload image from gallery
  static Future<ChatFileUploadResult?> pickAndUploadImageFromGallery({
    required String chatId,
    required String userId,
  }) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image == null) return null;

      final file = File(image.path);
      final downloadUrl = await uploadFile(
        file: file,
        chatId: chatId,
        userId: userId,
      );

      return ChatFileUploadResult(
        downloadUrl: downloadUrl,
        fileName: path.basename(image.path),
        fileSize: await file.length(),
        fileType: 'image',
        mimeType: lookupMimeType(image.path) ?? 'image/jpeg',
      );
    } catch (e) {
      throw Exception('Failed to pick and upload image from gallery: $e');
    }
  }

  /// Pick and upload PDF file
  static Future<ChatFileUploadResult?> pickAndUploadPDF({
    required String chatId,
    required String userId,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) return null;

      final file = result.files.first;

      if (kIsWeb) {
        // Handle web upload
        if (file.bytes == null) throw Exception('File bytes not available');

        final downloadUrl = await uploadFileFromBytes(
          bytes: file.bytes!,
          fileName: file.name,
          chatId: chatId,
          userId: userId,
        );

        return ChatFileUploadResult(
          downloadUrl: downloadUrl,
          fileName: file.name,
          fileSize: file.size,
          fileType: 'pdf',
          mimeType: 'application/pdf',
        );
      } else {
        // Handle mobile upload
        if (file.path == null) throw Exception('File path not available');

        final fileObj = File(file.path!);
        final downloadUrl = await uploadFile(
          file: fileObj,
          chatId: chatId,
          userId: userId,
        );

        return ChatFileUploadResult(
          downloadUrl: downloadUrl,
          fileName: file.name,
          fileSize: file.size,
          fileType: 'pdf',
          mimeType: 'application/pdf',
        );
      }
    } catch (e) {
      throw Exception('Failed to pick and upload PDF: $e');
    }
  }

  /// Pick and upload document (PDF, DOC, DOCX, TXT)
  static Future<ChatFileUploadResult?> pickAndUploadDocument({
    required String chatId,
    required String userId,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) return null;

      final file = result.files.first;

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw Exception('File size too large. Maximum size is 10MB.');
      }

      if (kIsWeb) {
        // Handle web upload
        if (file.bytes == null) throw Exception('File bytes not available');

        final downloadUrl = await uploadFileFromBytes(
          bytes: file.bytes!,
          fileName: file.name,
          chatId: chatId,
          userId: userId,
        );

        return ChatFileUploadResult(
          downloadUrl: downloadUrl,
          fileName: file.name,
          fileSize: file.size,
          fileType: _getFileType(file.extension ?? ''),
          mimeType: lookupMimeType(file.name) ?? 'application/octet-stream',
        );
      } else {
        // Handle mobile upload
        if (file.path == null) throw Exception('File path not available');

        final fileObj = File(file.path!);
        final downloadUrl = await uploadFile(
          file: fileObj,
          chatId: chatId,
          userId: userId,
        );

        return ChatFileUploadResult(
          downloadUrl: downloadUrl,
          fileName: file.name,
          fileSize: file.size,
          fileType: _getFileType(file.extension ?? ''),
          mimeType: lookupMimeType(file.name) ?? 'application/octet-stream',
        );
      }
    } catch (e) {
      throw Exception('Failed to pick and upload document: $e');
    }
  }

  /// Delete file from Firebase Storage
  static Future<void> deleteFile(String downloadUrl) async {
    try {
      final ref = _storage.refFromURL(downloadUrl);
      await ref.delete();
    } catch (e) {
      throw Exception('Failed to delete file: $e');
    }
  }

  /// Generate unique file name
  static String _generateFileName(String originalPath) {
    final extension = path.extension(originalPath);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'file_$timestamp$extension';
  }

  /// Get file type from extension
  static String _getFileType(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return 'pdf';
      case '.doc':
      case '.docx':
        return 'document';
      case '.txt':
      case '.rtf':
        return 'text';
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
      case '.webp':
        return 'image';
      default:
        return 'file';
    }
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get file icon based on type
  static String getFileIcon(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return '📄';
      case 'document':
        return '📝';
      case 'text':
        return '📃';
      case 'image':
        return '🖼️';
      default:
        return '📎';
    }
  }
}

/// Result of file upload operation
class ChatFileUploadResult {
  final String downloadUrl;
  final String fileName;
  final int fileSize;
  final String fileType;
  final String mimeType;

  ChatFileUploadResult({
    required this.downloadUrl,
    required this.fileName,
    required this.fileSize,
    required this.fileType,
    required this.mimeType,
  });

  Map<String, dynamic> toJson() {
    return {
      'downloadUrl': downloadUrl,
      'fileName': fileName,
      'fileSize': fileSize,
      'fileType': fileType,
      'mimeType': mimeType,
    };
  }

  factory ChatFileUploadResult.fromJson(Map<String, dynamic> json) {
    return ChatFileUploadResult(
      downloadUrl: json['downloadUrl'],
      fileName: json['fileName'],
      fileSize: json['fileSize'],
      fileType: json['fileType'],
      mimeType: json['mimeType'],
    );
  }
}
