import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class SearchFilterWidget extends StatelessWidget {
  final String? selectedCategory;
  final Function(String?) onCategoryChanged;
  final VoidCallback onClearFilters;

  const SearchFilterWidget({
    super.key,
    this.selectedCategory,
    required this.onCategoryChanged,
    required this.onClearFilters,
  });

  static const List<String> categories = [
    'Electronics',
    'Clothing',
    'Home & Garden',
    'Sports',
    'Books',
    'Beauty',
    'Automotive',
    'Toys',
    'Food',
    'Health',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.filter_list,
                color: AppColors.primaryBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Filters',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryBlue,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: onClearFilters,
                child: Text(
                  'Clear All',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Category Filter
          Text(
            'Category',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Category Chips
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // All Categories Chip
              FilterChip(
                label: const Text('All'),
                selected: selectedCategory == null,
                onSelected: (selected) {
                  if (selected) {
                    onCategoryChanged(null);
                  }
                },
                selectedColor: AppColors.primaryBlue.withValues(alpha: 0.2),
                checkmarkColor: AppColors.primaryBlue,
                labelStyle: AppTextStyles.bodySmall.copyWith(
                  color: selectedCategory == null 
                      ? AppColors.primaryBlue 
                      : AppColors.textSecondary,
                  fontWeight: selectedCategory == null 
                      ? FontWeight.w600 
                      : FontWeight.normal,
                ),
              ),
              
              // Category Chips
              ...categories.map((category) => FilterChip(
                label: Text(category),
                selected: selectedCategory == category,
                onSelected: (selected) {
                  onCategoryChanged(selected ? category : null);
                },
                selectedColor: AppColors.primaryBlue.withValues(alpha: 0.2),
                checkmarkColor: AppColors.primaryBlue,
                labelStyle: AppTextStyles.bodySmall.copyWith(
                  color: selectedCategory == category 
                      ? AppColors.primaryBlue 
                      : AppColors.textSecondary,
                  fontWeight: selectedCategory == category 
                      ? FontWeight.w600 
                      : FontWeight.normal,
                ),
              )),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Quick Filter Buttons
          Row(
            children: [
              Expanded(
                child: _buildQuickFilterButton(
                  'In Stock',
                  Icons.check_circle,
                  AppColors.success,
                  () {
                    // Implement in stock filter
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickFilterButton(
                  'Low Stock',
                  Icons.warning,
                  AppColors.warning,
                  () {
                    // Implement low stock filter
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickFilterButton(
                  'Out of Stock',
                  Icons.error,
                  AppColors.error,
                  () {
                    // Implement out of stock filter
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilterButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 16,
              color: color,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
