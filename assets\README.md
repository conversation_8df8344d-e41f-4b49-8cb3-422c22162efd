# Assets Organization

This document describes the organization of assets in the Projek Flutter application.

## Directory Structure

```
assets/
├── animations/          # Lottie animations and other animated assets
├── fonts/              # Custom fonts (Poppins)
│   ├── Poppins-Medium.ttf
│   └── Poppins-Regular.ttf
├── icons/              # Icon assets
│   ├── categories/     # Category-specific icons
│   ├── navigation/     # Navigation and UI icons
│   ├── bikecarbooking/ # Bike/car booking related icons
│   ├── delivery_icon/  # Delivery service icons
│   ├── icons/          # General purpose icons
│   └── splash_screen/  # Splash screen assets
└── images/             # Image assets
    ├── banners/        # Promotional banners and hero images
    ├── categories/     # Category images and thumbnails
    ├── products/       # Product images and thumbnails
    └── food/           # Food-related images (legacy)
```

## Asset Guidelines

### Images
- Use WebP format when possible for better compression
- Provide multiple resolutions (1x, 2x, 3x) for different screen densities
- Keep file sizes optimized for mobile devices
- Use descriptive filenames

### Icons
- Prefer SVG format for scalable icons
- Use PNG for complex icons that don't scale well as SVG
- Maintain consistent sizing and style
- Group related icons in subdirectories

### Fonts
- Include only necessary font weights to reduce app size
- Ensure proper licensing for commercial use

## Usage in Code

Assets are referenced in the pubspec.yaml file and can be used in Flutter code:

```dart
// Images
Image.asset('assets/images/categories/food_category.png')

// Icons
Image.asset('assets/icons/navigation/home_icon.png')

// Fonts are configured in pubspec.yaml and used via TextStyle
```

## Adding New Assets

1. Place assets in the appropriate subdirectory
2. Update pubspec.yaml if adding new directories
3. Use descriptive, consistent naming conventions
4. Optimize file sizes before adding
5. Test on different screen densities
