import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../config/constants.dart';
import '../config/app_config.dart';
import '../database/hive_service.dart';
import 'analytics_service.dart';

class AuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn();
  static final LocalAuthentication _localAuth = LocalAuthentication();

  // Current user
  static User? get currentUser => _auth.currentUser;
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Phone Authentication
  static Future<void> signInWithPhone(String phoneNumber) async {
    // This is a simplified version for compilation
    // In real implementation, you would handle the full phone auth flow
    throw UnimplementedError('Phone authentication not implemented yet');
  }

  static Future<void> signInWithPhoneCallback(
    String phoneNumber, {
    required Function(String verificationId) onCodeSent,
    required Function(String error) onError,
    Function(UserCredential credential)? onAutoVerification,
  }) async {
    try {
      await _auth.verifyPhoneNumber(
        phoneNumber: '+91$phoneNumber',
        verificationCompleted: (PhoneAuthCredential credential) async {
          if (onAutoVerification != null) {
            final userCredential = await _auth.signInWithCredential(credential);
            onAutoVerification(userCredential);
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          onError(e.message ?? 'Verification failed');
        },
        codeSent: (String verificationId, int? resendToken) {
          onCodeSent(verificationId);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Handle timeout
        },
        timeout: const Duration(seconds: 60),
      );
    } catch (e) {
      onError(e.toString());
    }
  }

  static Future<UserCredential> verifyOTP(
    String verificationId,
    String otp,
  ) async {
    try {
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      final userCredential = await _auth.signInWithCredential(credential);

      // Create user document if new user
      if (userCredential.additionalUserInfo?.isNewUser == true) {
        await _createUserDocument(userCredential.user!);
      }

      // Save user data locally
      await _saveUserDataLocally(userCredential.user!);

      // Log analytics
      await AnalyticsService.logLogin('phone');
      await _saveBiometricPreference(true);

      return userCredential;
    } catch (e) {
      throw Exception('Invalid OTP: ${e.toString()}');
    }
  }

  // Email Authentication
  static Future<UserCredential> signInWithEmail(
    String email,
    String password,
  ) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      await _saveUserDataLocally(userCredential.user!);
      await AnalyticsService.logLogin('email');
      await _saveBiometricPreference(true);

      return userCredential;
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  static Future<UserCredential> signUpWithEmail(
    String email,
    String password,
    String name,
  ) async {
    try {
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name
      await userCredential.user!.updateDisplayName(name);

      // Create user document
      await _createUserDocument(userCredential.user!, displayName: name);

      await _saveUserDataLocally(userCredential.user!);
      await AnalyticsService.logSignUp('email');
      await _saveBiometricPreference(true);

      return userCredential;
    } catch (e) {
      throw Exception('Registration failed: ${e.toString()}');
    }
  }

  // Google Sign In
  static Future<UserCredential> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw Exception('Google sign in cancelled');
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);

      // Create user document if new user
      if (userCredential.additionalUserInfo?.isNewUser == true) {
        await _createUserDocument(userCredential.user!);
      }

      await _saveUserDataLocally(userCredential.user!);
      await AnalyticsService.logLogin('google');
      await _saveBiometricPreference(true);

      return userCredential;
    } catch (e) {
      throw Exception('Google sign in failed: ${e.toString()}');
    }
  }

  // Password Reset
  static Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception('Password reset failed: ${e.toString()}');
    }
  }

  // Sign Out
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
      await _googleSignIn.signOut();
      await HiveService.clearUserData();
      await AnalyticsService.logEvent('user_logout', null);
    } catch (e) {
      throw Exception('Sign out failed: ${e.toString()}');
    }
  }

  // User Data Management
  static Future<UserModel?> getUserData(String uid) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(uid)
          .get();
      if (doc.exists) {
        return UserModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user data: ${e.toString()}');
    }
  }

  static Future<void> updateUserData(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .update(user.toMap());
      await _saveUserDataLocally(_auth.currentUser!);
    } catch (e) {
      throw Exception('Failed to update user data: ${e.toString()}');
    }
  }

  static Future<void> _createUserDocument(
    User user, {
    String? displayName,
  }) async {
    final userModel = UserModel(
      id: user.uid,
      email: user.email ?? '',
      phoneNumber: user.phoneNumber,
      displayName: displayName ?? user.displayName ?? '',
      photoURL: user.photoURL,
      role: _determineUserRole(),
      status: UserStatus.active,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      metadata: {
        'app_type': AppConfig.currentAppType.name,
        'registration_method': 'phone',
      },
    );

    await _firestore
        .collection(AppConstants.usersCollection)
        .doc(user.uid)
        .set(userModel.toMap());
  }

  static UserRole _determineUserRole() {
    switch (AppConfig.currentAppType) {
      case AppType.user:
        return UserRole.user;
      case AppType.rider:
        return UserRole.rider;
      case AppType.seller:
        return UserRole.seller;
    }
  }

  static Future<void> _saveUserDataLocally(User user) async {
    final token = await user.getIdToken();
    if (token != null) {
      await HiveService.saveUserToken(token);
    }
    await HiveService.saveUserId(user.uid);
    await AnalyticsService.setUserId(user.uid);
  }

  // Account Management
  static Future<void> deleteAccount() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        // Delete user document
        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(user.uid)
            .delete();

        // Delete user account
        await user.delete();

        // Clear local data
        await HiveService.clearUserData();

        await AnalyticsService.logEvent('account_deleted', null);
      }
    } catch (e) {
      throw Exception('Failed to delete account: ${e.toString()}');
    }
  }

  static Future<void> updateProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        await user.updateDisplayName(displayName);
        await user.updatePhotoURL(photoURL);

        // Update Firestore document
        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(user.uid)
            .update({
              'displayName': displayName,
              'photoURL': photoURL,
              'updatedAt': FieldValue.serverTimestamp(),
            });
      }
    } catch (e) {
      throw Exception('Failed to update profile: ${e.toString()}');
    }
  }

  // Email Verification
  static Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } catch (e) {
      throw Exception('Failed to send verification email: ${e.toString()}');
    }
  }

  static Future<void> reloadUser() async {
    try {
      await _auth.currentUser?.reload();
    } catch (e) {
      throw Exception('Failed to reload user: ${e.toString()}');
    }
  }

  // Biometric Authentication
  static Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.isDeviceSupported();
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      debugPrint('Error getting available biometrics: $e');
      return [];
    }
  }

  static Future<bool> authenticateWithBiometrics() async {
    try {
      final bool isAvailable = await isBiometricAvailable();
      if (!isAvailable) return false;

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access your account',
        options: const AuthenticationOptions(
          biometricOnly: false,
          stickyAuth: true,
        ),
      );

      return didAuthenticate;
    } on PlatformException catch (e) {
      debugPrint('Biometric authentication error: $e');
      return false;
    }
  }

  // Facebook Sign-In
  static Future<UserCredential> signInWithFacebook() async {
    try {
      final LoginResult result = await FacebookAuth.instance.login();

      if (result.status == LoginStatus.success) {
        final OAuthCredential credential = FacebookAuthProvider.credential(
          result.accessToken!.tokenString,
        );

        final userCredential = await _auth.signInWithCredential(credential);

        // Create user document if new user
        if (userCredential.additionalUserInfo?.isNewUser == true) {
          await _createUserDocument(userCredential.user!);
        }

        await _saveUserDataLocally(userCredential.user!);
        await AnalyticsService.logLogin('facebook');
        await _saveBiometricPreference(true);

        return userCredential;
      }
      throw Exception('Facebook sign-in cancelled');
    } catch (e) {
      throw Exception('Facebook sign-in failed: ${e.toString()}');
    }
  }

  // Apple Sign-In
  static Future<UserCredential> signInWithApple() async {
    try {
      if (!Platform.isIOS) {
        throw Exception('Apple Sign-In is only available on iOS devices.');
      }

      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      final userCredential = await _auth.signInWithCredential(oauthCredential);

      // Create user document if new user
      if (userCredential.additionalUserInfo?.isNewUser == true) {
        await _createUserDocument(userCredential.user!);
      }

      await _saveUserDataLocally(userCredential.user!);
      await AnalyticsService.logLogin('apple');
      await _saveBiometricPreference(true);

      return userCredential;
    } catch (e) {
      throw Exception('Apple sign-in failed: ${e.toString()}');
    }
  }

  // Biometric Preferences
  static Future<void> _saveBiometricPreference(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('biometric_enabled', enabled);
  }

  static Future<bool> getBiometricPreference() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('biometric_enabled') ?? false;
  }

  static Future<void> setBiometricEnabled(bool enabled) async {
    await _saveBiometricPreference(enabled);
  }

  static Future<bool> shouldUseBiometricLogin() async {
    if (!isLoggedIn) return false;
    final biometricEnabled = await getBiometricPreference();
    final biometricAvailable = await isBiometricAvailable();
    return biometricEnabled && biometricAvailable;
  }

  static Future<bool> quickBiometricLogin() async {
    try {
      final shouldUse = await shouldUseBiometricLogin();
      if (!shouldUse) return false;

      return await authenticateWithBiometrics();
    } catch (e) {
      debugPrint('Quick biometric login error: $e');
      return false;
    }
  }

  // Helper methods
  static bool get isLoggedIn => _auth.currentUser != null;
  static bool get isEmailVerified => _auth.currentUser?.emailVerified ?? false;
  static String? get currentUserId => _auth.currentUser?.uid;
  static String? get currentUserEmail => _auth.currentUser?.email;
  static String? get currentUserPhone => _auth.currentUser?.phoneNumber;
}
