import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/services/analytics_service.dart';
import 'faq_page.dart';
import 'contact_support_page.dart';
import 'live_chat_page.dart';
import 'tutorials_page.dart';

class HelpCenterPage extends ConsumerWidget {
  const HelpCenterPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🆘 Help Center'),
        backgroundColor: AppColors.userPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.userPrimary,
              AppColors.backgroundLight,
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Card
                _buildWelcomeCard(),
                const SizedBox(height: 24),

                // Quick Actions
                _buildQuickActions(context),
                const SizedBox(height: 24),

                // Help Categories
                _buildHelpCategories(context),
                const SizedBox(height: 24),

                // Popular Articles
                _buildPopularArticles(context),
                const SizedBox(height: 24),

                // Contact Options
                _buildContactOptions(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.support_agent,
            size: 48,
            color: AppColors.userPrimary,
          ),
          const SizedBox(height: 12),
          const Text(
            'How can we help you?',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Find answers to your questions or get in touch with our support team',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                '💬 Live Chat',
                'Chat with support',
                AppColors.success,
                () => _navigateToLiveChat(context),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                '📞 Call Us',
                'Speak to an agent',
                AppColors.info,
                () => _callSupport(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, String subtitle, Color color, VoidCallback onTap) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [color.withOpacity(0.8), color],
            ),
          ),
          child: Column(
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHelpCategories(BuildContext context) {
    final categories = [
      HelpCategory('❓ FAQ', 'Frequently Asked Questions', () => _navigateToFAQ(context)),
      HelpCategory('🎮 Games & Rewards', 'Spin wheel, daily rewards', () => _navigateToGamesHelp(context)),
      HelpCategory('🛒 Shopping', 'Orders, payments, returns', () => _navigateToShoppingHelp(context)),
      HelpCategory('🚗 Delivery', 'Tracking, rider issues', () => _navigateToDeliveryHelp(context)),
      HelpCategory('💰 Wallet', 'ProjekCoin, transactions', () => _navigateToWalletHelp(context)),
      HelpCategory('👤 Account', 'Profile, security, privacy', () => _navigateToAccountHelp(context)),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Help Categories',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: Text(
                  category.icon,
                  style: const TextStyle(fontSize: 24),
                ),
                title: Text(
                  category.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                subtitle: Text(
                  category.description,
                  style: TextStyle(color: Colors.grey[600]),
                ),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: category.onTap,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPopularArticles(BuildContext context) {
    final articles = [
      'How to earn ProjekCoins?',
      'How to place an order?',
      'Payment methods accepted',
      'How to track my delivery?',
      'Refund and return policy',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Popular Articles',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          child: Column(
            children: articles.map((article) => ListTile(
              leading: const Icon(Icons.article, color: AppColors.userPrimary),
              title: Text(article),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _openArticle(context, article),
            )).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildContactOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Still Need Help?',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.email, color: AppColors.userPrimary),
                  title: const Text('Email Support'),
                  subtitle: const Text('<EMAIL>'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _navigateToContactSupport(context),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.phone, color: AppColors.success),
                  title: const Text('Phone Support'),
                  subtitle: const Text('+91 1800-123-4567'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _callSupport(context),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.schedule, color: AppColors.warning),
                  title: const Text('Support Hours'),
                  subtitle: const Text('Mon-Sun: 9:00 AM - 9:00 PM'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Navigation methods
  void _navigateToFAQ(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const FAQPage()));
    AnalyticsService.logEvent('help_faq_opened', null);
  }

  void _navigateToLiveChat(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const LiveChatPage()));
    AnalyticsService.logEvent('help_live_chat_opened', null);
  }

  void _navigateToContactSupport(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const ContactSupportPage()));
    AnalyticsService.logEvent('help_contact_support_opened', null);
  }

  void _navigateToGamesHelp(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const TutorialsPage(category: 'games')));
  }

  void _navigateToShoppingHelp(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const TutorialsPage(category: 'shopping')));
  }

  void _navigateToDeliveryHelp(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const TutorialsPage(category: 'delivery')));
  }

  void _navigateToWalletHelp(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const TutorialsPage(category: 'wallet')));
  }

  void _navigateToAccountHelp(BuildContext context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const TutorialsPage(category: 'account')));
  }

  void _callSupport(BuildContext context) {
    // Implement phone call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('📞 Calling support: +91 1800-123-4567')),
    );
    AnalyticsService.logEvent('help_phone_support_called', null);
  }

  void _openArticle(BuildContext context, String article) {
    // Navigate to article detail page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('📖 Opening: $article')),
    );
    AnalyticsService.logEvent('help_article_opened', {'article': article});
  }
}

class HelpCategory {
  final String icon;
  final String title;
  final String description;
  final VoidCallback onTap;

  HelpCategory(this.title, this.description, this.onTap) : icon = title.split(' ')[0];
}
