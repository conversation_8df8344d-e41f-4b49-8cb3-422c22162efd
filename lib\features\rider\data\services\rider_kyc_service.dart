import 'dart:async';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/rider_kyc.dart';
import '../../../../demo/rider_demo_data.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/utils/app_logger.dart';

class RiderKYCService {
  final StorageService _storageService;
  final StreamController<RiderKYC?> _kycController = StreamController<RiderKYC?>.broadcast();

  RiderKYCService(this._storageService);

  Stream<RiderKYC?> get kycStream => _kycController.stream;

  Future<RiderKYC?> getRiderKYC(String riderId) async {
    try {
      final kyc = RiderDemoData.getRiderKYC(riderId);
      if (kyc != null) {
        _kycController.add(kyc);
      }
      return kyc;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get rider KYC', e, stackTrace);
      return null;
    }
  }

  Future<bool> submitKYC(String riderId) async {
    try {
      await Future.delayed(Duration(seconds: 1));
      
      final currentKYC = await getRiderKYC(riderId);
      if (currentKYC != null) {
        final updatedKYC = currentKYC.copyWith(
          status: KYCStatus.underReview,
          submittedAt: DateTime.now(),
        );
        _kycController.add(updatedKYC);
      }
      
      AppLogger.info('KYC submitted for rider $riderId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to submit KYC', e, stackTrace);
      return false;
    }
  }

  Future<KYCDocument?> uploadDocument(
    String riderId,
    DocumentType type,
    String documentNumber,
    List<File> imageFiles,
  ) async {
    try {
      await Future.delayed(Duration(seconds: 2));
      
      // Simulate document upload and OCR processing
      final imageUrls = imageFiles.map((file) => 
        'https://example.com/documents/${type.name}_${DateTime.now().millisecondsSinceEpoch}.jpg'
      ).toList();

      // Simulate OCR data extraction
      Map<String, dynamic> extractedData = {};
      double confidenceScore = 0.95;

      switch (type) {
        case DocumentType.aadhaar:
          extractedData = {
            'name': 'Rajesh Kumar',
            'dob': '15/06/1985',
            'address': 'House No. 123, Sector 15, Guwahati, Assam - 781001',
            'aadhaarNumber': documentNumber,
          };
          break;
        case DocumentType.pan:
          extractedData = {
            'name': 'RAJESH KUMAR',
            'panNumber': documentNumber,
            'dob': '15/06/1985',
          };
          break;
        case DocumentType.drivingLicense:
          extractedData = {
            'name': 'Rajesh Kumar',
            'licenseNumber': documentNumber,
            'validUpto': '14/06/2043',
            'vehicleClass': 'MCWG',
          };
          break;
        case DocumentType.vehicleRC:
          extractedData = {
            'ownerName': 'Rajesh Kumar',
            'registrationNumber': documentNumber,
            'vehicleClass': 'Motorcycle',
            'make': 'Honda',
            'model': 'Activa 6G',
          };
          break;
        default:
          extractedData = {'documentNumber': documentNumber};
      }

      final document = KYCDocument(
        id: 'doc_${DateTime.now().millisecondsSinceEpoch}',
        type: type,
        documentNumber: documentNumber,
        imageUrls: imageUrls,
        status: DocumentStatus.underReview,
        uploadedAt: DateTime.now(),
        extractedData: extractedData,
        confidenceScore: confidenceScore,
      );

      AppLogger.info('Document uploaded: ${document.id}');
      return document;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to upload document', e, stackTrace);
      return null;
    }
  }

  Future<bool> resubmitDocument(
    String riderId,
    String documentId,
    List<File> imageFiles,
  ) async {
    try {
      await Future.delayed(Duration(seconds: 2));
      
      // In a real app, this would update the document
      AppLogger.info('Document resubmitted: $documentId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to resubmit document', e, stackTrace);
      return false;
    }
  }

  Future<List<String>> getRequiredDocuments() async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      
      return [
        'Aadhaar Card',
        'PAN Card',
        'Driving License',
        'Vehicle RC',
        'Vehicle Insurance',
        'Pollution Certificate',
        'Profile Photo',
        'Vehicle Photo',
        'Bank Passbook/Cheque',
      ];
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get required documents', e, stackTrace);
      return [];
    }
  }

  Future<Map<DocumentType, DocumentStatus>> getDocumentStatus(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final kyc = await getRiderKYC(riderId);
      if (kyc == null) return {};

      final statusMap = <DocumentType, DocumentStatus>{};
      for (final doc in kyc.documents) {
        statusMap[doc.type] = doc.status;
      }

      return statusMap;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get document status', e, stackTrace);
      return {};
    }
  }

  Future<double> getKYCProgress(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      
      final kyc = await getRiderKYC(riderId);
      return kyc?.completionPercentage ?? 0.0;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get KYC progress', e, stackTrace);
      return 0.0;
    }
  }

  Future<List<KYCComment>> getKYCComments(String riderId) async {
    try {
      await Future.delayed(Duration(milliseconds: 300));
      
      final kyc = await getRiderKYC(riderId);
      return kyc?.comments ?? [];
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get KYC comments', e, stackTrace);
      return [];
    }
  }

  Future<bool> validateDocument(DocumentType type, String documentNumber) async {
    try {
      await Future.delayed(Duration(seconds: 1));
      
      // Simulate document validation
      switch (type) {
        case DocumentType.aadhaar:
          // Validate Aadhaar format (12 digits)
          return RegExp(r'^\d{12}$').hasMatch(documentNumber.replaceAll('-', '').replaceAll(' ', ''));
        case DocumentType.pan:
          // Validate PAN format (5 letters, 4 digits, 1 letter)
          return RegExp(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$').hasMatch(documentNumber.toUpperCase());
        case DocumentType.drivingLicense:
          // Basic validation for Indian DL format
          return documentNumber.length >= 10;
        case DocumentType.vehicleRC:
          // Basic validation for vehicle registration
          return documentNumber.length >= 8;
        default:
          return documentNumber.isNotEmpty;
      }
    } catch (e, stackTrace) {
      AppLogger.error('Failed to validate document', e, stackTrace);
      return false;
    }
  }

  Future<Map<String, dynamic>> getDocumentGuidelines(DocumentType type) async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      
      switch (type) {
        case DocumentType.aadhaar:
          return {
            'title': 'Aadhaar Card',
            'description': 'Upload clear photos of both front and back of your Aadhaar card',
            'requirements': [
              'Both front and back sides required',
              'All text should be clearly visible',
              'No blur or shadows',
              'Original document only (no photocopies)',
            ],
            'format': 'XXXX-XXXX-XXXX format',
            'maxSize': '5MB per image',
          };
        case DocumentType.pan:
          return {
            'title': 'PAN Card',
            'description': 'Upload a clear photo of your PAN card',
            'requirements': [
              'All text should be clearly visible',
              'No blur or shadows',
              'Original document only',
              'Signature should be visible',
            ],
            'format': '********** format',
            'maxSize': '5MB',
          };
        case DocumentType.drivingLicense:
          return {
            'title': 'Driving License',
            'description': 'Upload clear photos of both front and back of your driving license',
            'requirements': [
              'Both front and back sides required',
              'License should be valid',
              'All text should be clearly visible',
              'Photo should be clear',
            ],
            'format': 'State code followed by numbers',
            'maxSize': '5MB per image',
          };
        case DocumentType.vehicleRC:
          return {
            'title': 'Vehicle Registration Certificate',
            'description': 'Upload clear photos of your vehicle RC',
            'requirements': [
              'Both front and back sides required',
              'RC should be valid',
              'Vehicle details should match your profile',
              'Owner name should match your name',
            ],
            'format': 'State code followed by numbers and letters',
            'maxSize': '5MB per image',
          };
        default:
          return {
            'title': type.name,
            'description': 'Upload clear photos of the document',
            'requirements': ['Clear and readable', 'Original document'],
            'maxSize': '5MB',
          };
      }
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get document guidelines', e, stackTrace);
      return {};
    }
  }

  Future<bool> requestKYCReview(String riderId, String message) async {
    try {
      await Future.delayed(Duration(milliseconds: 500));
      
      // In a real app, this would send a review request
      AppLogger.info('KYC review requested for rider $riderId: $message');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to request KYC review', e, stackTrace);
      return false;
    }
  }

  void dispose() {
    _kycController.close();
  }
}

// Provider for RiderKYCService
final riderKYCServiceProvider = Provider<RiderKYCService>((ref) {
  final storageService = ref.read(storageServiceProvider);
  return RiderKYCService(storageService);
});

// Provider for rider KYC
final riderKYCProvider = FutureProvider.family<RiderKYC?, String>((ref, riderId) {
  final service = ref.read(riderKYCServiceProvider);
  return service.getRiderKYC(riderId);
});

// Provider for KYC progress
final kycProgressProvider = FutureProvider.family<double, String>((ref, riderId) {
  final service = ref.read(riderKYCServiceProvider);
  return service.getKYCProgress(riderId);
});

// Provider for document status
final documentStatusProvider = FutureProvider.family<Map<DocumentType, DocumentStatus>, String>((ref, riderId) {
  final service = ref.read(riderKYCServiceProvider);
  return service.getDocumentStatus(riderId);
});

// Provider for required documents
final requiredDocumentsProvider = FutureProvider<List<String>>((ref) {
  final service = ref.read(riderKYCServiceProvider);
  return service.getRequiredDocuments();
});

// Provider for KYC comments
final kycCommentsProvider = FutureProvider.family<List<KYCComment>, String>((ref, riderId) {
  final service = ref.read(riderKYCServiceProvider);
  return service.getKYCComments(riderId);
});
