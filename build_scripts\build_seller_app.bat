@echo off
echo Building Seller App...
echo.

REM Clean previous builds
echo Cleaning previous builds...
flutter clean
flutter pub get

REM Build Seller App Debug APK
echo Building Seller App Debug APK...
flutter build apk --debug --target=lib/main_seller.dart --flavor=sellerDev --no-tree-shake-icons

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Seller App Debug APK built successfully!
    echo Location: build\app\outputs\flutter-apk\app-seller-dev-debug.apk
) else (
    echo.
    echo ❌ Seller App Debug build failed!
    exit /b 1
)

echo.
echo Building Seller App Release APK...
flutter build apk --release --target=lib/main_seller.dart --flavor=sellerProd --no-tree-shake-icons

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Seller App Release APK built successfully!
    echo Location: build\app\outputs\flutter-apk\app-seller-prod-release.apk
) else (
    echo.
    echo ❌ Seller App Release build failed!
    exit /b 1
)

echo.
echo 🎉 Seller App build completed successfully!
echo.
echo Debug APK: build\app\outputs\flutter-apk\app-seller-dev-debug.apk
echo Release APK: build\app\outputs\flutter-apk\app-seller-prod-release.apk
pause
