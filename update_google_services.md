# 🔥 GOOGLE SERVICES JSON UPDATE GUIDE

## 📱 **STEP-BY-STEP INSTRUCTIONS**

### **🚀 STEP 1: DOWNLOAD REAL GOOGLE SERVICES JSON FILES**

#### **👤 User App (`com.projek.user`):**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select **"projek-user"** project
3. Click **Project Settings** (gear icon)
4. Scroll to **"Your apps"** section
5. Click on **Android app** (`com.projek.user`)
6. Click **"Download google-services.json"**
7. **IMPORTANT**: Save this file as `google-services-user.json` on your desktop

#### **🚗 Rider App (`com.projek.rider`):**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select **"projek-rider-575d2"** project
3. Click **Project Settings** (gear icon)
4. Scroll to **"Your apps"** section
5. Click on **Android app** (`com.projek.rider`)
6. Click **"Download google-services.json"**
7. **IMPORTANT**: Save this file as `google-services-rider.json` on your desktop

#### **🛒 Seller App (`com.projek.seller`):**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select **"projek-seller"** project
3. Click **Project Settings** (gear icon)
4. Scroll to **"Your apps"** section
5. Click on **Android app** (`com.projek.seller`)
6. Click **"Download google-services.json"**
7. **IMPORTANT**: Save this file as `google-services-seller.json` on your desktop

---

### **📁 STEP 2: REPLACE THE FILES IN YOUR PROJECT**

#### **Replace User App Google Services:**
1. Copy `google-services-user.json` from your desktop
2. Rename it to `google-services.json`
3. Replace the file at: `android/app/google-services.json`
4. Also copy it to: `android/app/src/user/google-services.json`

#### **Replace Rider App Google Services:**
1. Copy `google-services-rider.json` from your desktop
2. Rename it to `google-services.json`
3. Replace the file at: `android/app/src/rider/google-services.json`

#### **Replace Seller App Google Services:**
1. Copy `google-services-seller.json` from your desktop
2. Rename it to `google-services.json`
3. Replace the file at: `android/app/src/seller/google-services.json`

---

### **🔧 STEP 3: BUILD COMMANDS FOR EACH APP**

#### **Build User App:**
```bash
flutter build apk --flavor userProd --target lib/main_user.dart
```

#### **Build Rider App:**
```bash
flutter build apk --flavor riderProd --target lib/main_rider.dart
```

#### **Build Seller App:**
```bash
flutter build apk --flavor sellerProd --target lib/main_seller.dart
```

---

### **🚀 STEP 4: RUN COMMANDS FOR EACH APP**

#### **Run User App:**
```bash
flutter run --flavor userDev --target lib/main_user.dart
```

#### **Run Rider App:**
```bash
flutter run --flavor riderDev --target lib/main_rider.dart
```

#### **Run Seller App:**
```bash
flutter run --flavor sellerDev --target lib/main_seller.dart
```

---

### **✅ VERIFICATION CHECKLIST**

- [ ] Downloaded `google-services.json` for User project
- [ ] Downloaded `google-services.json` for Rider project  
- [ ] Downloaded `google-services.json` for Seller project
- [ ] Replaced `android/app/google-services.json` with User file
- [ ] Replaced `android/app/src/user/google-services.json` with User file
- [ ] Replaced `android/app/src/rider/google-services.json` with Rider file
- [ ] Replaced `android/app/src/seller/google-services.json` with Seller file
- [ ] Tested User app compilation
- [ ] Tested Rider app compilation
- [ ] Tested Seller app compilation

---

### **🎯 IMPORTANT NOTES**

1. **Each app MUST use its own `google-services.json`** from its respective Firebase project
2. **The package names in the JSON files must match** the app flavors:
   - User: `com.projek.user`
   - Rider: `com.projek.rider`
   - Seller: `com.projek.seller`
3. **Both SHA-1 certificates must be present** in each Firebase project
4. **Use the correct build flavors** when building/running each app

---

### **🔥 FIREBASE PROJECT SUMMARY**

| App | Firebase Project | Package Name | App ID |
|-----|------------------|--------------|--------|
| **User** | `projek-user` | `com.projek.user` | `1:625844282144:android:8b57ffd6c4eb27e1682d45` |
| **Rider** | `projek-rider-575d2` | `com.projek.rider` | `1:682359797670:android:55b4babc039eedd70e8167` |
| **Seller** | `projek-seller` | `com.projek.seller` | `1:93751239001:android:9f9270770c23fe0d60ac48` |

---

### **🆘 TROUBLESHOOTING**

If you get Firebase errors:
1. **Check package names** in `google-services.json` files
2. **Verify SHA-1 certificates** are added to Firebase projects
3. **Clean and rebuild** the project: `flutter clean && flutter pub get`
4. **Check flavor names** in build commands match the ones in `build.gradle`

---

**🎉 After completing these steps, all 3 apps will have proper Firebase integration with their respective projects!**
