@echo off
echo ========================================
echo Simple APK Build (Bypassing Code Generation Issues)
echo ========================================
echo.

echo [1/5] Stopping Gradle daemons...
call gradle --stop 2>nul
call .\android\gradlew --stop 2>nul

echo [2/5] Cleaning build cache...
call flutter clean

echo [3/5] Getting dependencies...
call flutter pub get

echo [4/5] Building APK without code generation...
echo Attempting debug build first...
call flutter build apk --debug --no-pub

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Debug APK built successfully!
    echo.
    echo [5/5] Building Release APK...
    call flutter build apk --release --no-shrink --no-pub
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo ✅ SUCCESS: APK BUILD COMPLETED!
        echo ========================================
        echo.
        echo APK files created:
        if exist "build\app\outputs\flutter-apk\app-debug.apk" (
            echo ✅ Debug APK: build\app\outputs\flutter-apk\app-debug.apk
            for %%I in ("build\app\outputs\flutter-apk\app-debug.apk") do echo    Size: %%~zI bytes
        )
        if exist "build\app\outputs\flutter-apk\app-release.apk" (
            echo ✅ Release APK: build\app\outputs\flutter-apk\app-release.apk
            for %%I in ("build\app\outputs\flutter-apk\app-release.apk") do echo    Size: %%~zI bytes
        )
        echo.
        echo You can now install the APK on your device!
        echo.
        echo To install on connected device:
        echo flutter install --debug   (for debug APK)
        echo flutter install --release (for release APK)
    ) else (
        echo.
        echo ❌ Release APK build failed. Check the error messages above.
        echo Debug APK is available for testing.
    )
) else (
    echo.
    echo ❌ Debug APK build failed. 
    echo Trying alternative approach with verbose output...
    echo.
    call flutter build apk --debug --verbose --no-pub
)

echo.
echo Build process completed.
pause
