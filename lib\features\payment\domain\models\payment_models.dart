import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'payment_method.dart';
// Removed wallet import - using payment domain models instead

part 'payment_models.g.dart';

@HiveType(typeId: 80)
@JsonSerializable()
class PaymentRequest extends Equatable {
  @HiveField(0)
  final String orderId;

  @HiveField(1)
  final double amount;

  @HiveField(2)
  final String currency;

  @HiveField(3)
  final String description;

  @HiveField(4)
  final PaymentMethod method;

  @HiveField(5)
  final String customerId;

  @HiveField(6)
  final String customerName;

  @HiveField(7)
  final String customerEmail;

  @HiveField(8)
  final String customerPhone;

  @HiveField(9)
  final Map<String, dynamic> metadata;

  @HiveField(10)
  final DateTime timestamp;

  const PaymentRequest({
    required this.orderId,
    required this.amount,
    this.currency = 'INR',
    required this.description,
    required this.method,
    required this.customerId,
    required this.customerName,
    required this.customerEmail,
    required this.customerPhone,
    this.metadata = const {},
    required this.timestamp,
  });

  factory PaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$PaymentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentRequestToJson(this);

  @override
  List<Object?> get props => [
    orderId,
    amount,
    currency,
    description,
    method,
    customerId,
    customerName,
    customerEmail,
    customerPhone,
    metadata,
    timestamp,
  ];
}

@HiveType(typeId: 81)
@JsonSerializable()
class PaymentResult extends Equatable {
  @HiveField(0)
  final bool success;

  @HiveField(1)
  final String? transactionId;

  @HiveField(2)
  final String orderId;

  @HiveField(3)
  final double amount;

  @HiveField(4)
  final PaymentMethod method;

  @HiveField(5)
  final DateTime timestamp;

  @HiveField(6)
  final String? error;

  @HiveField(7)
  final Map<String, dynamic> metadata;

  const PaymentResult({
    required this.success,
    this.transactionId,
    required this.orderId,
    required this.amount,
    required this.method,
    required this.timestamp,
    this.error,
    this.metadata = const {},
  });

  factory PaymentResult.fromJson(Map<String, dynamic> json) =>
      _$PaymentResultFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentResultToJson(this);

  @override
  List<Object?> get props => [
    success,
    transactionId,
    orderId,
    amount,
    method,
    timestamp,
    error,
    metadata,
  ];
}

@HiveType(typeId: 82)
@JsonSerializable()
class RefundResult extends Equatable {
  @HiveField(0)
  final bool success;

  @HiveField(1)
  final String? refundId;

  @HiveField(2)
  final double amount;

  @HiveField(3)
  final String status;

  @HiveField(4)
  final DateTime timestamp;

  @HiveField(5)
  final String? error;

  const RefundResult({
    required this.success,
    this.refundId,
    required this.amount,
    required this.status,
    required this.timestamp,
    this.error,
  });

  factory RefundResult.fromJson(Map<String, dynamic> json) =>
      _$RefundResultFromJson(json);

  Map<String, dynamic> toJson() => _$RefundResultToJson(this);

  @override
  List<Object?> get props => [
    success,
    refundId,
    amount,
    status,
    timestamp,
    error,
  ];
}

@HiveType(typeId: 83)
@JsonSerializable()
class UpiApp extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String displayName;

  @HiveField(3)
  final String packageName;

  @HiveField(4)
  final String icon;

  @HiveField(5)
  final bool isInstalled;

  @HiveField(6)
  final bool isSupported;

  const UpiApp({
    required this.id,
    required this.name,
    required this.displayName,
    required this.packageName,
    required this.icon,
    this.isInstalled = false,
    this.isSupported = true,
  });

  factory UpiApp.fromJson(Map<String, dynamic> json) => _$UpiAppFromJson(json);

  Map<String, dynamic> toJson() => _$UpiAppToJson(this);

  @override
  List<Object?> get props => [
    id,
    name,
    displayName,
    packageName,
    icon,
    isInstalled,
    isSupported,
  ];
}

@HiveType(typeId: 84)
@JsonSerializable()
class BankAccount extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String accountNumber;

  @HiveField(2)
  final String ifscCode;

  @HiveField(3)
  final String bankName;

  @HiveField(4)
  final String accountHolderName;

  @HiveField(5)
  final String accountType;

  @HiveField(6)
  final bool isVerified;

  @HiveField(7)
  final bool isPrimary;

  @HiveField(8)
  final DateTime createdAt;

  @HiveField(9)
  final DateTime? verifiedAt;

  const BankAccount({
    required this.id,
    required this.accountNumber,
    required this.ifscCode,
    required this.bankName,
    required this.accountHolderName,
    required this.accountType,
    this.isVerified = false,
    this.isPrimary = false,
    required this.createdAt,
    this.verifiedAt,
  });

  factory BankAccount.fromJson(Map<String, dynamic> json) =>
      _$BankAccountFromJson(json);

  Map<String, dynamic> toJson() => _$BankAccountToJson(this);

  String get maskedAccountNumber {
    if (accountNumber.length <= 4) return accountNumber;
    return 'XXXX${accountNumber.substring(accountNumber.length - 4)}';
  }

  @override
  List<Object?> get props => [
    id,
    accountNumber,
    ifscCode,
    bankName,
    accountHolderName,
    accountType,
    isVerified,
    isPrimary,
    createdAt,
    verifiedAt,
  ];
}

@HiveType(typeId: 85)
@JsonSerializable()
class SavedCard extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String last4Digits;

  @HiveField(2)
  final String cardType;

  @HiveField(3)
  final String bankName;

  @HiveField(4)
  final String expiryMonth;

  @HiveField(5)
  final String expiryYear;

  @HiveField(6)
  final String cardHolderName;

  @HiveField(7)
  final bool isDefault;

  @HiveField(8)
  final DateTime createdAt;

  @HiveField(9)
  final DateTime? lastUsed;

  const SavedCard({
    required this.id,
    required this.last4Digits,
    required this.cardType,
    required this.bankName,
    required this.expiryMonth,
    required this.expiryYear,
    required this.cardHolderName,
    this.isDefault = false,
    required this.createdAt,
    this.lastUsed,
  });

  factory SavedCard.fromJson(Map<String, dynamic> json) =>
      _$SavedCardFromJson(json);

  Map<String, dynamic> toJson() => _$SavedCardToJson(this);

  String get maskedCardNumber => 'XXXX XXXX XXXX $last4Digits';
  String get expiryDate => '$expiryMonth/$expiryYear';

  bool get isExpired {
    final now = DateTime.now();
    final expiry = DateTime(int.parse('20$expiryYear'), int.parse(expiryMonth));
    return now.isAfter(expiry);
  }

  @override
  List<Object?> get props => [
    id,
    last4Digits,
    cardType,
    bankName,
    expiryMonth,
    expiryYear,
    cardHolderName,
    isDefault,
    createdAt,
    lastUsed,
  ];
}

@HiveType(typeId: 86)
@JsonSerializable()
class PaymentSummary extends Equatable {
  @HiveField(0)
  final double subtotal;

  @HiveField(1)
  final double tax;

  @HiveField(2)
  final double deliveryFee;

  @HiveField(3)
  final double discount;

  @HiveField(4)
  final double cashback;

  @HiveField(5)
  final double total;

  @HiveField(6)
  final String currency;

  @HiveField(7)
  final Map<String, double> breakdown;

  const PaymentSummary({
    required this.subtotal,
    this.tax = 0.0,
    this.deliveryFee = 0.0,
    this.discount = 0.0,
    this.cashback = 0.0,
    required this.total,
    this.currency = 'INR',
    this.breakdown = const {},
  });

  factory PaymentSummary.fromJson(Map<String, dynamic> json) =>
      _$PaymentSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentSummaryToJson(this);

  String get formattedSubtotal => '₹${subtotal.toStringAsFixed(2)}';
  String get formattedTax => '₹${tax.toStringAsFixed(2)}';
  String get formattedDeliveryFee => '₹${deliveryFee.toStringAsFixed(2)}';
  String get formattedDiscount => '₹${discount.toStringAsFixed(2)}';
  String get formattedCashback => '₹${cashback.toStringAsFixed(2)}';
  String get formattedTotal => '₹${total.toStringAsFixed(2)}';

  @override
  List<Object?> get props => [
    subtotal,
    tax,
    deliveryFee,
    discount,
    cashback,
    total,
    currency,
    breakdown,
  ];
}

// Static data for UPI apps
class UpiApps {
  static const List<UpiApp> availableApps = [
    UpiApp(
      id: 'googlepay',
      name: 'Google Pay',
      displayName: 'Google Pay',
      packageName: 'com.google.android.apps.nbu.paisa.user',
      icon: 'assets/icons/googlepay.png',
    ),
    UpiApp(
      id: 'phonepe',
      name: 'PhonePe',
      displayName: 'PhonePe',
      packageName: 'com.phonepe.app',
      icon: 'assets/icons/phonepe.png',
    ),
    UpiApp(
      id: 'paytm',
      name: 'Paytm',
      displayName: 'Paytm',
      packageName: 'net.one97.paytm',
      icon: 'assets/icons/paytm.png',
    ),
    UpiApp(
      id: 'amazonpay',
      name: 'Amazon Pay',
      displayName: 'Amazon Pay',
      packageName: 'in.amazon.mShop.android.shopping',
      icon: 'assets/icons/amazonpay.png',
    ),
    UpiApp(
      id: 'bhim',
      name: 'BHIM',
      displayName: 'BHIM UPI',
      packageName: 'in.org.npci.upiapp',
      icon: 'assets/icons/bhim.png',
    ),
  ];
}
