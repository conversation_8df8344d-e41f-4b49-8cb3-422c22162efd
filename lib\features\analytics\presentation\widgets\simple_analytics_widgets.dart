import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/models/analytics_models.dart';

class MetricCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final double? trendPercentage;
  final bool? isPositiveTrend;
  final VoidCallback? onTap;

  const MetricCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.color,
    this.trendPercentage,
    this.isPositiveTrend,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const Spacer(),
                  if (trendPercentage != null && isPositiveTrend != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isPositiveTrend! 
                            ? AppColors.success.withOpacity(0.1)
                            : AppColors.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isPositiveTrend! ? Icons.trending_up : Icons.trending_down,
                            size: 16,
                            color: isPositiveTrend! ? AppColors.success : AppColors.error,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${trendPercentage!.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isPositiveTrend! ? AppColors.success : AppColors.error,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: AppTextStyles.headlineMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class SimpleChartWidget extends StatelessWidget {
  final List<ChartDataPoint> dataPoints;
  final String title;
  final String? subtitle;
  final Color primaryColor;
  final String chartType; // 'line', 'bar', 'pie'

  const SimpleChartWidget({
    super.key,
    required this.dataPoints,
    required this.title,
    this.subtitle,
    this.primaryColor = AppColors.primaryBlue,
    this.chartType = 'line',
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTextStyles.headlineSmall,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                subtitle!,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.grey300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildChartContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartContent() {
    switch (chartType) {
      case 'bar':
        return _buildBarChart();
      case 'pie':
        return _buildPieChart();
      case 'line':
      default:
        return _buildLineChart();
    }
  }

  Widget _buildLineChart() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.show_chart,
            size: 48,
            color: primaryColor,
          ),
          const SizedBox(height: 8),
          Text(
            'Line Chart',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${dataPoints.length} data points',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 16),
          // Simple data visualization
          if (dataPoints.isNotEmpty) ...[
            Text(
              'Latest: ${dataPoints.last.value.toStringAsFixed(1)}',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: dataPoints.last.value / 100, // Assuming max 100
              backgroundColor: AppColors.grey200,
              valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBarChart() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 48,
            color: primaryColor,
          ),
          const SizedBox(height: 8),
          Text(
            'Bar Chart',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 16),
          // Simple bar representation
          if (dataPoints.isNotEmpty) ...[
            ...dataPoints.take(3).map((point) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  SizedBox(
                    width: 80,
                    child: Text(
                      point.label,
                      style: AppTextStyles.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: LinearProgressIndicator(
                      value: point.value / 100, // Assuming max 100
                      backgroundColor: AppColors.grey200,
                      valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    point.value.toStringAsFixed(0),
                    style: AppTextStyles.bodySmall,
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildPieChart() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pie_chart,
            size: 48,
            color: primaryColor,
          ),
          const SizedBox(height: 8),
          Text(
            'Pie Chart',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 16),
          // Simple pie representation with legend
          if (dataPoints.isNotEmpty) ...[
            ...dataPoints.take(4).map((point) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: primaryColor.withOpacity(0.7),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      point.label,
                      style: AppTextStyles.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    '${point.value.toStringAsFixed(1)}%',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }
}

class AnalyticsFilterWidget extends StatelessWidget {
  final AnalyticsFilter currentFilter;
  final ValueChanged<AnalyticsFilter> onFilterChanged;

  const AnalyticsFilterWidget({
    super.key,
    required this.currentFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Time Range',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: AnalyticsTimeRange.values.where((range) => range != AnalyticsTimeRange.custom).map((range) {
                final isSelected = currentFilter.timeRange == range;
                return FilterChip(
                  label: Text(_getTimeRangeLabel(range)),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      onFilterChanged(AnalyticsFilter(timeRange: range));
                    }
                  },
                  selectedColor: AppColors.primaryBlue.withOpacity(0.2),
                  checkmarkColor: AppColors.primaryBlue,
                  labelStyle: TextStyle(
                    color: isSelected ? AppColors.primaryBlue : AppColors.textPrimary,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  String _getTimeRangeLabel(AnalyticsTimeRange range) {
    switch (range) {
      case AnalyticsTimeRange.today:
        return 'Today';
      case AnalyticsTimeRange.yesterday:
        return 'Yesterday';
      case AnalyticsTimeRange.last7Days:
        return 'Last 7 Days';
      case AnalyticsTimeRange.last30Days:
        return 'Last 30 Days';
      case AnalyticsTimeRange.last90Days:
        return 'Last 90 Days';
      case AnalyticsTimeRange.thisMonth:
        return 'This Month';
      case AnalyticsTimeRange.lastMonth:
        return 'Last Month';
      case AnalyticsTimeRange.thisYear:
        return 'This Year';
      case AnalyticsTimeRange.custom:
        return 'Custom';
    }
  }
}
