# My India First Super App - Firebase Configuration

## 🔥 **Firebase Services Setup**

### **1. Authentication Configuration**

#### Phone Authentication Setup
```javascript
// Firebase Console > Authentication > Sign-in method
// Enable Phone authentication

// Add test phone numbers for development:
+91 9999999999 -> OTP: 123456
+91 8888888888 -> OTP: 654321
```

#### Security Rules for Authentication
```javascript
// Firebase Console > Authentication > Settings
{
  "rules": {
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid",
        ".validate": "newData.hasChildren(['name', 'phone', 'createdAt'])"
      }
    }
  }
}
```

### **2. Firestore Database Structure**

#### Collections Schema
```
my_india_first_db/
├── users/
│   └── {userId}/
│       ├── name: string
│       ├── phone: string
│       ├── email: string (optional)
│       ├── profilePicture: string (optional)
│       ├── walletBalance: number
│       ├── totalEarned: number
│       ├── totalSpent: number
│       ├── isVerified: boolean
│       ├── createdAt: timestamp
│       └── updatedAt: timestamp
│
├── transactions/
│   └── {transactionId}/
│       ├── userId: string
│       ├── type: string (earn|spend|transfer)
│       ├── amount: number
│       ├── description: string
│       ├── category: string
│       ├── status: string (pending|completed|failed)
│       ├── createdAt: timestamp
│       └── metadata: object
│
├── bookings/
│   └── {bookingId}/
│       ├── userId: string
│       ├── vehicleId: string
│       ├── vehicleType: string (bike|car)
│       ├── startTime: timestamp
│       ├── endTime: timestamp
│       ├── totalAmount: number
│       ├── commission: number
│       ├── status: string
│       ├── pickupLocation: geopoint
│       ├── dropLocation: geopoint
│       └── createdAt: timestamp
│
├── vehicles/
│   └── {vehicleId}/
│       ├── name: string
│       ├── type: string
│       ├── pricePerHour: number
│       ├── location: geopoint
│       ├── isAvailable: boolean
│       ├── ownerId: string
│       ├── rating: number
│       └── features: array
│
├── games/
│   └── {gameId}/
│       ├── userId: string
│       ├── gameType: string (spin_wheel|staking)
│       ├── entryAmount: number
│       ├── winAmount: number
│       ├── result: string
│       ├── timestamp: timestamp
│       └── metadata: object
│
├── products/
│   └── {productId}/
│       ├── name: string
│       ├── price: number
│       ├── cashbackPercentage: number
│       ├── category: string
│       ├── vendorId: string
│       ├── rating: number
│       ├── inStock: boolean
│       └── images: array
│
└── support_chats/
    └── {chatId}/
        ├── userId: string
        ├── messages: array
        ├── status: string (open|closed)
        ├── priority: string
        └── createdAt: timestamp
```

### **3. Firestore Security Rules**

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Transactions - users can only see their own
    match /transactions/{transactionId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Bookings - users can only see their own
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Vehicles - read-only for users, write for owners
    match /vehicles/{vehicleId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        resource.data.ownerId == request.auth.uid;
    }
    
    // Games - users can only see their own game history
    match /games/{gameId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Products - read-only for users
    match /products/{productId} {
      allow read: if request.auth != null;
    }
    
    // Support chats - users can only access their own
    match /support_chats/{chatId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
        request.resource.data.keys().hasAll(['name', 'phone', 'createdAt']);
    }
  }
}
```

### **4. Cloud Functions**

#### Commission Calculation Function
```javascript
// functions/index.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

exports.calculateCommission = functions.firestore
  .document('bookings/{bookingId}')
  .onCreate(async (snap, context) => {
    const booking = snap.data();
    const commissionRate = 0.15; // 15% commission
    const commission = booking.totalAmount * commissionRate;
    
    // Update booking with commission
    await snap.ref.update({ commission });
    
    // Add commission to admin wallet
    const adminWalletRef = admin.firestore().doc('admin/wallet');
    await adminWalletRef.update({
      balance: admin.firestore.FieldValue.increment(commission),
      totalEarned: admin.firestore.FieldValue.increment(commission)
    });
    
    return null;
  });

exports.processGameResult = functions.firestore
  .document('games/{gameId}')
  .onCreate(async (snap, context) => {
    const game = snap.data();
    const userId = game.userId;
    const winAmount = game.winAmount;
    const entryAmount = game.entryAmount;
    
    // Update user wallet
    const userRef = admin.firestore().doc(`users/${userId}`);
    const netAmount = winAmount - entryAmount;
    
    await userRef.update({
      walletBalance: admin.firestore.FieldValue.increment(netAmount),
      totalEarned: admin.firestore.FieldValue.increment(winAmount),
      totalSpent: admin.firestore.FieldValue.increment(entryAmount)
    });
    
    // Create transaction record
    await admin.firestore().collection('transactions').add({
      userId,
      type: netAmount > 0 ? 'earn' : 'spend',
      amount: Math.abs(netAmount),
      description: `Spin & Earn Game - ${game.result}`,
      category: 'gaming',
      status: 'completed',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      metadata: {
        gameId: context.params.gameId,
        gameType: game.gameType,
        entryAmount,
        winAmount
      }
    });
    
    return null;
  });

exports.sendNotification = functions.firestore
  .document('transactions/{transactionId}')
  .onCreate(async (snap, context) => {
    const transaction = snap.data();
    const userId = transaction.userId;
    
    // Get user's FCM token
    const userDoc = await admin.firestore().doc(`users/${userId}`).get();
    const fcmToken = userDoc.data().fcmToken;
    
    if (fcmToken) {
      const message = {
        token: fcmToken,
        notification: {
          title: 'Transaction Alert',
          body: `₹${transaction.amount} ${transaction.type === 'earn' ? 'credited to' : 'debited from'} your wallet`
        },
        data: {
          transactionId: context.params.transactionId,
          type: transaction.type,
          amount: transaction.amount.toString()
        }
      };
      
      await admin.messaging().send(message);
    }
    
    return null;
  });
```

### **5. Firebase Storage Rules**

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User profile pictures
    match /users/{userId}/profile.{extension} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid == userId &&
        extension.matches('(jpg|jpeg|png)') &&
        request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    // Vehicle images
    match /vehicles/{vehicleId}/{imageId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // Vehicle owners only
    }
    
    // Product images
    match /products/{productId}/{imageId} {
      allow read: if request.auth != null;
    }
  }
}
```

### **6. Firebase Messaging Configuration**

#### FCM Setup for Push Notifications
```dart
// lib/core/services/notification_service.dart
class NotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  
  static Future<void> initialize() async {
    // Request permission
    await _messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    
    // Get FCM token
    final token = await _messaging.getToken();
    print('FCM Token: $token');
    
    // Save token to Firestore
    if (token != null) {
      await _saveTokenToFirestore(token);
    }
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
  }
  
  static Future<void> _saveTokenToFirestore(String token) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .update({'fcmToken': token});
    }
  }
}
```

### **7. Environment Configuration**

#### Development Environment
```dart
// lib/core/config/firebase_config.dart
class FirebaseConfig {
  static const String projectId = 'my-india-first-dev';
  static const String apiKey = 'your-dev-api-key';
  static const String appId = 'your-dev-app-id';
  
  static const Map<String, dynamic> devConfig = {
    'enableEmulator': true,
    'firestoreHost': 'localhost:8080',
    'authHost': 'localhost:9099',
    'storageHost': 'localhost:9199',
  };
}
```

#### Production Environment
```dart
class FirebaseConfig {
  static const String projectId = 'my-india-first-prod';
  static const String apiKey = 'your-prod-api-key';
  static const String appId = 'your-prod-app-id';
  
  static const Map<String, dynamic> prodConfig = {
    'enableEmulator': false,
    'enableAnalytics': true,
    'enableCrashlytics': true,
  };
}
```

### **8. Data Migration Scripts**

#### Migrate Existing Projek Data
```dart
// lib/core/services/migration_service.dart
class MigrationService {
  static Future<void> migrateProjekData() async {
    // Migrate existing wallet data
    await _migrateWalletData();
    
    // Migrate existing products
    await _migrateProductData();
    
    // Migrate existing user data
    await _migrateUserData();
  }
  
  static Future<void> _migrateWalletData() async {
    // Convert Projek Coins to My India First Coins
    // 1 Projek Coin = 1 My India First Coin
  }
}
```

### **9. Backup and Recovery**

#### Automated Backups
```javascript
// Cloud Function for daily backups
exports.scheduledBackup = functions.pubsub
  .schedule('0 2 * * *') // Daily at 2 AM
  .timeZone('Asia/Kolkata')
  .onRun(async (context) => {
    const client = new admin.firestore.v1.FirestoreAdminClient();
    const projectId = process.env.GCP_PROJECT || process.env.GCLOUD_PROJECT;
    const databaseName = client.databasePath(projectId, '(default)');
    
    const bucket = 'gs://my-india-first-backups';
    const timestamp = new Date().toISOString().split('T')[0];
    
    await client.exportDocuments({
      name: databaseName,
      outputUriPrefix: `${bucket}/${timestamp}`,
      collectionIds: []
    });
    
    console.log(`Backup completed for ${timestamp}`);
    return null;
  });
```

### **10. Monitoring and Analytics**

#### Performance Monitoring
```dart
// lib/core/services/analytics_service.dart
class AnalyticsService {
  static Future<void> logEvent(String eventName, Map<String, dynamic> parameters) async {
    await FirebaseAnalytics.instance.logEvent(
      name: eventName,
      parameters: parameters,
    );
  }
  
  static Future<void> logSpinWheelPlay(int entryAmount, int winAmount) async {
    await logEvent('spin_wheel_play', {
      'entry_amount': entryAmount,
      'win_amount': winAmount,
      'profit': winAmount - entryAmount,
    });
  }
  
  static Future<void> logVehicleBooking(String vehicleType, double amount) async {
    await logEvent('vehicle_booking', {
      'vehicle_type': vehicleType,
      'booking_amount': amount,
    });
  }
}
```

This Firebase configuration provides a robust foundation for the My India First super app with proper security, scalability, and monitoring capabilities.
