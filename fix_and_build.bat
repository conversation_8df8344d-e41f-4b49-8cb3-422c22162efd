@echo off
echo ========================================
echo FIXING PUB CACHE AND BUILDING USER APK
echo ========================================

echo Setting PUB_CACHE environment variable...
set PUB_CACHE=E:\Appdata\flutter_pub_cache
echo PUB_CACHE set to: %PUB_CACHE%

echo.
echo Creating cache directory if it doesn't exist...
if not exist "E:\Appdata\flutter_pub_cache" (
    mkdir "E:\Appdata\flutter_pub_cache"
    echo Created directory: E:\Appdata\flutter_pub_cache
) else (
    echo Directory already exists: E:\Appdata\flutter_pub_cache
)

echo.
echo Cleaning Flutter project...
flutter clean

echo.
echo Getting dependencies with new cache location...
flutter pub get

echo.
echo Building User App Release APK...
flutter build apk --flavor userProd --release

echo.
echo ========================================
echo BUILD COMPLETED!
echo ========================================
echo.
echo APK Location: build\app\outputs\flutter-apk\
dir build\app\outputs\flutter-apk\*.apk /b 2>nul
echo.
echo If APK is generated, you can install it with:
echo adb install build\app\outputs\flutter-apk\app-user-prod-release.apk
echo.
pause
