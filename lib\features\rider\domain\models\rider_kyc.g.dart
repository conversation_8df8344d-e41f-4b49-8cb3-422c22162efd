// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rider_kyc.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RiderKYCAdapter extends TypeAdapter<RiderKYC> {
  @override
  final int typeId = 34;

  @override
  RiderKYC read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RiderKYC(
      riderId: fields[0] as String,
      status: fields[1] as KYCStatus,
      documents: (fields[2] as List).cast<KYCDocument>(),
      submittedAt: fields[3] as DateTime?,
      reviewedAt: fields[4] as DateTime?,
      approvedAt: fields[5] as DateTime?,
      rejectionReason: fields[6] as String?,
      pendingDocuments: (fields[7] as List).cast<String>(),
      completionPercentage: fields[8] as double,
      reviewer: fields[9] as KYCReviewer?,
      comments: (fields[10] as List).cast<KYCComment>(),
    );
  }

  @override
  void write(BinaryWriter writer, RiderKYC obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.riderId)
      ..writeByte(1)
      ..write(obj.status)
      ..writeByte(2)
      ..write(obj.documents)
      ..writeByte(3)
      ..write(obj.submittedAt)
      ..writeByte(4)
      ..write(obj.reviewedAt)
      ..writeByte(5)
      ..write(obj.approvedAt)
      ..writeByte(6)
      ..write(obj.rejectionReason)
      ..writeByte(7)
      ..write(obj.pendingDocuments)
      ..writeByte(8)
      ..write(obj.completionPercentage)
      ..writeByte(9)
      ..write(obj.reviewer)
      ..writeByte(10)
      ..write(obj.comments);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RiderKYCAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class KYCDocumentAdapter extends TypeAdapter<KYCDocument> {
  @override
  final int typeId = 35;

  @override
  KYCDocument read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return KYCDocument(
      id: fields[0] as String,
      type: fields[1] as DocumentType,
      documentNumber: fields[2] as String,
      imageUrls: (fields[3] as List).cast<String>(),
      status: fields[4] as DocumentStatus,
      uploadedAt: fields[5] as DateTime,
      verifiedAt: fields[6] as DateTime?,
      rejectionReason: fields[7] as String?,
      extractedData: (fields[8] as Map).cast<String, dynamic>(),
      confidenceScore: fields[9] as double,
    );
  }

  @override
  void write(BinaryWriter writer, KYCDocument obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.documentNumber)
      ..writeByte(3)
      ..write(obj.imageUrls)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.uploadedAt)
      ..writeByte(6)
      ..write(obj.verifiedAt)
      ..writeByte(7)
      ..write(obj.rejectionReason)
      ..writeByte(8)
      ..write(obj.extractedData)
      ..writeByte(9)
      ..write(obj.confidenceScore);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is KYCDocumentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class KYCReviewerAdapter extends TypeAdapter<KYCReviewer> {
  @override
  final int typeId = 38;

  @override
  KYCReviewer read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return KYCReviewer(
      id: fields[0] as String,
      name: fields[1] as String,
      email: fields[2] as String,
      reviewedAt: fields[3] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, KYCReviewer obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.reviewedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is KYCReviewerAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class KYCCommentAdapter extends TypeAdapter<KYCComment> {
  @override
  final int typeId = 39;

  @override
  KYCComment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return KYCComment(
      id: fields[0] as String,
      message: fields[1] as String,
      authorId: fields[2] as String,
      authorName: fields[3] as String,
      timestamp: fields[4] as DateTime,
      type: fields[5] as CommentType,
    );
  }

  @override
  void write(BinaryWriter writer, KYCComment obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.message)
      ..writeByte(2)
      ..write(obj.authorId)
      ..writeByte(3)
      ..write(obj.authorName)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.type);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is KYCCommentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WithdrawalRequestAdapter extends TypeAdapter<WithdrawalRequest> {
  @override
  final int typeId = 41;

  @override
  WithdrawalRequest read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WithdrawalRequest(
      id: fields[0] as String,
      riderId: fields[1] as String,
      amount: fields[2] as double,
      method: fields[3] as WithdrawalMethod,
      status: fields[4] as WithdrawalStatus,
      requestedAt: fields[5] as DateTime,
      processedAt: fields[6] as DateTime?,
      transactionId: fields[7] as String?,
      failureReason: fields[8] as String?,
      processingFee: fields[9] as double,
      netAmount: fields[10] as double,
      paymentDetails: (fields[11] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, WithdrawalRequest obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.riderId)
      ..writeByte(2)
      ..write(obj.amount)
      ..writeByte(3)
      ..write(obj.method)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.requestedAt)
      ..writeByte(6)
      ..write(obj.processedAt)
      ..writeByte(7)
      ..write(obj.transactionId)
      ..writeByte(8)
      ..write(obj.failureReason)
      ..writeByte(9)
      ..write(obj.processingFee)
      ..writeByte(10)
      ..write(obj.netAmount)
      ..writeByte(11)
      ..write(obj.paymentDetails);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WithdrawalRequestAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BonusIncentiveAdapter extends TypeAdapter<BonusIncentive> {
  @override
  final int typeId = 44;

  @override
  BonusIncentive read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BonusIncentive(
      id: fields[0] as String,
      riderId: fields[1] as String,
      type: fields[2] as BonusType,
      title: fields[3] as String,
      description: fields[4] as String,
      amount: fields[5] as double,
      status: fields[6] as BonusStatus,
      validFrom: fields[7] as DateTime,
      validUntil: fields[8] as DateTime,
      criteria: (fields[9] as Map).cast<String, dynamic>(),
      progress: fields[10] as double,
      earnedAt: fields[11] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, BonusIncentive obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.riderId)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.title)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.amount)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.validFrom)
      ..writeByte(8)
      ..write(obj.validUntil)
      ..writeByte(9)
      ..write(obj.criteria)
      ..writeByte(10)
      ..write(obj.progress)
      ..writeByte(11)
      ..write(obj.earnedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BonusIncentiveAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class KYCStatusAdapter extends TypeAdapter<KYCStatus> {
  @override
  final int typeId = 100;

  @override
  KYCStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return KYCStatus.notStarted;
      case 1:
        return KYCStatus.inProgress;
      case 2:
        return KYCStatus.underReview;
      case 3:
        return KYCStatus.approved;
      case 4:
        return KYCStatus.rejected;
      case 5:
        return KYCStatus.resubmissionRequired;
      case 6:
        return KYCStatus.pending;
      case 7:
        return KYCStatus.incomplete;
      default:
        return KYCStatus.notStarted;
    }
  }

  @override
  void write(BinaryWriter writer, KYCStatus obj) {
    switch (obj) {
      case KYCStatus.notStarted:
        writer.writeByte(0);
        break;
      case KYCStatus.inProgress:
        writer.writeByte(1);
        break;
      case KYCStatus.underReview:
        writer.writeByte(2);
        break;
      case KYCStatus.approved:
        writer.writeByte(3);
        break;
      case KYCStatus.rejected:
        writer.writeByte(4);
        break;
      case KYCStatus.resubmissionRequired:
        writer.writeByte(5);
        break;
      case KYCStatus.pending:
        writer.writeByte(6);
        break;
      case KYCStatus.incomplete:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is KYCStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DocumentTypeAdapter extends TypeAdapter<DocumentType> {
  @override
  final int typeId = 101;

  @override
  DocumentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DocumentType.aadhaar;
      case 1:
        return DocumentType.pan;
      case 2:
        return DocumentType.drivingLicense;
      case 3:
        return DocumentType.vehicleRC;
      case 4:
        return DocumentType.insurance;
      case 5:
        return DocumentType.pollution;
      case 6:
        return DocumentType.profilePhoto;
      case 7:
        return DocumentType.vehiclePhoto;
      case 8:
        return DocumentType.bankPassbook;
      default:
        return DocumentType.aadhaar;
    }
  }

  @override
  void write(BinaryWriter writer, DocumentType obj) {
    switch (obj) {
      case DocumentType.aadhaar:
        writer.writeByte(0);
        break;
      case DocumentType.pan:
        writer.writeByte(1);
        break;
      case DocumentType.drivingLicense:
        writer.writeByte(2);
        break;
      case DocumentType.vehicleRC:
        writer.writeByte(3);
        break;
      case DocumentType.insurance:
        writer.writeByte(4);
        break;
      case DocumentType.pollution:
        writer.writeByte(5);
        break;
      case DocumentType.profilePhoto:
        writer.writeByte(6);
        break;
      case DocumentType.vehiclePhoto:
        writer.writeByte(7);
        break;
      case DocumentType.bankPassbook:
        writer.writeByte(8);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DocumentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WithdrawalMethodAdapter extends TypeAdapter<WithdrawalMethod> {
  @override
  final int typeId = 102;

  @override
  WithdrawalMethod read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return WithdrawalMethod.bankTransfer;
      case 1:
        return WithdrawalMethod.upi;
      case 2:
        return WithdrawalMethod.paytm;
      case 3:
        return WithdrawalMethod.phonepe;
      case 4:
        return WithdrawalMethod.googlepay;
      case 5:
        return WithdrawalMethod.amazonpay;
      default:
        return WithdrawalMethod.bankTransfer;
    }
  }

  @override
  void write(BinaryWriter writer, WithdrawalMethod obj) {
    switch (obj) {
      case WithdrawalMethod.bankTransfer:
        writer.writeByte(0);
        break;
      case WithdrawalMethod.upi:
        writer.writeByte(1);
        break;
      case WithdrawalMethod.paytm:
        writer.writeByte(2);
        break;
      case WithdrawalMethod.phonepe:
        writer.writeByte(3);
        break;
      case WithdrawalMethod.googlepay:
        writer.writeByte(4);
        break;
      case WithdrawalMethod.amazonpay:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WithdrawalMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WithdrawalStatusAdapter extends TypeAdapter<WithdrawalStatus> {
  @override
  final int typeId = 103;

  @override
  WithdrawalStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return WithdrawalStatus.pending;
      case 1:
        return WithdrawalStatus.processing;
      case 2:
        return WithdrawalStatus.completed;
      case 3:
        return WithdrawalStatus.failed;
      case 4:
        return WithdrawalStatus.cancelled;
      default:
        return WithdrawalStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, WithdrawalStatus obj) {
    switch (obj) {
      case WithdrawalStatus.pending:
        writer.writeByte(0);
        break;
      case WithdrawalStatus.processing:
        writer.writeByte(1);
        break;
      case WithdrawalStatus.completed:
        writer.writeByte(2);
        break;
      case WithdrawalStatus.failed:
        writer.writeByte(3);
        break;
      case WithdrawalStatus.cancelled:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WithdrawalStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BonusTypeAdapter extends TypeAdapter<BonusType> {
  @override
  final int typeId = 104;

  @override
  BonusType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BonusType.peakHour;
      case 1:
        return BonusType.dailyTarget;
      case 2:
        return BonusType.weeklyTarget;
      case 3:
        return BonusType.ratingBonus;
      case 4:
        return BonusType.referral;
      case 5:
        return BonusType.loyalty;
      case 6:
        return BonusType.surge;
      case 7:
        return BonusType.special;
      default:
        return BonusType.peakHour;
    }
  }

  @override
  void write(BinaryWriter writer, BonusType obj) {
    switch (obj) {
      case BonusType.peakHour:
        writer.writeByte(0);
        break;
      case BonusType.dailyTarget:
        writer.writeByte(1);
        break;
      case BonusType.weeklyTarget:
        writer.writeByte(2);
        break;
      case BonusType.ratingBonus:
        writer.writeByte(3);
        break;
      case BonusType.referral:
        writer.writeByte(4);
        break;
      case BonusType.loyalty:
        writer.writeByte(5);
        break;
      case BonusType.surge:
        writer.writeByte(6);
        break;
      case BonusType.special:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BonusTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BonusStatusAdapter extends TypeAdapter<BonusStatus> {
  @override
  final int typeId = 105;

  @override
  BonusStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BonusStatus.active;
      case 1:
        return BonusStatus.earned;
      case 2:
        return BonusStatus.expired;
      case 3:
        return BonusStatus.cancelled;
      default:
        return BonusStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, BonusStatus obj) {
    switch (obj) {
      case BonusStatus.active:
        writer.writeByte(0);
        break;
      case BonusStatus.earned:
        writer.writeByte(1);
        break;
      case BonusStatus.expired:
        writer.writeByte(2);
        break;
      case BonusStatus.cancelled:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BonusStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DocumentStatusAdapter extends TypeAdapter<DocumentStatus> {
  @override
  final int typeId = 37;

  @override
  DocumentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DocumentStatus.notUploaded;
      case 1:
        return DocumentStatus.uploaded;
      case 2:
        return DocumentStatus.underReview;
      case 3:
        return DocumentStatus.verified;
      case 4:
        return DocumentStatus.rejected;
      case 5:
        return DocumentStatus.expired;
      default:
        return DocumentStatus.notUploaded;
    }
  }

  @override
  void write(BinaryWriter writer, DocumentStatus obj) {
    switch (obj) {
      case DocumentStatus.notUploaded:
        writer.writeByte(0);
        break;
      case DocumentStatus.uploaded:
        writer.writeByte(1);
        break;
      case DocumentStatus.underReview:
        writer.writeByte(2);
        break;
      case DocumentStatus.verified:
        writer.writeByte(3);
        break;
      case DocumentStatus.rejected:
        writer.writeByte(4);
        break;
      case DocumentStatus.expired:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DocumentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CommentTypeAdapter extends TypeAdapter<CommentType> {
  @override
  final int typeId = 40;

  @override
  CommentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CommentType.info;
      case 1:
        return CommentType.warning;
      case 2:
        return CommentType.error;
      case 3:
        return CommentType.approval;
      case 4:
        return CommentType.rejection;
      default:
        return CommentType.info;
    }
  }

  @override
  void write(BinaryWriter writer, CommentType obj) {
    switch (obj) {
      case CommentType.info:
        writer.writeByte(0);
        break;
      case CommentType.warning:
        writer.writeByte(1);
        break;
      case CommentType.error:
        writer.writeByte(2);
        break;
      case CommentType.approval:
        writer.writeByte(3);
        break;
      case CommentType.rejection:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RiderKYC _$RiderKYCFromJson(Map<String, dynamic> json) => RiderKYC(
      riderId: json['riderId'] as String,
      status: $enumDecode(_$KYCStatusEnumMap, json['status']),
      documents: (json['documents'] as List<dynamic>)
          .map((e) => KYCDocument.fromJson(e as Map<String, dynamic>))
          .toList(),
      submittedAt: json['submittedAt'] == null
          ? null
          : DateTime.parse(json['submittedAt'] as String),
      reviewedAt: json['reviewedAt'] == null
          ? null
          : DateTime.parse(json['reviewedAt'] as String),
      approvedAt: json['approvedAt'] == null
          ? null
          : DateTime.parse(json['approvedAt'] as String),
      rejectionReason: json['rejectionReason'] as String?,
      pendingDocuments: (json['pendingDocuments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
      reviewer: json['reviewer'] == null
          ? null
          : KYCReviewer.fromJson(json['reviewer'] as Map<String, dynamic>),
      comments: (json['comments'] as List<dynamic>)
          .map((e) => KYCComment.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RiderKYCToJson(RiderKYC instance) => <String, dynamic>{
      'riderId': instance.riderId,
      'status': _$KYCStatusEnumMap[instance.status]!,
      'documents': instance.documents,
      'submittedAt': instance.submittedAt?.toIso8601String(),
      'reviewedAt': instance.reviewedAt?.toIso8601String(),
      'approvedAt': instance.approvedAt?.toIso8601String(),
      'rejectionReason': instance.rejectionReason,
      'pendingDocuments': instance.pendingDocuments,
      'completionPercentage': instance.completionPercentage,
      'reviewer': instance.reviewer,
      'comments': instance.comments,
    };

const _$KYCStatusEnumMap = {
  KYCStatus.notStarted: 'notStarted',
  KYCStatus.inProgress: 'inProgress',
  KYCStatus.underReview: 'underReview',
  KYCStatus.approved: 'approved',
  KYCStatus.rejected: 'rejected',
  KYCStatus.resubmissionRequired: 'resubmissionRequired',
  KYCStatus.pending: 'pending',
  KYCStatus.incomplete: 'incomplete',
};

KYCDocument _$KYCDocumentFromJson(Map<String, dynamic> json) => KYCDocument(
      id: json['id'] as String,
      type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
      documentNumber: json['documentNumber'] as String,
      imageUrls:
          (json['imageUrls'] as List<dynamic>).map((e) => e as String).toList(),
      status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
      uploadedAt: DateTime.parse(json['uploadedAt'] as String),
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
      rejectionReason: json['rejectionReason'] as String?,
      extractedData: json['extractedData'] as Map<String, dynamic>,
      confidenceScore: (json['confidenceScore'] as num).toDouble(),
    );

Map<String, dynamic> _$KYCDocumentToJson(KYCDocument instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$DocumentTypeEnumMap[instance.type]!,
      'documentNumber': instance.documentNumber,
      'imageUrls': instance.imageUrls,
      'status': _$DocumentStatusEnumMap[instance.status]!,
      'uploadedAt': instance.uploadedAt.toIso8601String(),
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
      'rejectionReason': instance.rejectionReason,
      'extractedData': instance.extractedData,
      'confidenceScore': instance.confidenceScore,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.aadhaar: 'aadhaar',
  DocumentType.pan: 'pan',
  DocumentType.drivingLicense: 'drivingLicense',
  DocumentType.vehicleRC: 'vehicleRC',
  DocumentType.insurance: 'insurance',
  DocumentType.pollution: 'pollution',
  DocumentType.profilePhoto: 'profilePhoto',
  DocumentType.vehiclePhoto: 'vehiclePhoto',
  DocumentType.bankPassbook: 'bankPassbook',
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.notUploaded: 'notUploaded',
  DocumentStatus.uploaded: 'uploaded',
  DocumentStatus.underReview: 'underReview',
  DocumentStatus.verified: 'verified',
  DocumentStatus.rejected: 'rejected',
  DocumentStatus.expired: 'expired',
};

KYCReviewer _$KYCReviewerFromJson(Map<String, dynamic> json) => KYCReviewer(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      reviewedAt: DateTime.parse(json['reviewedAt'] as String),
    );

Map<String, dynamic> _$KYCReviewerToJson(KYCReviewer instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'reviewedAt': instance.reviewedAt.toIso8601String(),
    };

KYCComment _$KYCCommentFromJson(Map<String, dynamic> json) => KYCComment(
      id: json['id'] as String,
      message: json['message'] as String,
      authorId: json['authorId'] as String,
      authorName: json['authorName'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      type: $enumDecode(_$CommentTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$KYCCommentToJson(KYCComment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'message': instance.message,
      'authorId': instance.authorId,
      'authorName': instance.authorName,
      'timestamp': instance.timestamp.toIso8601String(),
      'type': _$CommentTypeEnumMap[instance.type]!,
    };

const _$CommentTypeEnumMap = {
  CommentType.info: 'info',
  CommentType.warning: 'warning',
  CommentType.error: 'error',
  CommentType.approval: 'approval',
  CommentType.rejection: 'rejection',
};

WithdrawalRequest _$WithdrawalRequestFromJson(Map<String, dynamic> json) =>
    WithdrawalRequest(
      id: json['id'] as String,
      riderId: json['riderId'] as String,
      amount: (json['amount'] as num).toDouble(),
      method: $enumDecode(_$WithdrawalMethodEnumMap, json['method']),
      status: $enumDecode(_$WithdrawalStatusEnumMap, json['status']),
      requestedAt: DateTime.parse(json['requestedAt'] as String),
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      transactionId: json['transactionId'] as String?,
      failureReason: json['failureReason'] as String?,
      processingFee: (json['processingFee'] as num).toDouble(),
      netAmount: (json['netAmount'] as num).toDouble(),
      paymentDetails: json['paymentDetails'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$WithdrawalRequestToJson(WithdrawalRequest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'riderId': instance.riderId,
      'amount': instance.amount,
      'method': _$WithdrawalMethodEnumMap[instance.method]!,
      'status': _$WithdrawalStatusEnumMap[instance.status]!,
      'requestedAt': instance.requestedAt.toIso8601String(),
      'processedAt': instance.processedAt?.toIso8601String(),
      'transactionId': instance.transactionId,
      'failureReason': instance.failureReason,
      'processingFee': instance.processingFee,
      'netAmount': instance.netAmount,
      'paymentDetails': instance.paymentDetails,
    };

const _$WithdrawalMethodEnumMap = {
  WithdrawalMethod.bankTransfer: 'bankTransfer',
  WithdrawalMethod.upi: 'upi',
  WithdrawalMethod.paytm: 'paytm',
  WithdrawalMethod.phonepe: 'phonepe',
  WithdrawalMethod.googlepay: 'googlepay',
  WithdrawalMethod.amazonpay: 'amazonpay',
};

const _$WithdrawalStatusEnumMap = {
  WithdrawalStatus.pending: 'pending',
  WithdrawalStatus.processing: 'processing',
  WithdrawalStatus.completed: 'completed',
  WithdrawalStatus.failed: 'failed',
  WithdrawalStatus.cancelled: 'cancelled',
};

BonusIncentive _$BonusIncentiveFromJson(Map<String, dynamic> json) =>
    BonusIncentive(
      id: json['id'] as String,
      riderId: json['riderId'] as String,
      type: $enumDecode(_$BonusTypeEnumMap, json['type']),
      title: json['title'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num).toDouble(),
      status: $enumDecode(_$BonusStatusEnumMap, json['status']),
      validFrom: DateTime.parse(json['validFrom'] as String),
      validUntil: DateTime.parse(json['validUntil'] as String),
      criteria: json['criteria'] as Map<String, dynamic>,
      progress: (json['progress'] as num).toDouble(),
      earnedAt: json['earnedAt'] == null
          ? null
          : DateTime.parse(json['earnedAt'] as String),
    );

Map<String, dynamic> _$BonusIncentiveToJson(BonusIncentive instance) =>
    <String, dynamic>{
      'id': instance.id,
      'riderId': instance.riderId,
      'type': _$BonusTypeEnumMap[instance.type]!,
      'title': instance.title,
      'description': instance.description,
      'amount': instance.amount,
      'status': _$BonusStatusEnumMap[instance.status]!,
      'validFrom': instance.validFrom.toIso8601String(),
      'validUntil': instance.validUntil.toIso8601String(),
      'criteria': instance.criteria,
      'progress': instance.progress,
      'earnedAt': instance.earnedAt?.toIso8601String(),
    };

const _$BonusTypeEnumMap = {
  BonusType.peakHour: 'peakHour',
  BonusType.dailyTarget: 'dailyTarget',
  BonusType.weeklyTarget: 'weeklyTarget',
  BonusType.ratingBonus: 'ratingBonus',
  BonusType.referral: 'referral',
  BonusType.loyalty: 'loyalty',
  BonusType.surge: 'surge',
  BonusType.special: 'special',
};

const _$BonusStatusEnumMap = {
  BonusStatus.active: 'active',
  BonusStatus.earned: 'earned',
  BonusStatus.expired: 'expired',
  BonusStatus.cancelled: 'cancelled',
};
