import 'dart:async';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/rider_profile.dart';
import '../../../../demo/rider_demo_data.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/utils/app_logger.dart';

class RiderProfileService {
  final StorageService _storageService;
  final StreamController<RiderProfile?> _profileController = StreamController<RiderProfile?>.broadcast();

  RiderProfileService(this._storageService);

  Stream<RiderProfile?> get profileStream => _profileController.stream;

  Future<RiderProfile?> getCurrentProfile() async {
    try {
      // In a real app, this would fetch from API/database
      // For demo, we'll use the first rider profile
      final profile = RiderDemoData.riderProfiles.first;
      _profileController.add(profile);
      return profile;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get current profile', e, stackTrace);
      return null;
    }
  }

  Future<RiderProfile?> getProfile(String riderId) async {
    try {
      final profile = RiderDemoData.getRiderProfile(riderId);
      return profile;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get profile for rider $riderId', e, stackTrace);
      return null;
    }
  }

  Future<bool> updateProfile(RiderProfile profile) async {
    try {
      // In a real app, this would update via API
      // For demo, we'll simulate success
      await Future.delayed(Duration(milliseconds: 500));
      _profileController.add(profile);
      AppLogger.info('Profile updated successfully for rider ${profile.id}');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update profile', e, stackTrace);
      return false;
    }
  }

  Future<bool> updateOnlineStatus(String riderId, bool isOnline) async {
    try {
      // In a real app, this would update via API
      await Future.delayed(Duration(milliseconds: 200));
      
      final currentProfile = await getProfile(riderId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(isOnline: isOnline);
        _profileController.add(updatedProfile);
      }
      
      AppLogger.info('Online status updated: $isOnline for rider $riderId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update online status', e, stackTrace);
      return false;
    }
  }

  Future<bool> updateLocation(String riderId, String location) async {
    try {
      // In a real app, this would update via API
      await Future.delayed(Duration(milliseconds: 100));
      
      final currentProfile = await getProfile(riderId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(currentLocation: location);
        _profileController.add(updatedProfile);
      }
      
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update location', e, stackTrace);
      return false;
    }
  }

  Future<bool> updateVehicleInfo(String riderId, VehicleInfo vehicleInfo) async {
    try {
      // In a real app, this would update via API
      await Future.delayed(Duration(milliseconds: 500));
      
      final currentProfile = await getProfile(riderId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(vehicleInfo: vehicleInfo);
        _profileController.add(updatedProfile);
      }
      
      AppLogger.info('Vehicle info updated for rider $riderId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update vehicle info', e, stackTrace);
      return false;
    }
  }

  Future<bool> updateBankDetails(String riderId, BankDetails bankDetails) async {
    try {
      // In a real app, this would update via API
      await Future.delayed(Duration(milliseconds: 500));
      
      final currentProfile = await getProfile(riderId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(bankDetails: bankDetails);
        _profileController.add(updatedProfile);
      }
      
      AppLogger.info('Bank details updated for rider $riderId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update bank details', e, stackTrace);
      return false;
    }
  }

  Future<bool> updatePreferences(String riderId, RiderPreferences preferences) async {
    try {
      // In a real app, this would update via API
      await Future.delayed(Duration(milliseconds: 300));
      
      final currentProfile = await getProfile(riderId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(preferences: preferences);
        _profileController.add(updatedProfile);
      }
      
      AppLogger.info('Preferences updated for rider $riderId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update preferences', e, stackTrace);
      return false;
    }
  }

  Future<String?> uploadProfileImage(String riderId, File imageFile) async {
    try {
      // In a real app, this would upload to cloud storage
      await Future.delayed(Duration(seconds: 2));
      
      // Simulate successful upload
      final imageUrl = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150';
      
      final currentProfile = await getProfile(riderId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(profileImageUrl: imageUrl);
        _profileController.add(updatedProfile);
      }
      
      AppLogger.info('Profile image uploaded for rider $riderId');
      return imageUrl;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to upload profile image', e, stackTrace);
      return null;
    }
  }

  Future<List<String>?> uploadVehicleImages(String riderId, List<File> imageFiles) async {
    try {
      // In a real app, this would upload to cloud storage
      await Future.delayed(Duration(seconds: 3));
      
      // Simulate successful upload
      final imageUrls = imageFiles.map((file) => 
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300'
      ).toList();
      
      AppLogger.info('Vehicle images uploaded for rider $riderId');
      return imageUrls;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to upload vehicle images', e, stackTrace);
      return null;
    }
  }

  Future<bool> verifyPhoneNumber(String phoneNumber, String otp) async {
    try {
      // In a real app, this would verify via SMS service
      await Future.delayed(Duration(seconds: 1));
      
      // Simulate verification (accept any 6-digit OTP for demo)
      if (otp.length == 6 && RegExp(r'^\d+$').hasMatch(otp)) {
        AppLogger.info('Phone number verified: $phoneNumber');
        return true;
      }
      
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to verify phone number', e, stackTrace);
      return false;
    }
  }

  Future<bool> sendOTP(String phoneNumber) async {
    try {
      // In a real app, this would send OTP via SMS service
      await Future.delayed(Duration(seconds: 1));
      
      AppLogger.info('OTP sent to: $phoneNumber');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to send OTP', e, stackTrace);
      return false;
    }
  }

  Future<List<String>> getWorkingAreas() async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      return RiderDemoData.workingAreas;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get working areas', e, stackTrace);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getVehicleTypes() async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      return RiderDemoData.vehicleTypes;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get vehicle types', e, stackTrace);
      return [];
    }
  }

  Future<List<String>> getVehicleBrands(String vehicleType) async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      return RiderDemoData.vehicleBrands[vehicleType] ?? [];
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get vehicle brands', e, stackTrace);
      return [];
    }
  }

  Future<List<Map<String, String>>> getSupportedLanguages() async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      return RiderDemoData.supportedLanguages;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get supported languages', e, stackTrace);
      return [];
    }
  }

  Future<List<Map<String, String>>> getBankList() async {
    try {
      await Future.delayed(Duration(milliseconds: 200));
      return RiderDemoData.indianBanks;
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get bank list', e, stackTrace);
      return [];
    }
  }

  void dispose() {
    _profileController.close();
  }
}

// Provider for RiderProfileService
final riderProfileServiceProvider = Provider<RiderProfileService>((ref) {
  final storageService = ref.read(storageServiceProvider);
  return RiderProfileService(storageService);
});

// Provider for current rider profile
final currentRiderProfileProvider = StreamProvider<RiderProfile?>((ref) {
  final service = ref.read(riderProfileServiceProvider);
  return service.profileStream;
});

// Provider for rider profile by ID
final riderProfileProvider = FutureProvider.family<RiderProfile?, String>((ref, riderId) {
  final service = ref.read(riderProfileServiceProvider);
  return service.getProfile(riderId);
});

// Provider for working areas
final workingAreasProvider = FutureProvider<List<String>>((ref) {
  final service = ref.read(riderProfileServiceProvider);
  return service.getWorkingAreas();
});

// Provider for vehicle types
final vehicleTypesProvider = FutureProvider<List<Map<String, dynamic>>>((ref) {
  final service = ref.read(riderProfileServiceProvider);
  return service.getVehicleTypes();
});

// Provider for vehicle brands
final vehicleBrandsProvider = FutureProvider.family<List<String>, String>((ref, vehicleType) {
  final service = ref.read(riderProfileServiceProvider);
  return service.getVehicleBrands(vehicleType);
});

// Provider for supported languages
final supportedLanguagesProvider = FutureProvider<List<Map<String, String>>>((ref) {
  final service = ref.read(riderProfileServiceProvider);
  return service.getSupportedLanguages();
});

// Provider for bank list
final bankListProvider = FutureProvider<List<Map<String, String>>>((ref) {
  final service = ref.read(riderProfileServiceProvider);
  return service.getBankList();
});
