@echo off
REM Script to copy APK outputs to consolidated build directory

echo Copying APK outputs to consolidated build directory...

REM Create target directories if they don't exist
if not exist "build\android\outputs\apk\release" mkdir "build\android\outputs\apk\release"
if not exist "build\android\outputs\apk\debug" mkdir "build\android\outputs\apk\debug"

REM Copy release APKs if they exist
if exist "android\app\build\outputs\flutter-apk\app-release.apk" (
    copy "android\app\build\outputs\flutter-apk\app-release.apk" "build\android\outputs\apk\release\"
    echo Copied release APK to build\android\outputs\apk\release\
)

if exist "android\app\build\outputs\apk\release\app-release.apk" (
    copy "android\app\build\outputs\apk\release\app-release.apk" "build\android\outputs\apk\release\"
    echo Copied release APK to build\android\outputs\apk\release\
)

REM Copy debug APKs if they exist
if exist "android\app\build\outputs\flutter-apk\app-debug.apk" (
    copy "android\app\build\outputs\flutter-apk\app-debug.apk" "build\android\outputs\apk\debug\"
    echo Copied debug APK to build\android\outputs\apk\debug\
)

if exist "android\app\build\outputs\apk\debug\app-debug.apk" (
    copy "android\app\build\outputs\apk\debug\app-debug.apk" "build\android\outputs\apk\debug\"
    echo Copied debug APK to build\android\outputs\apk\debug\
)

echo APK copy operation completed.
echo.
echo APK files are now available in:
echo - build\android\outputs\apk\release\ (for release APKs)
echo - build\android\outputs\apk\debug\ (for debug APKs)
echo.
pause
