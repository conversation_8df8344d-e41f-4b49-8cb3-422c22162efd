<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow cleartext traffic for development -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Allow localhost for Flutter hot reload -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
        <!-- Add your development machine's IP address here -->
        <!-- Example: <domain includeSubdomains="true">*************</domain> -->
    </domain-config>

    <!-- Allow payment gateway domains -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.razorpay.com</domain>
        <domain includeSubdomains="true">checkout.razorpay.com</domain>
        <domain includeSubdomains="true">lumberjack.razorpay.com</domain>
    </domain-config>

    <!-- For production builds, restrict cleartext traffic -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
