import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';

class RatingWidget extends StatelessWidget {
  final double rating;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool allowHalfRating;
  final bool showRatingText;
  final String? ratingText;

  const RatingWidget({
    super.key,
    required this.rating,
    this.size = 20,
    this.activeColor,
    this.inactiveColor,
    this.allowHalfRating = true,
    this.showRatingText = false,
    this.ratingText,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(5, (index) {
          return Icon(
            _getStarIcon(index + 1),
            size: size,
            color: _getStarColor(index + 1),
          );
        }),
        if (showRatingText) ...[
          const SizedBox(width: 8),
          Text(
            ratingText ?? rating.toStringAsFixed(1),
            style: TextStyle(
              fontSize: size * 0.8,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ],
    );
  }

  IconData _getStarIcon(int position) {
    if (allowHalfRating) {
      if (rating >= position) {
        return Icons.star;
      } else if (rating >= position - 0.5) {
        return Icons.star_half;
      } else {
        return Icons.star_border;
      }
    } else {
      return rating >= position ? Icons.star : Icons.star_border;
    }
  }

  Color _getStarColor(int position) {
    final active = activeColor ?? AppColors.warning;
    final inactive = inactiveColor ?? AppColors.grey300;
    
    if (allowHalfRating) {
      if (rating >= position) {
        return active;
      } else if (rating >= position - 0.5) {
        return active;
      } else {
        return inactive;
      }
    } else {
      return rating >= position ? active : inactive;
    }
  }
}

class InteractiveRatingWidget extends StatefulWidget {
  final double initialRating;
  final ValueChanged<double> onRatingChanged;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;

  const InteractiveRatingWidget({
    super.key,
    this.initialRating = 0,
    required this.onRatingChanged,
    this.size = 32,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  State<InteractiveRatingWidget> createState() => _InteractiveRatingWidgetState();
}

class _InteractiveRatingWidgetState extends State<InteractiveRatingWidget> {
  late double _currentRating;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _currentRating = (index + 1).toDouble();
            });
            widget.onRatingChanged(_currentRating);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            child: Icon(
              _currentRating > index ? Icons.star : Icons.star_border,
              size: widget.size,
              color: _currentRating > index
                  ? (widget.activeColor ?? AppColors.warning)
                  : (widget.inactiveColor ?? AppColors.grey300),
            ),
          ),
        );
      }),
    );
  }
}

class RatingBar extends StatelessWidget {
  final int starCount;
  final int totalRatings;
  final double percentage;

  const RatingBar({
    super.key,
    required this.starCount,
    required this.totalRatings,
    required this.percentage,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          '$starCount',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 4),
        const Icon(
          Icons.star,
          size: 16,
          color: AppColors.warning,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: AppColors.grey200,
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.warning),
            minHeight: 6,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '$totalRatings',
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}

class RatingSummaryCard extends StatelessWidget {
  final double averageRating;
  final int totalRatings;
  final Map<int, int> ratingDistribution;
  final List<String> topTags;

  const RatingSummaryCard({
    super.key,
    required this.averageRating,
    required this.totalRatings,
    required this.ratingDistribution,
    required this.topTags,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      averageRating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    RatingWidget(
                      rating: averageRating,
                      size: 20,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$totalRatings reviews',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    children: [
                      for (int i = 5; i >= 1; i--)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: RatingBar(
                            starCount: i,
                            totalRatings: ratingDistribution[i] ?? 0,
                            percentage: totalRatings > 0
                                ? ((ratingDistribution[i] ?? 0) / totalRatings) * 100
                                : 0,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            if (topTags.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'Most mentioned:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: topTags.map((tag) {
                  return Chip(
                    label: Text(
                      tag,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: AppColors.primaryBlue.withOpacity(0.1),
                    labelStyle: TextStyle(color: AppColors.primaryBlue),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class RatingListItem extends StatelessWidget {
  final String userName;
  final String? userAvatar;
  final double rating;
  final String? review;
  final List<String> tags;
  final List<String> images;
  final DateTime createdAt;
  final int helpfulCount;
  final bool isVerified;
  final VoidCallback? onHelpfulTap;
  final VoidCallback? onReportTap;

  const RatingListItem({
    super.key,
    required this.userName,
    this.userAvatar,
    required this.rating,
    this.review,
    this.tags = const [],
    this.images = const [],
    required this.createdAt,
    this.helpfulCount = 0,
    this.isVerified = false,
    this.onHelpfulTap,
    this.onReportTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info and rating
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: userAvatar != null
                      ? NetworkImage(userAvatar!)
                      : null,
                  child: userAvatar == null
                      ? Text(userName.isNotEmpty ? userName[0].toUpperCase() : 'U')
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            userName,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (isVerified) ...[
                            const SizedBox(width: 4),
                            const Icon(
                              Icons.verified,
                              size: 16,
                              color: AppColors.success,
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 2),
                      RatingWidget(
                        rating: rating,
                        size: 16,
                      ),
                    ],
                  ),
                ),
                Text(
                  _formatDate(createdAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            
            // Review text
            if (review != null && review!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                review!,
                style: const TextStyle(fontSize: 14),
              ),
            ],
            
            // Tags
            if (tags.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 6,
                runSpacing: 4,
                children: tags.map((tag) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.success.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.success.withOpacity(0.3)),
                    ),
                    child: Text(
                      tag,
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.success,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
            
            // Images
            if (images.isNotEmpty) ...[
              const SizedBox(height: 12),
              SizedBox(
                height: 80,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: images.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          images[index],
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
            
            // Actions
            const SizedBox(height: 12),
            Row(
              children: [
                TextButton.icon(
                  onPressed: onHelpfulTap,
                  icon: const Icon(Icons.thumb_up_outlined, size: 16),
                  label: Text('Helpful ($helpfulCount)'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.textSecondary,
                    textStyle: const TextStyle(fontSize: 12),
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: onReportTap,
                  icon: const Icon(Icons.flag_outlined, size: 16),
                  label: const Text('Report'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.textSecondary,
                    textStyle: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
