# Multi-App Flutter Project Guide

This project contains 3 separate Flutter apps that can run independently:

## 📱 Apps Overview

| App | Package Name | Main File | Theme | Purpose |
|-----|-------------|-----------|-------|---------|
| **User App** | `com.projek.user` | `lib/main.dart` | Green | Food ordering and booking |
| **Rider App** | `com.projek.rider` | `lib/main_rider.dart` | Green | Delivery tracking and management |
| **Seller App** | `com.projek.seller` | `lib/main_seller.dart` | Blue | Restaurant/service management |

## 🎯 **NEW: Complete Three-App Ecosystem**

The project now includes a **complete order flow integration**:

**User App** → Places order → **Seller App** → Prepares & assigns → **Rider App** → Delivers

### **🛒 User App Features:**
- Browse restaurants and menu items
- Add items to cart and place orders
- Real-time order tracking
- Payment integration
- Order history and favorites

### **🏪 Seller App Features:**
- **Dashboard:** Today's orders, earnings, quick stats
- **Order Management:** Accept/reject orders, track preparation
- **Product Management:** Add/edit menu items, manage inventory
- **Earnings Analytics:** Revenue tracking and financial metrics
- **Profile Management:** Restaurant details and settings

### **🏍️ Rider App Features:**
- **Home Dashboard:** Online/offline toggle, daily summary
- **Order Management:** Active deliveries, pickup notifications
- **Earnings Tracking:** Daily earnings, trip history
- **Navigation:** GPS integration for deliveries
- **Profile:** Vehicle details, ratings, documents

## 🚀 Running Apps

### Method 1: Using Flutter Commands

**User App:**
```bash
flutter run --target lib/main.dart --flavor userDev
```

**Rider App:**
```bash
flutter run --target lib/main_rider.dart --flavor riderDev
```

**Seller App:**
```bash
flutter run --target lib/main_seller.dart --flavor sellerDev
```

### Method 2: Using Scripts

**Windows:**
```bash
# User App
scripts\run_user.bat

# Rider App
scripts\run_rider.bat

# Seller App
scripts\run_seller.bat
```

**Linux/Mac:**
```bash
# Make scripts executable (first time only)
chmod +x scripts/*.sh

# User App
./scripts/run_user.sh

# Rider App
./scripts/run_rider.sh

# Seller App
./scripts/run_seller.sh
```

## 🔧 Build Configurations

Each app has different build flavors:

### App Flavors:
- `user` - User app variant
- `rider` - Rider app variant  
- `seller` - Seller app variant

### Environment Flavors:
- `dev` - Development environment
- `prod` - Production environment

### Combined Flavors:
- `userDev` / `userProd`
- `riderDev` / `riderProd`
- `sellerDev` / `sellerProd`

## 📦 Building APKs

**Debug APKs:**
```bash
# User App
flutter build apk --target lib/main.dart --flavor userDev

# Rider App
flutter build apk --target lib/main_rider.dart --flavor riderDev

# Seller App
flutter build apk --target lib/main_seller.dart --flavor sellerDev
```

**Release APKs:**
```bash
# User App
flutter build apk --target lib/main.dart --flavor userProd --release

# Rider App
flutter build apk --target lib/main_rider.dart --flavor riderProd --release

# Seller App
flutter build apk --target lib/main_seller.dart --flavor sellerProd --release
```

## 🏗️ Project Structure

```
lib/
├── main.dart                 # User App entry point
├── main_rider.dart          # Rider App entry point
├── main_seller.dart         # Seller App entry point
├── core/                    # Shared core functionality
├── features/
│   ├── user/               # User app features
│   ├── rider/              # Rider app features
│   └── seller/             # Seller app features
└── shared/                 # Shared components
```

## ⚠️ Important Notes

1. **Each app runs independently** - They have different package names and won't overwrite each other
2. **Use flavors** - Always specify `--flavor` when running apps to ensure proper separation
3. **Hot reload works** - You can use `r` for hot reload and `R` for hot restart
4. **Multiple devices** - You can run different apps on different devices simultaneously
5. **Complete Integration** - All three apps work together in a seamless order flow
6. **Modern UI** - Clean, professional design with Material Design principles
7. **Demo Data** - Comprehensive demo data for testing all features

## 🐛 Troubleshooting

**Problem:** Apps overwriting each other
**Solution:** Always use `--flavor` parameter when running apps

**Problem:** Build errors
**Solution:** Run `flutter clean` and rebuild

**Problem:** Permission issues with scripts
**Solution:** Make scripts executable with `chmod +x scripts/*.sh`

## 🔄 Switching Between Apps

To switch from one app to another:

1. Stop the current app (`q` in terminal or Ctrl+C)
2. Run the desired app with the correct flavor
3. The new app will install with its own package name

Each app maintains its own:
- App data and preferences
- Database files
- Cached data
- User sessions

## 🔄 **Complete Order Flow Integration**

The three apps work together in a seamless workflow:

### **1. User Places Order (User App)**
- Browse restaurants and menu items
- Add items to cart
- Enter delivery address and payment details
- Place order and receive confirmation

### **2. Seller Manages Order (Seller App)**
- Receive order notification
- Accept or reject the order
- Prepare the food/items
- Mark order as ready for pickup
- Assign to available rider

### **3. Rider Delivers Order (Rider App)**
- Receive pickup notification
- Navigate to restaurant
- Pick up the order
- Navigate to customer location
- Complete delivery

### **4. Real-time Updates**
- All apps receive real-time status updates
- Users can track their orders live
- Sellers can monitor preparation progress
- Riders can manage multiple deliveries

## 📋 **Testing the Complete Flow**

To test the full integration:

1. **Run User App:** `flutter run --target lib/main.dart --flavor userDev`
2. **Run Seller App:** `flutter run --target lib/main_seller.dart --flavor sellerDev`
3. **Run Rider App:** `flutter run --target lib/main_rider.dart --flavor riderDev`

All three apps will install as separate applications and can run simultaneously for complete testing.

## 📚 **Additional Resources**

- See `THREE_APP_INTEGRATION_GUIDE.md` for detailed integration documentation
- Check `lib/shared/models/order_flow_models.dart` for shared data models
- Review `lib/demo/` folder for comprehensive demo data
