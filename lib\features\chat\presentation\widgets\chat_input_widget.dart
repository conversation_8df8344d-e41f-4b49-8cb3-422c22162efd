import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class ChatInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final VoidCallback onSendMessage;
  final VoidCallback onAttachmentTap;
  final VoidCallback onVoiceRecordStart;
  final VoidCallback onVoiceRecordEnd;
  final bool isTyping;
  final bool hasSelectedMedia;

  const ChatInputWidget({
    super.key,
    required this.controller,
    required this.onSendMessage,
    required this.onAttachmentTap,
    required this.onVoiceRecordStart,
    required this.onVoiceRecordEnd,
    this.isTyping = false,
    this.hasSelectedMedia = false,
  });

  @override
  State<ChatInputWidget> createState() => _ChatInputWidgetState();
}

class _ChatInputWidgetState extends State<ChatInputWidget>
    with TickerProviderStateMixin {
  late AnimationController _sendButtonController;
  late AnimationController _voiceButtonController;
  late Animation<double> _sendButtonAnimation;
  late Animation<double> _voiceButtonAnimation;
  
  bool _isRecording = false;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    
    _sendButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _voiceButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _sendButtonAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sendButtonController,
      curve: Curves.easeInOut,
    ));
    
    _voiceButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _voiceButtonController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _sendButtonController.dispose();
    _voiceButtonController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ChatInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isTyping != oldWidget.isTyping || 
        widget.hasSelectedMedia != oldWidget.hasSelectedMedia) {
      if (widget.isTyping || widget.hasSelectedMedia) {
        _sendButtonController.forward();
        _voiceButtonController.forward();
      } else {
        _sendButtonController.reverse();
        _voiceButtonController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Attachment button
          _buildAttachmentButton(),
          const SizedBox(width: 8),
          
          // Text input
          Expanded(child: _buildTextInput()),
          const SizedBox(width: 8),
          
          // Send/Voice button
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildAttachmentButton() {
    return GestureDetector(
      onTap: widget.onAttachmentTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.primaryBlue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          Icons.attach_file,
          color: AppColors.primaryBlue,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildTextInput() {
    return Container(
      constraints: const BoxConstraints(
        minHeight: 40,
        maxHeight: 120,
      ),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: TextField(
        controller: widget.controller,
        maxLines: null,
        textCapitalization: TextCapitalization.sentences,
        decoration: InputDecoration(
          hintText: 'Type a message...',
          hintStyle: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 10,
          ),
          suffixIcon: _buildInputActions(),
        ),
        style: AppTextStyles.bodyMedium,
        onChanged: (text) {
          // Handle typing indicator
        },
      ),
    );
  }

  Widget? _buildInputActions() {
    if (!widget.isTyping && !widget.hasSelectedMedia) return null;
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Emoji button
        GestureDetector(
          onTap: _showEmojiPicker,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Icon(
              Icons.emoji_emotions_outlined,
              color: AppColors.textSecondary,
              size: 20,
            ),
          ),
        ),
        
        // Camera button
        GestureDetector(
          onTap: _quickCamera,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Icon(
              Icons.camera_alt_outlined,
              color: AppColors.textSecondary,
              size: 20,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton() {
    return GestureDetector(
      onTap: _handleActionButtonTap,
      onLongPressStart: (_) => _startVoiceRecording(),
      onLongPressEnd: (_) => _endVoiceRecording(),
      child: AnimatedBuilder(
        animation: Listenable.merge([_sendButtonAnimation, _voiceButtonAnimation]),
        builder: (context, child) {
          return Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _isRecording
                    ? [AppColors.error, AppColors.error.withValues(alpha: 0.8)]
                    : [AppColors.primaryBlue, AppColors.secondaryOrange],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: (_isRecording ? AppColors.error : AppColors.primaryBlue)
                      .withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Send button
                if (_sendButtonAnimation.value > 0)
                  Opacity(
                    opacity: _sendButtonAnimation.value,
                    child: Transform.scale(
                      scale: _sendButtonAnimation.value,
                      child: const Center(
                        child: Icon(
                          Icons.send,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                
                // Voice button
                if (_voiceButtonAnimation.value > 0)
                  Opacity(
                    opacity: _voiceButtonAnimation.value,
                    child: Transform.scale(
                      scale: _voiceButtonAnimation.value,
                      child: Center(
                        child: Icon(
                          _isRecording ? Icons.stop : Icons.mic,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _handleActionButtonTap() {
    if (widget.isTyping || widget.hasSelectedMedia) {
      widget.onSendMessage();
    } else {
      // Quick voice message
      _toggleVoiceRecording();
    }
  }

  void _startVoiceRecording() {
    if (!widget.isTyping && !widget.hasSelectedMedia) {
      setState(() => _isRecording = true);
      widget.onVoiceRecordStart();
      
      // Show recording UI
      _showRecordingOverlay();
    }
  }

  void _endVoiceRecording() {
    if (_isRecording) {
      setState(() => _isRecording = false);
      widget.onVoiceRecordEnd();
      
      // Hide recording UI
      _hideRecordingOverlay();
    }
  }

  void _toggleVoiceRecording() {
    if (_isRecording) {
      _endVoiceRecording();
    } else {
      _startVoiceRecording();
    }
  }

  void _showEmojiPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.4,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Emoji grid
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 8,
                  childAspectRatio: 1,
                ),
                itemCount: _getEmojis().length,
                itemBuilder: (context, index) {
                  final emoji = _getEmojis()[index];
                  return GestureDetector(
                    onTap: () {
                      _insertEmoji(emoji);
                      Navigator.pop(context);
                    },
                    child: Container(
                      margin: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          emoji,
                          style: const TextStyle(fontSize: 24),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _quickCamera() {
    // Implement quick camera functionality
    widget.onAttachmentTap();
  }

  void _showRecordingOverlay() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.mic,
                color: AppColors.error,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'Recording...',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Release to send, tap to cancel',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _hideRecordingOverlay() {
    Navigator.of(context, rootNavigator: true).pop();
  }

  void _insertEmoji(String emoji) {
    final text = widget.controller.text;
    final selection = widget.controller.selection;
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      emoji,
    );
    
    widget.controller.value = widget.controller.value.copyWith(
      text: newText,
      selection: TextSelection.collapsed(
        offset: selection.start + emoji.length,
      ),
    );
  }

  List<String> _getEmojis() {
    return [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
      '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
      '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
      '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
      '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
      '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
      '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
      '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
      '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
      '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
      '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑',
      '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻',
      '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸',
      '😹', '😻', '😼', '😽', '🙀', '😿', '😾', '👋',
      '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞',
      '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇',
      '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜', '👏',
      '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅', '🤳',
    ];
  }
}
