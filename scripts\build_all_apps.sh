#!/bin/bash

echo "🏗️ Building All Projek Apps"
echo "============================"

# Create build directory
mkdir -p builds

# Build User App
echo "📱 Building User App..."
flutter build apk --target=lib/main_user.dart --release
mv build/app/outputs/flutter-apk/app-release.apk builds/projek-user-app.apk
echo "✅ User App built: builds/projek-user-app.apk"

# Build Rider App
echo "🚴 Building Rider App..."
flutter build apk --target=lib/main_rider.dart --release
mv build/app/outputs/flutter-apk/app-release.apk builds/projek-rider-app.apk
echo "✅ Rider App built: builds/projek-rider-app.apk"

# Build Seller App
echo "🏪 Building Seller App..."
flutter build apk --target=lib/main_seller.dart --release
mv build/app/outputs/flutter-apk/app-release.apk builds/projek-seller-app.apk
echo "✅ Seller App built: builds/projek-seller-app.apk"

echo "============================"
echo "🎉 All apps built successfully!"
echo "📁 Check the 'builds' folder for APK files"
