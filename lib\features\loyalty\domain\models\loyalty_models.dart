import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'loyalty_models.g.dart';

@HiveType(typeId: 60)
enum UserTier {
  @HiveField(0)
  bronze,
  @HiveField(1)
  silver,
  @HiveField(2)
  gold,
  @HiveField(3)
  platinum,
  @HiveField(4)
  diamond,
}

@HiveType(typeId: 61)
enum RewardType {
  @HiveField(0)
  points,
  @HiveField(1)
  cashback,
  @HiveField(2)
  discount,
  @HiveField(3)
  freeDelivery,
  @HiveField(4)
  voucher,
  @HiveField(5)
  referralBonus,
}

@HiveType(typeId: 62)
enum TransactionType {
  @HiveField(0)
  earned,
  @HiveField(1)
  redeemed,
  @HiveField(2)
  expired,
  @HiveField(3)
  bonus,
  @HiveField(4)
  refunded,
}

@HiveType(typeId: 63)
@JsonSerializable()
class LoyaltyAccount extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final int totalPoints;
  
  @HiveField(3)
  final int availablePoints;
  
  @HiveField(4)
  final double totalCashback;
  
  @HiveField(5)
  final double availableCashback;
  
  @HiveField(6)
  final UserTier currentTier;
  
  @HiveField(7)
  final int tierProgress; // Points towards next tier
  
  @HiveField(8)
  final int tierThreshold; // Points needed for next tier
  
  @HiveField(9)
  final double lifetimeSpending;
  
  @HiveField(10)
  final int totalReferrals;
  
  @HiveField(11)
  final DateTime createdAt;
  
  @HiveField(12)
  final DateTime updatedAt;
  
  @HiveField(13)
  final Map<String, dynamic> tierBenefits;

  const LoyaltyAccount({
    required this.id,
    required this.userId,
    this.totalPoints = 0,
    this.availablePoints = 0,
    this.totalCashback = 0.0,
    this.availableCashback = 0.0,
    this.currentTier = UserTier.bronze,
    this.tierProgress = 0,
    this.tierThreshold = 1000,
    this.lifetimeSpending = 0.0,
    this.totalReferrals = 0,
    required this.createdAt,
    required this.updatedAt,
    this.tierBenefits = const {},
  });

  factory LoyaltyAccount.fromJson(Map<String, dynamic> json) => _$LoyaltyAccountFromJson(json);
  Map<String, dynamic> toJson() => _$LoyaltyAccountToJson(this);

  double get tierProgressPercentage => tierThreshold > 0 ? (tierProgress / tierThreshold) * 100 : 0;
  
  String get nextTierName {
    switch (currentTier) {
      case UserTier.bronze:
        return 'Silver';
      case UserTier.silver:
        return 'Gold';
      case UserTier.gold:
        return 'Platinum';
      case UserTier.platinum:
        return 'Diamond';
      case UserTier.diamond:
        return 'Diamond (Max)';
    }
  }

  @override
  List<Object?> get props => [
    id, userId, totalPoints, availablePoints, totalCashback, availableCashback,
    currentTier, tierProgress, tierThreshold, lifetimeSpending, totalReferrals,
    createdAt, updatedAt, tierBenefits,
  ];
}

@HiveType(typeId: 64)
@JsonSerializable()
class RewardTransaction extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final TransactionType type;
  
  @HiveField(3)
  final RewardType rewardType;
  
  @HiveField(4)
  final int points;
  
  @HiveField(5)
  final double cashback;
  
  @HiveField(6)
  final String description;
  
  @HiveField(7)
  final String? orderId;
  
  @HiveField(8)
  final String? referenceId;
  
  @HiveField(9)
  final DateTime createdAt;
  
  @HiveField(10)
  final DateTime? expiresAt;
  
  @HiveField(11)
  final Map<String, dynamic> metadata;

  const RewardTransaction({
    required this.id,
    required this.userId,
    required this.type,
    required this.rewardType,
    this.points = 0,
    this.cashback = 0.0,
    required this.description,
    this.orderId,
    this.referenceId,
    required this.createdAt,
    this.expiresAt,
    this.metadata = const {},
  });

  factory RewardTransaction.fromJson(Map<String, dynamic> json) => _$RewardTransactionFromJson(json);
  Map<String, dynamic> toJson() => _$RewardTransactionToJson(this);

  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
  bool get isActive => !isExpired && (type == TransactionType.earned || type == TransactionType.bonus);

  @override
  List<Object?> get props => [
    id, userId, type, rewardType, points, cashback, description,
    orderId, referenceId, createdAt, expiresAt, metadata,
  ];
}

@HiveType(typeId: 65)
@JsonSerializable()
class ReferralProgram extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String referrerId;
  
  @HiveField(2)
  final String refereeId;
  
  @HiveField(3)
  final String referralCode;
  
  @HiveField(4)
  final int referrerRewardPoints;
  
  @HiveField(5)
  final double referrerRewardCashback;
  
  @HiveField(6)
  final int refereeRewardPoints;
  
  @HiveField(7)
  final double refereeRewardCashback;
  
  @HiveField(8)
  final DateTime referredAt;
  
  @HiveField(9)
  final DateTime? rewardedAt;
  
  @HiveField(10)
  final bool isCompleted;
  
  @HiveField(11)
  final String? completionOrderId;

  const ReferralProgram({
    required this.id,
    required this.referrerId,
    required this.refereeId,
    required this.referralCode,
    this.referrerRewardPoints = 500,
    this.referrerRewardCashback = 50.0,
    this.refereeRewardPoints = 200,
    this.refereeRewardCashback = 20.0,
    required this.referredAt,
    this.rewardedAt,
    this.isCompleted = false,
    this.completionOrderId,
  });

  factory ReferralProgram.fromJson(Map<String, dynamic> json) => _$ReferralProgramFromJson(json);
  Map<String, dynamic> toJson() => _$ReferralProgramToJson(this);

  @override
  List<Object?> get props => [
    id, referrerId, refereeId, referralCode, referrerRewardPoints, referrerRewardCashback,
    refereeRewardPoints, refereeRewardCashback, referredAt, rewardedAt, isCompleted, completionOrderId,
  ];
}

@HiveType(typeId: 66)
@JsonSerializable()
class RewardOffer extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String title;
  
  @HiveField(2)
  final String description;
  
  @HiveField(3)
  final RewardType type;
  
  @HiveField(4)
  final int pointsCost;
  
  @HiveField(5)
  final double cashbackValue;
  
  @HiveField(6)
  final double discountPercentage;
  
  @HiveField(7)
  final String? imageUrl;
  
  @HiveField(8)
  final List<UserTier> eligibleTiers;
  
  @HiveField(9)
  final DateTime validFrom;
  
  @HiveField(10)
  final DateTime validUntil;
  
  @HiveField(11)
  final int maxRedemptions;
  
  @HiveField(12)
  final int currentRedemptions;
  
  @HiveField(13)
  final bool isActive;
  
  @HiveField(14)
  final List<String> categories;
  
  @HiveField(15)
  final Map<String, dynamic> terms;

  const RewardOffer({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.pointsCost = 0,
    this.cashbackValue = 0.0,
    this.discountPercentage = 0.0,
    this.imageUrl,
    this.eligibleTiers = const [],
    required this.validFrom,
    required this.validUntil,
    this.maxRedemptions = -1, // -1 means unlimited
    this.currentRedemptions = 0,
    this.isActive = true,
    this.categories = const [],
    this.terms = const {},
  });

  factory RewardOffer.fromJson(Map<String, dynamic> json) => _$RewardOfferFromJson(json);
  Map<String, dynamic> toJson() => _$RewardOfferToJson(this);

  bool get isExpired => DateTime.now().isAfter(validUntil);
  bool get isAvailable => isActive && !isExpired && (maxRedemptions == -1 || currentRedemptions < maxRedemptions);
  bool get isUnlimited => maxRedemptions == -1;
  int get remainingRedemptions => isUnlimited ? -1 : maxRedemptions - currentRedemptions;

  @override
  List<Object?> get props => [
    id, title, description, type, pointsCost, cashbackValue, discountPercentage,
    imageUrl, eligibleTiers, validFrom, validUntil, maxRedemptions, currentRedemptions,
    isActive, categories, terms,
  ];
}

@HiveType(typeId: 67)
@JsonSerializable()
class RewardRedemption extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String offerId;
  
  @HiveField(3)
  final String offerTitle;
  
  @HiveField(4)
  final int pointsUsed;
  
  @HiveField(5)
  final double cashbackUsed;
  
  @HiveField(6)
  final DateTime redeemedAt;
  
  @HiveField(7)
  final DateTime? usedAt;
  
  @HiveField(8)
  final DateTime expiresAt;
  
  @HiveField(9)
  final String status; // 'active', 'used', 'expired'
  
  @HiveField(10)
  final String? orderId;
  
  @HiveField(11)
  final String redemptionCode;

  const RewardRedemption({
    required this.id,
    required this.userId,
    required this.offerId,
    required this.offerTitle,
    this.pointsUsed = 0,
    this.cashbackUsed = 0.0,
    required this.redeemedAt,
    this.usedAt,
    required this.expiresAt,
    this.status = 'active',
    this.orderId,
    required this.redemptionCode,
  });

  factory RewardRedemption.fromJson(Map<String, dynamic> json) => _$RewardRedemptionFromJson(json);
  Map<String, dynamic> toJson() => _$RewardRedemptionToJson(this);

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isUsed => status == 'used';
  bool get isActive => status == 'active' && !isExpired;

  @override
  List<Object?> get props => [
    id, userId, offerId, offerTitle, pointsUsed, cashbackUsed,
    redeemedAt, usedAt, expiresAt, status, orderId, redemptionCode,
  ];
}

// Tier configuration
class TierConfig {
  static const Map<UserTier, Map<String, dynamic>> tierConfigs = {
    UserTier.bronze: {
      'name': 'Bronze',
      'minSpending': 0.0,
      'pointsMultiplier': 1.0,
      'cashbackRate': 1.0, // 1%
      'benefits': ['Basic rewards', 'Standard support'],
      'color': 0xFFCD7F32,
      'nextTierThreshold': 5000.0,
    },
    UserTier.silver: {
      'name': 'Silver',
      'minSpending': 5000.0,
      'pointsMultiplier': 1.2,
      'cashbackRate': 1.5, // 1.5%
      'benefits': ['Enhanced rewards', 'Priority support', 'Free delivery on orders >₹500'],
      'color': 0xFFC0C0C0,
      'nextTierThreshold': 15000.0,
    },
    UserTier.gold: {
      'name': 'Gold',
      'minSpending': 15000.0,
      'pointsMultiplier': 1.5,
      'cashbackRate': 2.0, // 2%
      'benefits': ['Premium rewards', 'VIP support', 'Free delivery', 'Exclusive offers'],
      'color': 0xFFFFD700,
      'nextTierThreshold': 50000.0,
    },
    UserTier.platinum: {
      'name': 'Platinum',
      'minSpending': 50000.0,
      'pointsMultiplier': 2.0,
      'cashbackRate': 2.5, // 2.5%
      'benefits': ['Elite rewards', 'Dedicated support', 'Free delivery', 'Early access', 'Birthday rewards'],
      'color': 0xFFE5E4E2,
      'nextTierThreshold': 100000.0,
    },
    UserTier.diamond: {
      'name': 'Diamond',
      'minSpending': 100000.0,
      'pointsMultiplier': 3.0,
      'cashbackRate': 3.0, // 3%
      'benefits': ['Ultimate rewards', 'Personal concierge', 'Free delivery', 'Exclusive events', 'Custom offers'],
      'color': 0xFFB9F2FF,
      'nextTierThreshold': -1, // Max tier
    },
  };

  static Map<String, dynamic>? getTierConfig(UserTier tier) {
    return tierConfigs[tier];
  }

  static String getTierName(UserTier tier) {
    return tierConfigs[tier]?['name'] ?? tier.toString();
  }

  static double getPointsMultiplier(UserTier tier) {
    return tierConfigs[tier]?['pointsMultiplier'] ?? 1.0;
  }

  static double getCashbackRate(UserTier tier) {
    return tierConfigs[tier]?['cashbackRate'] ?? 1.0;
  }

  static List<String> getTierBenefits(UserTier tier) {
    return List<String>.from(tierConfigs[tier]?['benefits'] ?? []);
  }

  static int getTierColor(UserTier tier) {
    return tierConfigs[tier]?['color'] ?? 0xFF9E9E9E;
  }

  static UserTier calculateTier(double lifetimeSpending) {
    if (lifetimeSpending >= 100000) return UserTier.diamond;
    if (lifetimeSpending >= 50000) return UserTier.platinum;
    if (lifetimeSpending >= 15000) return UserTier.gold;
    if (lifetimeSpending >= 5000) return UserTier.silver;
    return UserTier.bronze;
  }
}
