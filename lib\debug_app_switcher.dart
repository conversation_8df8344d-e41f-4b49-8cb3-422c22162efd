import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/config/app_config.dart';
import 'main_user.dart';
import 'main_rider.dart';
import 'main_seller.dart';

/// Debug app switcher for development
/// Usage: flutter run -t lib/debug_app_switcher.dart
void main() {
  runApp(const ProviderScope(child: AppSwitcherApp()));
}

class AppSwitcherApp extends StatelessWidget {
  const AppSwitcherApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Projek App Switcher',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const AppSwitcherPage(),
    );
  }
}

class AppSwitcherPage extends StatelessWidget {
  const AppSwitcherPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🚀 Projek App Switcher'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.blue.shade50, Colors.white],
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'Choose App to Launch',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 40),

                // User App Card
                _buildAppCard(
                  context,
                  title: 'User App',
                  subtitle: 'Book services & track orders',
                  icon: Icons.person,
                  color: Colors.blue,
                  onTap: () => _launchApp(context, AppType.user),
                ),

                const SizedBox(height: 20),

                // Rider App Card
                _buildAppCard(
                  context,
                  title: 'Rider App',
                  subtitle: 'Deliver orders & navigate',
                  icon: Icons.delivery_dining,
                  color: Colors.green,
                  onTap: () => _launchApp(context, AppType.rider),
                ),

                const SizedBox(height: 20),

                // Seller App Card
                _buildAppCard(
                  context,
                  title: 'Seller App',
                  subtitle: 'Manage products & orders',
                  icon: Icons.store,
                  color: Colors.orange,
                  onTap: () => _launchApp(context, AppType.seller),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 8,
      shadowColor: color.withOpacity(0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [color.withOpacity(0.1), Colors.white],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: Colors.white, size: 32),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color.lerp(color, Colors.black, 0.3),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: color, size: 20),
            ],
          ),
        ),
      ),
    );
  }

  void _launchApp(BuildContext context, AppType appType) {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('Launching ${_getAppName(appType)}...'),
          ],
        ),
      ),
    );

    // Simulate app launch delay
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.of(context).pop(); // Close loading dialog

      // Set app type and navigate
      AppConfig.setAppType(appType);

      Widget app;
      switch (appType) {
        case AppType.user:
          app = const ProjekUserApp();
          break;
        case AppType.rider:
          app = const ProjekRiderApp();
          break;
        case AppType.seller:
          app = const ProjekSellerApp();
          break;
      }

      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => ProviderScope(child: app)),
      );
    });
  }

  String _getAppName(AppType appType) {
    switch (appType) {
      case AppType.user:
        return 'User App';
      case AppType.rider:
        return 'Rider App';
      case AppType.seller:
        return 'Seller App';
    }
  }
}
