{"java.configuration.updateBuildConfiguration": "automatic", "java.search.scope": "all", "dart.flutterSdkPath": null, "dart.debugExternalLibraries": false, "dart.debugSdkLibraries": false, "dart.hotReloadOnSave": "always", "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.flutterHotReloadOnSave": "always", "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreatePlatforms": ["android", "ios", "web", "windows"], "dart.env": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache", "FLUTTER_WEB_USE_SKIA": "true", "FLUTTER_WEB_AUTO_DETECT": "true"}, "files.associations": {"*.dart": "dart"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": true, "source.organizeImports": true}, "editor.rulers": [80, 120], "editor.wordWrap": "bounded", "editor.wordWrapColumn": 120, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.dart_tool": true, "**/android/.gradle": true, "**/android/app/build": true, "**/ios/build": true, "**/web/build": true}, "search.exclude": {"**/build": true, "**/.dart_tool": true, "**/android/.gradle": true, "**/android/app/build": true}, "flutter.experiments": {"flutter-web": true}, "terminal.integrated.env.windows": {"PUB_CACHE": "E:\\Appdata\\flutter_pub_cache", "FLUTTER_WEB_USE_SKIA": "true"}}