import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'rating_models.g.dart';

@HiveType(typeId: 20)
@JsonSerializable()
class Rating extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String targetId; // rider, seller, or service ID
  
  @HiveField(3)
  final String targetType; // 'rider', 'seller', 'service'
  
  @HiveField(4)
  final String orderId;
  
  @HiveField(5)
  final double rating; // 1-5 stars
  
  @HiveField(6)
  final String? review;
  
  @HiveField(7)
  final List<String> tags; // ['fast', 'polite', 'professional']
  
  @HiveField(8)
  final List<String> images; // review images
  
  @HiveField(9)
  final DateTime createdAt;
  
  @HiveField(10)
  final DateTime updatedAt;
  
  @HiveField(11)
  final bool isVerified;
  
  @HiveField(12)
  final int helpfulCount;
  
  @HiveField(13)
  final String userName;
  
  @HiveField(14)
  final String? userAvatar;

  const Rating({
    required this.id,
    required this.userId,
    required this.targetId,
    required this.targetType,
    required this.orderId,
    required this.rating,
    this.review,
    this.tags = const [],
    this.images = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.helpfulCount = 0,
    required this.userName,
    this.userAvatar,
  });

  factory Rating.fromJson(Map<String, dynamic> json) => _$RatingFromJson(json);
  Map<String, dynamic> toJson() => _$RatingToJson(this);

  @override
  List<Object?> get props => [
    id, userId, targetId, targetType, orderId, rating, review,
    tags, images, createdAt, updatedAt, isVerified, helpfulCount,
    userName, userAvatar,
  ];
}

@HiveType(typeId: 21)
@JsonSerializable()
class RatingSummary extends Equatable {
  @HiveField(0)
  final String targetId;
  
  @HiveField(1)
  final String targetType;
  
  @HiveField(2)
  final double averageRating;
  
  @HiveField(3)
  final int totalRatings;
  
  @HiveField(4)
  final Map<int, int> ratingDistribution; // {5: 120, 4: 80, 3: 20, 2: 5, 1: 2}
  
  @HiveField(5)
  final List<String> topTags; // most mentioned positive tags
  
  @HiveField(6)
  final DateTime lastUpdated;

  const RatingSummary({
    required this.targetId,
    required this.targetType,
    required this.averageRating,
    required this.totalRatings,
    required this.ratingDistribution,
    required this.topTags,
    required this.lastUpdated,
  });

  factory RatingSummary.fromJson(Map<String, dynamic> json) => _$RatingSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$RatingSummaryToJson(this);

  double get ratingPercentage => (averageRating / 5.0) * 100;
  
  String get ratingText {
    if (averageRating >= 4.5) return 'Excellent';
    if (averageRating >= 4.0) return 'Very Good';
    if (averageRating >= 3.5) return 'Good';
    if (averageRating >= 3.0) return 'Average';
    return 'Poor';
  }

  @override
  List<Object?> get props => [
    targetId, targetType, averageRating, totalRatings,
    ratingDistribution, topTags, lastUpdated,
  ];
}

@HiveType(typeId: 22)
enum RatingFilter {
  @HiveField(0)
  all,
  @HiveField(1)
  fiveStar,
  @HiveField(2)
  fourStar,
  @HiveField(3)
  threeStar,
  @HiveField(4)
  twoStar,
  @HiveField(5)
  oneStar,
  @HiveField(6)
  withReviews,
  @HiveField(7)
  withImages,
  @HiveField(8)
  verified,
}

// Predefined rating tags
class RatingTags {
  static const List<String> riderTags = [
    'Fast Delivery', 'Polite', 'Professional', 'On Time', 'Careful Handling',
    'Good Communication', 'Clean Vehicle', 'Safe Driving', 'Helpful',
  ];
  
  static const List<String> sellerTags = [
    'Quality Products', 'Fast Response', 'Good Packaging', 'Honest Pricing',
    'Professional Service', 'Reliable', 'Good Communication', 'Helpful',
  ];
  
  static const List<String> serviceTags = [
    'High Quality', 'Value for Money', 'Professional', 'On Time',
    'Clean Work', 'Skilled', 'Reliable', 'Courteous',
  ];
  
  static List<String> getTagsForType(String type) {
    switch (type.toLowerCase()) {
      case 'rider':
        return riderTags;
      case 'seller':
        return sellerTags;
      case 'service':
        return serviceTags;
      default:
        return [];
    }
  }
}
