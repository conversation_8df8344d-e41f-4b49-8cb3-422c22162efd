import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import '../../../../services/auth_service.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<OnboardingScreen> _screens = [
    OnboardingScreen(
      title: 'Welcome to Projek',
      subtitle: 'Proudly Made in India 🇮🇳',
      description: 'Join India\'s fastest growing delivery platform and start earning on your own schedule',
      imagePath: 'assets/images/onboarding_welcome.png',
      backgroundColor: const Color(0xFF4CAF50),
      accentColor: const Color(0xFFFF9800),
    ),
    OnboardingScreen(
      title: 'Earn Money on Your Schedule',
      subtitle: 'Flexible Income, Maximum Freedom',
      description: 'Earn ₹15,000 - ₹45,000 per month working part-time or full-time. You decide when to work!',
      imagePath: 'assets/images/onboarding_earnings.png',
      backgroundColor: const Color(0xFF2196F3),
      accentColor: const Color(0xFF4CAF50),
    ),
    OnboardingScreen(
      title: 'Dual Service Options',
      subtitle: 'Food Delivery + Ride Sharing',
      description: 'Maximize your earnings by delivering food AND providing rides. Switch between services instantly!',
      imagePath: 'assets/images/onboarding_services.png',
      backgroundColor: const Color(0xFF9C27B0),
      accentColor: const Color(0xFFFF5722),
    ),
    OnboardingScreen(
      title: 'Safety First',
      subtitle: 'Your Safety is Our Priority',
      description: 'Mandatory helmet usage, comprehensive insurance coverage, and 24/7 emergency support for all riders',
      imagePath: 'assets/images/onboarding_safety.png',
      backgroundColor: const Color(0xFFFF5722),
      accentColor: const Color(0xFF4CAF50),
    ),
    OnboardingScreen(
      title: 'Join the Projek Family',
      subtitle: 'Be Part of Something Bigger',
      description: 'Join thousands of riders across India who have made Projek their trusted earning partner',
      imagePath: 'assets/images/onboarding_community.png',
      backgroundColor: const Color(0xFF607D8B),
      accentColor: const Color(0xFFFF9800),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _screens.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  void _completeOnboarding() async {
    await AuthService().completeOnboarding();
    if (mounted) {
      context.go('/auth/register');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _screens[_currentPage].backgroundColor,
                  _screens[_currentPage].backgroundColor.withOpacity(0.8),
                ],
              ),
            ),
          ),
          
          // Indian flag pattern overlay (subtle)
          Positioned(
            top: -50,
            right: -50,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    Colors.orange.withOpacity(0.1),
                    Colors.white.withOpacity(0.05),
                    Colors.green.withOpacity(0.1),
                  ],
                ),
              ),
            ),
          ),
          
          // Main content
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
              _animationController.reset();
              _animationController.forward();
            },
            itemCount: _screens.length,
            itemBuilder: (context, index) {
              return _buildOnboardingScreen(_screens[index]);
            },
          ),
          
          // Navigation controls
          Positioned(
            bottom: 50,
            left: 20,
            right: 20,
            child: _buildNavigationControls(),
          ),
        ],
      ),
    );
  }

  Widget _buildOnboardingScreen(OnboardingScreen screen) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 80),
              
              // Illustration placeholder (in a real app, use actual images)
              Container(
                width: 280,
                height: 280,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _getScreenIcon(_currentPage),
                      size: 120,
                      color: Colors.white,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Illustration',
                      style: GoogleFonts.poppins(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 60),
              
              // Title
              Text(
                screen.title,
                style: GoogleFonts.poppins(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 12),
              
              // Subtitle
              Text(
                screen.subtitle,
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: screen.accentColor,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 24),
              
              // Description
              Text(
                screen.description,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.9),
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getScreenIcon(int index) {
    switch (index) {
      case 0:
        return Icons.waving_hand;
      case 1:
        return Icons.currency_rupee;
      case 2:
        return Icons.delivery_dining;
      case 3:
        return Icons.security;
      case 4:
        return Icons.groups;
      default:
        return Icons.star;
    }
  }

  Widget _buildNavigationControls() {
    return Column(
      children: [
        // Page indicators
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            _screens.length,
            (index) => Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentPage == index ? 24 : 8,
              height: 8,
              decoration: BoxDecoration(
                color: _currentPage == index
                    ? Colors.white
                    : Colors.white.withOpacity(0.4),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Navigation buttons
        Row(
          children: [
            // Skip button
            if (_currentPage < _screens.length - 1)
              TextButton(
                onPressed: _skipOnboarding,
                child: Text(
                  'Skip',
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 16,
                  ),
                ),
              )
            else
              const SizedBox(width: 60),
            
            const Spacer(),
            
            // Previous button
            if (_currentPage > 0)
              IconButton(
                onPressed: _previousPage,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                ),
              ),
            
            const SizedBox(width: 16),
            
            // Next/Get Started button
            ElevatedButton(
              onPressed: _nextPage,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: _screens[_currentPage].backgroundColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              child: Text(
                _currentPage == _screens.length - 1 ? 'Get Started' : 'Next',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class OnboardingScreen {
  final String title;
  final String subtitle;
  final String description;
  final String imagePath;
  final Color backgroundColor;
  final Color accentColor;

  const OnboardingScreen({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.imagePath,
    required this.backgroundColor,
    required this.accentColor,
  });
}
